/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin');
module.exports = {
  content: ['./src/**/*.{html,js,ts,jsx,vue}'],
  theme: {
    extend: {
      screens: {
        xs: { min: '320px', max: '374px' },
        sm: { min: '375px', max: '639px' },
      },
    },
  },
  plugins: [
    plugin(function ({ addComponents }) {
      addComponents({
        '.bg-linear-gradient-1': {
          background:
            'linear-gradient( 180deg, #2d709c 10.13%, rgba(145, 36, 254, 0) 55.51% ), #090422',
        },
      });
    }),
  ],
};
