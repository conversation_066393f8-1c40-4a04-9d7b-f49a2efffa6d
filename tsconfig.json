{"extends": "@quasar/app-vite/tsconfig-preset", "compilerOptions": {"baseUrl": ".", "paths": {"@*": ["src/*"], "@types": ["src/types"], "@components": ["src/components"], "@composables": ["src/composables"], "@helpers": ["src/helpers"], "@pages": ["src/pages"], "@stores": ["src/store"], "@repositories": ["src/repositories"], "@routerPages": ["src/routerPages"], "@routerDialogs": ["src/routerDialogs"], "@microRouter": ["src/microRouter"], "@constants": ["src/constants"], "@directives": ["src/directives"]}}, "exclude": ["./dist", "./.quasar", "./node_modules", "./src-capacitor", "./src-cordova", "./quasar.config.*.temporary.compiled*"]}