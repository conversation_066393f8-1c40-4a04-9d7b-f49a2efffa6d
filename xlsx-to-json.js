const XLSX = require('xlsx');
const fs = require('fs');

const workbook = XLSX.readFile('translation.xlsx');
const worksheet = workbook.Sheets[workbook.SheetNames[0]];
const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

const json = {};

const languages = data[0].slice(1);

data.slice(1).forEach((row) => {
  const key = row[0];
  const values = row.slice(1);

  languages.forEach((language, index) => {
    if (!json[language]) json[language] = {};
    json[language][key] = values[index];
  });
});

fs.writeFileSync('./src/i18n/temp.json', JSON.stringify(json, null, 2));
