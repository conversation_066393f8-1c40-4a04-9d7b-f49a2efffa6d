/* eslint-env node */

/*
 * This file runs in a Node context (it's NOT transpiled by Babel), so use only
 * the ES6 features that are supported by your Node version. https://node.green/
 */

// Configuration for your app
// https://v2.quasar.dev/quasar-cli-vite/quasar-config-js

const { configure } = require('quasar/wrappers');
const path = require('path');
const viteStringReplacement = require('vite-string-replacement');

module.exports = configure(function (/* ctx */) {
  return {
    // https://v2.quasar.dev/quasar-cli-vite/prefetch-feature
    // preFetch: true,

    // app boot file (/src/boot)
    // --> boot files are part of "main.js"
    // https://v2.quasar.dev/quasar-cli-vite/boot-files
    boot: [
      'i18n',
      'axios',
      'pinia',
      'tanstack-vue-query',
      'directives',
      // 'posthog',
    ],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#css
    css: ['app.scss'],

    // https://github.com/quasarframework/quasar/tree/dev/extras
    extras: [
      // 'ionicons-v4',
      // 'mdi-v7',
      // 'fontawesome-v6',
      // 'eva-icons',
      // 'themify',
      // 'line-awesome',
      // 'roboto-font-latin-ext', // this or either 'roboto-font', NEVER both!

      'roboto-font', // optional, you are not bound to it
      'material-icons', // optional, you are not bound to it
    ],

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#build
    build: {
      target: {
        browser: ['es2019', 'edge88', 'firefox78', 'chrome87', 'safari13.1'],
        node: 'node20',
      },

      vueRouterMode: 'history', // available values: 'hash', 'history'
      // vueRouterBase,
      // vueDevtools,
      // vueOptionsAPI: false,

      // rebuildCache: true, // rebuilds Vite/linter/etc cache on startup

      // publicPath: '/',
      // analyze: true,
      env: {
        CAMPAIGN_NAME: 'sentosa',
        APP_NAME: 'HTM_SG',
        APP_END_POINT: 'https://staging.htm-global.sqkii.com',
        APP_COUNTRY_CODE: 'SG',
        APP_LANGUAGE_CODE: 'en',
        APP_API_PREFIX: 'api',
        HMAC_SECRET: 'eDplDHuxCsGHiJTX',
        MAP_STYLE: 'https://worldwidemaps.sqkii.com/api/maps/test/style.json',
        SEASON_ONGOING: 'https://vn.huntthemouse.sqkii.com',
        BUILD_VERSION: '',
        GOOGLE_API_KEY: 'AIzaSyATkUOPjFKsOV7h-FnN2vfHxQBExXl2WmU',
        APP_VOUCHERS_END_POINT: 'https://staging.sdk.vouchers.sqkii.com',
        APP_VOUCHERS_API_PREFIX: 'api/v2',
        VOUCHERS_HMAC_SECRET: 'rB9d3JtZjIJffbGd',
        SIGN_KEY: 'cG1o5zczRX9hd7Aq61Hn6AMM',
        VOUCHERS_URL: 'https://staging.voucher.sqkii.com',
        TITLE: '#HuntTheMouse — DBS SG60 Edition',
        DESCRIPTION:
          'S$120,000 will be hidden in Singapore Heartlands from 14 August 2025 onwards. All coins must be found in the next 43 days. Pick up the coins and get cash.',
        ONE_SIGNAL_APP_ID: '2129c8ce-5bd1-4b70-b8a5-912c13a4d40e',
        ONE_SIGNAL_SAFARI_WEB_ID:
          'web.onesignal.auto.************************************',
        POSTHOG_API_KEY: 'phc_yPU7JsuGBjE2Lje9u5aBNLlKyPLbkKAV7J5WXoUFGZc',
        DEPLOYMENT_NUMBER: '1753763607',
        IS_TESTING_ENV: true,
        TURNSTILE_SITE_KEY: '0x4AAAAAABfyXJ1l3UYQfeFe',
      },
      // rawDefine: {}
      // ignorePublicFolder: true,
      // minify: false,
      // polyfillModulePreload: true,
      // distDir

      extendViteConf(config) {
        const aliases = {
          '@': './src',
          '@types': './src/types',
          '@components': './src/components',
          '@composables': './src/composables',
          '@helpers': './src/helpers',
          '@pages': './src/pages',
          '@stores': './src/store',
          '@repositories': './src/repositories',
          '@routerPages': './src/routerPages',
          '@routerDialogs': './src/routerDialogs',
          '@microRouter': './src/microRouter',
          '@constants': './src/constants',
          '@directives': './src/directives',
        };

        Object.keys(aliases).forEach((key) => {
          config.resolve.alias[key] = path.join(__dirname, aliases[key]);
        });

        config.build = config.build || {};
        config.build.rollupOptions = config.build.rollupOptions || {};
        const now = Date.now();

        config.build.rollupOptions.output = {
          entryFileNames: `assets/[name].${now}.js`,
          chunkFileNames: `assets/[name].${now}.js`,
          assetFileNames: `assets/[name].${now}.[ext]`,
        };

        return config;
      },
      // viteVuePluginOptions: {},

      vitePlugins: [
        [
          '@intlify/vite-plugin-vue-i18n',
          {
            // if you want to use Vue I18n Legacy API, you need to set `compositionOnly: false`
            // compositionOnly: false,

            // if you want to use named tokens in your Vue I18n messages, such as 'Hello {name}',
            // you need to set `runtimeOnly: false`
            runtimeOnly: false,

            // you need to set i18n resource including paths !
            include: path.resolve(__dirname, './src/i18n/**'),
          },
        ],
        [
          'unplugin-auto-import/vite',
          {
            imports: [
              'vue',
              'vue-router',
              'vue-i18n',
              'pinia',
              {
                quasar: ['LocalStorage', 'Platform'],
              },
              {
                'vue-micro-route': [['useRoute', 'useMicroRoute']],
              },
            ],
          },
        ],
        ['unplugin-vue-components/vite', { dirs: ['./src/@core'], dts: true }],

        viteStringReplacement([
          {
            filter: /\.vue$|\.ts$|\.scss$|.css$/,
            replace: [
              {
                from: /\/?imgs\//gim,
                to: 'https://assets.sqkii.com/htm1m/imgs/',
              },
              {
                from: /\/?audios\//gim,
                to: 'https://assets.sqkii.com/htm1m/audios/',
              },
              {
                from: /\/?anims\//gim,
                to: 'https://assets.sqkii.com/htm1m/anims/',
              },
            ],
          },
        ]),
      ],
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#devServer
    devServer: {
      // https: true
      open: true, // opens browser window automatically
    },

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#framework
    framework: {
      config: {},

      // iconSet: 'material-icons', // Quasar icon set
      // lang: 'en-US', // Quasar language pack

      // For special cases outside of where the auto-import strategy can have an impact
      // (like functional components as one of the examples),
      // you can manually specify Quasar components/directives to be available everywhere:
      //
      // components: [],
      // directives: [],

      // Quasar plugins
      plugins: ['Notify', 'LocalStorage', 'Loading'],
    },

    // animations: 'all', // --- includes all animations
    // https://v2.quasar.dev/options/animations
    animations: [],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#sourcefiles
    // sourceFiles: {
    //   rootComponent: 'src/App.vue',
    //   router: 'src/router/index',
    //   store: 'src/store/index',
    //   registerServiceWorker: 'src-pwa/register-service-worker',
    //   serviceWorker: 'src-pwa/custom-service-worker',
    //   pwaManifestFile: 'src-pwa/manifest.json',
    //   electronMain: 'src-electron/electron-main',
    //   electronPreload: 'src-electron/electron-preload'
    // },

    // https://v2.quasar.dev/quasar-cli-vite/developing-ssr/configuring-ssr
    ssr: {
      // ssrPwaHtmlFilename: 'offline.html', // do NOT use index.html as name!
      // will mess up SSR

      // extendSSRWebserverConf (esbuildConf) {},
      // extendPackageJson (json) {},

      pwa: true,

      // manualStoreHydration: true,
      // manualPostHydrationTrigger: true,

      prodPort: 3000, // The default port that the production server should use
      // (gets superseded if process.env.PORT is specified at runtime)

      middlewares: [
        'render', // keep this as last one
      ],
    },

    // https://v2.quasar.dev/quasar-cli-vite/developing-pwa/configuring-pwa
    pwa: {
      workboxMode: 'injectManifest', // or 'injectManifest'
      injectPwaMetaTags: true,
      swFilename: 'sw.js',
      manifestFilename: 'manifest.json',
      useCredentialsForManifestTag: false,
      // useFilenameHashes: true,
      // extendGenerateSWOptions (cfg) {}
      extendInjectManifestOptions(cfg) {
        cfg.globPatterns = [
          'anims/intro.mp4',
          'imgs/sov/**/*.{png,jpg,jpeg,svg}',
          'imgs/kv/*.{png,jpg,jpeg,svg}',
          'imgs/map/*.{png,jpg,jpeg,svg}',
        ];
      },
      // extendManifestJson (json) {}
      // extendPWACustomSWConf (esbuildConf) {}
    },

    sourceFiles: {
      pwaRegisterServiceWorker: 'src-pwa/register-service-worker',
      pwaServiceWorker: 'src-pwa/custom-service-worker',
      pwaManifestFile: 'src-pwa/manifest.json',
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-cordova-apps/configuring-cordova
    cordova: {
      // noIosLegacyBuildFlag: true, // uncomment only if you know what you are doing
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-capacitor-apps/configuring-capacitor
    capacitor: {
      hideSplashscreen: true,
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-electron-apps/configuring-electron
    electron: {
      // extendElectronMainConf (esbuildConf)
      // extendElectronPreloadConf (esbuildConf)

      inspectPort: 5858,

      bundler: 'packager', // 'packager' or 'builder'

      packager: {
        // https://github.com/electron-userland/electron-packager/blob/master/docs/api.md#options
        // OS X / Mac App Store
        // appBundleId: '',
        // appCategoryType: '',
        // osxSign: '',
        // protocol: 'myapp://path',
        // Windows only
        // win32metadata: { ... }
      },

      builder: {
        // https://www.electron.build/configuration/configuration

        appId: 'super-htm-frontend',
      },
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-browser-extensions/configuring-bex
    bex: {
      contentScripts: ['my-content-script'],

      // extendBexScriptsConf (esbuildConf) {}
      // extendBexManifestJson (json) {}
    },
  };
});
