/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Button: typeof import('./src/@core/components/Button.vue')['default']
    DbsDialog: typeof import('./src/@core/components/DbsDialog.vue')['default']
    Dialog: typeof import('./src/@core/components/Dialog.vue')['default']
    Expansion: typeof import('./src/@core/components/Expansion.vue')['default']
    HeaderCrystal: typeof import('./src/@core/components/HeaderCrystal.vue')['default']
    HunterTool: typeof import('./src/@core/components/HunterTool.vue')['default']
    Icon: typeof import('./src/@core/components/Icon.vue')['default']
    Input: typeof import('./src/@core/components/Input.vue')['default']
    InputCountry: typeof import('./src/@core/components/InputCountry.vue')['default']
    InventoryQuickView: typeof import('./src/@core/components/InventoryQuickView.vue')['default']
    MediaRenderer: typeof import('./src/@core/components/MediaRenderer.vue')['default']
    MicroRouterView: typeof import('./src/@core/layouts/MicroRouterView.vue')['default']
    NanciiInstructor: typeof import('./src/@core/components/NanciiInstructor.vue')['default']
    OTP: typeof import('./src/@core/components/OTP.vue')['default']
    Requirements: typeof import('./src/@core/components/Requirements.vue')['default']
    RMISqkiiInstructor: typeof import('./src/@core/components/RMISqkiiInstructor.vue')['default']
    RoutePage: typeof import('./src/@core/layouts/RoutePage.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Select: typeof import('./src/@core/components/Select.vue')['default']
    SelectCountry: typeof import('./src/@core/components/SelectCountry.vue')['default']
    ShinobiiInstructor: typeof import('./src/@core/components/ShinobiiInstructor.vue')['default']
    SilverCoinPowerUpSelection: typeof import('./src/@core/components/SilverCoinPowerUpSelection.vue')['default']
    SingleOTP: typeof import('./src/@core/components/SingleOTP.vue')['default']
    SqkiiInstructor: typeof import('./src/@core/components/SqkiiInstructor.vue')['default']
    StreamBarcodeReader: typeof import('./src/@core/components/StreamBarcodeReader.vue')['default']
    Tab: typeof import('./src/@core/components/Tab.vue')['default']
    TestingComponent: typeof import('./src/@core/components/TestingComponent.vue')['default']
    TimiiInstructor: typeof import('./src/@core/components/TimiiInstructor.vue')['default']
    UnifyInstructor: typeof import('./src/@core/components/UnifyInstructor.vue')['default']
    VeeCheckbox: typeof import('./src/@core/components/VeeCheckbox.vue')['default']
    VeeInput: typeof import('./src/@core/components/VeeInput.vue')['default']
    VeeInputCountry: typeof import('./src/@core/components/VeeInputCountry.vue')['default']
    VeeOTP: typeof import('./src/@core/components/VeeOTP.vue')['default']
    VeeSelect: typeof import('./src/@core/components/VeeSelect.vue')['default']
  }
}
