const XLSX = require('xlsx');
const fs = require('fs');

const data = require('./src/i18n/translation.json');

const workbook = XLSX.utils.book_new();
const filename = 'translation.xlsx';

fs.unlink(filename, (err) => {
  if (err) console.error(err);
});

const keys = Object.keys(data);
const worksheetData = [['key'].concat(keys)];

Object.keys(data[keys[0]]).forEach((key) => {
  const row = [key];
  keys.forEach((k) => {
    row.push(data[k][key]);
  });
  worksheetData.push(row);
});

const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
XLSX.utils.book_append_sheet(workbook, worksheet, 'translations');

XLSX.writeFile(workbook, filename);
