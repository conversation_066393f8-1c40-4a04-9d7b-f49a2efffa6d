#!/usr/bin/env bash

SERVER="oci_sqkii_staging"

DEPLOY_PATH="/home/<USER>"
SOURCE_FOLDER_NAME="dist/pwa"
DEPLOY_SOURCE_FOLDER_NAME="/var/www/HTM_GLOBAL/htm-global-frontend"
FILE_NAME="spa.tar.gz"

DEPLOYMENT_NUMBER=$(date +%s)

mv quasar.config.js quasar.config.bk
cp quasar.config.staging.js quasar.config.js
sed -i '' 's/{DEPLOYMENT_NUMBER}/'$DEPLOYMENT_NUMBER'/g' quasar.config.js
yarn
yarn build
rm -rf $FILE_NAME
sleep 2
tar --exclude="$SOURCE_FOLDER_NAME/imgs" --exclude="$SOURCE_FOLDER_NAME/audios" --exclude="$SOURCE_FOLDER_NAME/anims" -zcvf $FILE_NAME $SOURCE_FOLDER_NAME
echo "=================================="
echo "Upload source..."
scp $FILE_NAME $SERVER:$DEPLOY_PATH
echo "=================================="
echo "Deploying..."
mv quasar.config.bk quasar.config.js

SCRIPT1="sudo mkdir -p $DEPLOY_SOURCE_FOLDER_NAME && sudo tar -xzvf $FILE_NAME"
SCRIPT2="sudo rm -rf $DEPLOY_SOURCE_FOLDER_NAME/!\(uploads\)"
SCRIPT3="sudo rsync -a $SOURCE_FOLDER_NAME/* $DEPLOY_SOURCE_FOLDER_NAME/"
SCRIPT4="sudo rm -rf $SOURCE_FOLDER_NAME"
SCRIPT5="sudo rm $FILE_NAME"
ssh $SERVER "$SCRIPT1 && $SCRIPT2 && $SCRIPT3 && $SCRIPT4 && $SCRIPT5"

echo "=================================="
rm -rf $FILE_NAME
rm -rf $SOURCE_FOLDER_NAME
echo "Done"\

# curl https://api.notion.sqkii.com/notion -X GET -H "api-key:f5d6a39a-b45e-4301-86cf-c41140e44cca" -H "cid:Frontend" -H "env:Staging"

echo "=================================="
echo "Deploymennt Number: $DEPLOYMENT_NUMBER"
