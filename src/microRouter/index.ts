import { Holding } from '@components';

import {
  AboutEliminatedHint,
  AboutSilver,
  AboutTextHint,
  Announcement,
  BAStatus,
  BASurvey,
  BASurveyCompleted,
  BrandSponsor,
  BuildUpdate,
  CameraPermission,
  ChangeHunterID,
  ChangeHunterIDSuccess,
  ChangeMail,
  ChangePIN,
  CoinExtraSafety,
  CoinLimit,
  CoinVerificationVide,
  ConfirmEliminatedHint,
  ConfirmSubmit,
  ConfirmTextHint,
  ContestAnn,
  ContestReferral,
  CreateKeeAccount,
  DailyLogin,
  DetailsWinner,
  DontLikeHunterID,
  EliminatedHint,
  EnterPaymentCode,
  EnterPINPayment,
  EnterPINTopUp,
  EnterPromo,
  EnterSerialNumber,
  EnterTopUpPromo,
  EnterVerifyForm,
  ForfeitedOrTimeUpCoin,
  ForgotPassword,
  ForgotPIN,
  FoundCoin,
  GetSqkiiVouchers,
  GPS,
  HintAnimation,
  HintDetails,
  HourBonus,
  InsufficientCrystals,
  KeeForgotPW,
  KeeRecoverPW,
  Lang,
  LinkKee,
  LinkKeeWelcome,
  LockedAccount,
  Login,
  Logout,
  MetalSonarPowerUp,
  MultipleCardHint,
  NewCoinDrop,
  OTPAnnouncement,
  Outlets,
  PastVideo,
  PaymentResult,
  PayToMerchant,
  PromoSuccess,
  Qualifying,
  ReferralQuestionMark,
  Scan,
  ScanTopUpPromo,
  SetPIN,
  SignUp,
  SignUpSuccess,
  SilverCoin,
  SilverCoinForFounder,
  SingleCardHint,
  SonarConfirm,
  SonarStandBy,
  SonarTerms,
  SonarWarning,
  SqkiiVouchersWelcome,
  SubDomain,
  Survey,
  TAC,
  TextHint,
  TimelineRegister,
  TimeUpCoundown,
  TipsTrick,
  TopUpResult,
  TransactionDetails,
  UseShrink,
  VerifyMobileNumber,
  VotingContest,
  WelcomeGoldenCoin,
  WelcomeHunter,
  WelcomeSafety,
  Zalo,
  TrackStep,
  SetGoal,
  PedometerOnboarding,
  PedometerConfirmOnboarding,
  // AdventureLogV1,
  AdventureLogV2,
  TimelineV1,
  TimelineV2,
  GiveUpFreeShrink,
  Telegram,
  OnboardingGolden,
  VideoIntro,
  CrystalHistory,
  CrystalExpry,
  MyCrystal,
  LearnMore,
  SPFWelcome,
  SPFQuiz,
  SPFResult,
  SPFQuit,
  GiveUpTimeMission,
  SPFCompleted,
  BeaconWelcome,
  MissionStatus,
  FirstBonusCrystals,
  ConfirmFirstBonus,
  EnterPolicyId,
  MetalDetectorWelcome,
  MetalDetectorConfirm,
  LendleaseAction,
  TADaAction,
  TbBossCardAction,
  MetalDetectorWaring,
  MetalDetectorResult,
  RequestTBPermission,
  MetalSonarWelcome,
  MetalDetectorWaringPopup,
  MetalDetectorOverload,
  MidSurvey,
  BATac,
  MerchantAcquisition,
  MerchantAcquisitionSuccess,
  SqkiiNote,
  CapitalandDailyReward,
  GeneoCoinHints,
  CrystalCoinInfo,
  CrystalCoinVerificationVide,
  CrystalCoinForfeitedVide,
  CrystalCoinFound,
  Amenities,
  CapitalandDailyRewardClaimed,
  CapitastarIntro,
  MetalDetectorAccuracy,
  LyndenWoods,
  InventoryDetail,
  InventoryDiscard,
  InventoryHowToUse,
  RMILandMark,
  RMIPreviewImage,
  RMI,
  RMIMoreDeal,
  InventoryConfirmDiscard,
  GuestAccount,
} from '@routerDialogs';

import {
  Home,
  MenuV1,
  EnsuringFairness,
  Setting,
  Events,
  Offerwall,
  Referral,
  FAQ,
  Contest,
  EventUpdate,
  PushNotificationTesting,
  Hint,
  Shop,
  MapEliminatedGrids,
  FoundSilverCoinCongrat,
  FoundGoldenCoinCongrat,
  MenuV2,
  SqkiiVouchers,
  BAInstruction,
  Upload,
  Review,
  Submited,
  SqkiiVouchersSetting,
  SqkiiVouchersFAQ,
  MerchantList,
  OutletDetails,
  SwitchCountry,
  TransactionHistory,
  UseSqkiiVouchers,
  SearchOutlets,
  EventSetting,
  Missions,
  DevTool,
  EnsuringFairnessV2,
  Inventory,
  BeaconTrial,
  BeaconInventoryTrial,
  BeaconTrialSilverShrink,
  BeaconTrialApplied,
  BeaconTrialMetalDetector,
  BeaconTrialCoinSonar,
  CoinSonarTrial,
  MetalDetectorTrial,
  SilverCoinTrial,
  SilverCoinShrinkTrial,
  HuntingStop,
} from '@routerPages';

export const routes = [
  {
    path: 'home',
    component: Home,
    bgm: 'default',
  },
  {
    path: 'hunting_stop',
    component: HuntingStop,
    bgm: 'default',
  },
  {
    path: 'menu_v1',
    component: MenuV1,
    bgm: 'default',
  },
  {
    path: 'menu_v2',
    component: MenuV2,
    bgm: 'default',
  },
  {
    path: 'ensuring_fairness',
    component: EnsuringFairness,
    bgm: 'default',
  },
  {
    path: 'ensuring_fairness_v2',
    component: EnsuringFairnessV2,
    bgm: 'default',
  },
  {
    path: 'setting',
    component: Setting,
    bgm: 'default',
  },
  {
    path: 'events',
    component: Events,
  },
  {
    path: 'offer_wall',
    component: Offerwall,
    bgm: 'default',
  },
  {
    path: 'referral',
    component: Referral,
    bgm: 'default',
  },
  {
    path: 'faq',
    component: FAQ,
    bgm: 'default',
  },
  {
    path: 'contest',
    component: Contest,
    bgm: 'default',
  },
  {
    path: 'ba_instruction',
    component: BAInstruction,
    bgm: 'default',
  },
  {
    path: 'upload',
    component: Upload,
    bgm: 'default',
  },
  {
    path: 'review',
    component: Review,
    bgm: 'default',
  },
  {
    path: 'event_update',
    component: EventUpdate,
    bgm: 'default',
  },
  {
    path: 'submited',
    component: Submited,
    bgm: 'default',
  },
  {
    path: 'found_silver_coin_congrat',
    component: FoundSilverCoinCongrat,
    bgm: 'default',
  },
  {
    path: 'found_golden_coin_congrat',
    component: FoundGoldenCoinCongrat,
    bgm: 'default',
  },
  {
    path: 'my_hint',
    component: Hint,
    bgm: 'default',
  },
  {
    path: 'shop',
    component: Shop,
    bgm: 'default',
  },
  {
    path: 'map_eliminated_grids',
    component: MapEliminatedGrids,
    bgm: 'default',
  },
  {
    path: 'push_notification_testing',
    component: PushNotificationTesting,
    bgm: 'default',
  },
  {
    path: 'sqkii_vouchers',
    component: SqkiiVouchers,
    bgm: 'default',
  },
  {
    path: 'sqkii_vouchers_setting',
    component: SqkiiVouchersSetting,
    bgm: 'default',
  },
  {
    path: 'sqkii_vouchers_faq',
    component: SqkiiVouchersFAQ,
    bgm: 'default',
  },
  {
    path: 'merchant_list',
    component: MerchantList,
    bgm: 'default',
  },
  {
    path: 'outlet_details',
    component: OutletDetails,
    bgm: 'default',
  },
  {
    path: 'transaction_history',
    component: TransactionHistory,
    bgm: 'default',
  },
  {
    path: 'transaction_history',
    component: TransactionHistory,
    bgm: 'default',
  },
  {
    path: 'use_sqkii_vouchers',
    component: UseSqkiiVouchers,
    bgm: 'default',
  },
  {
    path: 'search_outlets',
    component: SearchOutlets,
    bgm: 'default',
  },
  {
    path: 'event_setting',
    component: EventSetting,
    bgm: 'default',
  },
  {
    path: 'missions',
    component: Missions,
    bgm: 'default',
  },
  {
    path: 'crystal_history',
    component: CrystalHistory,
    bgm: 'default',
  },
  {
    path: 'dev_tool',
    component: DevTool,
    bgm: 'default',
  },
  {
    path: 'inventory',
    component: Inventory,
    bgm: 'default',
  },
  {
    path: 'beacon_trial',
    component: BeaconTrial,
    bgm: 'default',
  },
  {
    path: 'beacon_inventory_trial',
    component: BeaconInventoryTrial,
    bgm: 'default',
  },
  {
    path: 'beacon_trial_coin_sonar',
    component: BeaconTrialCoinSonar,
    bgm: 'default',
  },
  {
    path: 'coin_sonar_trial',
    component: CoinSonarTrial,
    bgm: 'default',
  },
  {
    path: 'metal_detector_trial',
    component: MetalDetectorTrial,
    bgm: 'default',
  },
  {
    path: 'silver_coin_trial',
    component: SilverCoinTrial,
    bgm: 'default',
  },
];

export const dialogs = [
  {
    path: 'daily_login',
    component: DailyLogin,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'hour_bonus',
    component: HourBonus,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  // {
  //   path: 'adventure_log_v1',
  //   component: AdventureLogV1,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  {
    path: 'adventure_log_v2',
    component: AdventureLogV2,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'gps',
    component: GPS,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'welcome_hunter',
    component: WelcomeHunter,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'welcome_safety',
    component: WelcomeSafety,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'tac',
    component: TAC,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-left',
    transitionHide: 'slide-right',
    transitionDuration: 500,
  },
  {
    path: 'forfeited_timeup_coin',
    component: ForfeitedOrTimeUpCoin,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'coin_extra_safety',
    component: CoinExtraSafety,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'silver_coin',
    component: SilverCoin,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'use_shrink',
    component: UseShrink,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'about_silver',
    component: AboutSilver,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'insufficient_crystals',
    component: InsufficientCrystals,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'coin_verification_vide',
    component: CoinVerificationVide,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'silver_coin_founder',
    component: SilverCoinForFounder,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'details_winner',
    component: DetailsWinner,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'promo_success',
    component: PromoSuccess,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'found_coin',
    component: FoundCoin,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'referral_question_mark',
    component: ReferralQuestionMark,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'login',
    component: Login,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'signup',
    component: SignUp,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'forgot_password',
    component: ForgotPassword,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'signup_success',
    component: SignUpSuccess,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'logout',
    component: Logout,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'locked_account',
    component: LockedAccount,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'change_hunter_id',
    component: ChangeHunterID,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'change_hunter_id_success',
    component: ChangeHunterIDSuccess,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'enter_promo',
    component: EnterPromo,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'dont_like_hunter_id',
    component: DontLikeHunterID,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'confirm_submit',
    component: ConfirmSubmit,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'voting_contest',
    component: VotingContest,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'camera_permission',
    component: CameraPermission,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'contest_referral',
    component: ContestReferral,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'outlet',
    component: Outlets,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'qualifying',
    component: Qualifying,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'ba_status',
    component: BAStatus,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'mission_pending',
    component: MissionStatus,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'scan',
    component: Scan,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-left',
    transitionHide: 'slide-right',
    transitionDuration: 500,
  },
  {
    path: 'timeline_register',
    component: TimelineRegister,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'tips_trick',
    component: TipsTrick,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'new_coin_drop',
    component: NewCoinDrop,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'contest_announcement',
    component: ContestAnn,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'brand_sponsor',
    component: BrandSponsor,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'ba_survey',
    component: BASurvey,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'holding_page',
    component: Holding,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'ba_survey_completed',
    component: BASurveyCompleted,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'past_video',
    component: PastVideo,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'survey',
    component: Survey,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'mid_survey',
    component: MidSurvey,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'enter_serial_number',
    component: EnterSerialNumber,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'enter_verify_form',
    component: EnterVerifyForm,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'otp_announcement',
    component: OTPAnnouncement,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'verify_mobile_number',
    component: VerifyMobileNumber,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'zalo',
    component: Zalo,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'announcement',
    component: Announcement,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'build_update',
    component: BuildUpdate,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'sub_domain',
    component: SubDomain,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'coin_limit',
    component: CoinLimit,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'lang',
    component: Lang,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'sonar_terms',
    component: SonarTerms,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'sonar_warning',
    component: SonarWarning,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'sonar_confirm',
    component: SonarConfirm,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'metal_sonar_power_up',
    component: MetalSonarPowerUp,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'standby_metal_sonar',
    component: SonarStandBy,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'hint_details',
    component: HintDetails,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'eliminated_hint',
    component: EliminatedHint,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'about_eliminated_hint',
    component: AboutEliminatedHint,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'text_hint',
    component: TextHint,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'about_text_hint',
    component: AboutTextHint,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'confirm_text_hint',
    component: ConfirmTextHint,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'hint_animation',
    component: HintAnimation,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'single_card_hint',
    component: SingleCardHint,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'multiple_card_hint',
    component: MultipleCardHint,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'confirm_eliminated_hint',
    component: ConfirmEliminatedHint,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'welcome_golden_coin',
    component: WelcomeGoldenCoin,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'sqkii_vouchers_welcome',
    component: SqkiiVouchersWelcome,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'link_kee_welcome',
    component: LinkKeeWelcome,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'link_kee',
    component: LinkKee,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'kee_forgot_pw',
    component: KeeForgotPW,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'kee_recover_pw',
    component: KeeRecoverPW,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'set_pin',
    component: SetPIN,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'change_pin',
    component: ChangePIN,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'get_sqkii_vouchers',
    component: GetSqkiiVouchers,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'merchant_acquisition',
    component: MerchantAcquisition,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'merchant_acquisition_success',
    component: MerchantAcquisitionSuccess,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'enter_pin_top_up',
    component: EnterPINTopUp,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'top_up_result',
    component: TopUpResult,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'enter_top_up_promo',
    component: EnterTopUpPromo,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'scan_top_up_promo',
    component: ScanTopUpPromo,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'switch_country',
    component: SwitchCountry,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'transaction_details',
    component: TransactionDetails,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'time_up_coundown',
    component: TimeUpCoundown,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'forgot_pin',
    component: ForgotPIN,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'create_kee_account',
    component: CreateKeeAccount,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'pay_to_merchant',
    component: PayToMerchant,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'enter_pin_payment',
    component: EnterPINPayment,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'payment_result',
    component: PaymentResult,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'enter_payment_code',
    component: EnterPaymentCode,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'first_bonus_crystals',
    component: FirstBonusCrystals,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'confirm_first_bonus',
    component: ConfirmFirstBonus,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'track_step',
    component: TrackStep,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'set_goal',
    component: SetGoal,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'change_mail',
    component: ChangeMail,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'pedometer_onboarding',
    component: PedometerOnboarding,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'pedometer_confirm',
    component: PedometerConfirmOnboarding,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'time_line_v1',
    component: TimelineV1,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-left',
    transitionHide: 'slide-right',
    transitionDuration: 500,
  },
  {
    path: 'time_line_v2',
    component: TimelineV2,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-left',
    transitionHide: 'slide-right',
    transitionDuration: 500,
  },
  {
    path: 'give_up_free_shrink',
    component: GiveUpFreeShrink,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'telegram',
    component: Telegram,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'onboarding_golden',
    component: OnboardingGolden,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'video_intro',
    component: VideoIntro,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'crystal_history',
    component: CrystalHistory,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'crystal_expry',
    component: CrystalExpry,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'my_crystal',
    component: MyCrystal,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'learn_more',
    component: LearnMore,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-left',
    transitionHide: 'slide-right',
    transitionDuration: 500,
  },
  {
    path: 'spf_welcome',
    component: SPFWelcome,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-left',
    transitionHide: 'slide-right',
    transitionDuration: 500,
  },
  {
    path: 'spf_welcome_2',
    component: SPFWelcome,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-left',
    transitionHide: 'slide-right',
    transitionDuration: 500,
  },
  {
    path: 'spf_quiz',
    component: SPFQuiz,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'spf_result',
    component: SPFResult,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'spf_completed',
    component: SPFCompleted,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'spf_quit',
    component: SPFQuit,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'give_up_time_mission',
    component: GiveUpTimeMission,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'beacon_welcome',
    component: BeaconWelcome,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'enter_policy',
    component: EnterPolicyId,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },

  // metal detector
  {
    path: 'metal_detector_welcome',
    component: MetalDetectorWelcome,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'metal_detector_confirm',
    component: MetalDetectorConfirm,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'metal_detector_warning',
    component: MetalDetectorWaring,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'metal_detector_warning_popup',
    component: MetalDetectorWaringPopup,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'metal_detector_overload',
    component: MetalDetectorOverload,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'metal_detector_result',
    component: MetalDetectorResult,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'metal_detector_accuracy',
    component: MetalDetectorAccuracy,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'ba_lendlease',
    component: LendleaseAction,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'ba_tada',
    component: TADaAction,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'ba_tb_boss',
    component: TbBossCardAction,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },

  {
    path: 'request_tb_permission',
    component: RequestTBPermission,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'metal_sonar_welcome',
    component: MetalSonarWelcome,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'ba_tac',
    component: BATac,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'sqkii_note',
    component: SqkiiNote,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  // {
  //   path: 'sentosa_daily_reward',
  //   component: SentosaDailyReward,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  // {
  //   path: 'daily_reward_claimed',
  //   component: DailyRewardClaimed,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: true,
  //   transitionShow: 'fade',
  //   transitionHide: 'fade',
  // },
  // {
  //   path: 'crystal_coin_hints',
  //   component: CrystalCoinHints,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  // {
  //   path: 'crystal_coin_verification_vide',
  //   component: CrystalCoinVerificationVide,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  // {
  //   path: 'crystal_coin_forfeited_vide',
  //   component: CrystalCoinForfeitedVide,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  // {
  //   path: 'crystal_coin_found',
  //   component: CrystalCoinFound,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  // {
  //   path: 'crystal_coin_info',
  //   component: CrystalCoinInfo,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  // {
  //   path: 'sentosa_outlets',
  //   component: SentosaOutlets,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  // {
  //   path: 'special_event_announcement',
  //   component: SpecialEventAnnouncement,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  // {
  //   path: 'sentosa_silver_hint_drop_event',
  //   component: SentosaSilverHintDropEvent,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },
  // {
  //   path: 'sentosa_island_bounty',
  //   component: SentosaIslandBounty,
  //   actived: false,
  //   position: 'standard',
  //   fullscreen: false,
  //   transitionShow: '',
  //   transitionHide: '',
  // },

  // Capitaland
  {
    path: 'capitaland_daily_reward',
    component: CapitalandDailyReward,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'geneo_coin_hints',
    component: GeneoCoinHints,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'crystal_coin_info',
    component: CrystalCoinInfo,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'crystal_coin_verification_vide',
    component: CrystalCoinVerificationVide,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'crystal_coin_forfeited_vide',
    component: CrystalCoinForfeitedVide,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'crystal_coin_found',
    component: CrystalCoinFound,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'amenities',
    component: Amenities,
    actived: false,
    position: 'standard',
    fullscreen: false,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'capitaland_daily_reward_claimed',
    component: CapitalandDailyRewardClaimed,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'capitastar_intro',
    component: CapitastarIntro,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'lynden_woods',
    component: LyndenWoods,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: '',
    transitionHide: '',
  },

  // Inventory
  {
    path: 'inventory_detail',
    component: InventoryDetail,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'inventory_discard',
    component: InventoryDiscard,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'inventory_confirm_discard',
    component: InventoryConfirmDiscard,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'inventory_how_to_use',
    component: InventoryHowToUse,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: '',
    transitionHide: '',
  },
  {
    path: 'beacon_trial_silver_shrink',
    component: BeaconTrialSilverShrink,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'beacon_trial_metal_detector',
    component: BeaconTrialMetalDetector,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'beacon_trial_applied',
    component: BeaconTrialApplied,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'silver_coin_shrink_trial',
    component: SilverCoinShrinkTrial,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
  },
  {
    path: 'rmi_landmark',
    component: RMILandMark,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-up',
    transitionHide: 'slide-down',
    seamless: true,
  },
  {
    path: 'rmi_preview_image',
    component: RMIPreviewImage,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'fade',
    transitionHide: 'fade',
    seamless: true,
  },
  {
    path: 'rmi',
    component: RMI,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-up',
    transitionHide: 'slide-down',
    seamless: true,
  },

  {
    path: 'rmi_more_deal',
    component: RMIMoreDeal,
    actived: false,
    position: 'standard',
    fullscreen: true,
    transitionShow: 'slide-up',
    transitionHide: 'slide-down',
    seamless: true,
  },
  {
    path: 'guest_account',
    component: GuestAccount,
    actived: false,
    position: 'standard',
    fullscreen: true,
  },
];
