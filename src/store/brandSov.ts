import { USER } from '@repositories';
import type { ComponentId } from '@types';
import { defineStore } from 'pinia';

export const useBrandSovStore = defineStore('brand_sov', {
  state: (): {
    probs: Record<
      ComponentId,
      {
        prob: Array<[string, number]>;
        randomRange: [number, number];
      }
    >;
    ready: boolean;
    brands: Record<string, string>;
    assetPacks: Record<string, string>;
  } => ({
    probs: {} as any,
    brands: {},
    ready: false,
    assetPacks: {},
  }),

  getters: {},

  actions: {
    async fetchBrandSovSettings() {
      const { data } = await USER.getBrandSovSettings();

      for (const [id, probs] of Object.entries(data.probs || {})) {
        let total = 0;

        const componentId = id as ComponentId;
        this.probs[componentId] = {
          prob: [],
          randomRange: [0, 0],
        };

        for (const [brandId, prob] of Object.entries(probs)) {
          total += prob;
          this.probs[componentId].prob.push([brandId, total]);
        }

        this.probs[componentId].randomRange = [0, total];
      }
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      this.assetPacks = data.asset_packs || {};
      this.brands = data.brands || {};
      this.ready = true;
    },
  },
});
