import { BRAND_ACTION } from '@repositories';
import { defineStore } from 'pinia';
import type {
  IBrandAction,
  IGlobalBrandAction,
  IMilestones,
  IActivatingTimeMission,
  ITimeMission,
  ISPFQuiz,
  IGetQuizData,
} from '@types';

interface BAState {
  brand_actions: IBrandAction[];
  newBrandActions: IBrandAction[];
  user_brand_actions: IBrandAction[];
  milestones: IMilestones[];
  timeMissionData: IActivatingTimeMission | null;
  skip_mission_price: number;
  timed_mission_cooldown: string;
  quizzes: ISPFQuiz[];
  quizzData: IGetQuizData | null;
  countingWrongQuizzes: number;
  indexQuiz: number;
  holdingSpfQuizMission: IActivatingTimeMission | null; // hold current spf quiz mission to use in case the mission has been expired
  crime_advisory: string | null;
  canSkipQuiz: boolean;
  timedMissions: ITimeMission[];
  pendingMissions: IActivatingTimeMission[];
}

export const useBAStore = defineStore('brand_actions', {
  state: (): BAState => ({
    brand_actions: [],
    newBrandActions: [],
    user_brand_actions: [],
    milestones: [],
    timeMissionData: null,
    skip_mission_price: 0,
    timed_mission_cooldown: '',
    quizzes: [],
    countingWrongQuizzes: 0,
    indexQuiz: 0,
    holdingSpfQuizMission: null,
    crime_advisory: '',
    canSkipQuiz: false,
    timedMissions: [],
    pendingMissions: [],
    quizzData: null,
  }),

  getters: {
    isBANew: (state) => {
      return state.newBrandActions.length > 0;
    },
    brand_actions_byUniqueId: (state) => {
      return state.brand_actions.reduce<Record<string, IBrandAction>>(
        (r, a) => {
          r[a._id] = a;
          return r;
        },
        {}
      );
    },

    triggerTimeMission: (state) => {
      return !!state.timeMissionData?._id;
    },
  },

  actions: {
    async fetchBrandAction() {
      const { data } = await BRAND_ACTION.get();
      this.milestones = data.milestones;
      this.skip_mission_price = data.skip_mission_price;
      this.timed_mission_cooldown = data.timed_mission_cooldown;
      this.timedMissions = data.timed_missions ?? [];
      // this.pendingMissions = data.pendingMissions || [];
      this.mapBrandActions(data);

      this.mapTimeMissionData(
        data.unclaimed_timed_missions.length
          ? {
              ...data,
              activating_timed_mission: data.unclaimed_timed_missions[0],
            }
          : data
      );

      this.mapPendingTimedMission(data.pendingMissions || []);
    },

    mapBrandActions(data: IGlobalBrandAction) {
      const baMap = new Map();

      const brandActions = data.brand_actions.filter((ba) => ba.show_on_list);

      const featuredMap = new Map(
        data.featured_brand_actions?.map((item) => [item.brand_action, item])
      );

      const bonusMap = new Map(
        data.bonus_brand_actions?.map((item) => [item.brand_action, item])
      );

      this.brand_actions = brandActions.map((item) => {
        const newItem = {
          ...item,
          featured: featuredMap.get(item._id),
          bonus: bonusMap.get(item._id),
        };
        baMap.set(item.unique_id, newItem);
        return newItem as unknown as IBrandAction;
      });

      this.newBrandActions = this.brand_actions
        .filter((b) => b.can_perform)
        .map((b) => ({ ...b, status: 'new' }));

      this.user_brand_actions = data.user_brand_actions.map((uba) => {
        const ba = baMap.get(uba.ba_unique_id);
        return {
          ...ba,
          ...uba,
          _id: uba._id,
          reward: ba?.reward,
          user_reward: uba?.reward,
        };
      });
    },

    mapTimeMissionData(data: IGlobalBrandAction) {
      if (!data.activating_timed_mission) {
        if (this.timeMissionData) {
          this.timeMissionData = {
            ...this.timeMissionData,
            is_active: false,
          };
        }
        return;
      }
      const brandAction = data.brand_actions.find(
        (ba) => data.activating_timed_mission?.ba_unique_id === ba.unique_id
      ) as IBrandAction;

      const mission = data.timed_missions.find(
        (m) => data.activating_timed_mission?.mission_unique_id === m.unique_id
      ) as ITimeMission;

      const nextMission = data.timed_missions.find(
        (d) => d.unique_id === mission.next_mission_unique_id
      );

      this.timeMissionData = {
        ...data.activating_timed_mission,
        is_active: true,
        brandAction,
        mission,
        unclaimed_timed_missions: data.unclaimed_timed_missions,
        nextMission,
      };
    },

    mapPendingTimedMission(data: IActivatingTimeMission[]) {
      this.pendingMissions = data.map((el) => ({
        ...el,
        mission: this.timedMissions.find(
          (item) => item.unique_id === el.mission_unique_id
        ) as ITimeMission,
        brandAction: this.user_brand_actions.find(
          (ba) => ba.ba_unique_id === el.ba_unique_id
        ) as IBrandAction,
      }));
    },

    seenBa(_id: string) {
      const index = this.user_brand_actions.findIndex((e) => e._id === _id);
      if (index >= 0) this.user_brand_actions[index].seen = true;
    },

    setQuizzes(quizzes: ISPFQuiz[]) {
      this.quizzes = quizzes;
    },

    setQuizzData(data: IGetQuizData) {
      this.quizzData = data;
    },
  },
});
