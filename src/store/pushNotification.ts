import { useOneSignal } from '@helpers';

export const usePushNotificationStore = defineStore('pushNotification', () => {
  const oneSignal = useOneSignal();

  const initializing = ref(false);

  const initialized = ref(false);
  const permission = ref(oneSignal.Notifications.permission);
  const optedIn = ref(oneSignal.User.PushSubscription.optedIn);
  const oneSignalId = ref(oneSignal.User.onesignalId);
  const subscriptionId = ref(oneSignal.User.PushSubscription.id);

  const pushNotificationStates = computed(() => ({
    initialized: initialized.value,
    permission: permission.value,
    optedIn: optedIn.value,
    oneSignalId: oneSignalId.value,
    subscriptionId: subscriptionId.value,
  }));

  const initPushNotification = async () => {
    try {
      if (initialized.value || initializing.value) {
        return;
      }

      initializing.value = true;
      await oneSignal.init({
        appId: process.env.ONE_SIGNAL_APP_ID || '',
        safari_web_id: process.env.ONE_SIGNAL_SAFARI_WEB_ID,
        notifyButton: {
          enable: false,
        },
        allowLocalhostAsSecureOrigin: true,
        autoResubscribe: true,
        serviceWorkerPath: 'push/onesignal/OneSignalSDKWorker.js',
        serviceWorkerParam: { scope: '/push/onesignal/' },
      });

      initialized.value = true;
      onPermissionChange();
    } catch (error) {
      console.error('error', error);
    } finally {
      initializing.value = false;
    }
  };

  const onPermissionChange = () => {
    permission.value = oneSignal.Notifications.permission;
    optedIn.value = oneSignal.User.PushSubscription.optedIn;
    oneSignalId.value = oneSignal.User.onesignalId;
    subscriptionId.value = oneSignal.User.PushSubscription.id;
  };

  const requestPushNotiPermission = async (userId: string) => {
    if (!oneSignal.Notifications.isPushSupported()) {
      // TODO: track("push_notification_not_supported");
      return;
    }
    await oneSignal.login(userId);
    await oneSignal.Notifications.requestPermission();

    onPermissionChange();
  };

  const optIn = async () => {
    await oneSignal.User.PushSubscription.optIn();
  };

  const optOut = async () => {
    await oneSignal.User.PushSubscription.optOut();
  };

  onMounted(() => {
    oneSignal.User.PushSubscription.addEventListener(
      'change',
      onPermissionChange
    );
    oneSignal.Notifications.addEventListener(
      'permissionChange',
      (permission) => {
        console.log('permissionChange', permission);
      }
    );
    initPushNotification();
  });

  onUnmounted(() => {
    oneSignal.User.PushSubscription.removeEventListener(
      'change',
      onPermissionChange
    );
  });

  return {
    pushNotificationStates,
    oneSignal,

    initPushNotification,
    requestPushNotiPermission,
    optIn,
    optOut,
  };
});
