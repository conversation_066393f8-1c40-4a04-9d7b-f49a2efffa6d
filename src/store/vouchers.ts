import { defineStore } from 'pinia';
import { VOUCHERS } from '@repositories';
import { getCountryCode } from '@helpers';
import type { IOutlet, IVouchersUser } from '@types';

interface VouchersState {
  token: string | null;
  user: IVouchersUser | null;
  outlets: Record<string, IOutlet[]>;
}

export const useVouchersStore = defineStore('vouchers', {
  state: (): VouchersState => ({
    token:
      LocalStorage.getItem(`${process.env.APP_NAME}_SQKII_VOUCHER_TOKEN`) ||
      null,
    user: null,
    outlets: {},
  }),

  getters: {
    isSyncedWithKee: (state) => {
      return !!state.token;
    },
  },

  actions: {
    setUser(user: IVouchersUser) {
      const mobile_number = user.mobile_number;
      const country = getCountryCode(mobile_number);
      LocalStorage.set(
        `${process.env.APP_NAME}_SQKII_VOUCHER_COUNTRY`,
        country
      );
      this.user = {
        ...user,
        country,
      };
    },

    setToken(token: string) {
      this.token = token;
      LocalStorage.set(`${process.env.APP_NAME}_SQKII_VOUCHER_TOKEN`, token);
    },

    updateUser(fieldsToUpdate: Partial<IVouchersUser>) {
      this.user = { ...this.user, ...fieldsToUpdate } as IVouchersUser;
    },

    async fetchOutlets() {
      try {
        const { data } = await VOUCHERS.getOutlets();
        this.outlets = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchUser() {
      try {
        const { data } = await VOUCHERS.getMe();
        const { data: balance } = await VOUCHERS.getUserBalance();
        const country = LocalStorage.getItem(
          `${process.env.APP_NAME}_SQKII_VOUCHER_COUNTRY`
        );
        if (!country)
          LocalStorage.set(
            `${process.env.APP_NAME}_SQKII_VOUCHER_COUNTRY`,
            'SG'
          );

        this.user = {
          ...data.user,
          balance: balance.balance,
          currency: balance.currency,
          country: String(country || 'SG'),
        };
      } catch (error) {
        // console.error('error', error);
      }
    },

    async unSync() {
      try {
        await VOUCHERS.unsync();
      } catch (error) {
        // console.error('error', error);
      }
    },
  },
});
