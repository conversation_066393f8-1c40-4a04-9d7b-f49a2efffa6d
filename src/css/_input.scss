.legend-input {
  position: relative;
  .label {
    position: absolute;
    left: 20px;
    top: 18px;
    font-size: 12px;
    line-height: 14px;
    font-weight: 400;
    margin-top: -4px;

    &.center {
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &.input-focused {
    .label {
      top: 4px;
      transform: translateY(0);
      font-size: 10px;
      line-height: 10px;
      transition: all 0.1s;
      margin-top: 0;
      margin-left: -2px;
      text-align: left;
      width: 90%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .q-field__native {
      margin-top: 4px;
    }
    .q-field__prefix {
      margin-top: 3px;
    }
  }

  .text-normal {
    color: #505050;
  }

  .q-input {
    height: 44px;
    color: #fff;
    background: #04081d;
    border-radius: 10px;
    border: 1px solid #04081d;
    box-shadow: 2px 2px 10px rgba(#04081d, 0.1);
    &.error-input {
      border: 1px solid #ff0000 !important;
      box-shadow: 2px 2px 10px rgba(255, 32, 32, 0.4);

      .q-field__prefix,
      .q-field__native {
        color: #ff0000 !important;
      }
    }
    .q-field__native {
      font-size: 14px;
      line-height: 18px;
    }
    .q-field--outlined .q-field__control {
      border-radius: 10px;
    }
    &.q-field--outlined .q-field__control:before,
    &.q-field--outlined .q-field__control:after {
      border: unset;
    }

    &.disable {
      background: rgba(#04081d, 0.5) !important;
      border: 0px !important;
      .q-field__prefix {
        color: rgba(#fff, 0.5) !important;
      }
    }
  }
  .error-input {
    .q-field__prefix {
      color: #ff0000;
    }
  }

  .q-field__control {
    height: 42px;
  }
  .q-field__native,
  .q-field__prefix,
  .q-field__suffix,
  .q-field__input {
    color: #ffffff !important;
  }

  .icon-password {
    position: absolute;
    top: 50%;
    right: -5px;
    transform: translate(0%, -50%);
    display: flex;
    img {
      width: 16px;
      height: 16px;
    }
  }
}

.legend-input.has-mobile-number {
  .q-input {
    height: 55px;
  }
  .q-field__control {
    margin-top: 12px;
  }

  .q-input .q-field__native {
    padding-left: 70px;
  }
}

.legend-input.has-select {
  .q-input {
    height: 44px !important;
  }
  .q-field__control {
    margin-top: 2px;
  }

  .q-input .q-field__native {
    padding-left: 10px;
  }

  // .q-field__marginal {
  // display: none !important;
  // }
}

.prefix-select {
  position: absolute;
  left: 0;
  top: 9px;
  width: 65px;
  height: calc(100% - 18px);
  &::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 18px;
    background-color: rgba(#fff, 0.5);
    z-index: 1;
    right: 0;
    top: 6px;
  }

  .q-btn {
    &::before {
      box-shadow: none !important;
    }
  }
  i {
    display: none;
  }
}

textarea.q-field__native {
  color: #fff;
}

// OTP input
.v-otp-input {
  display: grid;
  gap: 8px;
  .single-otp {
    align-items: center;
    background: rgba($color: #00f7ff, $alpha: 0.2);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    padding-bottom: calc(100% + 12px);
    position: relative;
    width: 100%;
    input {
      background-color: initial;
      box-shadow: none;
      color: #fff;
      font-size: 30px;
      font-weight: 700;
      line-height: 30px;
      left: 0;
      outline: none;
      padding: 0;
      position: absolute;
      right: 0;
      text-align: center;
      top: 50%;
      transform: translateY(-50%);
      width: 70%;
      border: none;
      margin: 0 auto;
    }
  }
}
