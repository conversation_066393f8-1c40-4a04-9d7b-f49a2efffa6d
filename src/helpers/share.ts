import { errorNotify } from '@helpers';

export function canShare(data: ShareData) {
  return !!navigator.canShare(data) && navigator.share !== void 0;
}

export async function tryShare(
  data: ShareData
): Promise<{ status: 'not_supported' | 'success' | 'failed' }> {
  const { t } = useI18n();

  if (!canShare(data)) {
    errorNotify({
      message: t('SHARE_NOT_SUPPORT'),
    });
    return {
      status: 'not_supported',
    };
  }

  try {
    navigator.share(data);
    return {
      status: 'success',
    };
  } catch (e) {
    return {
      status: 'failed',
    };
  }
}

export async function tryShare2(
  data: ShareData
): Promise<{ status: 'not_supported' | 'success' | 'failed' }> {
  if (!canShare(data)) {
    return {
      status: 'not_supported',
    };
  }

  try {
    await navigator.share(data);
    return {
      status: 'success',
    };
  } catch (e) {
    return {
      status: 'failed',
    };
  }
}
