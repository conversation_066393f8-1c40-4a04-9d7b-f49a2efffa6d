import type { ICountryRegion } from '@types';

export const countries: ICountryRegion[] = [
  {
    name: 'VietNam',
    code: '+84',
    iso: 'VN',
    flag: 'https://cdn.kcak11.com/CountryFlags/countries/vn.svg',
    mask: ['### ### ###'],
    currency: 'VND',
    currencyName: 'Vietnamese Dong (VND)',
    url: 'https://vn.huntthemouse.sqkii.com',
  },
  {
    name: 'Thailand',
    code: '+66',
    iso: 'TH',
    flag: 'https://cdn.kcak11.com/CountryFlags/countries/th.svg',
    mask: ['## #### ####'],
    currency: 'THB',
    currencyName: 'Thai Baht (THB)',
    url: '',
  },
  {
    name: 'Singapore',
    code: '+65',
    iso: 'SG',
    flag: 'https://cdn.kcak11.com/CountryFlags/countries/sg.svg',
    mask: ['#### ####'],
    currency: 'SGD',
    currencyName: 'Singapore Dollar (SGD)',
    url: 'https://huntthemouse.sqkii.com',
  },
  {
    name: 'Malaysia',
    code: '+60',
    iso: 'MY',
    flag: 'https://cdn.kcak11.com/CountryFlags/countries/my.svg',
    mask: [
      '## #### ####',
      '# #### ####',
      '## ### ###',
      '(###)### ###',
      '## ### ####',
    ],
    currency: 'MYR',
    currencyName: 'Malaysian Ringgit (MYR)',
    url: '',
  },
  {
    name: 'Indonesia',
    code: '+62',
    iso: 'ID',
    flag: 'https://cdn.kcak11.com/CountryFlags/countries/id.svg',
    mask: [
      '### #### ####',
      '## ### ##',
      '## ### ###',
      '## ### ####',
      '(8##)### ###',
      '(8##)### ## ###',
    ],
    currency: 'IDR',
    currencyName: 'Indonesian Rupiah (IDR)',
    url: '',
  },
];
