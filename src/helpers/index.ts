import dayjs, { Dayjs } from 'dayjs';
import numeral from 'numeral';
import { LocalStorage, Notify } from 'quasar';
import {
  EMAIL_REGEX,
  FULL_DATE_TIME_FORMAT,
  SINGAPORE_PHONE_REGEX,
} from './config';
import { pickBy, identity } from 'lodash';
import type { NotifyOtp } from './notify';
import type { IAmenities, IUserLang, TSeasonISO } from '@types';
import duration from 'dayjs/plugin/duration';
import utc from 'dayjs/plugin/utc';
import isBetween from 'dayjs/plugin/isBetween';
import 'dayjs/locale/vi';
import 'dayjs/locale/en';
import 'dayjs/locale/ja';
import type { ClassValue } from 'clsx';
import clsx from 'clsx';
import { twMerge } from 'tailwind-merge';
import { CountryCode, parsePhoneNumberFromString } from 'libphonenumber-js';
import { BRAND_SOV_LABELS } from '@constants';
import { USER } from '@repositories';
import type { QNotifyCreateOptions } from 'quasar';

export * from './config';
export * from './countrySeason';
export * from './oneSignal';
export * from './tac_v2';
export * from './share';

dayjs.extend(duration);
dayjs.extend(utc);
dayjs.extend(isBetween);

let dismiss: (opt?: NotifyOtp) => void;

const TIME = {
  vi: {
    d: 'ngày',
    h: 'giờ',
    m: 'phút',
    s: 'giây',
  },
  en: {
    d: 'd',
    h: 'h',
    m: 'm',
    s: 's',
  },
  ja: {
    d: '日',
    h: '時間',
    m: '分',
    s: '秒',
  },
} as Record<IUserLang, Record<string, string>>;

export function showNotify(options: NotifyOtp) {
  dismiss != void 0 && dismiss();
  if (!options) return;

  const notifyOptions: QNotifyCreateOptions = { ...options, html: true };
  dismiss = Notify.create(notifyOptions);
  return dismiss;
}

export function closeNotify() {
  dismiss && dismiss();
}

export function successNotify(options: NotifyOtp) {
  return showNotify({
    classes: options.classes || 'notify-success',
    position: options.position || 'top',
    timeout: options.timeout || 5000,
    message: options.message,
    actions: options.actions || [
      {
        handler: closeNotify,
      },
    ],
  });
}

export function errorNotify(options: NotifyOtp) {
  return showNotify({
    classes: options.classes || 'notify-error',
    position: options.position || 'top',
    timeout: options.timeout || 5000,
    message: options.message,
    actions: options.actions || [
      {
        handler: closeNotify,
      },
    ],
  });
}

export function numeralFormat(number: number, type = 'AUTO') {
  if (type === 'AUTO') {
    switch (true) {
      case Number.isNaN(number):
        type = '0,0';
        break;
      case Math.floor(number) !== number:
        type = '0,0.00';
        break;
      default:
        type = '0,0';
        break;
    }
  }
  return numeral(number).format(
    type,
    (value: number) => Math.round(value * 100) / 100
  );
}

export function formatName(str: string) {
  if (str) {
    const convertToArray = str.toLowerCase().split('_');
    const result = convertToArray.map(function (val) {
      return val.replace(val.charAt(0), val.charAt(0).toUpperCase());
    });
    return result.join(' ');
  }
  return '';
}

export function removeSpace(str: string) {
  return str.replace(/\s/g, '');
}

export function getCountryCode(mobile_number: string) {
  const number = Number(mobile_number.replace('+', '').substring(0, 2));
  const code: Record<number, string> = {
    65: 'SG',
    60: 'MY',
  };
  return code[number] || 'SG';
}

export function dateTimeFormat(
  date: Dayjs | string | number | Date,
  format = FULL_DATE_TIME_FORMAT
) {
  const lang = LocalStorage.getItem('lang') as IUserLang;
  return dayjs(date)
    .locale(lang || process.env.APP_LANGUAGE_CODE)
    .format(format);
}

export function isDifferentDay(date1: string, date2: string) {
  if (!date1 || !date2) return false;
  const d1 = dayjs(date1, 'YYYY-MM-DD');
  const d2 = dayjs(date2, 'YYYY-MM-DD');
  return !!dayjs(d1).diff(d2, 'day');
}

export function isSameDay(date1: string, date2: string) {
  if (!date1 || !date2) return false;
  const d1 = dayjs(date1, 'YYYY-MM-DD');
  const d2 = dayjs(date2, 'YYYY-MM-DD');
  return !!dayjs(d1).isSame(d2, 'day');
}

export function convertTime(milisecons: number) {
  const mils = Math.max(milisecons, 0);
  const days = Math.floor(mils / (1000 * 60 * 60 * 24));
  const hours = Math.floor((mils / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((mils / 1000 / 60) % 60);
  const seconds = Math.floor((mils / 1000) % 60);

  return {
    days: ('0' + days).slice(-2),
    hours: ('0' + hours).slice(-2),
    seconds: ('0' + seconds).slice(-2),
    minutes: ('0' + minutes).slice(-2),
  };
}

export function isMatchSeasonNumber(
  mobile_number: string,
  country?: TSeasonISO
) {
  const countryCodes = {
    SG: /^\+?65(8|9)\d{7}$/,
    MY: /^(\+?6?01)[0|1|2|3|4|6|7|8|9]\-*[0-9]{7,8}$/,
    ID: /^\+?62(8)\d{7,10}$/,
    TH: /^\+?66(08|09)\d{8}$/,
    VN: /^\+?84(3|5|7|8|9)\d{8}$/,
    JP: /^(\+?81[ \-]?(\(0\))?|0)[6789]0[ \-]?\d{4}[ \-]?\d{4}$/,
  };

  if (country) return countryCodes[country]?.test(mobile_number) || false;

  return Object.values(countryCodes).some((regex) => regex.test(mobile_number));
}

export function timeCountDown(time: number) {
  const lang = (LocalStorage.getItem('lang') as IUserLang) || 'en';
  const langSource = TIME[lang] || 'en';

  if (time <= 0) return `00${langSource.s}`;

  const { days, hours, minutes, seconds } = convertTime(time);

  if (+days > 0) {
    return `${days}${langSource.d} ${hours}${langSource.h}`;
  } else if (+hours > 0) {
    return `${hours}${langSource.h} ${minutes}${langSource.m}`;
  } else if (+minutes > 0) {
    return `${minutes}${langSource.m} ${seconds}${langSource.s}`;
  } else {
    return `${seconds}${langSource.s}`;
  }
}
export function dataURLtoFile(dataurl: any, filename: string) {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: mime });
}

export function metaPixelTacking(type: string) {
  if (typeof (window as any).fbq === 'function') {
    (window as any).fbq('track', type);
  } else {
    console.error(
      'fbq is not a function. Please check the Facebook Pixel initialization.'
    );
  }
}

export function isSingaporeNumber(mobile_number: string) {
  return SINGAPORE_PHONE_REGEX.test(mobile_number);
}

export function isEmailValid(email: string) {
  return EMAIL_REGEX.test(email);
}

export function deleteEmptyFields(objects: Record<string, any>) {
  return pickBy(objects, identity);
}

export function deleteField(objects: Record<string, any>, fields: string[]) {
  fields.map((f) => {
    if (objects.hasOwnProperty(f)) delete objects[f];
  });
  return objects;
}

export function getSocials() {
  return [
    {
      link: 'https://t.me/sqkiisg',
      icon: 'telegram',
    },
    {
      link: 'https://www.facebook.com/sqkii',
      icon: 'fb',
    },
    {
      link: 'https://www.instagram.com/sqkiimouse',
      icon: 'insta',
    },
    {
      link: 'https://www.tiktok.com/@sqkiimouse',
      icon: 'tiktok',
    },
  ];
}

export async function downloadPDF(id: string) {
  const htmlContent = document.getElementById(id)?.outerHTML;
  if (!htmlContent) return;
  // Create a hidden iFrame to load the HTML content and print it
  const iFrame = document.createElement('iframe');
  iFrame.style.display = 'none';
  document.body.appendChild(iFrame);

  // Load the HTML content into the iFrame and print it
  const pdfBlob: Blob = await new Promise((resolve) => {
    iFrame.onload = () => {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const iFrameWindow: Window = iFrame.contentWindow!;
      iFrameWindow.print();
      iFrameWindow.addEventListener(
        'afterprint',
        () => {
          // When the print dialog is closed, get the resulting PDF Blob and resolve the Promise
          const pdfBlob: Blob = new Blob();
          resolve(pdfBlob);
          document.body.removeChild(iFrame);
        },
        { once: true }
      );
    };
    iFrame.srcdoc = htmlContent;
  });

  // Create a download link for the resulting PDF Blob and trigger the download
  const downloadLink = document.createElement('a');
  downloadLink.href = URL.createObjectURL(pdfBlob);
  downloadLink.download = 'WinnerAgreement.pdf';
  downloadLink.onclick = () => downloadLink.remove();
  document.body.appendChild(downloadLink);
  downloadLink.click();
}

export const baBtnName = {
  pending: 'OFFERWALL_PENDING_ACTIONBUTTON',
  verified: 'OFFERWALL_CLAIM_ACTIONBUTTON',
  new: 'OFFERWALL_BASE_BRANDACTIONACTIONBUTTON',
  claimed: 'OFFERWALL_CLAIMED_ACTIONBUTTON',
  rejected: 'OFFERWALL_REJECTED_ACTIONBUTTON',
  completed: 'OFFERWALL_COMPLETED_ACTIONBUTTON',
};

export const newBtnBa = {
  receipt_verification: 'SUBMIT_RECEIPT',
};

export const baBtnBg: Record<string, 'purple' | 'primary' | 'secondary'> = {
  pending: 'purple',
  verified: 'secondary',
  new: 'primary',
  claimed: 'purple',
  rejected: 'purple',
};

export const stars = [
  { size: 10, top: '20px', left: '20px', bottom: 'auto', right: 'auto' },
  { size: 20, top: '30px', left: '5px', bottom: 'auto', right: 'auto' },
  { size: 10, top: 'auto', left: 'auto', bottom: '-4px', right: '20px' },
  { size: 20, top: 'auto', left: 'auto', bottom: '10px', right: '5px' },
  { size: 20, top: 'auto', left: '5%', bottom: '20px', right: 'auto' },
  { size: 20, top: 'auto', left: 'auto', bottom: '0', right: '25%' },
  { size: 20, top: '30px', right: '5px', bottom: 'auto', left: 'auto' },
  { size: 10, top: '20px', left: 'auto', bottom: '-auto', right: '80%' },
  { size: 10, top: 'auto', left: 'auto', bottom: '0', right: '80%' },
];

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getSovAsset(
  asset_pack: string,
  brand_unique_id: string,
  suffix = ''
) {
  return `sov/${asset_pack}/${brand_unique_id}${suffix}`;
}

export function getSovAssetAndTrack(
  asset_pack: string,
  brand_unique_id: string,
  suffix = ''
) {
  USER.trackData('brand_sov', {
    component_id: 'brand_icon',
    result: brand_unique_id,
    success: true,
  });
  const asset = getSovAsset(asset_pack, brand_unique_id, suffix);
  return asset;
}

export function formatMobileNumber(
  mobileNumber: string,
  countryCode?: CountryCode
): string {
  // Parse the mobile number with the given country code
  const phoneNumber = parsePhoneNumberFromString(
    countryCode ? mobileNumber : `+${mobileNumber}`.replace(/\++/, '+'),
    countryCode
  );

  // Check if the number is valid
  if (!phoneNumber) return mobileNumber;

  // Return the formatted number in international format
  return phoneNumber.formatInternational();
}
export function generateRandomString(length: number) {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters[randomIndex];
  }
  return result;
}

export function getSOVName(brand_unique_id: string) {
  const BRAND_SOV_OPTIONS = Object.entries(BRAND_SOV_LABELS).map(
    ([value, label]) => ({
      value,
      label,
    })
  );
  const brand = BRAND_SOV_OPTIONS.find((b) => b.value === brand_unique_id);
  return brand?.label || '';
}

export function checkIfAmenitiesCheckInAvailable(
  amenities: IAmenities,
  timeToCheck: Date = new Date()
): boolean {
  const currentFromStartOfWeekInMinute =
    dayjs(timeToCheck).startOf('week').diff(dayjs(timeToCheck), 'minutes') * -1;

  const openHours = amenities.computed_opening_hours || [];

  for (const openHour of openHours) {
    const [openingTimeInMinute, closingTimeInMinute] = openHour;
    if (
      currentFromStartOfWeekInMinute >= openingTimeInMinute &&
      currentFromStartOfWeekInMinute < closingTimeInMinute
    ) {
      return true;
    }
  }

  return false;
}
