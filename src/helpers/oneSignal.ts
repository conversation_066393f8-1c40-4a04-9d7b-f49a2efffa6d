import type {
  IOneSignal,
  IOneSignalConfig,
  IOneSignalNotifications,
  IOneSignalPushSubscription,
  IOneSignalUser,
  NotificationEventName,
  NotificationEventTypeMap,
  OneSignalWrappedWindow,
  SubscriptionChangeEvent,
} from '@types';

const ONE_SIGNAL_SCRIPT_SRC =
  'https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.page.js';

let isOneSignalInitialized = false;
let isOneSignalScriptFailed = false;

const wrappedWindow = window as OneSignalWrappedWindow;

if (typeof window !== 'undefined') {
  wrappedWindow.OneSignalDeferred = wrappedWindow.OneSignalDeferred || [];
  addSDKScript();
}

function addSDKScript() {
  const script = document.createElement('script');
  script.defer = true;
  script.src = ONE_SIGNAL_SCRIPT_SRC;

  script.onerror = () => {
    isOneSignalScriptFailed = true;
  };

  document.head.appendChild(script);
}

function init(options: IOneSignalConfig) {
  if (isOneSignalInitialized) return;
  return new Promise<void>((resolve) => {
    wrappedWindow.OneSignalDeferred?.push((OneSignal) => {
      OneSignal.init(options).then(() => {
        isOneSignalInitialized = true;
        resolve();
      });
    });
  });
}

async function runAsDeferred<T>(callback: (OneSignal: any) => T): Promise<T> {
  if (isOneSignalScriptFailed) {
    return Promise.reject();
  }
  const result = await new Promise<T>((resolve) => {
    wrappedWindow.OneSignalDeferred?.push((OneSignal) => {
      resolve(callback(OneSignal));
    });
  });

  return result;
}

function oneSignalLogin(userId: string) {
  return runAsDeferred((OneSignal) => OneSignal.login(userId));
}

function oneSignalLogout() {
  return runAsDeferred((OneSignal) => OneSignal.logout());
}

function pushSubscriptionOptIn(): Promise<void> {
  return new Promise(function (resolve, reject) {
    if (isOneSignalScriptFailed) {
      reject();
    }

    try {
      wrappedWindow.OneSignalDeferred?.push((OneSignal) => {
        OneSignal?.User?.PushSubscription.optIn()
          .then((value) => resolve(value))
          .catch((error) => reject(error));
      });
    } catch (error) {
      reject(error);
    }
  });
}

function pushSubscriptionOptOut(): Promise<void> {
  return new Promise(function (resolve, reject) {
    if (isOneSignalScriptFailed) {
      reject();
    }

    try {
      wrappedWindow.OneSignalDeferred?.push((OneSignal) => {
        OneSignal?.User?.PushSubscription.optOut()
          .then((value) => resolve(value))
          .catch((error) => reject(error));
      });
    } catch (error) {
      reject(error);
    }
  });
}

function pushSubscriptionAddEventListener(
  event: 'change',
  listener: (change: SubscriptionChangeEvent) => void
): void {
  wrappedWindow.OneSignalDeferred?.push((OneSignal) => {
    OneSignal?.User?.PushSubscription.addEventListener(event, listener);
  });
}

function pushSubscriptionRemoveEventListener(
  event: 'change',
  listener: (change: SubscriptionChangeEvent) => void
): void {
  wrappedWindow.OneSignalDeferred?.push((OneSignal) => {
    OneSignal?.User?.PushSubscription.removeEventListener(event, listener);
  });
}

function notificationsAddEventListener<K extends NotificationEventName>(
  event: K,
  listener: (obj: NotificationEventTypeMap[K]) => void
): void {
  wrappedWindow?.OneSignalDeferred?.push((OneSignal) => {
    OneSignal.Notifications.addEventListener(event, listener);
  });
}

function notificationsRemoveEventListener<K extends NotificationEventName>(
  event: K,
  listener: (obj: NotificationEventTypeMap[K]) => void
): void {
  wrappedWindow?.OneSignalDeferred?.push((OneSignal) => {
    OneSignal.Notifications.removeEventListener(event, listener);
  });
}

const NotificationsNamespace: IOneSignalNotifications = {
  get permission(): boolean | undefined {
    return wrappedWindow.OneSignal?.Notifications?.permission;
  },
  isPushSupported: () =>
    wrappedWindow.OneSignal?.Notifications?.isPushSupported?.() || false,
  requestPermission: () =>
    wrappedWindow.OneSignal?.Notifications?.requestPermission() as Promise<void>,
  addEventListener: notificationsAddEventListener,
  removeEventListener: notificationsRemoveEventListener,
};

const PushSubscriptionNamespace: IOneSignalPushSubscription = {
  get id(): string | undefined {
    return wrappedWindow.OneSignal?.User?.PushSubscription?.id;
  },
  get token(): string | undefined {
    return wrappedWindow.OneSignal?.User?.PushSubscription?.token;
  },
  get optedIn(): boolean | undefined {
    return wrappedWindow.OneSignal?.User?.PushSubscription?.optedIn;
  },
  optIn: pushSubscriptionOptIn,
  optOut: pushSubscriptionOptOut,
  addEventListener: pushSubscriptionAddEventListener,
  removeEventListener: pushSubscriptionRemoveEventListener,
};

const UserNamespace: IOneSignalUser = {
  get onesignalId(): string | undefined {
    return wrappedWindow.OneSignal?.User?.onesignalId;
  },
  get externalId(): string | undefined {
    return wrappedWindow.OneSignal?.User?.externalId;
  },
  PushSubscription: PushSubscriptionNamespace,
};

export function useOneSignal(): IOneSignal {
  return {
    init,
    login: oneSignalLogin,
    logout: oneSignalLogout,
    Notifications: NotificationsNamespace,
    User: UserNamespace,
  };
}
