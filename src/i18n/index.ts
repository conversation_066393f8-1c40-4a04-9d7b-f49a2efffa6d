import { api } from '@/boot/axios';
import { groupBy, mapValues } from 'lodash';
import json from './translation.json';

export async function getLocalize() {
  try {
    const { data } = await api.get('/content/all');
    const mappedData = data.map((item: any) => {
      const updatedDataObject = Object.fromEntries(
        Object.entries(item.data).map(([key, value]) => [
          key,
          value === '' || value === null || value === undefined ? key : value,
        ])
      );

      return {
        ...item,
        data: updatedDataObject,
      };
    });

    const grouped = groupBy(mappedData, 'lang');
    const result = mapValues(grouped, (items) => items[0].data);
    return result;
  } catch (error) {
    return json;
  }
}
