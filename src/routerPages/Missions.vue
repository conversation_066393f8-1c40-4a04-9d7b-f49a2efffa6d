<script lang="ts" setup>
import {
  DailyMissionItem,
  TimedMissionItem,
  PendingMissionItem,
} from '@components';
import { useTimedMission } from '@composables';
import { timeCountDown } from '@helpers';
import { useBAStore, useUserStore } from '@stores';

const storeUser = useUserStore();
const storeBA = useBAStore();

const { dailyMissions } = storeToRefs(storeUser);
const { pendingMissions } = storeToRefs(storeBA);
const { t } = useI18n();
const { push } = useMicroRoute();
const { expiredAt, timeMissionData, hasActiveMission, timeMissionCooldown } =
  useTimedMission();

onMounted(async () => {
  await nextTick();
  storeUser.fetchDailyMission();
  storeBA.fetchBrandAction();
});
</script>
<template>
  <div
    class="fullscreen flex flex-col flex-nowrap bg-[#090422] overflow-hidden px-5 pb-5"
  >
    <div class="flex justify-center items-center w-full h-16 px-20 mb-3">
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold text-center w-full"
        v-html="t('DAILY_MISSIONS_HEADER')"
      ></div>
      <HeaderCrystal class="absolute right-4" />
    </div>
    <div class="flex flex-col flex-nowrap text-center h-full overflow-y-auto">
      <div class="text-sm mb-8" v-html="t('DAILY_MISSIONS_DESC_1')"></div>
      <div
        class="text-base font-bold mb-3"
        v-html="t('DAILY_MISSIONS_DESC_2')"
      ></div>

      <template v-if="timeMissionData?.unclaimed_timed_missions.length">
        <TimedMissionItem :activating-mission="timeMissionData" class="mb-8" />
      </template>
      <template v-else-if="hasActiveMission && !!expiredAt">
        <div
          class="text-sm mb-2"
          v-html="
            t('DAILY_MISSIONS_DESC_3', { TIME: timeCountDown(expiredAt) })
          "
        ></div>
        <TimedMissionItem
          v-if="timeMissionData"
          :activating-mission="timeMissionData"
          class="mb-4"
        />

        <template v-if="!!timeMissionData?.nextMission">
          <div
            class="text-sm opacity-70 mb-2"
            v-html="t('DAILY_MISSIONS_DESC_5')"
          ></div>
          <div
            class="text-sm opacity-70 mb-8"
            v-html="t(timeMissionData.nextMission.description)"
          ></div>
        </template>
      </template>
      <template v-else-if="!hasActiveMission && !!timeMissionCooldown">
        <div
          class="text-sm mb-5"
          v-html="
            t('DAILY_MISSIONS_DESC_6', {
              TIME: timeCountDown(timeMissionCooldown),
            })
          "
        ></div>
      </template>
      <template v-else>
        <div class="mb-2 text-sm" v-html="t('DAILY_MISSIONS_DESC_7')"></div>
        <Icon name="sf-map-icon" :size="150" class="mx-auto mb-8" />
      </template>
      <template v-if="pendingMissions?.length">
        <div class="mb-8">
          <p
            class="text-base font-bold mb-3"
            v-html="t('MISSIONS_PENDING_TITLE')"
          ></p>
          <PendingMissionItem
            class="mb-4"
            :pendingMission="pendingMission"
            v-for="(pendingMission, index) in pendingMissions"
            :key="pendingMission._id + index"
          />
        </div>
      </template>

      <div
        class="text-base font-bold mb-3"
        v-html="t('DAILY_MISSIONS_DESC_4')"
      ></div>
      <DailyMissionItem
        class="mb-5"
        v-for="mission in dailyMissions"
        :key="mission.unique_id"
        :mission="mission"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.tab-mission {
  border-radius: 4px 4px 0px 0px;
  background: #222f47;
  .q-tab--active {
    border-radius: 5px 5px 0px 0px;
    border-top: 1px solid #11d1f9;
    border-right: 1px solid #11d1f9;
    border-left: 1px solid #11d1f9;
    background: #091a3b;
  }
  .q-tab--inactive {
    border-bottom: 1px solid #11d1f9;
  }
}
.tab-mission-pannels {
  background: #091a3b;
  width: 100%;
  height: 100%;
  border-radius: 0px 0px 5px 5px;
  border-right: 1px solid #11d1f9;
  border-bottom: 1px solid #11d1f9;
  border-left: 1px solid #11d1f9;
}
</style>
