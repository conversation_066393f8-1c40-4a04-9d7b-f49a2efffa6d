<script lang="ts" setup>
import { useDialogStore, useMapStore, useUserStore } from '@stores';
import {
  MapCreationStatus,
  Mapbox,
  GeoJsonSource,
  FillLayer,
  LineLayer,
  useMapbox,
  useFlyTo,
  useCreateMarker,
} from 'vue3-maplibre-gl';
import {
  GRID_GOLDEN_FILL_LAYER,
  GRID_GOLDEN_LINE_LAYER,
  GRID_ELIMINATED_FILL_LAYER,
  GRID_ELIMINATED_LINE_LAYER,
  GRID_PAID_ELIMINATED_FILL_LAYER,
  GRID_PAID_ELIMINATED_LINE_LAYER,
} from '@constants';
import { MAP } from '@repositories';
import { difference } from 'lodash';
import {
  delay,
  playSFX,
  useBrandDoodleSov,
  useBrandSov,
  useGlobalInstructor,
  useMapHelpers,
  useShop,
  useTrackData,
} from '@composables';
import { Loading } from 'quasar';
import gsap, { Expo, Linear } from 'gsap';
import type { TSourceGeoHashes, ISourceGeoHashes, IMapGeoHashes } from '@types';
import type { LngLatLike, MapOptions } from 'vue3-maplibre-gl';
import { getSovAsset } from '@helpers';
import { useQuery } from '@tanstack/vue-query';

interface Props {
  quantity: number;
  grids: string[];
  totals: number;
}

const props = defineProps<Props>();

const storeUser = useUserStore();
const storeMap = useMapStore();
const storeDialog = useDialogStore();

const { lastLocations } = storeToRefs(storeMap);
const { showInstructor, instructorData } = storeToRefs(storeDialog);
const { seasonCode, hintState } = storeToRefs(storeUser);
const { makeSource, decode } = useMapHelpers();
const { getMoreEliminatedHint, loading } = useShop();
const { push } = useMicroRoute();
const { grids } = toRefs(props);
const { t } = useI18n();
const { register: registerMap, mapStatus, mapInstance } = useMapbox();
const { flyTo } = useFlyTo({
  map: storeMap.mapIns,
});
const { openUnifyInstructor, closeUnifyInstructor } = useGlobalInstructor();
const { track } = useTrackData();

const skipped = ref(false);
const locationWhenCheckPrice = ref([0, 0]);

const mapOptions = computed<Partial<MapOptions>>(() => {
  const center: Record<string, LngLatLike> = {
    VN: [103.8097, 1.3535],
    SG: [103.8097, 1.3535],
  };

  const maxBounds: Record<string, [LngLatLike, LngLatLike]> = {
    VN: [
      [103.553005, 0.881618],
      [104.089966, 1.922244],
    ],
    SG: [
      [103.553005, 0.881618],
      [104.089966, 1.922244],
    ],
  };

  return {
    container: 'map_eliminated_grids',
    style: process.env.MAP_STYLE,
    minZoom: 9,
    maxZoom: 19,
    zoom: 9.4,
    center: center[seasonCode.value],
    maxBounds: maxBounds[seasonCode.value],
  };
});

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const sourceGeoHashes = ref<Record<TSourceGeoHashes, ISourceGeoHashes>>({});
const mapGeoHashes = ref<IMapGeoHashes>({
  golden: [],
  eliminated: [],
  paidEliminated: [],
});

const showSkipBtn = computed(() => !skipped.value || grids.value.length);

const amount = computed(() => {
  return Number(hintState.value?.eliminate_grid_price) * props.quantity;
});

const goldenSources = computed(() => {
  return makeSource(sourceGeoHashes.value.golden?.features);
});

const eliminatedSources = computed(() => {
  return makeSource(sourceGeoHashes.value.eliminated?.features);
});

const paidEliminatedSources = computed(() => {
  return makeSource(sourceGeoHashes.value.paidEliminated?.features);
});

watch(mapStatus, async (status) => {
  if (status === MapCreationStatus.Loaded) {
    Loading.hide();
    await delay(1000);
    handleAddGridMarker();
  }
});

async function getSourcesGeoHashes() {
  const { data: dataGrids } = await MAP.getGeoHashes();
  const { data: eliminatedGrids } = await MAP.getEliminatedGeoHashes();

  mapGeoHashes.value.golden = difference(dataGrids, eliminatedGrids.free);
  mapGeoHashes.value.eliminated = eliminatedGrids.free;
  mapGeoHashes.value.paidEliminated = [];

  sourceGeoHashes.value = storeMap.makeSourceFromMapGeoHashes(
    mapGeoHashes.value
  );
}

async function handleAddGridMarker() {
  // Remove existing grid markers
  const gridsEl = document.querySelectorAll('.grid-marker');
  if (gridsEl.length) gridsEl.forEach((grid) => grid.remove());

  let index = 0;
  while (grids.value.length && !skipped.value) {
    const geohash = grids.value[0];

    const { latitude, longitude } = decode(geohash);

    await flyTo({
      center: [longitude, latitude],
      zoom: 13,
      animate: true,
      duration: 2000,
    });
    await delay(1500);

    useCreateMarker({
      map: mapInstance.value,
      lnglat: [longitude, latitude],
      options: {
        className: `grid-marker maplibregl-marker-${index}`,
      },
    });
    gsap.from(`.maplibregl-marker-${index}`, {
      top: -20,
      opacity: 0,
      duration: 0.5,
      ease: Expo.easeOut,
      onComplete: () => {
        gsap.killTweensOf(`.maplibregl-marker-${index}`);
      },
    });
    playSFX('grid_eliminating');
    addPaidEliminated([geohash]);
    await delay(500);
    // sourceGeoHashes.value = storeMap.makeSourceFromMapGeoHashes(
    //   mapGeoHashes.value
    // );
    index++;
    await delay(1000);
    grids.value.shift();
  }
  skipped.value = true;
  flyToCenter();
}

function addPaidEliminated(geohashes: string[]) {
  mapGeoHashes.value.paidEliminated.push(...geohashes);
  mapGeoHashes.value.eliminated = difference(
    mapGeoHashes.value.eliminated,
    mapGeoHashes.value.paidEliminated
  );
  mapGeoHashes.value.golden = difference(
    mapGeoHashes.value.golden,
    mapGeoHashes.value.eliminated,
    mapGeoHashes.value.paidEliminated
  );
  storeMap.mapGeoHashes = mapGeoHashes.value;
}

async function handleAddAllGridMarker() {
  track('reveal_eliminated_location', {
    screen: 'reveal_eliminated_location',
    button: 'skip_animation',
  });
  await new Promise(async (resolve) => {
    const geohashes = [];
    for (const [index, geohash] of Object.entries(grids.value)) {
      const { latitude, longitude } = decode(geohash);
      useCreateMarker({
        map: mapInstance.value,
        lnglat: [longitude, latitude],
        options: {
          className: `grid-marker maplibregl-marker-${index}`,
        },
      });
      geohashes.push(geohash);
      resolve(true);
    }

    // addPaidEliminated(geohashes);
    // sourceGeoHashes.value = storeMap.makeSourceFromMapGeoHashes(
    //   mapGeoHashes.value
    // );
  });
  await flyToCenter();
  skipped.value = true;
  grids.value.length = 0;
}

const eliminateSovTag = computed(() => {
  if (!skipped.value) return null;
  const { randomResult, ready } = useBrandSov(
    'gold_coin_location_eliminations_logo',
    {
      id: 'gold_coin_location_eliminations_character',
      track: false,
    }
  );
  return { randomResult, ready };
});

const eliminateSovAgent = computed(() => {
  if (!eliminateSovTag.value) return null;
  const doodle = useBrandDoodleSov({
    ready: eliminateSovTag.value.ready,
    randomResult: eliminateSovTag.value.randomResult,
    referenceId: 'gold_coin_location_eliminations_logo',
    doodleId: 'gold_coin_location_eliminations_character',
  });
  return doodle.value;
});

const eliminateSovContext = computed(() => {
  if (!eliminateSovTag.value?.randomResult) return null;
  if (!eliminateSovAgent.value) return null;
  return {
    sovAgent: getSovAsset(
      'character',
      eliminateSovAgent.value.brand_unique_id ?? 'tiger_broker',
      '_shinobii'
    ),
    sovTag:
      eliminateSovTag.value.randomResult.value.gold_coin_location_eliminations_logo.getAsset(),
  };
});

function showDoodleAnimation() {
  if (!eliminateSovContext.value) return;
  openUnifyInstructor('shinobii', {
    agent: eliminateSovContext.value.sovAgent,
    tag: eliminateSovContext.value.sovTag,
    sequences: [
      {
        target: '#maplibre_container',
        css: { top: '0' },
        message: t('ELIMINATED_HINT_TOTALS_GRID', {
          TOTALS: props.totals,
        }),
        actions: {
          async cb(_next, close) {
            await close();
            track('reveal_eliminated_location', {
              screen: 'reveal_eliminated_location',
              button: 'back_to_shop',
            });
            handleBack();
          },
        },
      },
    ],
  });
}

async function flyToCenter() {
  await gsap.fromTo(
    '.get-more',
    {
      opacity: 0,
      y: -20,
    },
    {
      delay: 1,
      opacity: 1,
      duration: 0.25,
      stagger: 0.1,
      y: 0,
      ease: Linear.easeOut,
    }
  );
  await flyTo({
    center: [103.85876175581991, 1.294674696996273],
    zoom: 9,
    animate: true,
    duration: 2500,
  });
}

async function getHintPrice() {
  locationWhenCheckPrice.value = lastLocations.value;
  const [lng, lat] = locationWhenCheckPrice.value;

  const { data } = await MAP.getEliminatedPrice({
    quantity: props.quantity,
    lng,
    lat,
  });

  return data;
}

const { data: discount } = useQuery({
  queryKey: ['getHintPrices'],
  queryFn: getHintPrice,
  refetchInterval: 1000 * 5,
});

watch(eliminateSovContext, (v) => {
  if (v) showDoodleAnimation();
});

function handleBack() {
  push('shop');
  storeMap.fetchMapGeoHashes();
  storeMap.fromGrids = true;
}

onMounted(async () => {
  await nextTick();
  Loading.show();
  await getSourcesGeoHashes();
});
</script>
<template>
  <Mapbox :options="mapOptions" @register="registerMap">
    <template v-if="mapStatus === MapCreationStatus.Loaded">
      <ShinobiiInstructor
        v-if="showInstructor === 'shinobii'"
        :agent="instructorData.agent"
        :sequences="instructorData.sequences"
        :tag="instructorData.tag"
      />
      <Button
        v-if="showSkipBtn"
        class="absolute top-2 right-2 z-10"
        :class="{
          'pointer-events-none': skipped,
        }"
        :label="t('BUTTON_SHOW_ALL')"
        size="max-content"
        variant="purple"
        @click="handleAddAllGridMarker"
      />
      <div
        class="fixed left-1/2 -translate-x-1/2 bottom-0 text-center z-10 overlay"
        v-show="!showSkipBtn"
      >
        <div
          class="get-more text-sm mb-5"
          v-html="t('ELIMINATED_HINT_WANT_MORE')"
        ></div>
        <Button
          class="get-more"
          :title="t('BUTTON_QUANTITY_GET_MORE', { QUANTITY: quantity })"
          :old-amount="discount?.beacon ? amount : undefined"
          :amount="
            discount?.is_free ? 0 : discount?.beacon ? discount?.price : amount
          "
          :loading="loading"
          @click="
            handleBack();
            closeUnifyInstructor();
            track('reveal_eliminated_location', {
              screen: 'reveal_eliminated_location',
              button: 'use_more',
            });
            getMoreEliminatedHint(quantity);
          "
        />
      </div>
      <GeoJsonSource :data="goldenSources">
        <FillLayer
          :style="{
            ...GRID_GOLDEN_FILL_LAYER,
          }"
        />
        <LineLayer
          :style="{
            ...GRID_GOLDEN_LINE_LAYER,
          }"
        />
      </GeoJsonSource>
      <GeoJsonSource :data="eliminatedSources">
        <FillLayer
          :style="{
            ...GRID_ELIMINATED_FILL_LAYER,
          }"
        />
        <LineLayer
          :style="{
            ...GRID_ELIMINATED_LINE_LAYER,
          }"
        />
      </GeoJsonSource>
      <GeoJsonSource :data="paidEliminatedSources">
        <FillLayer
          :style="{
            ...GRID_PAID_ELIMINATED_FILL_LAYER,
          }"
        />
        <LineLayer
          :style="{
            ...GRID_PAID_ELIMINATED_LINE_LAYER,
          }"
        />
      </GeoJsonSource>
    </template>
  </Mapbox>
</template>
<style lang="scss" scoped>
.overlay {
  z-index: 999999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 40vw;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.5) 100%
  );
}
</style>
