<script lang="ts" setup>
import { useUserStore } from '@stores';
import { getSocials } from '@helpers';
import { useWindowSize } from '@vueuse/core';
import { playSFX, useTick, useTrackData } from '@composables';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination } from 'swiper/modules';
import type { ITimeline } from '@types';
import 'swiper/css';
import 'swiper/css/pagination';

const storeUser = useUserStore();

const {
  seasonCode,
  timelines,
  currentSeason,
  isSeasonStarting,
  initialSlide,
  user,
  events,
} = storeToRefs(storeUser);
const { push, openDialog } = useMicroRoute();
const { t } = useI18n();
const { width } = useWindowSize();
const { track } = useTrackData();
const { now } = useTick();

const deploymentNumber = computed(() => process.env.DEPLOYMENT_NUMBER);

const defaultInitialSlide = ref(initialSlide.value);
const pagination = {
  clickable: true,
  dynamicBullets: true,
};
const modules = [Pagination];

const seasonIndex = computed(() => {
  return timelines.value.findIndex((t) => t.country_code === seasonCode.value);
});

const slidesMenu = computed(() => {
  return [
    {
      title: t('MENU_FAIRNESS_HEADER'),
      content: t('MENU_FAIRNESS_DESCRIPTION'),
      cb: () => push('ensuring_fairness_v2'),
      permission: true,
    },
    {
      title: t('MENU_REFERRAL_HEADER'),
      content: t('MENU_REFERRAL_DESCRIPTION'),
      cb: () => push('referral'),
      permission: isSeasonStarting.value,
    },
  ].filter((s) => s.permission);
});

const filterdMenu = computed(() => {
  if (isSeasonStarting.value) return [...slidesMenu.value, ...slidesMenu.value];
  return slidesMenu.value;
});

const eventOptions = computed(() => [
  {
    title: t('MENU_INVENTORY_FOUND_COIND'),
    icon: 'silver-coin',
    cb: () => {
      track('found_coin');
      // if (user.value && +new Date(user.value.coin_lock_until) > now.value)
      //   return openDialog('coin_limit');

      openDialog('enter_serial_number');
    },
    disabled: !isSeasonStarting.value,
  },

  {
    title: t('MENU_INVENTORY_GET_CRYSTAL'),
    icon: 'get-crystals',
    cb: () => {
      track('visit_ba_button');
      push('offer_wall');
    },
    disabled: !isSeasonStarting.value,
  },
  {
    title: t('MENU_INVENTORY_EVENT_UPDATE'),
    icon: 'megaphone',
    cb: () => {
      track('check_events');
      push('events');
    },
    disabled: !isSeasonStarting.value,
    notification:
      events.value && events.value.event_updates.some((e) => !e.seen),
  },
]);

function goTimeLine(tl: ITimeline, index: number) {
  track('menu_city', {
    city: tl.city,
    status: tl.status,
    hunt_name: tl.hunt_name,
  });
  track('calendar_check', {
    event: tl._id,
  });
  defaultInitialSlide.value = index;
  openDialog('time_line_v1', {
    defaultInitialSlide: defaultInitialSlide.value,
  });
}

onBeforeMount(() => {
  defaultInitialSlide.value = timelines.value.findIndex(
    (t) => t.hunt_name === currentSeason.value?.hunt_name
  );
});
</script>

<template>
  <div class="pb-5 fullscreen menu" :class="`menu-${seasonCode.toLowerCase()}`">
    <TestingComponent>
      <div
        style="
          position: fixed;
          bottom: 10px;
          width: 100%;
          text-align: center;
          font-size: 8px;
          color: #777;
        "
      >
        {{ deploymentNumber }}
      </div>
    </TestingComponent>
    <div class="flex items-center justify-between px-4 py-5">
      <Button shape="square" variant="secondary" @click="push('setting')">
        <Icon name="settings" />
      </Button>
      <Icon name="logo_htm" :size="170" class="mt-2" />
      <Button shape="square" @click="push(-1)">
        <Icon name="cross" :size="15" />
      </Button>
    </div>

    <div class="w-full h-full overflow-y-auto">
      <template v-if="defaultInitialSlide > -1">
        <Swiper
          :slidesPerView="width / 95"
          :initial-slide="defaultInitialSlide"
          :space-between="15"
          centered-slides
          slide-to-clicked-slide
          class="relative season-timeline flex justify-center items-center min-h-[160px] mb-5"
        >
          <SwiperSlide
            v-for="(tl, index) in timelines"
            :key="tl._id"
            v-slot="{ isActive }"
            :class="{
              'prev-season': index < seasonIndex,
              'next-season': index > seasonIndex,
              '!w-[90px] h-[90px] -mt-[6px]': !!tl.logo,
            }"
            @click="goTimeLine(tl, index)"
          >
            <div
              class="season flex justify-center items-center min-h-[80px]"
              :class="{
                '!w-[90px] h-[90px]': !!tl.logo && !isActive,
                '!w-[125px] h-[125px] -mt-[25px] -ml-[25px]':
                  !!tl.logo && isActive,
              }"
            >
              <div
                :class="{
                  line: !isActive,
                  first: index === 0,
                  last: timelines.length - 1 === index,
                  '!top-[55.5%] !w-[100px] left-0': !!tl.logo,
                }"
              ></div>
              <img
                v-if="!!tl.logo"
                :src="tl.logo"
                :class="{
                  'w-[125px] h-[125px]': isActive,
                }"
              />
              <div
                v-else
                class="circle-logo relative flex justify-center items-center text-[#76490a] w-12 h-12 rounded-full"
                :class="{
                  'w-[65px] h-[65px] mb-1': isActive,
                }"
                v-html="tl.country_code"
              ></div>
            </div>
            <div
              class="-mt-3 text-sm text-center text-white opacity-20"
              :class="{
                'font-bold text-lg !opacity-100 !mt-0': isActive && !tl.logo,
                'font-bold text-lg !opacity-100 !-mt-5 ': isActive && !!tl.logo,
                '!-mt-4': !isActive && !!tl.logo,
              }"
              v-html="tl.time"
            ></div>
            <div
              class="text-[#ffbc3a] text-sm text-center w-max mx-auto"
              v-if="isActive"
            >
              {{
                tl.status === 'ongoing'
                  ? t('TIMELINE_STATUS_ONGOING')
                  : tl.status === 'ended'
                  ? t('TIMELINE_STATUS_ENDED')
                  : t('TIMELINE_STATUS_COMING')
              }}
            </div>
          </SwiperSlide>
        </Swiper>
      </template>
      <Swiper
        :slides-per-view="width / 315"
        :initial-slide="1"
        :pagination="pagination"
        :modules="modules"
        :space-between="20"
        centered-slides
        :loop="isSeasonStarting"
        class="mb-5 slide-menu"
      >
        <SwiperSlide
          v-for="(s, index) in filterdMenu"
          :key="index"
          v-slot="{ isActive }"
          @click="s.cb"
        >
          <div
            class="slide-item"
            :class="{
              'mt-5': !isActive,
            }"
          >
            <div class="p-4 h-[calc(100%-40px)]">
              <div class="flex flex-col justify-center h-full mt-1">
                <div class="text-lg font-bold">{{ s.title }}</div>
                <div class="text-sm">
                  {{ s.content }}
                </div>
              </div>
            </div>
          </div>
        </SwiperSlide>
      </Swiper>
      <div class="flex items-center justify-center gap-3 mb-5">
        <div
          class="relative flex flex-col items-center justify-center gap-1 event"
          :class="{
            'pointer-events-none opacity-50': event.disabled,
          }"
          v-for="(event, index) in eventOptions"
          :key="index"
          @click="
            event.cb();
            playSFX('button');
          "
        >
          <div
            v-if="!!event.notification"
            class="absolute dot top-3 right-3"
          ></div>
          <Icon class="mt-4" :name="event.icon" :size="40" />
          <div
            class="flex items-center justify-center flex-grow w-full text-sm leading-4 text-center"
            v-html="event.title"
          ></div>
        </div>
      </div>
      <div class="mb-5 text-center">
        <div class="mb-5 text-sm" v-html="t('MENU_CTA')"></div>
        <div class="flex items-center justify-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>
      <div
        class="px-5 text-sm text-center opacity-70"
        v-html="t('MENU_SQKII')"
      ></div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.menu {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &.menu-vn {
    background: url('/imgs/bg-menu-vn.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  &.menu-sg {
    background: url('/imgs/bg-menu-sg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  .event {
    width: 105px;
    height: 115px;
    background-image: url('/imgs/menu-option.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
</style>
<style lang="scss">
.season-timeline {
  .swiper-wrapper {
    margin-top: 30px;
  }
  .line {
    border: 1px dashed rgba($color: #ffffff, $alpha: 0.6);
    position: absolute;
    z-index: -1;
    width: 95px;
    top: 50%;
    &.first {
      width: 50%;
      right: 0;
    }
    &.last {
      width: 50%;
      right: 50%;
    }
  }
  .swiper-slide-active {
    border-radius: 50%;
    width: 75px !important;
    height: 75px;
    background-image: url('/imgs/current-hunt.png');
    background-size: 100%;
    font-weight: 700;
    color: #76490a;
    font-size: 20px;
  }
  .prev-season {
    .circle-logo {
      background: linear-gradient(180deg, #38238d 0%, #14595f 100%);
      color: rgba($color: #ffffff, $alpha: 0.2);
    }
  }
  .next-season {
    .circle-logo {
      background: linear-gradient(180deg, #461e97 0%, #2a0c67 100%);
      color: rgba($color: #ffffff, $alpha: 0.2);
    }
  }
  .actived-circle {
    width: 65px;
    height: 65px;
    box-shadow: 0 0 10px 10px #ffffff29;
    background: linear-gradient(180deg, #461e97, #7b60c880);
  }
  .circle-logo {
    background: linear-gradient(180deg, #ffc267 0%, #ffa11f 100%);
  }
}
.slide-menu {
  .slide-item {
    height: 125px;
    background-image: url('/imgs/hint-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .swiper-pagination {
    bottom: 0;
  }
  .swiper-pagination-bullet-active {
    width: 25px !important;
    height: 8px !important;
    background: #9d93fb !important;
    border-radius: 4px !important;
  }
  .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: #48418e;
    border-radius: 4px;
    opacity: 1;
  }
}
</style>
