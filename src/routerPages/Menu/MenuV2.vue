<script lang="ts" setup>
import { useUserStore } from '@stores';
import { getSocials } from '@helpers';
import { useWindowSize } from '@vueuse/core';
import { playSFX, useTrackData } from '@composables';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import { Swiper as ISwiper } from 'swiper/types';
import { BRAND_SOV } from '@constants';

const storeUser = useUserStore();

const {
  seasonCode,
  isSeasonStarting,
  // events,
  features,
  // perpetualHunt,
  // sentosaGoldenCoin,
} = storeToRefs(storeUser);
const { push, openDialog } = useMicroRoute();
const { t } = useI18n();
const { width } = useWindowSize();
const { track } = useTrackData();
const pagination = {
  clickable: true,
  dynamicBullets: true,
};
const mySwiper = ref<ISwiper | undefined>();
const modules = [Pagination];

const deploymentNumber = computed(() => process.env.DEPLOYMENT_NUMBER);

const onSwiper = (swiper: ISwiper) => {
  mySwiper.value = swiper;
};

const slidesMenu = computed(() => {
  return [
    {
      title: t('MENU_TIP_TRICK_HEADER'),
      content: t('MENU_TIP_TRICK_DESCRIPTION'),
      cb: () => {
        track('menu_tipsandtricks');
        openDialog('tips_trick');
      },
      permission: true,
    },
    {
      title: t('MENU_FAIRNESS_HEADER'),
      content: t('MENU_FAIRNESS_DESCRIPTION'),
      cb: () => {
        track('menu_ensuringfairness');
        push('ensuring_fairness_v2');
      },
      permission: true,
    },
    // {
    //   title: t('MENU_EVENT_HEADER'),
    //   content: t('MENU_EVENT_DESCRIPTION'),
    //   cb: () => {
    //     track('menu_notifications');
    //     push('events');
    //   },
    //   permission: isSeasonStarting.value,
    //   notification:
    //     events.value && events.value.event_updates.some((e) => !e.seen),
    // },
  ].filter((s) => s.permission);
});

const filterdMenu = computed(() => {
  if (isSeasonStarting.value) return [...slidesMenu.value, ...slidesMenu.value];
  return slidesMenu.value;
});

const eventOptions = computed(() => [
  {
    title: t('MENU_INVENTORY_SQKII_VOUCHERS'),
    icon: '/menu/sqkii-vouchers',
    cb: () => {
      track('menu_sqkiivouchers');
      push('sqkii_vouchers');
    },
    disabled: !isSeasonStarting.value || !features.value?.sqkii_voucher,
  },
  {
    title: t('MENU_INVENTORY_FOUND_COIND'),
    icon: '/menu/coin',
    cb: () => {
      track('menu_foundacoin');
      // if (
      //   user.value &&
      //   (user.value.total_coin_submissions >= 5 ||
      //     user.value.total_gold_coin_submissions > 0)
      // )
      //   return openDialog('coin_limit');

      // if (coinLimitCountdown.value) return openDialog('coin_limit');

      openDialog('enter_serial_number');
    },
    disabled: !isSeasonStarting.value || !features.value?.enter_serial_number,
  },
  {
    title: t('MENU_INVENTORY_REFERRAL'),
    icon: '/menu/referral',
    cb: () => {
      track('menu_myreferrals');
      push('referral');
    },
    disabled: !isSeasonStarting.value || !features.value?.referral,
  },
  {
    title: t('MENU_INVENTORY_FAQS'),
    icon: '/menu/questions',
    cb: () => {
      track('menu_faqs');
      push('faq');
    },
    disabled: false,
  },
]);

function handleBack() {
  push(-1);
  // TO DO: [For Guest Account only] Player claims Crystals from the Offer wall and returns to home screen
  // if (!isLogged.value) {
  //   closeMessage();
  //   switch (storeDialog.triggerGuestReminder) {
  //     case 'claimed_ba':
  //       triggerMessage('dialogue_signup_guestreminder_1');
  //       break;
  //     // case 'grids_eliminated':
  //     //   if (perpetualHunt.value) return;
  //     //   triggerMessage('dialogue_signup_guestreminder_3');
  //     //   break;
  //     // case 'text_hints':
  //     //   if (perpetualHunt.value) return;
  //     //   triggerMessage('dialogue_signup_guestreminder_4');
  //     //   break;
  //     case 'enter_serial_number':
  //       triggerMessage('dialogue_signup_guestreminder_5');
  //       break;
  //   }
  //   storeDialog.triggerGuestReminder = '';
  // }
}

// const isHintShopOpen = computed(
//   () =>
//     !['found', 'verifying'].includes(settings.value?.golden_coin.status || '')
// );
// function onClickHintShop() {

// if (isHintShopOpen.value) {

// } else {
//   errorNotify({
//     message:
//       settings.value?.golden_coin?.status === 'verifying'
//         ? t('DIALOGUE_GOLDEN_VERIFYING')
//         : t('DIALOGUE_GOLDEN_FOUND'),
//   });
// }
// }

onMounted(async () => {
  await nextTick();
  storeUser.fetchUser();
  storeUser.fetchSentosaGoldenCoin();
});
</script>

<template>
  <div class="pb-5 fullscreen menu" :class="`menu-${seasonCode.toLowerCase()}`">
    <TestingComponent>
      <div
        style="
          position: fixed;
          bottom: 10px;
          width: 100%;
          text-align: center;
          font-size: 8px;
          color: #777;
        "
      >
        Build ({{ deploymentNumber }}):
        {{ new Date(Number(deploymentNumber) * 1000).toLocaleString() }}
      </div>
    </TestingComponent>
    <div class="flex items-center justify-between px-4 py-5">
      <Button
        shape="square"
        variant="secondary"
        @click="
          push('setting');
          track('menu_settings');
        "
      >
        <Icon name="settings" />
      </Button>
      <Icon :name="`/sov/menu/${BRAND_SOV.DBS}`" :size="210" class="mt-2" />
      <Button shape="square" @click="handleBack">
        <Icon name="cross" :size="15" />
      </Button>
    </div>

    <div class="w-full h-full overflow-y-auto">
      <!-- <template v-if="!perpetualHunt">
      <div class="flex items-center justify-center mb-5">
        <div
          v-if="features?.brand_action"
          class="relative get-crytals"
          @click="
            push('offer_wall');
            track('menu_getcrystals');
          "
        >
          <div
            class="absolute text-lg font-bold text-center -translate-x-1/2 left-1/2 top-7"
            v-html="t('MENU_CARD_GET_CRYSTALS')"
          ></div>
        </div>
        <div
          class="relative get-hints pointer-events-none opacity-50"
          @click="
            track('menu_gethints');
            openDialog('crystal_coin_hints', {
              coin: sentosaGoldenCoin
                .filter((c) => ['ongoing', 'scheduled'].includes(c.status))
                .at(0),
              sentosaGoldenCoin,
            });
          "
        >
          <div
            class="absolute text-lg font-bold text-center -translate-x-1/2 left-1/2 top-7"
            v-html="t('MENU_CARD_GET_HINTS')"
          ></div>
        </div>
      </div>
      </template> -->

      <div class="flex items-center justify-center mb-5">
        <div
          v-if="features?.brand_action"
          class="relative get-crytals-horizontal"
          @click="
            push('offer_wall');
            track('menu_getcrystals');
          "
        >
          <div
            class="absolute text-lg font-bold text-center left-[45%] top-1/2 -translate-y-1/2"
            v-html="t('MENU_CARD_GET_CRYSTALS')"
          ></div>
        </div>
      </div>
      <div
        class="grid items-center justify-center grid-cols-2 px-3 mb-5 gap-x-3 gap-y-4"
      >
        <div
          class="relative flex items-center justify-center gap-1 event"
          :class="{
            'pointer-events-none opacity-50': event.disabled,
          }"
          v-for="(event, index) in eventOptions"
          :key="index"
          @click="
            event.cb();
            playSFX('button');
          "
        >
          <Icon
            :name="event.icon.replace(/^\//, '')"
            :size="41"
            class="absolute top-0 left-0"
          />
          <div
            class="flex justify-center w-full font-bold text-center flex-center"
          >
            <p v-html="event.title" class="mr-[-8%]"></p>
          </div>
        </div>
      </div>
      <Swiper
        :slides-per-view="width / 315"
        :initial-slide="1"
        :pagination="pagination"
        :modules="modules"
        :space-between="0"
        centered-slides
        :loop="isSeasonStarting"
        class="pb-10 mb-8 slide-menu"
        @swiper="onSwiper"
      >
        <SwiperSlide
          v-for="(s, index) in filterdMenu"
          :key="index"
          @click="
            playSFX('button');
            s.cb();
          "
        >
          <div class="slide-item">
            <div class="p-4 h-[calc(100%-40px)] flex flex-center">
              <div class="flex flex-col justify-center h-full flex-nowrap">
                <!-- <div v-if="!!s.notification" class="absolute dot right-3"></div> -->
                <div class="text-lg font-bold">{{ s.title }}</div>
                <div>
                  {{ s.content }}
                </div>
              </div>
            </div>
          </div>
        </SwiperSlide>
        <div class="flex items-center justify-between w-full px-5">
          <div
            @click="
              playSFX('button');
              mySwiper?.slidePrev();
            "
            class="w-[64px] h-[46px] bg-contain"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
            }"
          ></div>
          <div
            @click="
              playSFX('button');
              mySwiper?.slideNext();
            "
            class="w-[64px] h-[46px] bg-contain rotate-180"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
            }"
          ></div>
        </div>
      </Swiper>

      <div class="mb-5 text-center">
        <div class="mb-5 text-sm" v-html="t('MENU_CTA')"></div>
        <div class="flex items-center justify-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
            @click="
              track('menu_social', {
                social: link,
                action: icon,
              })
            "
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>
      <div
        @click="
          openDialog('adventure_log_v2');
          track('menu_adventurelog');
        "
        class="mb-5 text-sm text-center underline text-link"
        v-html="t('MENU_PAST_HUNT')"
      ></div>
      <div
        class="px-5 text-sm text-center opacity-70"
        v-html="t('MENU_SQKII')"
      ></div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.menu {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &.menu-vn {
    background: url('/imgs/bg-menu-vn.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  &.menu-sg {
    background: url('/imgs/bg-menu-sg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .get-crytals {
    width: 40vw;
    height: 50vw;
    background-image: url('/imgs/menu/get-crytals.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .get-crytals-horizontal {
    width: 60vw;
    height: 32vw;
    background-image: url('/imgs/menu/get-dbs-crytals-horizontal.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .get-hints {
    width: 40vw;
    height: 50vw;
    background-image: url('/imgs/menu/get-hints.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .event {
    max-width: 180px;
    width: 100%;
    height: 47px;
    background-image: url('/imgs/menu/menu_btn.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
</style>
<style lang="scss">
.slide-menu {
  .slide-item {
    height: 125px;
    background-image: url('/imgs/menu_slide_item.png');
    background-size: 100% 100%;
    padding: 0 20px;
    background-repeat: no-repeat;
  }
  .swiper-wrapper {
    padding-bottom: 5px;
  }
  .swiper-pagination {
    bottom: 16px;
    display: flex;
    align-items: center;
    width: 65px !important;
    overflow: visible;
  }
  .swiper-pagination-bullet-active {
    width: 15px !important;
    height: 7px !important;
    border-radius: 2px;
    background: #00e0ff !important;
    opacity: 1;
  }
  .swiper-pagination-bullet {
    width: 5px;
    height: 5px;
    background: #425e9c;
    border-radius: 5px !important;
    opacity: 1;
  }
}
</style>
