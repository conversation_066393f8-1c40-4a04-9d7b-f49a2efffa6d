<script setup lang="ts">
import { useBAStore, useMapStore, useUserStore } from '@stores';
import { QBtn, QInput, QToggle } from 'quasar';

const { t } = useI18n();
const FIXED_LOCATIONS: Array<{ name: string; coordinates: [number, number] }> =
  [
    {
      name: '🇸🇬 SG',
      coordinates: [103.8198, 1.3521],
    },
    {
      name: '🇻🇳 HCM',
      coordinates: [106.6297, 10.8231],
    },
    {
      name: '🇲🇾 MY',
      coordinates: [101.9758, 4.2105],
    },
    {
      name: '🇯🇵 TYO',
      coordinates: [139.6917, 35.6895],
    },
    {
      name: '🇰🇷 SEO',
      coordinates: [126.978, 37.5665],
    },
  ];

const mapStore = useMapStore();
const userStore = useUserStore();
const baStore = useBAStore();
const { push } = useMicroRoute();

const { lastLocations, geoState, devTools, mapIns } = storeToRefs(mapStore);
const { isEnabledGPS, capitalandAmenities } = storeToRefs(userStore);
const { timeMissionData } = storeToRefs(baStore);

function setFakeLat(lat: any) {
  devTools.value.location[1] = +lat || 0;
  lastLocations.value = devTools.value.location;
}

function setFakeLng(lng: any) {
  devTools.value.location[0] = +lng || 0;
  lastLocations.value = devTools.value.location;
}

function toggleFakeGps(value: boolean) {
  devTools.value.fakeGps = value;

  if (!devTools.value.fakeGps) {
    devTools.value.location = lastLocations.value[0]
      ? [...lastLocations.value]
      : [...FIXED_LOCATIONS[0].coordinates];
    lastLocations.value = [...devTools.value.realLocation];
    isEnabledGPS.value = devTools.value.isEnableGPS;
  } else {
    devTools.value.location = lastLocations.value[0]
      ? [...lastLocations.value]
      : [...FIXED_LOCATIONS[0].coordinates];
    devTools.value.realLocation = [...lastLocations.value];
    devTools.value.isEnableGPS = isEnabledGPS.value;
    if (!isEnabledGPS.value) {
      isEnabledGPS.value = true;
    }
  }
}

function turnOnPickLocationMode() {
  if (devTools.value.pickLocationMode) {
    devTools.value.pickLocationMode = false;
    return;
  }
  devTools.value.pickLocationMode = true;
  devTools.value.targetLocation = [...devTools.value.location];
  push(-1);
}

function onSelectFixedLocation(coordinates: [number, number]) {
  if (devTools.value.fakeGps) {
    devTools.value.location = [...coordinates];
    lastLocations.value = [...coordinates];
    return;
  } else {
    mapIns.value?.flyTo({
      center: coordinates,
      zoom: 10,
      duration: 2500,
      minZoom: 8,
    });
    push(-1);
  }
}

function flyToFakeLocation() {
  if (devTools.value.fakeGps) {
    mapIns.value?.flyTo({
      center: devTools.value.location,
      duration: 2500,
    });
    push(-1);
  }
}
</script>

<template>
  <TestingComponent>
    <div class="relative overflow-hidden bg-black">
      <div class="p-2 space-y-2 overflow-auto h-[100svh] max-h-[100svh]">
        <div class="relative w-full pt-2">
          <Button
            shape="square"
            class="absolute top-0"
            variant="secondary"
            @click="push(-1)"
          >
            <Icon name="arrow-left" />
          </Button>
          <div class="text-lg font-bold text-center">Dev Tools</div>
        </div>
        <div>
          <QToggle
            :model-value="devTools.fakeGps"
            @update:model-value="toggleFakeGps"
            label="Fake GPS"
          />
          <Button shape="square" @click="push('push_notification_testing')">
            <Icon name="notifications" :size="15" />
          </Button>
        </div>
        <p class="select-text">
          Real GPS status:
          {{ devTools.fakeGps ? devTools.realLocation : lastLocations }}
          {{ geoState }}
        </p>
        <p class="select-text">
          Fake GPS status: {{ devTools.location }} {{ devTools.fakeGps }}
        </p>
        <hr class="my-2" />

        <div class="flex gap-2">
          <QBtn
            dark
            dense
            outline
            v-for="location in FIXED_LOCATIONS"
            :key="location.name"
            @click="() => onSelectFixedLocation(location.coordinates)"
          >
            {{ location.name }}
          </QBtn>
        </div>

        <div v-if="devTools.fakeGps" class="flex gap-2">
          <QInput
            dark
            dense
            outlined
            :model-value="devTools.location[0]"
            @update:model-value="setFakeLng"
            label="lng"
            class="bg-black w-36"
          />
          <QInput
            dark
            dense
            outlined
            :model-value="devTools.location[1]"
            @update:model-value="setFakeLat"
            label="lat"
            class="bg-black w-36"
          />
          <QBtn
            dark
            dense
            outline
            color="primary"
            @click="turnOnPickLocationMode"
          >
            <Icon name="location" />
          </QBtn>
          <QBtn
            v-if="devTools.fakeGps"
            dark
            dense
            outline
            color="secondary"
            @click="flyToFakeLocation"
          >
            <Icon name="location" />
          </QBtn>
        </div>

        <hr class="my-2" />

        <div v-if="timeMissionData?.type === 'location_based'">
          location based mission: {{ timeMissionData?.mission }}
        </div>
        <QToggle
          :model-value="devTools.dailyRewardMode"
          @update:model-value="devTools.dailyRewardMode = $event"
          label="Cheat Claim Daily Reward"
        />

        <hr class="my-2" />
      </div>
    </div>
  </TestingComponent>
</template>

<style lang="scss">
.faq {
  position: relative;
  background-color: #090422;
  padding: 10px;
  display: flex;
  flex-flow: column;
  width: 100%;
  height: 100%;
  .select-search {
    .q-field__label {
      color: #ffffff;
      opacity: 0.5;
    }
    .q-field__inner {
      z-index: 9;
      background: #2e3b54;
      border-radius: 4px;
    }

    .q-field__native {
      color: #ffffff;
    }
    .q-chip {
      background: #6e60cb;
      color: #ffffff;
    }
  }

  &-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    .faq-card {
      background: linear-gradient(
        178.55deg,
        rgba(37, 25, 109, 0.95) 1.24%,
        rgba(29, 65, 137, 0.9) 46.04%
      );
      border-radius: 4px;
      padding: 20px 12px;
      &:not(:last-child) {
        margin-bottom: 20px;
      }
    }
    li {
      margin-bottom: 10px;
    }
  }
}
</style>
