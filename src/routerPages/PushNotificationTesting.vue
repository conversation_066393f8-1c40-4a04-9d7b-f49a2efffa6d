<script lang="ts" setup>
import { successNotify } from '@helpers';
import { usePushNotificationStore, useUserStore } from '@stores';
import { copyToClipboard } from 'quasar';
import 'swiper/css';
import 'swiper/css/pagination';

const { push } = useMicroRoute();
const pushNotificationStore = usePushNotificationStore();
const userStore = useUserStore();

const { user } = storeToRefs(userStore);
const { pushNotificationStates } = storeToRefs(pushNotificationStore);

function handleCopyClipboard(str?: string) {
  if (!str) return;
  copyToClipboard(str);
  successNotify({
    message: 'Copied',
    timeout: 3000,
  });
}
</script>

<template>
  <div class="pb-5 fullscreen menu menu-vn">
    <Icon name="logo_htm" :size="170" class="mt-4 mb-5 mx-auto" />
    <Button class="absolute right-4 top-2" shape="square" @click="push(-1)">
      <Icon name="cross" :size="15" />
    </Button>

    <div class="w-full h-full overflow-y-auto">
      <div v-if="pushNotificationStates.initialized">
        <div
          v-if="!pushNotificationStates.permission"
          class="flex items-center justify-center gap-3 mb-5"
        >
          <Button
            @click="
              () =>
                user?.id &&
                pushNotificationStore.requestPushNotiPermission(user.id)
            "
          >
            Request Permission
          </Button>
        </div>
        <template v-if="pushNotificationStates.permission">
          <div
            v-if="
              pushNotificationStates.permission &&
              !pushNotificationStates.optedIn
            "
            class="flex items-center justify-center gap-3 mb-5"
          >
            <Button @click="pushNotificationStore.optIn"> Subscribe </Button>
          </div>
          <div
            v-if="
              pushNotificationStates.permission &&
              pushNotificationStates.optedIn
            "
            class="flex items-center justify-center gap-3 mb-5"
          >
            <Button @click="pushNotificationStore.optOut"> Unsubscribe </Button>
          </div>
        </template>
      </div>
      <div
        class="flex flex-col justify-center w-full gap-3 my-5 text-lg text-center"
      >
        <div>
          OneSignal initialized: {{ pushNotificationStates.initialized }}
        </div>

        <div>Permission: {{ pushNotificationStates.permission }}</div>

        <div>Opted-in: {{ pushNotificationStates.optedIn }}</div>

        <div
          @click="() => handleCopyClipboard(pushNotificationStates.oneSignalId)"
        >
          OneSignal ID <br />
          {{ pushNotificationStates.oneSignalId }}
        </div>

        <div
          @click="
            () => handleCopyClipboard(pushNotificationStates.subscriptionId)
          "
        >
          Subscription ID <br />
          {{ pushNotificationStates.subscriptionId }}
        </div>

        <div @click="() => handleCopyClipboard(user?.id)">
          User ID <br />
          {{ user?.id }}
        </div>

        <i class="text-xs text-gray-300">Click to copy</i>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.menu {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &.menu-vn {
    background: url('/imgs/bg-menu-vn.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  &.menu-sg {
    background: url('/imgs/bg-menu-sg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  .event {
    width: 105px;
    height: 115px;
    background-image: url('/imgs/menu-option.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
</style>
<style lang="scss">
.season-timeline {
  .swiper-wrapper {
    margin-top: 30px;
  }
  .line {
    border: 1px dashed rgba($color: #ffffff, $alpha: 0.6);
    position: absolute;
    z-index: -1;
    width: 95px;
    top: 50%;
    &.first {
      width: 50%;
      right: 0;
    }
    &.last {
      width: 50%;
      right: 50%;
    }
  }
  .swiper-slide-active {
    border-radius: 50%;
    width: 75px !important;
    height: 75px;
    background-image: url('/imgs/current-hunt.png');
    background-size: 100%;
    font-weight: 700;
    color: #76490a;
    font-size: 20px;
  }
  .prev-season {
    .circle-logo {
      background: linear-gradient(180deg, #38238d 0%, #14595f 100%);
      color: rgba($color: #ffffff, $alpha: 0.2);
    }
  }
  .next-season {
    .circle-logo {
      background: linear-gradient(180deg, #461e97 0%, #2a0c67 100%);
      color: rgba($color: #ffffff, $alpha: 0.2);
    }
  }
  .actived-circle {
    width: 65px;
    height: 65px;
    box-shadow: 0 0 10px 10px #ffffff29;
    background: linear-gradient(180deg, #461e97, #7b60c880);
  }
  .circle-logo {
    background: linear-gradient(180deg, #ffc267 0%, #ffa11f 100%);
  }
}
.slide-menu {
  .slide-item {
    height: 125px;
    background-image: url('/imgs/hint-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .swiper-pagination {
    bottom: 0;
  }
  .swiper-pagination-bullet-active {
    width: 25px !important;
    height: 8px !important;
    background: #9d93fb !important;
    border-radius: 4px !important;
  }
  .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: #48418e;
    border-radius: 4px;
    opacity: 1;
  }
}
</style>
