<script lang="ts" setup>
import { useDialogStore, useUserStore } from '@stores';
import { Loading } from 'quasar';
import { useBrandSov, useTrackData } from '@composables';
import { BRAND_SOV } from '@constants';
import { errorNotify } from '@helpers';
import gsap, { Linear, Elastic } from 'gsap';

const storeDialog = useDialogStore();
const storeUser = useUserStore();
const tl = gsap.timeline();

const { isVerifyingGoldenCoin, isFoundGoldenCoin, lastTenGrids } =
  storeToRefs(storeUser);
const { push, openDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();

const { randomResult } = useBrandSov('gold_coin_hint_shop_kv');

const disabled = computed(() => {
  return isVerifyingGoldenCoin.value || isFoundGoldenCoin.value;
});

const LOGO = computed(
  () =>
    `url(/imgs/${randomResult.value.gold_coin_hint_shop_kv.getAsset(
      '_logo'
    )}.png)`
);

const WINDOW = computed(
  () =>
    `url(/imgs/${randomResult.value.gold_coin_hint_shop_kv.getAsset(
      '_window'
    )}.png)`
);

const SHINOBI = computed(
  () => `${randomResult.value.gold_coin_hint_shop_kv.getAsset('_shinobi')}`
);

const ROOSTER = computed(
  () => `${randomResult.value.gold_coin_hint_shop_kv.getAsset('_rooster')}`
);

const PANDA = computed(
  () => `${randomResult.value.gold_coin_hint_shop_kv.getAsset('_panda')}`
);

function animated() {
  tl.fromTo(
    '.panda',
    {
      opacity: 0,
      y: 10,
    },
    {
      opacity: 1,
      duration: 0.5,
      y: 0,
      delay: 0.3,
      ease: Linear.easeIn,
    }
  )
    .fromTo(
      '.rooster',
      {
        opacity: 0,
        y: 100,
      },
      {
        opacity: 1,
        y: 0,
        duration: 1.5,
        ease: Elastic.easeInOut,
      },
      '-=1'
    )
    .fromTo(
      '.shinobi',
      {
        opacity: 0,
        x: 100,
      },
      {
        opacity: 1,
        x: 0,
        duration: 0.5,
        ease: Elastic.easeOut,
      },
      '-=0.5'
    );
}

async function handleSelectHint(path: string) {
  if (path === 'eliminated_hint' && lastTenGrids.value) {
    errorNotify({
      message: t('LAST_10_GRIDS_ERRORS'),
    });
    return;
  }
  Loading.show();
  await storeUser.checkHint();
  openDialog(path);
  Loading.hide();
}

onMounted(async () => {
  await nextTick();
  animated();
});

onBeforeUnmount(() => {
  tl.kill();
});
</script>
<template>
  <div class="fullscreen shop bg-[#462472]">
    <Button
      class="absolute z-50 top-2 left-2"
      shape="square"
      variant="secondary"
      @click="
        track('golden_coin_shop', {
          screen: 'golden_coin_shop',
          button: 'back',
        });
        push(-1);
      "
    >
      <Icon name="arrow-left" />
    </Button>

    <div
      class="absolute z-50 text-[17px] font-bold -translate-x-1/2 top-3 left-1/2"
      :class="{
        'text-black':
          randomResult.gold_coin_hint_shop_kv.brand_unique_id ===
          BRAND_SOV.TIGER_BROKER,
      }"
      v-html="t('SHOP_TITLE')"
    ></div>
    <div class="absolute z-50 flex flex-col items-center top-2 right-1">
      <HeaderCrystal class="z-20 w-full" />
      <Button
        class="-mt-2 z-10 w-[80px]"
        :label="t('BUTTON_GET_MORE')"
        size="small"
        @click="
          track('golden_coin_shop', {
            screen: 'golden_coin_shop',
            button: 'get_more',
          });
          push('offer_wall');
        "
      />
    </div>
    <div class="window">
      <template v-if="disabled">
        <div class="w-full h-[65vw] absolute bottom-0 z-10">
          <Icon name="inactive-shop" class="!w-full h-full" />
          <div class="notes">
            <div
              class="text-center"
              style="transform: skew(10deg, -2deg)"
              v-html="t('INTACTIVE_SHOP_NOTES')"
            ></div>
          </div>
        </div>
      </template>
      <Icon
        v-if="
          randomResult.gold_coin_hint_shop_kv.brand_unique_id ===
          BRAND_SOV.TIGER_BROKER
        "
        class="panda absolute top-[30%] right-[35%] opacity-0 pointer-events-none"
        :name="PANDA"
        :size="40"
      />
      <Icon
        class="rooster absolute bottom-0 left-16 w-[25vw] z-[-1] opacity-0 pointer-events-none"
        :name="ROOSTER"
      />
      <Icon
        class="shinobi absolute bottom-0 right-16 w-[30vw] z-[-1] opacity-0 pointer-events-none"
        :name="SHINOBI"
      />
    </div>
    <div class="flex justify-center items-center gap-5 pt-[15vw] mb-5">
      <div
        :class="{
          'text-hint': !disabled,
          'text-hint-inactive pointer-events-none': disabled,
        }"
        @click="
          handleSelectHint('text_hint');
          track('golden_coin_shop', {
            screen: 'golden_coin_shop',
            button: 'golden_text_hint',
          });
        "
      >
        <div
          class="absolute bottom-4 text-center text-[#090422] text-sm font-bold px-3"
          :class="{
            'opacity-50': disabled,
          }"
          v-html="t('SHOP_ITEM_1')"
        ></div>
      </div>
      <div
        :class="{
          'eliminated-hint': !disabled,
          'eliminated-hint-inactive pointer-events-none': disabled,
        }"
        @click="
          handleSelectHint('eliminated_hint');
          track('golden_coin_shop', {
            screen: 'golden_coin_shop',
            button: 'eliminate_pu',
          });
        "
      >
        <div
          class="absolute bottom-4 text-center text-[#090422] text-sm font-bold px-3"
          :class="{
            'opacity-50': disabled,
          }"
          v-html="t('SHOP_ITEM_2')"
        ></div>
      </div>
    </div>
    <div class="text-center">
      <Button
        :label="t('BUTTON_GO_MY_HINTS')"
        class="!w-[210px]"
        @click="
          track('golden_coin_shop', {
            screen: 'golden_coin_shop',
            button: 'go_to_my_hints',
          });
          push('my_hint');
        "
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.shop {
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 32.5vw;
    background-image: url(/imgs/shop/bottom.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    pointer-events: none;
  }
  .window {
    pointer-events: none;
    position: relative;
    width: 100%;
    height: 77vw;
    background-image: v-bind(WINDOW);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 1;
    &::before {
      content: '';
      position: absolute;
      z-index: 999;
      bottom: -12vw;
      left: 50%;
      transform: translateX(-50%);
      width: 79vw;
      height: 19vw;
      background-image: v-bind(LOGO);
      background-size: 100% 100%;
    }
  }
  .hint {
    position: relative;
    width: 38vw;
    height: 44vw;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .text-hint {
    @extend .hint;
    background-image: url(/imgs/shop/text-hint.png);
  }
  .text-hint-inactive {
    @extend .hint;
    background-image: url(/imgs/shop/text-hint-inactive.png);
  }
  .eliminated-hint {
    @extend .hint;
    background-image: url(/imgs/shop/eliminated-hint.png);
  }
  .eliminated-hint-inactive {
    @extend .hint;
    background-image: url(/imgs/shop/eliminated-hint-inactive.png);
  }
  .notes {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -55%);
    width: 192px;
    height: 135px;
    background-image: url(/imgs/shop-note.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: #1c164c;
    font-family: 'Games Studio';
    font-size: 14px;
    padding: 30px;
  }
}
</style>
