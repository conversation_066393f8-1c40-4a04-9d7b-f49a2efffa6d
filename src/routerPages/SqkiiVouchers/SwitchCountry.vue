<script lang="ts" setup>
import { useVouchersStore } from '@stores';
import { Loading } from 'quasar';

interface Props {
  code: string;
}

const props = defineProps<Props>();

const storeVouchers = useVouchersStore();

const { t } = useI18n();
const { closeAllDialog, push } = useMicroRoute();

async function changeCountry() {
  Loading.show();
  storeVouchers.updateUser({ country: props.code });
  LocalStorage.set(`${process.env.APP_NAME}_SQKII_VOUCHER_COUNTRY`, props.code);
  await storeVouchers.fetchUser();
  closeAllDialog();
  push(-1);
  Loading.hide();
}
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('SWITCH_COUNTRY_HEADER')"></div>
    </template>
    <div class="text-center">
      <Icon
        class="!w-full !object-cover rounded-lg mb-5"
        name="sqkii_voucher_kv"
      />
      <div class="text-sm mb-5" v-html="t('SWITCH_COUNTRY_DESC')"></div>
      <Button :label="t('SWITCH_COUNTRY_CONFIRM')" @click="changeCountry" />
    </div>
  </Dialog>
</template>
