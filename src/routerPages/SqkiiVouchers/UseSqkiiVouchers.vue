<script lang="ts" setup>
import { useMediaDevice, useTrackData } from '@composables';
import { errorNotify } from '@helpers';
import { VOUCHERS } from '@repositories';
import { last, throttle } from 'lodash';
import type { IAPIVouchersResponseError } from '@types';

const { t } = useI18n();
const { push, openDialog, closeDialog } = useMicroRoute();
const { track } = useTrackData();

const streamVideo = ref<MediaStream | null>(null);

const { request } = useMediaDevice(
  {
    video: {
      facingMode: 'environment',
    },
    audio: false,
  },
  requestCameraSuccess,
  requestCameraFailed
);

function requestCameraSuccess(stream: MediaStream) {
  streamVideo.value = stream;
  closeDialog('camera_permission');
  track('camera_pop_up', {
    result: false,
  });
}

function requestCameraFailed() {
  errorNotify({
    message: t('CAMERA_SCAN_PAYMENT_NOT_DETECTED'),
    timeout: 8000,
  });
  closeDialog('camera_permission');
  track('camera_pop_up', {
    result: false,
  });
}

const throttleDecode = throttle(onDecode, 1000);

async function onDecode(code: string) {
  try {
    const qr_code = last(code.split('/'));
    if (!qr_code) return;
    const { data } = await VOUCHERS.paymentScanQR(qr_code);
    openDialog('pay_to_merchant', {
      outlet: data,
    });
  } catch (err) {
    const { data, message } = err as IAPIVouchersResponseError;
    switch (true) {
      case data?.info === 'outlet_invalid':
        errorNotify({
          message: t('OUTLET_INVALID_CODE'),
        });
        break;
      default:
        errorNotify({
          message: message,
        });
        break;
    }
  }
}

onMounted(async () => {
  await nextTick();
  if (!!LocalStorage.getItem('camera_permission')) request();
  else {
    openDialog('camera_permission', {
      onRequest: request,
      description: t('SQKII_VOUCHERS_CAMERA_PERMISSION_DESC'),
    });
  }
});
</script>
<template>
  <div class="fullscreen flex flex-col flex-nowrap bg-[#46425B] pb-5">
    <div
      class="flex justify-center items-center h-16 px-20 relative shrink-0 mb-5"
    >
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold text-center w-full"
        v-html="t('USE_SQKII_VOUCHERS_HEADER')"
      ></div>
      <Button
        shape="square"
        class="absolute right-4"
        @click="push('search_outlets')"
      >
        <Icon name="scan" :size="20" />
      </Button>
    </div>
    <div class="flex flex-col flex-nowrap justify-around h-full">
      <div class="text-center">
        <div class="text-sm mb-5" v-html="t('USE_SQKII_VOUCHERS_DESC_1')"></div>
        <div class="size-[297px] bg-[#555] rounded-lg mx-auto relative mb-10">
          <div
            class="w-full h-full flex justify-center items-center"
            v-if="!streamVideo"
          >
            <q-spinner color="primary" size="5em" :thickness="3" />
          </div>
          <StreamBarcodeReader
            v-if="streamVideo"
            :stream="streamVideo"
            class="!w-full !h-full rounded-lg"
            @decode="throttleDecode"
          />
        </div>
      </div>
      <div class="text-center">
        <div
          class="text-sm mb-5 px-10"
          v-html="t('USE_SQKII_VOUCHERS_DESC_2')"
        ></div>
        <Button
          class="!w-[210px]"
          :label="t('USE_SQKII_VOUCHERS_BTN_ID')"
          variant="purple"
          @click="openDialog('enter_payment_code')"
        />
      </div>
    </div>
  </div>
</template>
