<script lang="ts" setup>
import {
  SqkiiVouchersTransactionTypeLabels,
  SqkiiVouchersTransactionStatus,
  SqkiiVouchersTransactionType,
} from '@constants';
import { onClickOutside } from '@vueuse/core';
import { useQuery } from '@tanstack/vue-query';
import { VOUCHERS } from '@repositories';
import {
  dateTimeFormat,
  FULL_DATE_TIME_24H_FORMAT,
  numeralFormat,
} from '@helpers';
import type { ITransactionHistory } from '@types';

const { t } = useI18n();
const { push, openDialog } = useMicroRoute();

const transactions = ref<ITransactionHistory[]>([]);
const totalPage = ref(1);
const page = ref(1);
const PAGE_SIZE = 20;
const target = ref(null);
const showFilter = ref(false);
const filterBy = ref('all');
const tab = ref('All');
const tabLabels = computed(() => {
  return [
    'All',
    ...Object.values(SqkiiVouchersTransactionTypeLabels),
    ...Object.values(SqkiiVouchersTransactionStatus),
  ];
});
const FILTER_LIST = [
  {
    label: 'All Time',
    value: 'all',
  },
  {
    label: 'Today',
    value: 'today',
  },
  {
    label: 'Last 30 Days',
    value: '30d',
  },
  {
    label: 'Last 60 Days',
    value: '60d',
  },
  {
    label: 'Last 90 Days',
    value: '90d',
  },
];

onClickOutside(target, () => {
  showFilter.value = false;
});

const params = computed(() => {
  const type =
    Object.keys(SqkiiVouchersTransactionTypeLabels).find(
      (key) =>
        SqkiiVouchersTransactionTypeLabels[
          key as SqkiiVouchersTransactionType
        ] === tab.value
    ) || 'all';
  const status = Object.values(SqkiiVouchersTransactionStatus).includes(
    tab.value as SqkiiVouchersTransactionStatus
  )
    ? tab.value
    : undefined;

  return {
    filter: filterBy.value,
    type,
    status,
    page: page.value,
  };
});

useQuery({
  queryKey: ['TRANSACTIONS_HISTORY', params],
  queryFn: () => {
    return VOUCHERS.getTransactionHistory(params.value);
  },
  select({ data: { data, total } }) {
    totalPage.value = Math.ceil(total / PAGE_SIZE);
    transactions.value =
      totalPage.value > 1 ? [...transactions.value, ...data] : data;
  },
});

function onLoad(_index: number, done: (stop?: boolean) => void) {
  if (page.value < totalPage.value) page.value++;
  done();
}
</script>
<template>
  <div
    class="fullscreen flex flex-col flex-nowrap bg-[#090422] overflow-hidden"
  >
    <div
      class="relative flex items-center justify-center h-16 px-20 mb-5 shrink-0"
    >
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="w-full text-lg font-extrabold text-center"
        v-html="t('TRANSACTION_HISTORY_HEADER')"
      ></div>
      <Button
        shape="square"
        class="absolute right-4"
        @click="showFilter = !showFilter"
      >
        <Icon name="ic_fillter" :size="14" />
      </Button>
      <div
        ref="target"
        class="flex flex-col items-end justify-center gap-4 overflow-hidden filter-hint"
        :class="{
          'filter-hint-open': showFilter,
        }"
      >
        <div
          v-for="{ value, label } in FILTER_LIST"
          :key="value"
          class="relative text-base"
          @click="
            filterBy = value;
            showFilter = false;
          "
        >
          <span
            class="text-sm"
            :class="{
              'font-bold': filterBy === value,
            }"
          >
            {{ label }}
          </span>
          <Icon
            v-if="filterBy === value"
            name="arrow-left"
            :size="15"
            class="absolute top-1 -right-4"
          />
        </div>
      </div>
    </div>
    <div
      class="flex items-center gap-2 mx-10 mb-5 overflow-x-auto flex-nowrap snap-x snap-mandatory shrink-0"
    >
      <div
        class="capitalize snap-start bg-[#200D37] border border-[#B663E9] rounded-[10px] px-5 py-3 w-max shrink-0"
        :class="{
          'bg-[#5D3AC0] ': item === tab,
        }"
        v-for="item in tabLabels"
        :key="item"
        v-html="item"
        @click="tab = item"
      ></div>
    </div>

    <q-infinite-scroll
      @load="onLoad"
      :offset="200"
      class="flex flex-col h-full gap-5 px-5 pb-5 overflow-y-auto flex-nowrap"
    >
      <template v-if="transactions.length">
        <div
          v-for="item in transactions"
          :key="item.id"
          class="relative px-3 py-5 rounded"
          style="
            background: linear-gradient(
              179deg,
              rgba(37, 25, 109, 0.95) 1.24%,
              rgba(29, 65, 137, 0.9) 46.04%
            );
          "
          @click="
            openDialog('transaction_details', {
              transaction: item,
            })
          "
        >
          <div class="flex items-center justify-between mb-2">
            <div class="capitalize" :class="item.status">
              {{ item.status }}
            </div>
            <Icon name="arrow-left" class="rotate-180" :size="16" />
          </div>
          <div
            class="text-sm"
            v-html="
              t('TRANSACTION_HISTORY_ID', {
                ID:
                  item.type === SqkiiVouchersTransactionType.PAYMENT
                    ? item.payment_id
                    : item.txn_id,
              })
            "
          ></div>
          <div
            class="text-sm"
            v-html="
              t('TRANSACTION_HISTORY_AMOUNT', {
                AMOUNT: `${item.currency}$ ${numeralFormat(
                  item.amount,
                  '0,0.00'
                )}`,
              })
            "
          ></div>
          <div class="text-sm">{{ item.outlet_name }}</div>
          <div class="text-sm opacity-50">
            {{ dateTimeFormat(item.updated_at, FULL_DATE_TIME_24H_FORMAT) }}
          </div>
        </div>
      </template>
      <template v-else>
        <div
          class="flex items-center justify-center h-full text-2xl font-bold"
          v-html="t('TRANSACTION_HISTORY_NO_DATA')"
        ></div>
      </template>
      <template v-slot:loading>
        <div class="justify-center row q-my-md">
          <q-spinner-dots color="primary" size="20px" />
        </div>
      </template>
    </q-infinite-scroll>
  </div>
</template>
<style lang="scss" scoped>
.filter-hint {
  position: absolute;
  bottom: 20px;
  right: 20px;
  transform: translateY(calc(100% + 14px));
  width: max-content;
  background: linear-gradient(
    180.05deg,
    rgba(37, 25, 109, 0.9) 15.77%,
    rgba(29, 65, 137, 0.9) 98.5%
  );
  padding: 0;
  border: 1px solid #11d1f9;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
  border-radius: 10px 2px 10px 10px;
  opacity: 0;
  transition: all 0.3s;
  flex-wrap: nowrap;
  z-index: 9999999;
  height: 0px;
  &-open {
    height: 200px;
    transition: all 0.3s;
    opacity: 1;
    padding: 0 30px;
  }
}
.refunded {
  margin-left: -16px;
  background: linear-gradient(90deg, #bc18d7 0%, #dc4bf3 100%);
  width: max-content;
  padding: 2px 10px;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 2px;
}
.failed {
  margin-left: -16px;
  background: linear-gradient(90deg, #f64343 -0.34%, #ff4d4d 99.59%);
  width: max-content;
  padding: 2px 10px;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 2px;
}
.completed {
  margin-left: -16px;
  background: linear-gradient(90deg, #30a256 -0.34%, #3ee75a 99.59%);
  width: max-content;
  padding: 2px 10px;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 2px;
}
</style>
