<script lang="ts" setup>
const { t } = useI18n();
const { push } = useMicroRoute();

const FAQ = [
  {
    question: 'What is the difference between a voucher and a coupon?',
    answer:
      'A voucher is a bond of the redeemable transaction type which is worth a certain monetary value and which may be spent only for specific reasons or on specific goods. A coupon is a voucher that entitles the holder to a discount off a particular product, service, or other. Vouchers are given out by retailers and manufacturers as a part of sales promotions.',
  },
  {
    question: 'How do I use a voucher?',
    answer:
      'To use a voucher, you will need to present it to the retailer or manufacturer. The retailer or manufacturer will then verify the voucher and apply the discount to your purchase.',
  },
  {
    question: 'How do I redeem a voucher?',
    answer:
      'To redeem a voucher, you will need to present it to the retailer or manufacturer. The retailer or manufacturer will then verify the voucher and apply the discount to your purchase.',
  },
  {
    question: 'How do I redeem a coupon?',
    answer:
      'To redeem a coupon, you will need to present it to the retailer or manufacturer. The retailer or manufacturer will then verify the coupon and apply the discount to your purchase.',
  },
  {
    question: 'How I can get a voucher?',
    answer:
      'You can get a voucher by purchasing a product or service from a retailer or manufacturer that is offering a voucher as part of a sales promotion. You can also get a voucher by participating in a contest or sweepstakes that offers vouchers as prizes.',
  },
  {
    question: 'Can I use a voucher online?',
    answer:
      'Yes, you can use a voucher online. Many retailers and manufacturers offer online vouchers that can be redeemed on their websites. To use an online voucher, you will need to enter the voucher code at the checkout page when making a purchase.',
  },
];
</script>
<template>
  <div class="fullscreen bg-[#090422]">
    <div
      class="fixed top-0 left-0 z-10 flex justify-center items-center h-20 w-full"
    >
      <Button
        class="absolute left-3"
        shape="square"
        variant="secondary"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold"
        v-html="t('SQKII_VOUCHERS_FAQ_HEADING')"
      ></div>
    </div>
    <div
      class="relative w-full h-[calc(100%-80px)] overflow-y-auto mt-20 px-6 pb-5"
    >
      <div class="flex flex-col flex-nowrap gap-5">
        <Expansion
          v-for="{ answer, question } in FAQ"
          :key="question"
          switch-toggle-side
          expand-separator
          group="faq"
          class="rounded-[10px]"
          style="
            background: linear-gradient(
              179deg,
              rgba(37, 25, 109, 0.95) 1.24%,
              rgba(29, 65, 137, 0.9) 46.04%
            );
          "
        >
          <template #header>
            <div class="text-xl font-bold w-[90%]" v-html="question"></div>
          </template>
          <div class="text-sm px-4 pb-5" v-html="answer"></div>
        </Expansion>
      </div>
    </div>
  </div>
</template>
