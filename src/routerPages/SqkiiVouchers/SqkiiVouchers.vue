<script lang="ts" setup>
import { useClick, useTick, useTrackData } from '@composables';
import { formatName, numeralFormat } from '@helpers';
import { useMapStore, useUserStore, useVouchersStore } from '@stores';
import { useScroll, useElementSize } from '@vueuse/core';
import { last } from 'lodash';
import type { IOutlet } from '@types';
import dayjs from 'dayjs';
import { BRAND_SOV } from '@constants';

const storeVouchers = useVouchersStore();
const storeMap = useMapStore();
const storeUser = useUserStore();

const { user, merchantAcquisition } = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const {
  outlets,
  user: vouchersUser,
  isSyncedWithKee,
} = storeToRefs(storeVouchers);
const { t } = useI18n();
const { push, openDialog, currentPath, dialogs } = useMicroRoute();
const { now } = useTick();
const { track } = useTrackData();

const scrollerEl = ref<HTMLElement>();
const bannerEl = ref<HTMLElement>();
const tab = ref('All');
const search = ref('');
const closed = ref(false);

const { y } = useScroll(scrollerEl);
const { height } = useElementSize(bannerEl);

useClick('MERCHANT_ACQUISITION', () => {
  openDialog('merchant_acquisition');
});

const isSVHome = computed(
  () => last(currentPath.value.split('/')) === 'sqkii_vouchers'
);

const time = ref(dayjs().add(1, 'minute').toISOString());
const show = ref(false);

const remainingTime = computed(() => {
  return +new Date(time.value) - now.value;
});

const categories = computed(() => {
  const data: string[] = [];
  Object.keys(outlets.value).forEach((key) => {
    outlets.value[key].forEach((item) => {
      if (!data.includes(item.brand_type)) data.push(item.brand_type);
    });
  });
  return ['All', ...data];
});

const sortedOutlets = computed(() => {
  const sortedData: Record<string, IOutlet[]> = {};
  const lowercaseSearch = search.value.toLowerCase();
  const filteredKeys = Object.keys(outlets.value).filter((key) => {
    const _outlets = outlets.value[key];
    const filterByBrandType = _outlets.every(
      (item) => item.brand_type === tab.value
    );
    return (
      (tab.value === 'All' || filterByBrandType) &&
      (key.toLowerCase().includes(lowercaseSearch) ||
        _outlets.some(
          (item) =>
            item.name.toLowerCase().includes(lowercaseSearch) ||
            item.address.toLowerCase().includes(lowercaseSearch)
        ))
    );
  });
  filteredKeys
    .sort((a, b) => a.localeCompare(b))
    .forEach((key) => {
      outlets.value[key].sort((a, b) => {
        const distanceA = calculateDistance(
          a.location.coordinates,
          lastLocations.value
        );
        const distanceB = calculateDistance(
          b.location.coordinates,
          lastLocations.value
        );
        return distanceA - distanceB;
      });

      sortedData[key] = outlets.value[key];
    });
  return sortedData;
});

function calculateDistance(coord1: [number, number], coord2: [number, number]) {
  const [lon1, lat1] = coord1;
  const [lon2, lat2] = coord2;
  const R = 6371; // Radius of the Earth in km
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    0.5 -
    Math.cos(dLat) / 2 +
    (Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      (1 - Math.cos(dLon))) /
      2;
  return R * 2 * Math.asin(Math.sqrt(a));
}

watch(remainingTime, (time) => {
  if (show.value) return;
  if (dialogs.value.some((d) => d.actived) || !isSVHome.value) return;
  if (time <= 0 && vouchersUser.value && !vouchersUser.value.total_topup) {
    openDialog('first_bonus_crystals');
    show.value = true;
  }
});

watch(isSVHome, (isHome) => {
  if (isHome) {
    time.value = dayjs().add(1, 'minute').toISOString();
    show.value = false;
  }
});

useClick('SQKII_VOUCHERS_CREATE', () => {
  if (!user.value?.mobile_number) openDialog('sqkii_vouchers_welcome');
  else openDialog('create_kee_account');
});

useClick('SQKII_VOUCHERS_LINK', () => {
  if (!user.value?.mobile_number) openDialog('sqkii_vouchers_welcome');
  else openDialog('link_kee_welcome');
});

function scrollTop() {
  scrollerEl.value?.scrollTo({ top: 0, behavior: 'smooth' });
}

function handleCloseCallout() {
  closed.value = true;
  storeUser.updateMerchantAcquisition('closed_callout');
}

onBeforeMount(() => {
  const token = LocalStorage.getItem(
    `${process.env.APP_NAME}_SQKII_VOUCHER_TOKEN`
  );
  if (!token) storeVouchers.$reset();
  storeUser.merchantAcquisition.submitted = false;
});

onMounted(async () => {
  storeUser.updateMerchantAcquisition('visited_sv');
  track('sv_home_access_daily');
  if (!vouchersUser.value?.id) openDialog('sqkii_vouchers_welcome');
  if (isSyncedWithKee.value) await storeVouchers.fetchUser();
  await storeVouchers.fetchOutlets();
  if (vouchersUser.value && !vouchersUser.value.hasPin) openDialog('set_pin');
});
</script>
<template>
  <div ref="scrollerEl" class="fullscreen bg-[#200D37] overflow-y-auto">
    <div ref="bannerEl" class="banner relative">
      <div
        class="fixed z-30 flex justify-center items-center w-full h-16 px-20 bg-[#16181D]"
        :class="{
          'bg-[#200D37]': y >= height - 80,
        }"
      >
        <Button
          variant="secondary"
          shape="square"
          class="absolute left-4"
          @click="push(-1)"
        >
          <Icon name="arrow-left" />
        </Button>
        <div
          class="text-lg font-extrabold text-center w-full"
          v-html="t('SQKII_VOUCHERS_HEADER')"
        ></div>
        <HeaderCrystal class="absolute right-4" />
      </div>
      <div class="card-balance">
        <div class="frame-balance flex flex-nowrap flex-col justify-center">
          <div
            class="flex items-start justify-between"
            :class="{
              'mb-5': !!vouchersUser,
            }"
          >
            <div class="flex flex-col mb-2">
              <div
                class="text-base font-medium text-white opacity-70"
                v-html="t('SQKII_VOUCHERS_BALANCE_TITLE')"
              ></div>
              <div class="text-3xl font-bold">
                {{ vouchersUser?.currency || 'S$' }}
                {{
                  numeralFormat(Number(vouchersUser?.balance || 0), '0,0.00')
                }}
              </div>
            </div>
            <Icon
              v-if="!vouchersUser?.id"
              class="absolute right-5 -top-20"
              :name="`/sov/vouchers/${BRAND_SOV.DBS}`"
              :size="180"
            />
            <div class="flex items-center gap-2" v-if="vouchersUser?.id">
              <Button
                :disable="!vouchersUser"
                shape="square"
                @click="
                  push('transaction_history');
                  track('sv_transactionhistory');
                "
              >
                <Icon name="history" />
              </Button>
              <Button
                shape="square"
                @click="
                  push('sqkii_vouchers_setting');
                  track('sv_settings');
                "
              >
                <Icon name="settings" :size="14" />
              </Button>
            </div>
          </div>
          <div
            class="flex flex-nowrap items-start gap-2 mb-1"
            v-if="!vouchersUser"
          >
            <Icon name="exclamation-mark" :size="15" class="mt-1" />
            <div
              class="text-sm"
              v-html="t('SQKII_VOUCHERS_BALANCE_DESC')"
            ></div>
          </div>
          <div class="flex flex-nowrap items-center gap-4 mb-2">
            <Button
              variant="purple"
              :label="t('SQKII_VOUCHERS_BTN_GET_VOUVHERS')"
              @click="
                !vouchersUser
                  ? openDialog(
                      !user?.mobile_number
                        ? 'sqkii_vouchers_welcome'
                        : 'create_kee_account'
                    )
                  : openDialog('get_sqkii_vouchers');
                track('sv_prelogin_getvouchers');
              "
            />
            <Button
              :label="t('SQKII_VOUCHERS_BTN_USE_VOUVHERS')"
              class="!w-full"
              @click="
                !vouchersUser
                  ? openDialog(
                      !user?.mobile_number
                        ? 'sqkii_vouchers_welcome'
                        : 'create_kee_account'
                    )
                  : push('use_sqkii_vouchers');
                track('sv_prelogin_usevouchers');
              "
            />
          </div>
        </div>
        <Icon
          v-if="vouchersUser?.id"
          class="absolute -top-20 left-1/2 -translate-x-1/2 -z-10"
          :name="`/sov/vouchers/${BRAND_SOV.DBS}`"
          :size="180"
        />
      </div>
    </div>
    <div
      class="py-5"
      :class="{
        'sticky top-0 h-full z-20': y >= height - 80,
        'pt-20': y >= height - 40,
      }"
    >
      <div
        class="frame-balance-small hidden justify-between"
        :class="{
          '!flex transition-all duration-200': y >= height - 80,
        }"
      >
        <div class="flex items-center gap-2">
          <Icon name="sv_balance" :size="48" />
          <div class="flex flex-col">
            <div
              class="text-base font-medium text-white opacity-70"
              v-html="t('SQKII_VOUCHERS_BALANCE_TITLE')"
            ></div>
            <div class="text-2xl font-bold">
              {{ vouchersUser?.currency || 'S$' }}
              {{ numeralFormat(Number(vouchersUser?.balance || 0), '0,0.00') }}
            </div>
          </div>
        </div>
        <div class="flex flex-col items-center">
          <Button variant="secondary" shape="square" @click="scrollTop">
            <Icon name="arrow-up" />
          </Button>
          <div
            class="font-bold text-xs"
            v-html="t('SQKII_VOUCHERS_BACK_TO_TOP')"
          ></div>
        </div>
      </div>
      <div class="flex flex-col gap-5 mb-5 px-5">
        <div
          class="text-sm text-center"
          v-html="t('SQKII_VOUCHERS_BALANCE_DESC_1')"
        ></div>
        <Input
          class="full-width"
          type="search"
          v-model="search"
          :label="t('SQKII_VOUCHERS_SEARCH')"
        />
      </div>
      <div
        class="flex flex-nowrap items-center gap-2 overflow-x-auto snap-x snap-mandatory mb-5 mx-5"
        v-if="Object.keys(outlets).length"
      >
        <div
          v-for="item in categories"
          style="background: linear-gradient(#b663e9, #4f46c1)"
          class="p-[1px] rounded-[10px]"
          :key="item"
        >
          <div
            class="snap-start bg-[#200D37] rounded-[9px] px-5 py-3 w-max shrink-0"
            :class="{
              'bg-[#5D3AC0] ': item === tab,
            }"
            v-html="formatName(item)"
            @click="tab = item"
          ></div>
        </div>
      </div>
      <div
        class="card-alert relative mx-5 mb-5 py-2.5 px-4 text-sm italic"
        :class="[merchantAcquisition.last_type]"
        v-if="
          merchantAcquisition.show_callout &&
          !closed &&
          !merchantAcquisition.submitted
        "
      >
        <div
          class="text-sm pr-10"
          v-html="
            merchantAcquisition.last_type === 'friends'
              ? t('MERCHANT_ACQUISITION_FRIENDS')
              : t('MERCHANT_ACQUISITION_OWNERS')
          "
        ></div>
        <Icon
          class="absolute right-2 top-2"
          name="cross"
          :size="12"
          @click="handleCloseCallout"
        />
      </div>
      <div
        class="grid grid-cols-3 gap-[18px] h-[calc(100%-300px)] overflow-y-auto px-5"
      >
        <div
          v-for="(outlets, name) in sortedOutlets"
          :key="name"
          @click="
            push('merchant_list', {
              merchant: name,
              outlets,
            })
          "
        >
          <Icon
            :name="outlets[0].image"
            type="url"
            lazy
            class="!w-full aspect-square rounded-[10px]"
          />
          <div class="text-center font-bold mt-[15px]" v-html="t(name)"></div>
          <!-- <div
            class="flex flex-nowrap gap-2"
            @click="
              push('merchant_list', {
                merchant: name,
                outlets,
              })
            "
          >
            <div class="size-[96px] rounded-[10px] bg-[#D9D9D9] shrink-0">
              <Icon
                :name="outlets[0].image"
                type="url"
                lazy
                class="!w-full h-full rounded-[10px]"
              />
            </div>
            <div class="flex flex-col bg-[#5d3ac030] rounded-[10px] p-4 w-full">
              <div class="text-base font-bold" v-html="name"></div>
              <div class="text-sm text-ellipsis line-clamp-2">
                <div v-html="outlets[0].address"></div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.banner {
  background: url('/imgs/sqkii-vouchers-bg.png') no-repeat;
  background-size: cover;
  width: 100%;
  height: 90.5vw;
}
.card-balance {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 96vw;
  height: 52vw;

  .frame-balance {
    padding: 10px 15px 20px;
    width: 100%;
    height: 100%;
    background-image: url('/imgs/sqkii-vouchers-balance.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 10;
  }
}

.card-alert {
  border-radius: 10px;
  background: #320b5b;
  &.owners {
    border: 2px solid #38cfe5;
    box-shadow: 0px 0px 7px 0px #11d1f9;
  }
  &.friends {
    border: 2px solid #fc5ac9;
    box-shadow: 0px 0px 7px 0px #fc78d6;
  }
}

.frame-balance-small {
  margin: 8px 8px 20px;
  width: calc(100% - 16px);
  height: auto;
  padding: 10px 25px;
  border-radius: 20px;
  border: 2px solid #b663e9;
  background: linear-gradient(180deg, #1b0d46 50.43%, #271364 109.13%);
  box-shadow: 0px 4px 8px 2px rgba(73, 163, 191, 0.25),
    0px 4px 9px 6px rgba(159, 74, 211, 0.2);
}
</style>
