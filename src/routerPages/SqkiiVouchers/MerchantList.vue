<script lang="ts" setup>
import type { IOutlet } from '@types';

interface Props {
  merchant: string;
  outlets: IOutlet[];
}

defineProps<Props>();

const { t } = useI18n();
const { push } = useMicroRoute();
</script>
<template>
  <div
    class="fullscreen bg-[#090422] flex flex-nowrap flex-col overflow-hidden"
  >
    <div
      class="relative flex justify-center items-center w-full h-16 px-20 mb-5 shrink-0"
    >
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold text-center w-full"
        v-html="merchant"
      ></div>
    </div>
    <div
      class="px-6 text-sm mb-7"
      v-html="
        t('MERCHANT_LOCATION_AVAILABLE', {
          NUMBER: outlets.length,
        })
      "
    ></div>
    <div class="flex flex-nowrap flex-col gap-4 overflow-y-auto px-6 pb-5">
      <div class="bg-[#843EFF] rounded flex flex-nowrap gap-2 p-2.5">
        <Icon name="exclamation-mark" class="size-5" />
        <div class="flex flex-col gap-1">
          <div
            class="text-xs font-bold"
            v-html="
              t('SV_TAC_HEADER', {
                MERCHANT: merchant,
              })
            "
          ></div>

          <div
            class="text-xs"
            v-html="
              outlets[0].type === 'The Eyes Inc'
                ? t('SV_TAC_DESC_TEI')
                : outlets[0].min_spend && outlets[0].min_spend > 0
                ? t('SV_TAC_DESC_1', {
                    AMOUNT: outlets[0].min_spend,
                    NAME: outlets[0].name,
                  })
                : t('SV_TAC_DESC')
            "
          ></div>
        </div>
      </div>
      <div
        class="flex flex-nowrap justify-between items-center px-3 py-5 bg-[#5d3ac030] rounded-[10px]"
        v-for="outlet in outlets"
        :key="outlet.code"
        @click="
          push('outlet_details', {
            outlet,
            merchant,
          })
        "
      >
        <div class="text-lg font-semibold" v-html="outlet.name"></div>
        <Icon name="arrow-left" class="rotate-180" />
      </div>
    </div>
  </div>
</template>
