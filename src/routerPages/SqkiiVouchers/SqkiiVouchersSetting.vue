<script lang="ts" setup>
import { useAsync, useClick } from '@composables';
import { successNotify } from '@helpers';
import { useUserStore, useVouchersStore } from '@stores';

const storeVouchers = useVouchersStore();
const storeUser = useUserStore();

const { user } = storeToRefs(storeVouchers);
const { seasonCode } = storeToRefs(storeUser);
const { t } = useI18n();
const { push, openDialog } = useMicroRoute();

const expand = ref(false);

const COUNTRY = [
  {
    code: 'SG',
    name: 'Singapore',
  },
  {
    code: 'MY',
    name: 'Malaysia',
  },
];

useClick('SQKII_VOUCHERS_FAQ', () => {
  push('sqkii_vouchers_faq');
});

const { loading, execute } = useAsync({
  fn: async () => {
    await storeVouchers.unSync();
    return { data: true };
  },
  onSuccess: ({ data }) => {
    if (data) {
      push('home');
      storeVouchers.user = null;
      storeVouchers.token = null;
      LocalStorage.remove(`${process.env.APP_NAME}_SQKII_VOUCHER_COUNTRY`);
      LocalStorage.remove(`${process.env.APP_NAME}_SQKII_VOUCHER_TOKEN`);
      successNotify({
        message: t('SQKII_VOUCHERS_UNSYNC_SUCCESS'),
      });
    }
  },
});
</script>
<template>
  <div
    class="fullscreen sqkii-vouchers-setting"
    :class="`setting-${seasonCode.toLowerCase()}`"
  >
    <div
      class="fixed bg-black opacity-50 w-full h-full pointer-events-none"
    ></div>
    <div
      class="fixed top-0 left-0 z-10 flex justify-center items-center h-20 w-full"
    >
      <Button
        class="absolute left-3"
        shape="square"
        variant="secondary"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold"
        v-html="t('SQKII_VOUCHERS_SETTING_HEADING')"
      ></div>
    </div>
    <div
      class="relative w-full h-[calc(100%-150px)] overflow-y-auto mt-[150px] px-5 pb-5"
    >
      <div class="text-center flex flex-col gap-2 mb-5" v-if="user?.email">
        <div
          class="text-xs opacity-70"
          v-html="t('SQKII_VOUCHERS_SETTING_DESC')"
        ></div>
        <div class="text-base font-bold">{{ user?.email }}</div>
      </div>
      <div class="flex flex-col gap-5 justify-center items-center mb-10">
        <Button
          class="!w-[250px]"
          :disable="!!user?.email"
          @click="openDialog('link_kee')"
        >
          <div class="flex items-center gap-2 flex-nowrap">
            <div v-html="t('SQKII_VOUCHERS_SETTING_BTN_SYNC')"></div>
            <Icon v-if="!!user?.email" name="ticked" :size="20" />
          </div>
        </Button>
        <Button
          variant="purple"
          class="!w-[250px]"
          :label="t('SQKII_VOUCHERS_SETTING_BTN_CHANGE_PIN')"
          :disable="!user?.email"
          @click="openDialog('change_pin')"
        />
        <Button
          variant="purple"
          class="!w-[250px]"
          :label="t('SQKII_VOUCHERS_SETTING_BTN_CHANGE_EMAIL')"
          :disable="!user?.email"
          @click="openDialog('change_mail')"
        />
        <Button
          variant="purple"
          class="!w-[250px]"
          :label="t('SQKII_VOUCHERS_UNSYNC')"
          v-if="!!user?.email"
          :loading="loading"
          @click="execute"
        />
      </div>
      <!-- <template v-if="user?.email">
        <div class="max-w-[250px] mx-auto">
          <div
            class="text-sm mb-2 opacity-70"
            v-html="t('SQKII_VOUCHERS_SETTING_DESC_1')"
          ></div>
          <Expansion
            v-model="expand"
            group="language"
            class="language !w-full mb-10"
          >
            <template v-slot:header>
              <div class="column flex-nowrap flex-1">
                <div
                  class="text-[8px] transition-all"
                  :class="{
                    'mb-2': expand,
                  }"
                  v-html="t('SETTING_LANGUAGE')"
                ></div>
                <div
                  class="text-sm"
                  :class="{
                    'text-[#00e0ff] font-bold': expand,
                  }"
                >
                  {{ COUNTRY.find((l) => l.code === user?.country)?.name }}
                </div>
              </div>
            </template>
            <q-card style="background: transparent">
              <div
                class="text-sm mx-4 mb-3 pt-2"
                style="border-top: 0.5px solid #00e0ff"
                v-for="c in COUNTRY.filter((c) => c.code !== user?.country)"
                :key="c.code"
                v-html="c.name"
                @click="
                  openDialog('switch_country', {
                    code: c.code,
                  });
                  expand = false;
                "
              ></div>
            </q-card>
          </Expansion>
        </div>
      </template> -->
      <!-- <div
        class="text-center px-10"
        v-html="t('SQKII_VOUCHERS_SETTING_FAQ')"
      ></div> -->
    </div>
  </div>
</template>
<style lang="scss" scoped>
.sqkii-vouchers-setting {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &.setting-vn {
    background: url('/imgs/bg-menu-vn.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  &.setting-sg {
    background: url('/imgs/bg-menu-sg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .language {
    width: 70%;
    background: #04081d;
    border-radius: 10px;
    box-shadow: 2px 2px 10px rgba(#04081d, 0.1);
    border: 1px solid #00e0ff;
  }
}
</style>
