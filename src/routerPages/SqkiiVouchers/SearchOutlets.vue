<script lang="ts" setup>
import { useAsync } from '@composables';
import { VOUCHERS } from '@repositories';
import type { IRcentOutletsData, IRecentOutlet } from '@types';

const { t } = useI18n();
const { push, openDialog } = useMicroRoute();

const search = ref('');
const recentOutlets = ref<IRcentOutletsData>({
  recent: [],
  outlets: [],
});
const selectedOutlet = ref<IRecentOutlet>();

const filteredOutlets = computed(() => {
  return recentOutlets.value.outlets.filter((outlet) =>
    outlet.name.toLowerCase().includes(search.value.toLowerCase())
  );
});

async function getRecentOutlets() {
  try {
    const { data } = await VOUCHERS.getRecentOutlets();
    recentOutlets.value = data;
  } catch (error) {
    console.error(error);
  }
}

const { loading, execute: handleNext } = useAsync({
  fn: async () => {
    if (!selectedOutlet.value) return;
    const { data } = await VOUCHERS.paymentScanQR(selectedOutlet.value.code);
    return data;
  },
  onSuccess: (data) => {
    push(-1);
    openDialog('pay_to_merchant', {
      outlet: data,
    });
  },
});

onMounted(async () => {
  await nextTick();
  getRecentOutlets();
});
</script>
<template>
  <div class="search-outlets flex flex-col flex-nowrap fullscreen">
    <div
      class="flex justify-center items-center h-16 px-20 relative shrink-0 mb-5"
    >
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold text-center w-full"
        v-html="t('SEARCH_OUTLET_HEADER')"
      ></div>
    </div>
    <div class="flex flex-col justify-between items-center px-10 pb-5 h-full">
      <div class="w-full">
        <Input
          class="full-width"
          type="search"
          :label="t('FAQ_SEARCH')"
          v-model="search"
          @update:model-value="selectedOutlet = undefined"
        />
        <div
          class="rounded-b-lg flex flex-col px-5 flex-nowrap gap-3 max-h-[300px] overflow-y-auto"
          style="background: rgba(55, 80, 121, 0.3)"
          v-if="!selectedOutlet"
        >
          <template v-if="filteredOutlets.length">
            <div
              v-for="outlet in filteredOutlets"
              :key="outlet.code"
              class="flex flex-col py-2 [&:not(:last-child)]:border-b [&:not(:last-child)]:border-[#ffffff10]"
              @click="
                selectedOutlet = outlet;
                search = outlet.name;
              "
            >
              <div class="text-base font-bold" v-html="outlet.name"></div>
              <div
                class="text-sm"
                v-html="
                  t('SEARCH_OUTLET_QR_ID', {
                    CODE: outlet.code,
                  })
                "
              ></div>
            </div>
          </template>
          <template v-else>
            <div
              class="p-4 text-base font-bold"
              v-html="t('SEARCH_OUTLET_NO_RESULT')"
            ></div>
          </template>
        </div>
      </div>
      <Button
        :label="t('SEARCH_OUTLET_BTN_NEXT')"
        class="!w-[210px]"
        :disable="!selectedOutlet"
        :loading="loading"
        @click="handleNext"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.search-outlets {
  background: linear-gradient(
      180deg,
      #1f7c90 -13.42%,
      rgba(145, 36, 254, 0) 50%
    ),
    #090422;
}
</style>
