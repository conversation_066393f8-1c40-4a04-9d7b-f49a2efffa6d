<script lang="ts" setup>
import type { IOutlet } from '@types';

interface Props {
  merchant: string;
  outlet: IOutlet;
}

defineProps<Props>();

const { t } = useI18n();
const { push } = useMicroRoute();
</script>
<template>
  <div
    class="fullscreen bg-[#090422] flex flex-nowrap flex-col overflow-hidden"
  >
    <div
      class="relative flex justify-center items-center w-full h-16 px-20 mb-5 shrink-0"
    >
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold text-center w-full"
        v-html="merchant"
      ></div>
    </div>

    <div class="overflow-y-auto px-6 pb-5">
      <div class="text-2xl font-bold mb-6" v-html="outlet.name"></div>
      <div class="text-xl font-bold" v-html="t('OUTLET_DETAILS_ADDRESS')"></div>
      <div class="text-sm mb-6" v-html="outlet.address"></div>
      <div
        class="text-xl font-bold mb-3"
        v-html="t('OUTLET_DETAILS_OPENING')"
      ></div>
      <div class="mb-10 flex flex-col gap-2">
        <div
          class="flex flex-col"
          v-for="key in Object.keys(outlet).filter((k) =>
            [
              'monday',
              'tuesday',
              'wednesday',
              'thursday',
              'friday',
              'saturday',
              'sunday',
              'weekday',
              'weekend',
              'eve_holiday',
              'public_holiday',
            ].includes(k) && !!outlet[k as keyof IOutlet]
          )"
          :key="key"
        >
          <div
            v-if="!!outlet[key as keyof IOutlet]"
            class="text-xs text-[#BCBCBC] mb-1"
            v-html="t(key)"
          ></div>
          <div
            class="text-sm font-medium"
            v-html="outlet[key as keyof IOutlet]"
          ></div>
        </div>
      </div>
      <div
        class="text-center text-sm px-5"
        v-html="t('OUTLET_DETAILS_CONTACT')"
      ></div>
    </div>
  </div>
</template>
