<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { onClickOutside } from '@vueuse/core';
import { delay, useGlobalInstructor, useInventory } from '@composables';
import { InventoryItemCategory, InventoryItemType } from '@constants';
import { formatName, numeralFormat } from '@helpers';
import { useUserStore } from '@stores';
import { InventoryItem } from '@types';

const SORT_OPTIONS = {
  EXPIRING_FIRST: 'expiring_first',
  EXPIRING_LAST: 'expiring_last',
  MOST_TO_LEAST: 'most_to_least',
} as const;

const INVENTORY_TABS = [
  { name: 'All', icon: 'ic_all' },
  { name: 'expiring', icon: 'ic_clock' },
  { name: InventoryItemCategory.POWER_UP, icon: 'ic_energy' },
  // { name: InventoryItemCategory.VOUCHER, icon: 'ic_voucher' },
  // { name: InventoryItemCategory.FOOD, icon: 'ic_food' },
] as const;

const TAB_WIDTH = 100 / INVENTORY_TABS.length + '%';

const CRYSTAL_DISPLAY_LIMIT = 99999;
const GRID_COLUMNS = 3;

const storeUser = useUserStore();

const { crystals } = storeToRefs(storeUser);

const { t } = useI18n();
const { push } = useMicroRoute();
const {
  inventoryItems,
  TOTAL_ITEMS,
  MAX_INVENTORY_SIZE,
  itemDisplayNames,
  itemAssetNames,
  getItemExpirationText,
  isFirstInventory,
  getTime,
  triggerInventoryOnboarding,
  handleViewPowerUpDetail,
} = useInventory();
const { closeUnifyInstructor } = useGlobalInstructor();

const activeTab = ref<string>(INVENTORY_TABS[0].name);
const filterRef = ref<HTMLElement | null>(null);
const isFilterExpanded = ref(false);
const selectedSortOption = ref<string>(SORT_OPTIONS.EXPIRING_FIRST);

onClickOutside(filterRef, () => {
  isFilterExpanded.value = false;
});

const sortItems = (
  items: InventoryItem[],
  sortOption: string
): InventoryItem[] => {
  switch (sortOption) {
    case SORT_OPTIONS.EXPIRING_FIRST:
      return items.toSorted(
        (a, b) => getTime(a.expires_at) - getTime(b.expires_at)
      );
    case SORT_OPTIONS.EXPIRING_LAST:
      return items.toSorted(
        (a, b) => getTime(b.expires_at) - getTime(a.expires_at)
      );
    case SORT_OPTIONS.MOST_TO_LEAST:
      return items.toSorted((a, b) => b.quantity - a.quantity);
    default:
      return items;
  }
};

const filteredByTab = computed(() => {
  if (['All', 'expiring'].includes(activeTab.value))
    return inventoryItems.value;

  return inventoryItems.value.filter(
    (item) => item.item_category === activeTab.value
  );
});

const sortedAndFilteredItems = computed(() => {
  return sortItems(filteredByTab.value, selectedSortOption.value);
});

const inventoryStatus = computed(() => ({
  total: TOTAL_ITEMS.value,
  max: MAX_INVENTORY_SIZE.value,
  text: `${TOTAL_ITEMS.value}/${MAX_INVENTORY_SIZE.value}`,
  isMax: TOTAL_ITEMS.value >= MAX_INVENTORY_SIZE.value,
}));

const displayCrystals = computed(() => {
  return crystals.value > CRYSTAL_DISPLAY_LIMIT
    ? `${CRYSTAL_DISPLAY_LIMIT.toLocaleString()}<sup>+</sup>`
    : numeralFormat(crystals.value);
});

const handleSortChange = (sortOption: string) => {
  selectedSortOption.value = sortOption;
  isFilterExpanded.value = false;
};

const getItemGridClass = (index: number): string => {
  const isAlternatingRow = Math.floor(index / GRID_COLUMNS) % 2 === 0;
  return isAlternatingRow ? 'bg-purple-800/50' : '';
};

onMounted(async () => {
  await nextTick();
  if (isFirstInventory.value && inventoryItems.value.length > 0) {
    await delay(300);
    triggerInventoryOnboarding();
  }
});

onBeforeUnmount(() => {
  if (isFirstInventory.value && inventoryItems.value.length > 0) {
    closeUnifyInstructor();
    storeUser.updateOnboarding('first_inventory');
  }
});
</script>
<template>
  <div class="fullscreen column items-center overflow-hidden flex-nowrap">
    <div class="top-space"></div>
    <div
      class="inventory-container flex-1 w-full px-2.5 column items-center overflow-hidden"
    >
      <div class="relative flex text-center w-full z-[4] mt-[30px] flex-center">
        <Button
          variant="secondary"
          shape="square"
          class="absolute left-4"
          @click="push(-1)"
        >
          <Icon name="arrow-left"></Icon>
        </Button>
        <div
          class="text-lg font-extrabold text-center w-full px-[90px]"
          v-html="t('INVENTORY_HEADER')"
        ></div>
        <div
          style="
            background: linear-gradient(
              180deg,
              rgba(147, 75, 218, 0.8) 0%,
              #511d85 100%
            );
          "
          class="px-2 py-2 rounded-md flex items-center justify-center gap-1 absolute right-4"
        >
          <Icon name="dbs_crystal" :size="16" />
          <div class="text-xs font-bold" v-html="displayCrystals"></div>
        </div>
      </div>
      <q-tabs v-model="activeTab" dense class="tab-inventory mt-7 w-full">
        <q-tab
          class="flex item-center flex-nowrap"
          v-for="tabItem in INVENTORY_TABS"
          :key="tabItem.name"
          :name="tabItem.name"
        >
          <Icon
            :name="tabItem.icon"
            :size="24"
            v-if="tabItem.name === activeTab"
          />
          <Icon :name="tabItem.icon" :size="24" v-else class="opacity-[0.4]" />
          <span v-if="tabItem.name === activeTab">
            {{ formatName(tabItem.name) }}
          </span>
        </q-tab>
      </q-tabs>
      <div class="w-full flex-1 bg-[#451A75] py-[25px] overflow-auto">
        <div class="flex items-start justify-between w-full px-4">
          <div
            class="text-white text-lg font-bold mt-2"
            v-html="inventoryStatus.text"
          ></div>
          <div class="w-[146px] h-[44px] relative" ref="filterRef">
            <q-expansion-item
              dense
              dense-toggle
              expand-separator
              :label="
                isFilterExpanded
                  ? t('INVENTORY_SORT_BY')
                  : formatName(selectedSortOption)
              "
              v-model="isFilterExpanded"
              class="bg-[#091A3C] rounded-[4px] w-[146px] absolute top-0 left-0 z-[50]"
            >
              <q-card-section class="px-2 py-0">
                <div
                  class="text-base mb-2 cursor-pointer"
                  :class="{
                    'text-[#00E0FF] font-bold': selectedSortOption === item,
                  }"
                  v-for="item in Object.values(SORT_OPTIONS)"
                  @click="handleSortChange(item)"
                  :key="item"
                >
                  {{ formatName(item) }}
                </div>
              </q-card-section>
            </q-expansion-item>
          </div>
        </div>
        <div class="grid grid-cols-3 pt-6">
          <div
            v-for="(item, index) in sortedAndFilteredItems"
            :key="item._id"
            :class="getItemGridClass(index)"
            class="relative flex flex-col justify-center items-center mb-5 p-1 gap-1"
            @click="handleViewPowerUpDetail(item)"
          >
            <div class="relative size-[85px]">
              <Icon
                :name="itemAssetNames[item.item_type]"
                class="size-[85px]"
              />
              <div
                class="px-[5px] py-[2px] absolute -left-3 bottom-0 font-semibold"
                style="
                  border-radius: 10px;
                  background: #6c39f0;
                  box-shadow: 1px 2px 4px 0px rgba(0, 0, 0, 0.2);
                "
              >
                x{{ item.quantity }}
              </div>
            </div>

            <div
              class="font-bold text-center grow"
              v-html="
                item.item_type === InventoryItemType.COIN_SONAR
                  ? `${itemDisplayNames[item.item_type]}
                    <br/> (${item.radius}m)`
                  : itemDisplayNames[item.item_type]
              "
            ></div>
            <div
              class="uppercase text-xs font-bold text-[#FF7878]"
              v-html="getItemExpirationText(item)"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
.top-space {
  width: 100%;
  height: 18px;
  background: #291557;
}
.inventory-container {
  .grid > div:nth-child(6n + 1),
  .grid > div:nth-child(6n + 2),
  .grid > div:nth-child(6n + 3) {
    background: rgba(59, 17, 105, 0.5);
  }
  .q-expansion-item {
    .q-expansion-item__container {
      .q-item {
        width: 100% !important;
        padding: 0 !important;
        padding-right: 4px !important;
        padding-left: 7px !important;
        height: 44px !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
      }

      .q-item__section {
        position: relative;
        right: 0px;
        .q-icon {
          font-size: 20px;
        }
      }
    }
  }

  .tab-inventory {
    height: 55px;
    .q-tab__content {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: nowrap;
      flex-direction: row !important;
      gap: 10px;
    }
    .q-tab {
      border-radius: 15px 15px 0px 0px;
      border-bottom: none;
      border-radius: 15px 15px 0px 0px;
      background: url('/imgs/inventory-tab.png') no-repeat center;
      background-size: 100% 100%;
      text-transform: none !important;
      padding: 0px !important;
      height: 44px;
      width: v-bind(TAB_WIDTH);
      margin-top: 11px;
      &--active {
        border-bottom: none;
        background: url('/imgs/inventory-tab-active.png') no-repeat center !important;
        background-size: 100% 100% !important;
        color: white;
        height: 55px;
        width: v-bind(TAB_WIDTH);
        border-bottom: none;
        margin-top: 0 !important;
      }

      &__indicator {
        display: none;
      }
    }
  }
  background: linear-gradient(0deg, #165a73, #19053c);
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0;
    width: 100%;
    height: 39px;
    background-image: url('/imgs/zip.png');
    background-repeat: repeat-x;
    background-size: cover;
    background-position: center;
    z-index: 2;
  }
}
</style>
