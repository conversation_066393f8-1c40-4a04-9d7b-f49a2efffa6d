<script lang="ts" setup>
import { TrialFrame } from '@components';
import {
  delay,
  useGlobalInstructor,
  useInventory,
  useMapHelpers,
} from '@composables';
import {
  ONGOING_COIN_FREE_FILL_LAYER,
  ONGOING_COIN_FREE_LINE_GLOW_LAYER,
  ONGOING_COIN_FREE_LINE_LAYER,
} from '@constants';
import { useMapStore, useUserStore } from '@stores';
import { successNotify } from '@helpers';
import { point } from '@turf/helpers';
import {
  FillLayer,
  GeoJsonSource,
  LineLayer,
  LngLatBoundsLike,
  MapCreationStatus,
  SymbolLayer,
} from 'vue3-maplibre-gl';
import distance from '@turf/distance';
import bbox from '@turf/bbox';
import circle from '@turf/circle';
import destination from '@turf/destination';
import gsap, { Bounce } from 'gsap';

type AccuracyLevel = 'low' | 'medium' | 'high';
type Step = (typeof TUTORIAL_STEPS)[keyof typeof TUTORIAL_STEPS];

const TUTORIAL_STEPS = {
  NONE: -1,
  CONTROL: 1,
  ACCURACY: 2,
  TIMER: 3,
} as const;

const OFFSET = 0.0005; // ~0.0005 degrees is ~50m
const BASE_RADIUS = 150;
const COIN_DISTANCE_METERS = 10;
const COIN_BEARING_DEGREES = 80;
const MAP_PADDING = 25;
const MAP_ZOOM_FACTOR = 1.2;

const DISTANCE_THRESHOLDS = {
  CLOSE: 5, // meters
  MEDIUM: 15, // meters
};

const PROGRESS_RANGES = {
  CLOSE: { min: 0.85, max: 1 },
  MEDIUM: { min: 0.3, max: 0.85 },
  FAR: { min: 0.1, max: 0.3 },
};

const GRADIENT_STYLES = {
  FULL: 'linear-gradient(to right, rgb(238, 77, 77), rgb(243, 8, 8), rgb(239, 173, 50), rgb(242, 242, 59), rgb(17, 174, 17), rgb(22, 219, 22)',
  MEDIUM:
    'linear-gradient(to right, rgb(238, 77, 77), rgb(243, 8, 8), rgb(239, 173, 50), rgb(242, 242, 59))',
  LOW: 'linear-gradient(to right, rgb(238, 77, 77), rgb(243, 8, 8))',
};

const storeMap = useMapStore();
const storeUser = useUserStore();

const { lastLocations } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();
const { isFirstMetalDetectorTrial } = useInventory();
const { openUnifyInstructor } = useGlobalInstructor();
const { t } = useI18n();

const trialFrameRef = ref<InstanceType<typeof TrialFrame>>();
const accuracy = ref<AccuracyLevel>('high');
const vibrateProgress = ref(0);
const step = ref<Step>(TUTORIAL_STEPS.NONE);
const startTrial = ref(false);

const shadowLocation = lastLocations.value;

const mapInstance = computed(() => trialFrameRef.value?.mapInstance);
const mapStatus = computed(() => trialFrameRef.value?.mapStatus);

const circleProperties = computed(() => {
  const [lng, lat] = lastLocations.value;
  return [
    {
      lng: lng - OFFSET * 1.5,
      lat: lat - OFFSET,
      radius: BASE_RADIUS * 2,
    },
  ];
});

const coinLocation = computed(() => {
  const [lng, lat] = shadowLocation;
  const dest = destination(
    [lng, lat],
    COIN_DISTANCE_METERS,
    COIN_BEARING_DEGREES,
    { units: 'meters' }
  );
  return dest.geometry.coordinates;
});

const distanceInMeters = computed(() => {
  const [lng, lat] = lastLocations.value;
  const [coinLng, coinLat] = coinLocation.value;
  return distance([lng, lat], [coinLng, coinLat], { units: 'meters' });
});

const circleSource = computed(() => {
  const circles = circleProperties.value.map((c) =>
    circle([c.lng, c.lat], c.radius, {
      units: 'meters',
      properties: c,
    })
  );
  return makeSource(circles);
});

const coinSource = computed(() => {
  const [lng, lat] = coinLocation.value;
  return makeSource([point([lng, lat], { icon: 'shiny-coin' })]);
});

const vibrateColor = computed(() => {
  if (!startTrial.value || !distanceInMeters.value) return '';

  if (distanceInMeters.value <= DISTANCE_THRESHOLDS.CLOSE) {
    return GRADIENT_STYLES.FULL;
  }

  if (distanceInMeters.value <= DISTANCE_THRESHOLDS.MEDIUM) {
    return GRADIENT_STYLES.MEDIUM;
  }

  return GRADIENT_STYLES.LOW;
});

const fitBounds = (radius: number): void => {
  const [lng, lat] = lastLocations.value;
  const c = circle([lng, lat], radius * MAP_ZOOM_FACTOR, {
    units: 'meters',
  });
  const box = bbox(c);
  mapInstance.value?.fitBounds(box as LngLatBoundsLike, {
    padding: MAP_PADDING,
  });
};

function getRandomInRange(min: number, max: number): number {
  return Number(Math.random() * (max - min) + min);
}

function updateVibrateProgress(): void {
  if (!startTrial.value || !distanceInMeters.value) {
    vibrateProgress.value = 0;
    return;
  }

  if (distanceInMeters.value <= DISTANCE_THRESHOLDS.CLOSE) {
    vibrateProgress.value = getRandomInRange(
      PROGRESS_RANGES.CLOSE.min,
      PROGRESS_RANGES.CLOSE.max
    );
  } else if (distanceInMeters.value <= DISTANCE_THRESHOLDS.MEDIUM) {
    vibrateProgress.value = getRandomInRange(
      PROGRESS_RANGES.MEDIUM.min,
      PROGRESS_RANGES.MEDIUM.max
    );
  } else {
    vibrateProgress.value = getRandomInRange(
      PROGRESS_RANGES.FAR.min,
      PROGRESS_RANGES.FAR.max
    );
  }
}

const startTutorial = async (): Promise<void> => {
  await delay(500);
  step.value = TUTORIAL_STEPS.CONTROL;

  openUnifyInstructor('timii', {
    sequences: [
      {
        target: '.trial-frame-container',
        hideClose: true,
        backdropCss: true,
        css: { bottom: '18%' },
        message: t('METAL_DETECTOR_TRIAL_TUTORIAL_1'),
        actions: {
          cb: (next) => {
            next();
            step.value = TUTORIAL_STEPS.ACCURACY;
          },
        },
      },
      {
        target: '.trial-frame-container',
        hideClose: true,
        backdropCss: true,
        css: { bottom: '15%' },
        message: t('METAL_DETECTOR_TRIAL_TUTORIAL_2'),
        actions: {
          cb: (next) => {
            next();
            step.value = TUTORIAL_STEPS.TIMER;
          },
        },
      },
      {
        target: '.trial-frame-container',
        hideClose: true,
        backdropCss: true,
        css: { top: '20%' },
        message: t('METAL_DETECTOR_TRIAL_TUTORIAL_3'),
        actions: {
          cb: (_, close) => {
            close();
            successNotify({
              message: t('METAL_DETECTOR_TRIAL_TUTORIAL_4'),
            });
            step.value = TUTORIAL_STEPS.NONE;
            startTrial.value = true;
            storeUser.updateOnboarding('first_trial_metal_detector');
            textAnim();
          },
        },
      },
    ],
  });
};

const textAnim = (): void => {
  gsap
    .timeline()
    .to('.mapbanner', {
      opacity: 1,
      duration: 2,
      ease: Bounce.easeInOut,
    })
    .fromTo(
      '.mapbanner-text',
      {
        text: '',
      },
      {
        duration: 1,
        text: 'Metal Detector is starting...',
      },
      '-=1'
    )
    .to('.mapbanner', {
      delay: 2,
      opacity: 0,
      duration: 2,
      ease: Bounce.easeInOut,
    });
};

watch(distanceInMeters, updateVibrateProgress);

watch(mapStatus, async (val) => {
  if (val === MapCreationStatus.Loaded) {
    await delay(1000);
    fitBounds(BASE_RADIUS);
    if (isFirstMetalDetectorTrial.value) startTutorial();
  }
});

onMounted(() => {
  if (!isFirstMetalDetectorTrial.value) startTrial.value = true;
});
</script>

<template>
  <TrialFrame ref="trialFrameRef" class="metal-detector-trial">
    <!-- Timer display (shown during timer tutorial step) -->
    <div
      v-if="step === TUTORIAL_STEPS.TIMER"
      class="timer absolute z-[999999] left-1/2 -translate-x-1/2 top-3 w-[68px] h-[80px]"
    >
      <Icon class="w-full relative z-10" name="mdbattery" />
      <div
        class="w-full h-full absolute top-[6px] left-0 flex justify-center items-center z-20"
      >
        <q-circular-progress
          :value="1"
          :thickness="1"
          size="62px"
          color="dark"
          center-color="transparent"
          track-color="transparent"
        />

        <div
          class="text-[18px] text-center leading-none absolute -translate-x-1/2 -translate-y-full top-[58px] left-1/2 z-20"
        >
          <b>60s</b><br />
          left
        </div>
      </div>
    </div>

    <!-- Map banner -->
    <div
      class="mapbanner text-center absolute z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0"
    >
      <Icon name="mapbanner" :size="200"></Icon>
      <div
        class="absolute text-center top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-[14px] w-full mapbanner-text"
      ></div>
    </div>

    <!-- Accuracy indicator -->
    <div
      class="signal-accuracy flex justify-center items-center"
      :class="{
        '!z-[999999]':
          step === TUTORIAL_STEPS.ACCURACY || step === TUTORIAL_STEPS.CONTROL,
      }"
    >
      <span class="mt-1.5">
        Accuracy:
        <span
          class="font-extrabold capitalize"
          :class="{
            'text-[#FF4B4B]': accuracy === 'low',
            'text-[#FFD325]': accuracy === 'medium',
            'text-[#41CF5B]': accuracy === 'high',
          }"
        >
          {{ accuracy }}
        </span>
      </span>
    </div>

    <!-- Metal detector progress bar -->
    <div
      class="metal-detector-control absolute left-1/2 -translate-x-1/2 bottom-10 w-4/5 h-5 z-10"
      :class="{
        'z-[999999]': step === TUTORIAL_STEPS.CONTROL,
      }"
    >
      <q-linear-progress
        rounded
        class="metal-detector-progress w-full h-full"
        :value="vibrateProgress"
      />
      <Icon
        class="absolute top-1/2 -translate-y-1/2 -left-4"
        name="metal-detector"
        :size="42"
      />
      <Icon
        class="absolute top-1/2 -translate-y-1/2 right-1"
        name="coin_detector"
        :size="19"
      />
    </div>

    <!-- Map layers -->
    <GeoJsonSource id="trial_circle" :data="circleSource">
      <FillLayer
        id="trial_fill_layer"
        :style="{ ...ONGOING_COIN_FREE_FILL_LAYER }"
      />
      <LineLayer
        id="trial_line_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_LAYER,
          'line-width': ['get', 'width'],
        }"
      />
      <LineLayer
        id="trial_line_glow_layer"
        :style="{ ...ONGOING_COIN_FREE_LINE_GLOW_LAYER }"
      />
    </GeoJsonSource>

    <!-- Coin marker -->
    <GeoJsonSource id="coin_location" :data="coinSource">
      <SymbolLayer
        id="coin_location_symbol_layer"
        :style="{
          'icon-image': ['get', 'icon'],
          'icon-size': 1,
          'icon-allow-overlap': true,
          'icon-anchor': 'bottom',
          'icon-opacity': [
            'interpolate',
            ['linear'],
            ['zoom'],
            15,
            0,
            15.5,
            0.5,
            16,
            1,
          ],
        }"
      />
    </GeoJsonSource>
  </TrialFrame>
</template>
<style lang="scss">
.metal-detector-trial {
  .green,
  .red,
  .yellow {
    width: calc(100svw - 20px);
    height: calc(100svh - 20px);
    position: relative;
  }

  .green {
    animation: glowGreen 2s infinite;
  }

  .red {
    animation: glowRed 2s infinite;
  }

  .yellow {
    animation: glowYellow 2s infinite;
  }

  @keyframes glowGreen {
    0%,
    100% {
      box-shadow: 0 0 30px rgb(49, 248, 49);
    }
    50% {
      box-shadow: 0 0 30px rgb(164, 172, 164);
    }
  }

  @keyframes glowRed {
    0%,
    100% {
      box-shadow: 0 0 30px rgba(247, 55, 55);
    }
    50% {
      box-shadow: 0 0 30px rgb(164, 172, 164);
    }
  }

  @keyframes glowYellow {
    0%,
    100% {
      box-shadow: 0 0 30px rgb(244, 244, 82);
    }
    50% {
      box-shadow: 0 0 30px rgb(164, 172, 164);
    }
  }

  .metal-detector-progress {
    border-radius: 4px;
    border: 1.5px solid #742ebb;

    .q-linear-progress__track {
      background-color: #08142d;
      opacity: unset;
    }

    .q-linear-progress__model--determinate {
      border-radius: 0 4px 4px 0;
      background: v-bind(vibrateColor);
    }
  }

  .signal-accuracy {
    position: absolute;
    bottom: 60px;
    z-index: 10;
    left: 50%;
    transform: translateX(-50%);
    background-image: url(/imgs/signal-accuracy.png);
    width: 165px;
    height: 30px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
</style>
