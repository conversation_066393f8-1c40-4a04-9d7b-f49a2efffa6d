<script lang="ts" setup>
import {
  RiveViewModel,
  useBrandActions,
  useHuntingStop,
  useRive,
  useTick,
} from '@composables';
import { HuntingStop } from '@types';
import { Fit, Alignment, Layout } from '@rive-app/canvas';
import { useBAStore } from '@stores';
import { BrandActionItem } from '@components';
import {
  HUNTINGSTOPREWARDINFO,
  HuntingStopRewardType,
  HuntingStopType,
} from '@constants';
import { timeCountDown } from '@helpers';
import gsap from 'gsap';

interface Props {
  data: HuntingStop;
}

interface RewardDetailModel {
  name: string;
  icon: HuntingStopRewardType;
  qty: number;
  des: string;
  btnTxt: string;
  action: () => void;
}

const props = defineProps<Props>();

const tl = gsap.timeline();
const storeBA = useBAStore();
const brandHooks = useBrandActions();

const { newBrandActions } = storeToRefs(storeBA);
const { push } = useMicroRoute();
const { t } = useI18n();
const { now } = useTick();
const {
  alertMessage,
  errorMessage,
  hasErrorMsg,
  rewardAssetNames,
  claimReward,
  getHuntingStopInfo,
  setHuntingStopInfo,
} = useHuntingStop();

const huntingStop = ref(props.data);
const stallRef = ref<HTMLCanvasElement | null>(null);
const dialogMessage = ref('');
const dialogDetail = ref(false);
const rewardDetailModel = ref<RewardDetailModel>({
  name: '',
  icon: HuntingStopRewardType.CRYSTAL,
  qty: 0,
  des: '',
  btnTxt: '',
  action: () => void 0,
});

// item?.ba_unique_ids?.includes(huntingStop.value.brand_unique_id)

const brandActionByUniqueId = computed(() => {
  return newBrandActions.value.filter(
    (item) => item.brand_unique_id === huntingStop.value.brand_unique_id || huntingStop.value.ba_unique_ids?.includes(item.unique_id)
  )
});

const randomBrandAction = computed(() => {
  if (!brandActionByUniqueId.value.length) return undefined;
  const randomIndex = Math.floor(
    Math.random() * brandActionByUniqueId.value.length
  );
  return brandActionByUniqueId.value[randomIndex];
});

const countdownReward = computed(() => {
  if (!huntingStop.value.lock_until) return 0;
  return Math.max(0, +new Date(huntingStop.value.lock_until) - now.value);
});

const canUse = computed(() => {
  return countdownReward.value <= 0 && !huntingStop.value.rewards;
});

const isMegaStop = computed(
  () => huntingStop.value.type === HuntingStopType.MEGA
);

const createRiveVMConfig = (canUse: boolean): RiveViewModel[] => {
  const characterType = isMegaStop.value ? 'nancii' : 'sqkii';

  const baseConfig: RiveViewModel[] = [
    {
      path: 'selectCharacter',
      type: 'enum',
      value: characterType,
    },
  ];

  const megaStopConfig: RiveViewModel[] = isMegaStop.value
    ? [
      {
        path: 'property of vmCharacter/clothDesign',
        type: 'enum',
        value: 'branded',
      },
    ]
    : [];

  if (canUse) {
    return [
      ...baseConfig,
      {
        path: 'isDoorOpen',
        type: 'boolean',
        value: true,
      },
      {
        path: 'property of vmCharacter/tickleCounter',
        type: 'number',
        value: 15,
      },
      {
        path: 'property of vmCharacter/beingTickled',
        type: 'boolean',
        value: true,
      },
      ...megaStopConfig,
    ];
  }

  return [
    ...baseConfig,
    {
      path: 'isDoorOpen',
      type: 'boolean',
      value: async () => {
        const reward = await claimReward(props.data.unique_id);
        if (!reward) return;

        huntingStop.value = {
          ...huntingStop.value,
          rewards: reward.rewards,
          lock_until: reward.lock_until,
        };
      },
    },
    {
      path: 'property of vmCharacter/tap',
      type: 'trigger',
      value: () => setDialogMessage(props.data.fun_facts),
    },
    ...megaStopConfig,
  ];
};

const vmConfig = computed(() => createRiveVMConfig(!canUse.value));

const setDialogMessage = (message: string) => {
  if (!message) return;
  dialogMessage.value = t(message);
};

const getWelcomeText = (): string => {
  const text = isMegaStop.value ? 'MEGASTOP_WELCOME' : 'HUNTINGSTOP_WELCOME';
  if (!canUse.value) return props.data.fun_facts || text;
  return text;
};

const animateDialogMessage = async () => {
  if (!dialogMessage.value || hasErrorMsg.value) return;
  await tl.fromTo(
    '.speech',
    {
      opacity: 0,
      y: 5,
    },
    {
      opacity: 1,
      y: 0,
      delay: 0.8,
      duration: 1,
    }
  );
};

const onRiveLoaded = async () => {
  const text = getWelcomeText();
  setDialogMessage(t(text));
  if (canUse.value && !hasErrorMsg.value)
    gsap.to('.tap', {
      opacity: 1,
      duration: 1,
      delay: 5,
    })
};

const showRewardDetailDialog = (reward: HuntingStopRewardType, qty: number) => {



  rewardDetailModel.value = {
    ...HUNTINGSTOPREWARDINFO[reward],
    qty,
    btnTxt:
      reward === HuntingStopRewardType.CRYSTAL
        ? t('HUNTINGSTOP_REWARD_MORECRYSTAL')
        : t('HUNTINGSTOP_REWARD_INVENTORY'),
    action: () => {
      reward === HuntingStopRewardType.CRYSTAL
        ? push('offer_wall')
        : push('inventory');
      dialogDetail.value = false;
    },
  };
  dialogDetail.value = true;
};

useRive({
  canvas: stallRef,
  src: '/imgs/huntingstop/hunting_stop.riv',
  autoplay: true,
  autoBind: false,
  stateMachines: 'State Machine',
  layout: new Layout({
    fit: Fit.FitWidth,
    alignment: Alignment.BottomCenter,
  }),
  data: vmConfig.value,
  onLoad: onRiveLoaded,
});

watchEffect(() => animateDialogMessage(), { flush: 'post' });

watch(
  () => props.data,
  (newData) => {
    setHuntingStopInfo(newData);
  },
  { immediate: true }
);

watch(countdownReward, async (newVal) => {
  if (newVal <= 0) {
    const data = await getHuntingStopInfo(props.data.unique_id);
    if (!data) return;
    huntingStop.value = data;
  }
});
</script>

<template>
  <div
    class="fullscreen stall"
    :class="{ mega: isMegaStop }"
  >
    <Button
      variant="secondary"
      shape="square"
      class="absolute left-1 top-1 z-50"
      @click="push(-1)"
    >
      <Icon name="arrow-left" />
    </Button>

    <div
      class="location-name absolute left-1/2 -translate-x-1/2 z-10"
      :class="{ mega: isMegaStop }"
    >
      <span
        :class="{
          'mb-2': isMegaStop,
        }"
        class="line-clamp-2 px-4"
        v-html="t(huntingStop.name)"
      ></span>
    </div>

    <Icon
      v-if="isMegaStop"
      name="/huntingstop/chain"
      class="w-[260px] h-10 z-10 absolute top-0 left-1/2 -translate-x-1/2"
    />

    <img
      :src="isMegaStop ? '/imgs/huntingstop/dbs-scifi.png' : '/imgs/huntingstop/flag.png'"
      class=" z-[100] absolute  -translate-x-1/2"
      :class="[isMegaStop ? 'w-[18%] bottom-[81vw] left-[12vw]' : 'w-[10%] bottom-[79vw] left-[15vw]']"
    />
    <img
      :src="isMegaStop ? '/imgs/huntingstop/mega-diamond.png' : '/imgs/huntingstop/diamond.png'
        "
      class=" z-[100] absolute  -translate-x-1/2"
      :class="[isMegaStop ? 'w-[28%] bottom-[78vw] -right-[12vw]' : 'w-[25%] bottom-[76vw] right-[-5vw]']"
    />

    <div
      class="flex justify-center w-full flex-col flex-auto top-[90px] px-4 flex-nowrap items-stretch gap-5 absolute left-1/2 -translate-x-1/2 bottom-[120vw] z-50"
      :class="[(!canUse && !hasErrorMsg) || hasErrorMsg ? 'justify-between' : 'justify-center']"
    >
      <div
        v-if="!canUse && !hasErrorMsg"
        class="banner text-center p-4"
        v-html="t('HUNTINGSTOP_NEXT_REWARD', {
          TIME: timeCountDown(countdownReward),
        })
          "
      />
      <!-- Error Banner -->
      <div
        v-if="hasErrorMsg"
        class="banner text-center p-4 error !mt-5"
        v-html="t(alertMessage || errorMessage)"
      />

      <!-- Speech Bubble -->
      <div
        class="speech opacity-0"
        v-if="dialogMessage && !hasErrorMsg"
      >
        <span
          id="text"
          v-html="dialogMessage"
        > </span>
      </div>
    </div>

    <canvas
      ref="stallRef"
      class="w-full h-full absolute bottom-0 left-0 z-10"
      @click="gsap.killTweensOf('.tap'); gsap.set('.tap', { display: 'none' })"
      :class="{
        'opacity-0': hasErrorMsg,
        'pointer-events-none': hasErrorMsg || !canUse,
      }"
    />
    <img
      src="/imgs/huntingstop/tap.png"
      class="tap opacity-0 pointer-events-none left-1/2 fixed bottom-[50vw] -translate-x-1/2 -translate-y-1/2 w-24 h-auto z-20"
      alt=""
    >
    <div
      v-if="randomBrandAction && !hasErrorMsg"
      class="absolute z-50  left-1/2 -translate-x-1/2 scale-all-98"
      :class="[isMegaStop ? 'w-5/6 bottom-[13vw]' : 'w-4/5 bottom-[16vw]']"
    >
      <BrandActionItem
        class="w-full"
        :class="{ 'opacity-0 pointer-events-none': canUse }"
        :brand-hooks="brandHooks"
        :data="randomBrandAction"
        show-type="twinkle"
        from-sponsor
      />
    </div>
    <div
      v-if="huntingStop.rewards && !hasErrorMsg"
      class="flex justify-center items-center gap-3 absolute z-50 left-1/2 -translate-x-1/2 bottom-[8vw]"
    >
      <div
        v-for="(value, reward) in huntingStop.rewards"
        :key="reward"
        @click="showRewardDetailDialog(reward, value)"
        class="w-[16vw] h-14 relative flex items-center justify-center"
      >
        <Icon
          :name="rewardAssetNames[reward]"
          class="!w-full object-contain object-center"
          :class="[`hs-${reward}`]"
        />
        <div class="absolute bottom-0 text-[18px] font-black text-stroke right-1 text-border">
          {{ value }}
        </div>
      </div>
    </div>
  </div>
  <q-dialog
    v-model="dialogDetail"
    persistent
    full-height
    full-width
  >
    <Dialog @close="dialogDetail = false">
      <template #header>
        <div class="text-[14px] text-center font-normal">Reward obtained</div>
        <div class="text-base font-bold">
          {{ rewardDetailModel.name }} x{{ rewardDetailModel.qty }}
        </div>
      </template>
      <div class="flex flex-col items-center justify-center gap-10">
        <div class="flex flex-col gap-4 items-center justify-center -ml-2 flex-nowrap">
          <Icon
            :name="rewardAssetNames[rewardDetailModel.icon]"
            :size="120"
          />
          <div class="text-center text-base font-bold" v-html="t(rewardDetailModel.des)">
          </div>
        </div>

        <Button
          class="mx-auto"
          variant="purple"
          :label="rewardDetailModel.btnTxt"
          @click="() => rewardDetailModel.action?.()"
        />
      </div>
    </Dialog>
  </q-dialog>
</template>
<style lang="scss" scoped>
.stall {
  background: url('/imgs/huntingstop/stall-bg.png') no-repeat center center,
    linear-gradient(180deg, #3c1526 8.46%, #8c1946 94.96%), #d9d9d9;
  background-size: 130% auto;

  &.mega {
    background: url('/imgs/huntingstop/stall-bg-mega.png') no-repeat center center,
      linear-gradient(180deg, #3c1526 8.46%, #8c1946 94.96%), #d9d9d9;
    background-size: cover;
  }

  &::before {
    width: 126%;
    position: absolute;
    height: 100%;
    background: url('/imgs/huntingstop/stall-top.png') no-repeat top center;
    background-size: 100% auto;
    content: '';
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &.mega::before {
    width: 100%;
    position: absolute;
    height: 100%;
    background: url('/imgs/huntingstop/stall-top-mega.png') no-repeat top center;
    background-size: 100% auto;
    content: '';
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &::after {
    width: 100%;
    position: absolute;
    height: 130vw;
    background: url('/imgs/huntingstop/stall-bot.png') no-repeat top center;
    background-size: 100% 105%;
    content: '';
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &.mega::after {
    width: 100%;
    position: absolute;
    height: 83vw;
    background: url('/imgs/huntingstop/stall-bot-mega.png') no-repeat top center;
    background-size: 110% 110%;
    content: '';
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  .speech {
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 12px;
    padding-bottom: calc(12px + 4vw);
    background: url('/imgs/huntingstop/speech.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .location-name {
    top: 20px;
    border-radius: 8px;
    border: 3px solid #e2b595;
    background: linear-gradient(198deg, #bb7d54 28.64%, #88502b 119.57%);
    font-size: 16px;
    font-weight: 700;
    width: 280px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 52px;

    &.mega {
      position: relative;
      background: url('/imgs/huntingstop/banner.png') no-repeat center center;
      background-size: 100% 100%;
      border: none;
      color: black;
      height: 60px;
      width: 300px;
      text-align: center;
      padding: 8px;
      top: 40px;
    }
  }

  .banner {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    width: 100%;
    min-height: 64px;
    background: url('/imgs/huntingstop/alert.png') no-repeat center center;
    background-size: 100% 100%;
    z-index: 20;

    &.error {
      background: url('/imgs/huntingstop/error-alert.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
}

.hs-crystal {
  width: 80% !important;
  height: auto;
}

.text-stroke {
  -webkit-text-stroke: 1px #41063c;
}

.scale-all-98 {
  * {
    transform: scale(0.98);
  }
}
</style>
