<script setup lang="ts">
import { useMapStore, useBAStore, useUserStore, useDialogStore } from '@stores';
import {
  delay,
  useBrandActions,
  useBrandSov,
  useState,
  useTrackData,
} from '@composables';
import { BrandActionItem, BrandActionFeatured } from '@components';
import { uniqBy } from 'lodash';
import type { IBrandAction, IGlobeState } from '@types';
import gsap, { Elastic, Linear, Expo } from 'gsap';
import ScrollToPlugin from 'gsap/ScrollToPlugin';
import { USER } from '@repositories';
import { errorNotify } from '@helpers';

gsap.registerPlugin(ScrollToPlugin);

interface Props {
  code_query?: string;
  scrollToElement?: string;
  state_query?: string;
}

const props = defineProps<Props>();

const storeBA = useBAStore();
const storeMap = useMapStore();
const storeUser = useUserStore();

const { t } = useI18n();
const { openDialog, push } = useMicroRoute();
const { state } = useState<IGlobeState>();
const { openUnifyInstructor, closeUnifyInstructor } = useDialogStore();
const { newBrandActions, user_brand_actions, brand_actions } =
  storeToRefs(storeBA);

const { lastLocations, brandActionPhycial } = storeToRefs(storeMap);
const { user, isTriggerSurvey, listCrystalExpiring } = storeToRefs(storeUser);
const { trackTime, track } = useTrackData();
const onboarding = ref(
  !!props.code_query && !props.state_query && !!newBrandActions.value.length
);
const brandHooks = useBrandActions();

const { randomResult } = useBrandSov('brand_actions_offerwall_kv');

//--------------------------Properties-------------------------------
const tab = ref<'new' | 'pending'>('new');
const showFilter = ref(false);
const filterList = ref({
  new: lastLocations.value.length
    ? [
        t('OFFERWALL_BASE_SORT_1'),
        t('OFFERWALL_BASE_SORT_2'),
        t('OFFERWALL_BASE_SORT_3'),
      ]
    : [t('OFFERWALL_BASE_SORT_1'), t('OFFERWALL_BASE_SORT_3')],
  pending: [
    t('OFFERWALL_PENDING_SORT_1'),
    t('OFFERWALL_PENDING_SORT_2'),
    t('OFFERWALL_PENDING_SORT_3'),
  ],
});

const listNewTab = computed(() =>
  uniqBy(
    [
      ...(user_brand_actions.value?.filter(
        (uba) => uba.status === 'verified'
      ) || []),
      ...newBrandActions.value,
    ],
    'unique_id'
  ).filter(
    (item) =>
      item.status !== 'new' ||
      item.type !== 'verify_mobile_number' ||
      (!!user.value?.mobile_number && !user.value?.verified_mobile_number_at)
  )
);

const parseNumber = (numberStr: string | number, isMax = true) => {
  try {
    if (isNaN(+numberStr)) return isMax ? Number.MAX_VALUE : Number.MIN_VALUE;
    return +numberStr;
  } catch (error) {
    return isMax ? Number.MAX_VALUE : Number.MIN_VALUE;
  }
};

const listBrandActionSorted = computed(() => {
  if (tab.value === 'new') {
    const list: IBrandAction[] = JSON.parse(JSON.stringify(listNewTab.value));
    //-------sort on new tab--------
    switch (filterList.value.new[state.value.brand_filter.new]) {
      case t('OFFERWALL_BASE_SORT_3'):
        list.sort((a, b) => {
          return (
            parseNumber(brandHooks.calculateReward(b)) -
            parseNumber(brandHooks.calculateReward(a))
          );
        });
        return list;
      case t('OFFERWALL_BASE_SORT_2'):
        list.sort((a, b) => {
          const aDistance =
            brandActionPhycial.value?.[a._id]?.distance === undefined
              ? Number.MAX_VALUE
              : brandActionPhycial.value?.[a._id]?.distance;
          const bDistance =
            brandActionPhycial.value?.[b._id]?.distance === undefined
              ? Number.MAX_VALUE
              : brandActionPhycial.value?.[b._id]?.distance;
          return aDistance - bDistance;
        });
        return list;
      default:
        return list;
    }
  } else {
    //-------sort on pending/claimed tab----------
    const list: IBrandAction[] = JSON.parse(
      JSON.stringify(
        user_brand_actions.value.filter((item) => item.status !== 'verified')
      )
    );
    switch (filterList.value.pending[state.value.brand_filter.pending]) {
      case t('OFFERWALL_PENDING_SORT_1'):
        list.sort((a, b) => {
          const da = +new Date(
            a.claimed_at || a.rejected_at || a.created_at || 0
          );
          const db = +new Date(
            b.claimed_at || b.rejected_at || b.created_at || 0
          );
          return db - da;
        });
        return list;
      case t('OFFERWALL_PENDING_SORT_2'):
        list.sort((a, b) => {
          if (a.status !== 'pending' && b.status === 'pending') return 1;
          return -1;
        });
        return list;
      case t('OFFERWALL_PENDING_SORT_3'):
        list.sort((a, b) => {
          if (a.status !== 'claimed' && b.status === 'claimed') return 1;
          return -1;
        });
        return list;
      default:
        return list;
    }
  }
});

const listFeatured = computed(() => {
  return listNewTab.value.filter((item) => item.can_perform && item?.featured);
});

const haveFeatured = computed(() => !!listFeatured.value.length);

const listShowFeatured = ref(listFeatured.value);

// -------------------------Functions-------------------------------------
const select = (idx: number) => {
  state.value.brand_filter[tab.value] = idx;
};

const handleFilterOnClick = (e: any) => {
  if (e.target.id !== 'filter-menu' && showFilter.value) {
    showFilter.value = false;
  }
};

const killGsap = () => {
  gsap.killTweensOf('#featured');
  gsap.killTweensOf('.ofw-banner-overlay');
};

const startFeturedAnimation = () => {
  killGsap();
  gsap.fromTo(
    '#featured',
    {
      y: -500,
    },
    {
      ease: Elastic.easeInOut.config(1, 0.35),
      y: 10,
      duration: 1.5,
    }
  );
  gsap.fromTo(
    '.ofw-banner-overlay',
    {
      opacity: 0,
    },
    {
      opacity: 1,
      duration: 0.2,
      ease: Linear.easeNone,
      delay: 0.5,
    }
  );
};
const closeFeturedAnimation = () => {
  killGsap();
  gsap.to('#featured', {
    ease: Elastic.easeInOut.config(0.35, 1),
    y: -500,
    duration: 1.5,
    onComplete: () => {
      listShowFeatured.value = [];
    },
  });
  gsap.to('.ofw-banner-overlay', {
    opacity: 0,
    duration: 0.2,
    ease: Linear.easeNone,
    delay: 0.5,
  });
};

//-------------------------Lifecycle Hooks---------------------------------

watch(haveFeatured, (isExist) => {
  if (isExist) {
    listShowFeatured.value = listFeatured.value;
    startFeturedAnimation();
  } else {
    closeFeturedAnimation();
  }
});

onMounted(async () => {
  await nextTick();
  await storeBA.fetchBrandAction();
  await storeMap.fetchBrandActionPhycical();
  addEventListener('click', handleFilterOnClick);

  if (!!listShowFeatured.value.length) startFeturedAnimation();
  if (onboarding.value) {
    await delay(100);
    openUnifyInstructor('timii', {
      sequences: [
        {
          message: t('NOTIFY_RETURNING'),
          actions: {
            cb: (_, close) => {
              close();
              onboarding.value = false;
            },
          },
        },
      ],
    });
  }
  if (props.code_query && props.state_query) {
    push('ba_instruction', {
      data: brand_actions.value.find((item) => item.unique_id === 'tb_2'),
    });
    try {
      await USER.getTigerBrokerAccessToken({
        code: props.code_query,
        state: props.state_query,
      });
      storeUser.updateUser({
        tiger_permission_allowed: true,
      });
    } catch (error) {
      errorNotify({
        message: t('PERMISSION_TB_ERROR'),
      });
    }
  }
  // Scroll to verify mobile number
  if (!!props.scrollToElement) {
    gsap.to('.ofw--body', {
      duration: 1,
      ease: Expo.easeInOut,
      scrollTo: {
        y: props.scrollToElement,
        offsetY: 100,
      },
    });
  }
});

onBeforeUnmount(() => {
  trackTime('offerwall_mistaps');
  removeEventListener('click', handleFilterOnClick);
  killGsap();
});
</script>
<template>
  <div class="fullscreen">
    <div class="relative flex text-center ofw--header flex-center">
      <div
        v-show="onboarding"
        class="absolute fullscreen"
        style="background: rgba(0, 0, 0, 0.8); z-index: 2"
      ></div>
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="brandHooks.handleBack"
      >
        <Icon name="arrow-left"></Icon>
      </Button>
      <p class="text-lg font-extrabold text-center w-full px-[90px]">
        {{ t('OFFERWALL_BASE_HEADER') }}
      </p>
      <HeaderCrystal
        class="absolute right-4"
        @click="openDialog('my_crystal')"
      />
    </div>

    <div class="ofw--body overflow-auto pb-[15vw]">
      <div
        class="ofw-banner relative flex flex-center pt-[64px] pb-[15vw] z-10"
        :style="{
          backgroundImage: `url(/imgs/${randomResult.brand_actions_offerwall_kv.getAsset()}.png)`,
        }"
      >
        <div
          v-show="!!listShowFeatured.length"
          class="ofw-banner-overlay absolute top-[64px] bottom-[13vw] left-0 right-0 z-10"
        ></div>
        <BrandActionFeatured
          id="featured"
          class="relative z-20"
          v-show="!!listShowFeatured.length"
          :listFeatured="listShowFeatured"
        />
      </div>
      <q-tabs
        v-model="tab"
        dense
        class="text-grey"
        active-color="primary"
        indicator-color="primary"
        align="justify"
        narrow-indicator
      >
        <q-tab
          @click="
            track('offer_wall', {
              action: 'new_ba_tab',
            })
          "
          name="new"
          :label="t('OFFERWALL_BASE_TABHEADER_2')"
        />
        <q-tab
          @click="
            track('offer_wall', {
              action: 'pending_claimed_ba_tab',
            })
          "
          name="pending"
          :label="t('OFFERWALL_BASE_TABHEADER_1')"
        />
      </q-tabs>

      <div class="p-5 relative z-30 bg-[#200D37]">
        <div
          class="flex items-center justify-between full-width"
          v-if="!!listBrandActionSorted.length"
        >
          <div
            v-show="onboarding"
            class="z-40 fullscreen"
            style="background: rgba(0, 0, 0, 0.8)"
          ></div>
          <div
            style="flex: 1; padding-right: 10px"
            class="relative z-[999] bg-[#200D37] rounded-[5px]"
            :class="onboarding && 'p-2.5'"
          >
            <p class="text-lg font-extrabold">
              {{ t('OFFERWALL_BASE_LISTOFBRANDACTIONS') }}
            </p>
            <div class="flex justify-between">
              <p>
                {{ t('OFFERWALL_BASE_PROMOCODETAB') }}
                <span
                  class="text-link"
                  @click="
                    openDialog('enter_promo', {
                      code_query: code_query || '',
                    });
                    track('offer_wall', {
                      action: 'ba_promo_code',
                    });
                    onboarding = false;
                    closeUnifyInstructor();
                  "
                  v-html="t('OFFERWALL_TAP_HERE')"
                ></span>
              </p>
            </div>
          </div>
          <div class="relative">
            <Button
              variant="secondary"
              shape="square"
              @click="
                push('crystal_history');
                track('crystalexpiration');
                track('offer_wall', {
                  action: 'ba_history',
                });
              "
            >
              <Icon name="history" />
              <div
                v-if="
                  !user?.onboarding.crystal_expiring &&
                  !!listCrystalExpiring.length
                "
                class="absolute top-0 right-0 dot"
              ></div>
            </Button>
          </div>
          <div class="relative">
            <Button
              shape="square"
              class="relative"
              @click="
                track('offer_wall', {
                  action: 'ba_filter',
                });
                showFilter = !showFilter;
              "
            >
              <Icon name="ic_fillter" :size="14" />
            </Button>
            <div
              id="filter-menu"
              class="items-end justify-center gap-2 overflow-hidden column filter-menu"
              :class="[showFilter && 'filter-menu-open']"
            >
              <p
                v-for="(item, idx) in filterList[tab]"
                :key="`filter-` + idx"
                class="relative text-base"
                :class="idx === state.brand_filter[tab] && 'font-bold'"
                @click="select(idx)"
              >
                {{ item }}

                <Icon
                  v-show="idx === state.brand_filter[tab]"
                  name="arrow-left"
                  :size="15"
                  style="position: absolute; top: 5px; right: -16px"
                />
              </p>
            </div>
          </div>
        </div>
        <BrandActionItem
          class="mt-[30px]"
          v-for="item in listBrandActionSorted"
          :key="`brand-action-${item._id}`"
          :data="item"
          :brandHooks="brandHooks"
        />
        <div v-if="!listBrandActionSorted.length" class="px-3 mt-4 text-center">
          <template v-if="tab === 'new'">
            <template v-if="isTriggerSurvey">
              <p
                class="mb-4 text-lg font-extrabold"
                v-html="t('OFFERWALL_BASE_SURVEY')"
              ></p>
              <p v-html="t('OFFERWALL_BASE_SURVEY_DESC')"></p>
            </template>
            <template v-else>
              <div
                class="mb-4 text-lg font-extrabold"
                v-html="t('OFFERWALL_BASE_MAKESQKIILAUGH')"
              ></div>
              <p
                class="text-center"
                v-html="t('PROMOCODE_POPUP_INSTRUCTIONS')"
              ></p>
            </template>
            <Button
              class="mt-[25px] mx-auto"
              variant="primary"
              :label="
                isTriggerSurvey
                  ? t('API_POPUP_BUTTONCOPY_HAVEACCOUNT')
                  : t('PROMOCODE_POPUP_HEADER')
              "
              @click="
                () => {
                  if (isTriggerSurvey)
                    track('offer_wall_no_new_ba', {
                      action: 'no_new_ba_survey',
                    });
                  openDialog(isTriggerSurvey ? 'mid_survey' : 'enter_promo');
                }
              "
            >
            </Button>
          </template>
          <template v-else>
            <p
              class="mb-4 text-lg font-extrabold"
              v-html="t('OFFER_WALL_PENDING_EMPTY_TITLE')"
            ></p>
            <p v-html="t('OFFER_WALL_PENDING_EMPTY_CONTENT')"></p>
          </template>
        </div>
      </div>
    </div>
    <div
      class="fixed left-0 right-0 z-50 flex w-full bottom-11 flex-center"
      v-if="listBrandActionSorted.some((item) => item.status === 'verified')"
    >
      <Button
        @click="brandHooks.claimAll"
        variant="secondary"
        class="!w-[195px]"
        >{{ t('OFFERWALL_MULTICLAIM_ACTIONTAB') }}</Button
      >
    </div>

    <div
      class="bottom_shadow"
      :class="{
        'have-claim': listBrandActionSorted.some(
          (item) => item.status === 'verified'
        ),
      }"
    ></div>
  </div>
</template>
<style lang="scss">
.ofw {
  background: rgba(0, 0, 0, 1);
  &--header {
    width: 100%;
    background-color: rgba(0, 0, 0, 1);
    height: 64px;
    position: relative;
    z-index: 2;
  }
  &--body {
    background: #200d37;
    position: relative;
    z-index: 1;
    height: 100%;

    width: 100%;
    margin-top: -64px;
    .ofw-banner {
      background-repeat: no-repeat;
      background-size: cover;
      width: 100%;
      min-height: 90.66vw;
      &-overlay {
        background: linear-gradient(
          380deg,
          #111f53 41.84%,
          rgba(26, 49, 130, 0.5) 100%
        );
      }
    }
    .filter-menu {
      position: absolute;
      bottom: 0;
      right: 0;
      transform: translateY(calc(100% + 14px));
      width: max-content;
      background: linear-gradient(
        180.05deg,
        rgba(37, 25, 109, 0.9) 15.77%,
        rgba(29, 65, 137, 0.9) 98.5%
      );
      padding: 0;
      border: 1px solid #11d1f9;
      box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
      border-radius: 10px 2px 10px 10px;
      opacity: 0;
      transition: all 0.3s;
      flex-wrap: nowrap;
      z-index: 9999999999;
      height: 0px;
      &-open {
        height: 120px;
        transition: all 0.3s;
        opacity: 1;
        padding: 0 30px;
      }
    }
    .q-tab-panels {
      min-height: calc(100%);
      background: transparent;
      margin-top: 20px;
    }
    .q-tabs {
      background: transparent;
      z-index: 20;
      margin-top: -15vw;
      .q-tab {
        width: 50%;
        height: 47px;
        font-style: normal;
        font-weight: 700;
        font-size: 16px;
        opacity: 1 !important;
        line-height: 16px;
        color: white !important;
        text-transform: capitalize;

        background: #200d37;
        border-radius: 10px 10px 0px 0px;
        &__indicator {
          display: none !important;
        }

        &--inactive {
          background: #5b4674;

          .q-tab__label {
            opacity: 0.4;
          }
        }
      }
    }
  }
}
.have-claim {
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    #071027 70.54%
  ) !important;
}
.bottom_shadow {
  background: url(/imgs/offerwall_dbs_bottom.png) no-repeat;
  // height: 25vw;
  // background-size: cover;
  height: 100px;
  background-size: contain;
  position: fixed;
  // bottom: 0;
  bottom: -25px;
  left: 0;
  right: 0;
  pointer-events: none;
  z-index: 12;
}
</style>
