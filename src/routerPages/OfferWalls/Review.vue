<script setup lang="ts">
import { useTrackData } from '@composables';

const props = defineProps(['data', 'select']);
const emits = defineEmits(['delete']);

const { push } = useMicroRoute();
const { t } = useI18n();

const loading = ref(false);
const { track } = useTrackData();
const showConfirm = ref(false);
const imgs = ref(props.data);
const review = ref(props.select + 1);

const deleteImg = () => {
  const removeIndex = review.value - 1;
  if (review.value === imgs.value.length) {
    if (review.value === 1) {
      push(-1);
    } else review.value--;
  }

  emits('delete', removeIndex);
  track('receiptverification_deletephoto', {
    action: 'receiptverification_deletephoto_delete',
  });
  showConfirm.value = false;
};

const showImg = (img: File) => {
  return URL.createObjectURL(img);
};
</script>
<template>
  <div class="fullscreen" :style="loading && 'pointer-events:none'">
    <q-dialog maximized persistent v-model="showConfirm">
      <Dialog
        @close="
          showConfirm = false;
          track('receiptverification_deletephoto', {
            action: 'receiptverification_deletephoto_close',
          });
        "
      >
        <template #header
          >{{ t('RECEIPT_VERIFICATION_12_DELETECONFIRMATION_HEADER') }}
        </template>
        <div class="full-width column items-center justify-start text-center">
          <p>
            {{ t('RECEIPT_VERIFICATION_12_DELETECONFIRMATION_DESCRIPTION') }}
          </p>
          <div class="flex flex-center gap-4 full-width mt-4">
            <Button size="max-content" variant="purple" @click="deleteImg">{{
              t('delete')
            }}</Button>
            <Button
              size="max-content"
              variant="primary"
              @click="
                showConfirm = false;
                track('receiptverification_deletephoto', {
                  action: 'receiptverification_deletephoto_keep',
                });
              "
              >{{
                t('RECEIPT_VERIFICATION_8_RETAKECONFIRMATION_ACTIONBUTTON_1')
              }}</Button
            >
          </div>
        </div>
      </Dialog>
    </q-dialog>

    <div class="review-upload px-5 py-5 fit overflow-auto relative">
      <div class="flex flex-center">
        <p class="text-lg font-extrabold">
          {{ t('RECEIPT_VERIFICATION_7_PHOTOTAKEN_HEADER') }}
        </p>
      </div>
      <div class="column justify-start items-center">
        <div
          class="guild mt-10 flex flex-center color-black text-lg font-extrabold relative"
        >
          <div class="fit relative">
            <Button
              style="z-index: 2; position: absolute; top: 10px; right: 12px"
              shape="square"
              variant="secondary"
              @click="
                showConfirm = true;
                track('receiptverification_reviewgallery', {
                  action: 'receiptverification_reviewgallery_delete',
                });
              "
            >
              <Icon name="icons/delete" :size="12" />
            </Button>
            <div
              class="flex items-center justify-between absolute-full"
              style="left: -20px; right: -20px"
              v-if="imgs.length > 1"
            >
              <Button
                style="z-index: 2"
                shape="square"
                @click="
                  review = review === 1 ? imgs.length : review - 1;
                  track('receiptverification_reviewgallery', {
                    action: 'receiptverification_reviewgallery_left',
                  });
                "
              >
                <Icon name="arrow-left" :size="10" />
              </Button>
              <Button
                style="z-index: 2"
                shape="square"
                @click="
                  review = review === imgs.length ? 1 : review + 1;
                  track('receiptverification_reviewgallery', {
                    action: 'receiptverification_reviewgallery_right',
                  });
                "
              >
                <Icon name="arrow-left" :size="10" class="rotate-[180deg]" />
              </Button>
            </div>
            <q-carousel
              class="fit"
              animated
              v-model="review"
              swipeable
              infinite
              transition-prev="slide-right"
              transition-next="slide-left"
              style="border-radius: 10px"
            >
              <q-carousel-slide
                v-for="(item, index) in imgs"
                :key="'imgs.name-' + index"
                :name="index + 1"
                :img-src="showImg(item)"
              />
            </q-carousel>
            <div
              class="flex flex-center full-width gap-2.5 my-[15px]"
              v-if="imgs.length >= 2"
            >
              <span
                v-for="(_, index) in imgs"
                :key="'dot-' + index"
                :style="`background: #ffffff; opacity: ${
                  index === review - 1 ? 1 : 0.5
                }; border-radius: 50px;width:${
                  index === review - 1 ? 26 : 4
                }px;height:4px`"
              ></span>
            </div>
          </div>
        </div>

        <div class="text-center mt-[30px]">
          {{ t('RECEIPT_VERIFICATION_6_TAKEPHOTO_DESCRIPTION') }}
        </div>

        <Button
          variant="purple"
          class="mt-2.5"
          @click="
            push(-1);
            track('receiptverification_reviewgallery', {
              action: 'receiptverification_reviewgallery_back',
            });
          "
          >{{ t('back') }}</Button
        >
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.review-upload {
  background-image: url(/imgs/gradient_color.png),
    linear-gradient(#090422, #090422);
  background-size: 100% 87vw, 100% 100%;
  background-position: center top, center center;
  background-repeat: no-repeat;

  .guild {
    background: #d9d9d9;
    border-radius: 10px;
    width: 320px;
    height: 320px;
  }
}
</style>
