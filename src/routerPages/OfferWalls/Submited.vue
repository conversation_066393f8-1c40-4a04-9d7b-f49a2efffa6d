<script setup lang="ts">
import { playSFX } from '@composables';
import gsap, { Linear } from 'gsap';

interface Props {
  title?: string;
  header: string;
  isReceipt?: boolean;
}

defineProps<Props>();

const tl = gsap.timeline();

const { push } = useMicroRoute();
const { t } = useI18n();

playSFX('success');

onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      tl.fromTo(
        '.upload_flare',
        {
          scale: 0,
        },
        {
          scale: 1,
          duration: 1,
          delay: 0.5,
        }
      ).fromTo(
        '.upload_flare',
        {
          rotate: 0,
        },
        {
          rotate: 720,
          duration: 20,
          repeat: -1,
          ease: Linear.easeNone,
        }
      );
      gsap.fromTo(
        '.upload_star',
        {
          opacity: 0,
        },
        {
          opacity: 1,
          yoyo: true,
          repeat: -1,
          duration: 1.5,
        }
      );
      gsap.fromTo(
        '.nancii',
        {
          y: -20,
          opacity: 0,
        },
        {
          opacity: 1,
          y: 0,
          duration: 1,
        }
      );
      gsap.fromTo(
        '.upload_star',
        {
          opacity: 0,
        },
        {
          opacity: 1,
          yoyo: true,
          repeat: -1,
          delay: 1,
          duration: 1,
        }
      );
    });
  });
});

onBeforeUnmount(() => {
  tl?.kill();
  gsap.killTweensOf('.nancii');
  gsap.killTweensOf('.submited_flare');
  gsap.killTweensOf('.submited_star');
});
</script>
<template>
  <div class="fullscreen">
    <div
      class="submited py-5 fit overflow-y-auto overflow-x-hidden relative text-center"
    >
      <div class="flex flex-center">
        <p class="text-lg font-extrabold" v-html="header"></p>
      </div>

      <div class="relative full-width flex flex-center" style="height: 100vw">
        <Icon
          class="upload_flare absolute"
          style="top: 10vw; left: -10vw; width: 120vw"
          name="upload_flare"
        />
        <Icon
          class="upload_star full-width absolute"
          style="top: 0; left: 0; z-index: 2"
          name="star_frame"
        />
        <Icon
          name="nancii_1"
          :size="128"
          class="relative nancii mt-[20vw]"
          style="z-index: 3"
        />
      </div>
      <div class="mt-[-100px] px-[30px]">
        <p
          class="text-lg font-bold mt-5"
          v-html="title || t('SUBMITED_THANK_TITLE')"
        ></p>
        <p class="my-5">
          {{
            t(
              isReceipt
                ? 'RECEIPT_VERIFICATION_13_RECEIPTSUBMITTED_DESCRIPTION_2'
                : 'VERIFICATION_13_RECEIPTSUBMITTED_DESCRIPTION_2'
            )
          }}
        </p>
        <p>
          {{ t('RECEIPT_VERIFICATION_13_RECEIPTSUBMITTED_DESCRIPTION_3') }}
        </p>
        <div class="full-width flex flex-center mt-[50px]">
          <Button @click="push('offer_wall')">{{
            t('RECEIPT_VERIFICATION_13_RECEIPTSUBMITTED_ACTIONBUTTON')
          }}</Button>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.submited {
  background: #090422;

  .guild {
    background: #d9d9d9;
    border-radius: 10px;
    width: 320px;
    height: 320px;
  }
}
</style>
