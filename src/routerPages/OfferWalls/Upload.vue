<script setup lang="ts">
import {
  useMediaDevice,
  playSFX,
  useGlobal,
  useTrackData,
  useClick,
} from '@composables';
import { showNotify, dataURLtoFile, closeNotify } from '@helpers';
import { BRAND_ACTION } from '@repositories';
import { useBAStore } from '@stores';
import { Loading } from 'quasar';
import type { IBrandAction } from '@types';

interface Props {
  data: IBrandAction;
}

const props = defineProps<Props>();

const storeBA = useBAStore();

const { trackUserLocation } = useGlobal();
const { push, openDialog, closeDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();

useClick('troubeshoot_android', () => {
  track('receiptverification_error_cameraundetected', {
    action: 'receiptverification_error_troubleshootandroid',
  });
});
useClick('troubeshoot_ios', () => {
  track('receiptverification_error_cameraundetected', {
    action: 'receiptverification_error_troubleshootios',
  });
});
const { isAllowed, request, preRequest, requested } = useMediaDevice(
  {
    video: {
      facingMode: 'environment',
      width: { ideal: 720 },
      height: { ideal: 720 },
    },
    audio: false,
  },
  (stream, size) => {
    streamVideo = stream;
    sizeVideo = size;
    videoElement.srcObject = stream;
    closeDialog('camera_permission');
    track('camera_pop_up', {
      result: true,
    });
  },
  () => {
    showNotify({
      message: t('CAMERA_ERROR_NOT_DETECTED'),
      classes: 'errors-notify',
      position: 'top',
      timeout: 8000,
      actions: [
        {
          handler: async () => {
            closeNotify();
            track('receiptverification_error_cameraundetected', {
              action: 'receiptverification_error_cameraundetected_close',
            });
          },
        },
      ],
    });
    closeDialog('camera_permission');
    track('camera_pop_up', {
      result: false,
    });
  }
);

let sizeVideo: any = {};
let streamVideo: any = null;
let videoElement: any = null;
const btnRef = ref();
const imgs = ref<File[]>([]);
const loading = ref(false);
const confirmRetake = ref(false);
const photo = ref();
const videoLoading = ref(false);

//----------------------Function-----------------------
const canPlay = () => {
  videoLoading.value = true;
};

const takePhoto = () => {
  const canvas = document.getElementById('canvas') as any;
  if (!canvas) return;
  canvas.width = sizeVideo.width;
  canvas.height = sizeVideo.height;
  var context = canvas.getContext('2d');
  context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
  photo.value = canvas.toDataURL('image/png');
};

const retake = () => {
  photo.value = undefined;
  confirmRetake.value = false;
};
const clickBtnImg = () => {
  btnRef.value.click();
  !imgs.value.length
    ? track('receiptverification_takephoto', {
        action: 'receiptverification_takephoto_upload',
      })
    : track('receiptverification_gallery', {
        action: 'receiptverification_gallery_takephoto',
      });
};
const savePhoto = () => {
  const file = dataURLtoFile(photo.value, 'imageUpload_' + Date.now() + '.png');
  if (imgs.value.length < 5) imgs.value.push(file);
  photo.value = undefined;
};

const upload = async (include_milo: boolean) => {
  Loading.show();
  try {
    const data = new FormData();
    data.append('brand_action_id', props.data._id);
    data.append('include_milo', include_milo ? 'yes' : 'no');
    imgs.value.forEach((file) => data.append('receipts', file));
    await BRAND_ACTION.uploadFile(data);
    await storeBA.fetchBrandAction();
    await trackUserLocation('brand_action', {
      type: props.data.type,
      brand_action_id: props.data._id,
    });
    push('submited', {
      header: t('RECEIPT_VERIFICATION_13_RECEIPTSUBMITTED_HEADER'),
      title: t('RECEIPT_VERIFICATION_13_RECEIPTSUBMITTED_DESCRIPTION_1'),
      isReceipt: true,
    });
    track('receiptverification_complete', {
      action: 'receiptverification_complete_backtooffers',
    });
    playSFX('success');
    imgs.value = [];
    btnRef.value.value = null;
  } catch (error) {
    console.log(error);
  } finally {
    Loading.hide();
    closeDialog('confirm_submit');
  }
};

const pickImage = (e: any) => {
  if (e?.target?.files)
    imgs.value = [...imgs.value, ...e.target.files].slice(0, 5);
};
const deleteImg = (index: number) => {
  imgs.value.splice(index, 1);
};
const showImg = (img: File) => {
  return URL.createObjectURL(img);
};
const later = ref(false);

//-----------------HOOKS---------------------
onMounted(() => {
  if (!isAllowed)
    openDialog('camera_permission', {
      onRequest: request,
      onClose: () => {
        later.value = true;
        closeDialog('camera_permission');
      },
    });
  nextTick(() => {
    videoElement = document.getElementById('video');
    videoElement?.addEventListener('canplay', canPlay);
    if (isAllowed) preRequest();
  });
});

onBeforeUnmount(() => {
  streamVideo?.getTracks().forEach(function (track: any) {
    track.stop();
  });
  videoElement?.removeEventListener('canplay', canPlay);
});
</script>
<template>
  <div class="fullscreen" :style="loading && 'pointer-events:none'">
    <q-dialog maximized persistent v-model="confirmRetake">
      <Dialog
        @close="
          confirmRetake = false;
          track('receiptverification_retake', {
            action: 'receiptverification_retake_close',
          });
        "
      >
        <template #header>{{
          t('RECEIPT_VERIFICATION_8_RETAKECONFIRMATION_ACTIONBUTTON')
        }}</template>
        <div class="full-width column items-center justify-start text-center">
          <p>{{ t('RECEIPT_VERIFICATION_8_RETAKECONFIRMATION_MESSAGE') }}</p>
          <div class="flex flex-center gap-4 full-width mt-4">
            <Button
              size="max-content"
              variant="purple"
              @click="
                retake();
                track('receiptverification_retake', {
                  action: 'receiptverification_retake',
                });
              "
              >{{
                t('RECEIPT_VERIFICATION_8_RETAKECONFIRMATION_ACTIONBUTTON')
              }}</Button
            >
            <Button
              size="max-content"
              variant="primary"
              @click="
                confirmRetake = false;
                track('receiptverification_retake', {
                  action: 'receiptverification_retake_save',
                });
              "
              >{{
                t('RECEIPT_VERIFICATION_8_RETAKECONFIRMATION_ACTIONBUTTON_1')
              }}</Button
            >
          </div>
        </div>
      </Dialog>
    </q-dialog>
    <div class="upload py-5 fit overflow-auto relative">
      <div class="flex flex-center">
        <p class="text-lg font-extrabold" v-if="photo">
          {{ t('RECEIPT_VERIFICATION_7_PHOTOTAKEN_HEADER') }}
        </p>
        <p class="text-lg font-extrabold" v-else-if="imgs.length > 0">
          {{ t('RECEIPT_VERIFICATION_9_1PHOTOSAVED_HEADER') }}
        </p>
        <p class="text-lg font-extrabold" v-else>
          {{ t('RECEIPT_VERIFICATION_6_TAKEPHOTO_HEADER') }}
        </p>
        <Button
          class="absolute btn-back-left"
          shape="square"
          variant="secondary"
          :loading="!requested && !later"
          @click="
            push(-1);
            imgs.length
              ? track('receiptverification_gallery', {
                  action: 'receiptverification_gallery_back',
                })
              : !photo
              ? track('receiptverification_takephoto', {
                  action: 'receiptverification_takephoto_back',
                })
              : track('receiptverification_review', {
                  action: 'receiptverification_review_back',
                });
          "
        >
          <Icon name="arrow-left" />
        </Button>
      </div>
      <div class="column justify-start items-center px-5">
        <div
          class="guild mt-10 flex flex-center color-black text-lg font-extrabold relative"
        >
          <video
            v-show="!photo"
            id="video"
            width="320"
            height="320"
            style="border-radius: 10px"
            playsinline
            autoplay
          ></video>
          <img
            v-show="photo"
            :src="photo"
            width="320"
            height="320"
            style="border-radius: 10px; object-fit: contain"
          />
          <canvas v-show="false" id="canvas" width="320" height="320"></canvas>
        </div>
        <div
          @click="
            takePhoto();
            track('receiptverification_takephoto', {
              action: 'receiptverification_takephoto_camera',
            });
          "
          class="full-width flex flex-center mt-[-30px]"
          :style="
            (!videoLoading || imgs.length >= 5) &&
            'pointer-events:none;filter: grayscale(1);'
          "
          v-if="!photo"
        >
          <div class="take_photo">
            <Icon name="icons/camera" :size="24" />
          </div>

          <!-- <img src="icons/pri.png" width="60" /> -->
        </div>
        <div class="text-center" :class="[photo ? 'mt-[30px]' : 'mt-2.5']">
          {{
            imgs.length > 0 && !photo
              ? t('RECEIPT_VERIFICATION_9_1PHOTOSAVED_MESSAGE')
              : t('RECEIPT_VERIFICATION_6_TAKEPHOTO_DESCRIPTION')
          }}
        </div>

        <div
          class="flex flex-center gap-1.5 my-[15px]"
          v-if="imgs.length > 0 && !photo"
        >
          <q-img
            :ratio="1"
            style="width: 50px; background: white; border-radius: 6px"
            fit="cover"
            v-for="(img, index) in imgs"
            :key="img.name"
            :src="showImg(img)"
            @click="
              push('review', {
                select: index,
                onDelete: deleteImg,
                data: imgs,
              })
            "
          />
        </div>
        <div class="italic full-width text-center mb-4" v-if="imgs.length > 0">
          {{
            t('RECEIPT_VERIFICATION_9_1PHOTOSAVED_MESSAGE_1', {
              num: imgs.length,
            })
          }}
        </div>
        <div v-if="photo" class="flex flex-center gap-4 full-width mt-5">
          <Button
            size="max-content"
            variant="purple"
            @click="
              confirmRetake = true;
              track('receiptverification_review', {
                action: 'receiptverification_review_retake',
              });
            "
            >{{
              t('RECEIPT_VERIFICATION_8_RETAKECONFIRMATION_ACTIONBUTTON')
            }}</Button
          >
          <Button
            size="max-content"
            variant="primary"
            @click="
              savePhoto();
              track('receiptverification_review', {
                action: 'receiptverification_review_save',
              });
            "
            >{{ t('RECEIPT_VERIFICATION_7_TAKEPHOTO_ACTIONBUTTON_1') }}</Button
          >
        </div>
        <Button
          v-else-if="imgs.length === 0"
          class="mt-5"
          variant="purple"
          @click="clickBtnImg"
          >{{ t('RECEIPT_VERIFICATION_6_TAKEPHOTO_ACTIONBUTTON') }}</Button
        >
        <Button
          v-else
          class="mt-2.5"
          @click="
            track('receiptverification_gallery', {
              action: 'receiptverification_gallery_submit',
            });
            openDialog('confirm_submit', {
              header: t(`VERIFYCATION_CONFIRM_HEADER`),
              data,
              onSubmit: upload,
            });
          "
          >{{ t('RECEIPT_VERIFICATION_9_1PHOTOSAVED_ACTIONBUTTON') }}</Button
        >

        <input
          type="file"
          accept="image/*"
          multiple
          v-show="false"
          ref="btnRef"
          @change="pickImage"
        />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.upload {
  background-image: url(/imgs/gradient_color.png),
    linear-gradient(#090422, #090422);
  background-size: 100% 87vw, 100% 100%;
  background-position: center top, center center;
  background-repeat: no-repeat;

  .guild {
    background: #d9d9d9;
    border-radius: 10px;
    width: 320px;
    height: 320px;
  }
  .take_photo {
    width: 60px;
    height: 60px;
    min-width: 60px;
    min-height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    color: #000000;
    font-weight: 700;
    justify-content: center;
    position: relative;
    z-index: 2;
    background: linear-gradient(180deg, #38e7d2 0%, #1da1d1 100%);
    &::before {
      content: '';
      position: absolute;
      top: 3px;
      left: 3px;
      right: 3px;
      bottom: 3px;
      z-index: 3;
      border-radius: 50%;
      border: 2px solid #000000;
    }
  }
}
</style>
