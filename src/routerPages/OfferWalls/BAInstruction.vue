<script setup lang="ts">
import { ReceiptRequirement, SlideIllustration } from '@components';
import { useAsync, useHandleGoButton, useTrackData } from '@composables';
import { BRAND_ACTION } from '@repositories';
import { useBAStore, useUserStore } from '@stores';
import type { IBrandAction } from '@types';
// import { useQuasar } from 'quasar';

interface Props {
  data: IBrandAction;
}

const props = defineProps<Props>();

const { track } = useTrackData();
const { push } = useMicroRoute();
const { t } = useI18n();
const { openDialog, closeDialog } = useMicroRoute();
const userStore = useUserStore();
const baStore = useBAStore();
const { visitWebAction, openSentosaAppAction } = useHandleGoButton();
const { user } = storeToRefs(userStore);
const steps = props.data.instructions;
const showRequire = ref(false);

// const { platform } = useQuasar();
// const getAppLink = () => {
//   const link = platform.is.android
//     ? props.data.metadata.android_link
//     : props.data.metadata.app_store_link;
//   return link?.length ? link : props.data.metadata.web_link || '';
// };
const tbSyncSteps = [
  {
    content: t(steps[0] || ''),
    actions: [
      {
        content: t(
          user.value?.tiger_permission_allowed
            ? 'PERMISSION_ALLOWED_BTN'
            : 'PERMISSION_ALLOW_BTN'
        ),
        is_btn: true,
        disabled: !!user.value?.tiger_permission_allowed,
        action: () => {
          openDialog('request_tb_permission');
        },
        active: true,
      },
    ],
  },
  {
    content: t(steps[1] || ''),
    // actions: [
    //   {
    //     content: `<a href="${
    //       props.data.metadata.web_link || ''
    //     }" target="_blank">${t('TIGER_BROKER_LINK_CONTENT_2')}</a>`,
    //     action: () => {},
    //     active: true,
    //   },
    // ],
  },
];

const tbBossCard = [
  {
    content: t(steps[0] || ''),
    // actions: [
    //   {
    //     content: `<a href="${
    //       props.data.metadata.web_link || ''
    //     }" target="_blank">${t('TIGER_BROKER_LINK_CONTENT_3')}</a>`,
    //     action: () => {},
    //     active: true,
    //   },
    // ],
  },
  {
    content: t(steps[1] || ''),
  },
  {
    content: t(steps[2] || ''),
  },
];
const tbDepositSteps = [
  {
    content: t(steps[0] || ''),
    // actions: [
    //   {
    //     content: `<a href="${
    //       props.data.metadata.web_link || ''
    //     }" target="_blank">${t('TIGER_BROKER_LINK_CONTENT_4')}</a>`,
    //     action: () => {},
    //     active: true,
    //   },
    // ],
  },
  {
    content: t(steps[1] || ''),
    actions: [
      {
        content: t(
          user.value?.tiger_permission_allowed
            ? 'PERMISSION_ALLOWED_BTN'
            : 'PERMISSION_ALLOW_BTN'
        ),
        is_btn: true,
        disabled: !!user.value?.tiger_permission_allowed,
        action: () => {
          openDialog('request_tb_permission');
        },
        active: true,
      },
    ],
  },
  {
    content: t(steps[2] || ''),
  },
  {
    content: t(steps[3] || ''),
  },
];

const lendleaseSteps = [
  {
    content: t(steps[0] || ''),
    // actions: [
    //   {
    //     content: `<a href="${getAppLink()}" target="_blank">${t(
    //       'TIGER_BROKER_LINK_CONTENT_5'
    //     )}</a>`,
    //     action: () => {},
    //     active: true,
    //   },
    // ],
  },
  {
    content: t(steps[1] || ''),
  },
  {
    content: t(steps[2] || ''),
  },
];
const tadaSteps = [
  {
    content: t(steps[0] || ''),
    // actions: [
    //   {
    //     content: `<a href="${getAppLink()}" target="_blank">${t(
    //       'TIGER_BROKER_LINK_CONTENT_6'
    //     )}</a>`,
    //     action: () => {},
    //     active: true,
    //   },
    // ],
  },
  {
    content: t(steps[1] || ''),
  },
  {
    content: t(steps[2] || ''),
  },
];

const etiquaSteps = [
  {
    content: t(steps[0] || ''),
    // actions: [
    //   {
    //     content: `<a href="${
    //       props.data.metadata.web_link || ''
    //     }" target="_blank">${t(t('ETIQUA_DETAIL'))}</a>`,
    //     action: () => {},
    //     active: true,
    //   },
    // ],
  },
  {
    content: t(steps[1] || ''),
  },
  {
    content: t(steps[2] || ''),
  },
];
const verifySteps = [
  {
    content: t(steps[0] || ''),
    actions: [
      {
        content: t(props.data.outlet_copy || ''),
        action: () => {
          track('receiptverification_info', {
            action: 'receiptverification_info_participatingoutlets',
          });
          openDialog('outlet', {
            header: props.data.outlet_popup_header || '',
            brand_unique_id: props.data.brand_unique_id,
            onClose: () => {
              closeDialog('outlet');
              track('receiptverification_participatingoutlets', {
                action: 'receiptverification_participatingoutlets_close',
              });
            },
          });
        },
        active: !!props.data.outlet_copy,
      },
    ],
  },
  {
    content: t(steps[1] || ''),
    actions: [
      {
        content: t('RECEIPT_VERIFICATION_1_INFO_HEADER_ACTION_2_DESCRIPTION'),
        action: () => {
          track('receiptverification_info', {
            action: 'receiptverification_info_photorequirements',
          });
          showRequire.value = true;
        },
        active: true,
      },
    ],
  },
  {
    content: t(steps[2] || ''),
  },
  {
    content: t(steps[3] || ''),
  },
];

const linkAccountStep = [
  {
    content: t(props.data.instruction || ''),
    actions: [
      {
        content: t(props.data.outlet_copy || ''),
        action: () => {
          openDialog('outlet', {
            header: props.data.outlet_popup_header || '',
            brand_unique_id: props.data.brand_unique_id,
          });
        },
        active: !!props.data.outlet_copy,
      },
    ],
  },
  {
    content: `Come back to this page and key in the
                ${
                  props.data.verify_type === 'email' ? 'email' : 'mobile number'
                }
                you signed up with`,
  },
  {
    content: 'Claim rewards!',
  },
];

const listPromoStep = [
  {
    content: steps[0] && t(steps[0]),

    actions: [
      {
        content: t(props.data.outlet_copy || ''),
        action: () => {
          openDialog('outlet', {
            header: props.data.outlet_popup_header || '',
            brand_unique_id: props.data.brand_unique_id,
          });
        },
        active: !!props.data.outlet_copy,
      },
      {
        content: t('qualifying'),
        action: () => {
          openDialog('qualifying');
        },
        active: props.data.type === 'enter_barcode',
      },
    ],
  },
  {
    content: steps[1] && t(steps[1]),
  },
  {
    content: steps[2] && t(steps[2]),
  },
  {
    content: steps[3] && t(steps[3]),
  },
];

const spfWASteps = [
  {
    content: steps[0] && t(steps[0]),
  },
  {
    content: steps[1] && t(steps[1]),
  },
  {
    content: steps[2] && t(steps[2]),
  },
  {
    content: steps[3] && t(steps[3]),
  },
  {
    content: steps[4] && t(steps[4]),
  },
];

const createGenericSteps = (totalSteps: number) => {
  return Array.from({ length: totalSteps }, (_, i) => ({
    content: t(steps[i] || ''),
  })).filter((step) => !!step.content);
};

const { loading, execute: checkTBDeposit } = useAsync({
  async fn() {
    return await BRAND_ACTION.checkReposit();
  },
  onSuccess() {
    baStore.fetchBrandAction();
    push('submited', {
      header: t('TB_REPOSIT_SUBMITED_HEADER'),
    });
  },
});

const handleAction = () => {
  track('ba_instruction', {
    ba_unique_id: props.data.unique_id,
  });

  if (props.data.unique_id === 'spf_4') {
    visitWebAction(props.data).then(() => {
      push(-1);
    });
    return;
  }

  if (props.data.type === 'open_sentosa_app') {
    openSentosaAppAction(props.data).then(() => {
      push(-1);
      baStore.fetchBrandAction();
    });
    return;
  }

  switch (props.data.type) {
    case 'promo_code':
    case 'enter_promo_code':
    case 'enter_barcode':
    case 'scan_qrcode':
      openDialog('enter_promo', {
        type: props.data.type === 'enter_barcode' ? 'barcode' : 'promocode',
        data: props.data,
      });
      break;
    case 'receipt_verification':
      track('receiptverification_info', {
        action: 'receiptverification_info_go',
      });
      push('upload', {
        data: props.data,
      });
      break;
    case 'etiqa_insurance':
      openDialog('enter_policy');
      break;
    case 'tada_ride':
      openDialog('ba_tada', {
        data: props.data,
      });
      break;
    case 'lendlease_sync_account':
      openDialog('ba_lendlease', {
        data: props.data,
      });
      break;
    case 'tiger_broker_register_boss_card':
      openDialog('ba_tb_boss', {
        data: props.data,
      });
      break;
    case 'tiger_broker_sync_account':
      if (!!user.value?.tiger_permission_allowed) {
        checkTBDeposit();
      } else {
        push('submited', {
          header: t('TB_SYNC_ACCOUNT_SUBMITED_HEADER'),
        });
      }
      break;
    case 'tiger_broker_deposit':
      checkTBDeposit();
      break;
    default:
      break;
  }
};

const listStep = computed<any>(() => {
  if (props.data.unique_id === 'spf_4') {
    return spfWASteps;
  }
  switch (props.data.type) {
    case 'promo_code':
    case 'enter_promo_code':
      return listPromoStep;
    case 'lendlease_sync_account':
      return lendleaseSteps;
    case 'client_verify':
      return linkAccountStep;
    case 'receipt_verification':
      return verifySteps;
    case 'etiqa_insurance':
      return etiquaSteps;
    case 'tada_ride':
      return tadaSteps;
    case 'tiger_broker_sync_account':
      return tbSyncSteps;
    case 'tiger_broker_deposit':
      return tbDepositSteps;
    case 'tiger_broker_register_boss_card':
      return tbBossCard;
    case 'open_sentosa_app':
    case 'scan_qrcode':
      return createGenericSteps(steps.length);
    default:
      return [];
  }
});

const btnType = computed<{
  disabled: boolean;
  type: 'btn' | 'link';
  link: string;
}>(() => {
  if (props.data.unique_id === 'spf_4') {
    return {
      type: 'link',
      disabled: false,
      link: props.data.link || '',
    };
  }
  switch (props.data.type) {
    case 'tiger_broker_deposit':
      return {
        disabled: !user.value?.tiger_permission_allowed,
        type: 'btn',
        link: '',
      };
    case 'tiger_broker_sync_account':
      return {
        disabled: false,
        type: 'link',
        link: `https://www.tigersecurities.com/signup?invite=sqkii&utm_source=sqkii&external_id=${user.value?.id}&external_client_id=63e0a0e5c5974c51aff2a039f3d7b7e9`,
      };
    default:
      return {
        type: 'btn',
        disabled: false,
        link: '',
      };
  }
});

const img: () => {
  name: string;
  type: 'gif' | 'png' | 'svg';
}[] = () => {
  if (props.data.unique_id === 'spf_4') {
    return [
      { name: 'ba_spf_4_1', type: 'png' },
      { name: 'ba_spf_4_2', type: 'gif' },
      { name: 'ba_spf_4_3', type: 'png' },
      { name: 'ba_spf_4_4', type: 'png' },
    ];
  }

  switch (props.data.type) {
    case 'enter_barcode':
      return [
        'ba_barcode_1',
        'ba_barcode_2',
        'ba_barcode_3',
        'ba_barcode_4',
      ].map((item) => ({ name: item, type: 'png' }));
    case 'promo_code':
    case 'enter_promo_code':
      if (props.data.unique_id === 'spf_5')
        return [
          { name: 'ncpc_1', type: 'png' },
          { name: 'ncpc_2', type: 'gif' },
          { name: 'ncpc_3', type: 'png' },
          { name: 'ncpc_4', type: 'png' },
        ];
      return ['ba_dbs_promocode_1', 'ba_promocode_2', 'ba_promocode_3'].map(
        (item) => ({ name: item, type: 'png' })
      );
    case 'receipt_verification':
      return [
        'ba_receipt_1',
        'ba_receipt_2',
        'ba_receipt_3',
        'ba_receipt_4',
      ].map((item) => ({ name: item, type: 'png' }));
    case 'etiqa_insurance':
      return ['ba_etiqua_1', 'ba_etiqua_2', 'ba_etiqua_3'].map((item) => ({
        name: item,
        type: 'png',
      }));
    case 'tiger_broker_sync_account':
      return [
        { name: 'ba_tb_sync_1', type: 'png' },
        { name: 'ba_tb_sync_2', type: 'png' },
        { name: 'ba_tb_sync_3', type: 'gif' },
        { name: 'ba_tb_sync_4', type: 'gif' },
        { name: 'ba_tb_sync_5', type: 'png' },
      ];
    case 'tiger_broker_deposit':
      return [
        { name: 'ba_tb_deposit_1', type: 'png' },
        { name: 'ba_tb_deposit_2', type: 'gif' },
        { name: 'ba_tb_deposit_3', type: 'png' },
        { name: 'ba_tb_deposit_4', type: 'gif' },
        { name: 'ba_tb_deposit_5', type: 'png' },
      ];
    case 'open_sentosa_app':
      return [{ name: 'islander-sentosa-ba', type: 'gif' }];
    default:
      return ['ba_checkmobile_1', 'ba_checkmobile_2', 'ba_checkmobile_3'].map(
        (item) => ({ name: item, type: 'png' })
      );
  }
};

const handleBack = () => {
  switch (props.data.type) {
    case 'receipt_verification':
      track('', {
        action: 'receiptverification_info_back',
      });
      break;

    default:
      break;
  }
  push(-1);
};

const handleTriggerItem = (event: Event) => {
  const target = event.target as HTMLElement;
  const data = target.dataset;
  const triggerType = data.triggerType;

  switch (triggerType) {
    case 'go-btn':
      handleAction();
      break;
    case 'view-outlets':
      openDialog('sentosa_outlets');
      break;
  }
};

onMounted(() => {
  document.querySelectorAll('.trigger-item').forEach((el) => {
    el.addEventListener('click', handleTriggerItem);
  });
});

onUnmounted(() => {
  document.querySelectorAll('.trigger-item').forEach((el) => {
    el.removeEventListener('click', handleTriggerItem);
  });
});
</script>
<template>
  <div
    class="items-center overflow-hidden fullscreen column flex-nowrap ba_instruction"
  >
    <q-dialog maximized persistent v-model="showRequire">
      <ReceiptRequirement
        :data="data"
        @close="
          showRequire = false;
          track('receiptverification_photorequirements', {
            action: 'receiptverification_photorequirements_close',
          });
        "
      />
    </q-dialog>
    <div class="flex items-start justify-center pb-4 mt-4 full-width">
      <p
        class="text-lg font-extrabold px-[60px] text-center"
        v-html="t(data.header_copy || 'Missing header copy!')"
      ></p>
      <Button
        class="fixed btn-back-left"
        style="top: 12px"
        shape="square"
        variant="secondary"
        @click="handleBack"
      >
        <Icon name="arrow-left" :size="14" />
      </Button>
    </div>
    <div class="flex-1 w-full px-5 pb-5 overflow-y-auto">
      <SlideIllustration class="mb-10" :illustrations="img()" />

      <div
        class="promo-step px-2.5 gap-[30px] column justify-start text-base overflow-hidden"
      >
        <div
          v-for="(step, idx) in listStep"
          :key="`step_${idx}`"
          class="flex max-w-full overflow-hidden flex-nowrap"
        >
          <div class="relative">
            <span class="num_line" />
            <p class="num_step">{{ idx + 1 }}</p>
          </div>

          <div class="flex items-center flex-1 pl-5">
            <p class="font-bold" v-html="step.content"></p>
            <p
              v-if="!!step.des"
              v-html="step.des"
              class="font-normal mt-[5px] hyphens-manual"
            />
            <div
              v-for="(action,i) in (step.actions?.filter((e:any) => e.active) || [])"
              :key="`action-${i}`"
              class="mt-1"
            >
              <Button
                variant="purple"
                :disable="action.disabled"
                @click="action.action"
                v-if="action.is_btn"
                >{{ action.content }}</Button
              >
              <p
                v-else
                class="text-[#58E9E0] font-normal underline mt-[5px]"
                @click="action.action"
                v-html="action.content"
              ></p>
            </div>
          </div>
        </div>
      </div>
      <!-- <div
        v-if="data.unique_id === 'tb_1'"
        class="fixed bottom-[16px] left-0 right-0 flex justify-evenly flex-nowrap px-5 gap-3"
      >
        <Button
          class="flex-1 flex-shrink-0"
          variant="purple"
          :loading="loading"
          :disable="loading"
          @click="
            () => {
              !user?.tiger_permission_allowed
                ? openDialog('request_tb_permission')
                : checkTBDeposit();
              track('btn_tb_existing_user');
            }
          "
          :label="t('ba_tb_1_button_copy_existing_user')"
        />
        <Button
          class="min-w-[150px]"
          :loading="loading"
          size="medium-fit"
          :disable="btnType.disabled"
          @click="handleAction"
          :label="t(data.button_copy || 'Missing button copy!')"
        />
      </div> -->
      <div class="fixed bottom-[16px] left-[50%] translate-x-[-50%]">
        <a
          v-if="btnType.type === 'link'"
          :href="btnType.link"
          target="_blank"
          :class="{ 'pointer-events-none': btnType.disabled }"
        >
          <Button
            :loading="loading"
            :disable="btnType.disabled"
            @click="handleAction"
            :label="t(data.button_copy || 'Missing button copy!')"
          />
        </a>
        <Button
          v-else
          :loading="loading"
          :disable="btnType.disabled"
          @click="handleAction"
          :label="t(data.button_copy || 'Missing button copy!')"
        />
      </div>
    </div>
  </div>
</template>
<style>
.text-link {
  text-decoration-line: underline;
  color: #58e9e0;
}
</style>
<style lang="scss" scoped>
.ba_instruction {
  padding-bottom: 70px;
  background-image: url(/imgs/gradient_color.png),
    linear-gradient(#090422, #090422);
  background-size: 100% 87vw, 100% 100%;
  background-position: center top, center center;
  background-repeat: no-repeat;

  .guild {
    background: #d9d9d9;
    border-radius: 10px;
    width: 100%;
  }
  .num_line {
    position: absolute;
    left: 15px;
    bottom: calc(100% - 15px);
    width: 4px;
    height: 100vh;
    background: #1d1e58;
    z-index: -1;
  }
  .num_step {
    width: 34px;
    height: 34px;
    min-width: 34px;
    min-height: 34px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    color: #000000;
    font-weight: 700;
    justify-content: center;
    position: relative;
    z-index: 2;
    background: linear-gradient(180deg, #38e7d2 0%, #1da1d1 100%);
    &::before {
      content: '';
      position: absolute;
      top: 3px;
      left: 3px;
      right: 3px;
      bottom: 3px;
      z-index: 3;
      border-radius: 50%;
      border: 1px solid #000000;
    }
  }
}
</style>
