<script lang="ts" setup>
import { TrialFrame } from '@components';
import { useTick } from '@composables';
import { timeCountDown } from '@helpers';
import dayjs from 'dayjs';

const { t } = useI18n();
const { now } = useTick();
const { closeDialog, openDialog } = useMicroRoute();

const TIME = dayjs().add(1, 'hour').toISOString();

const nextShrinkCountdown = computed(() => {
  const cd = +new Date(TIME) - now.value;
  return timeCountDown(cd);
});

function handleUseShrink() {
  closeDialog('beacon_trial_silver_shrink');
  openDialog('beacon_trial_applied');
}
</script>
<template>
  <TrialFrame hidden-map>
    <Dialog @close="closeDialog('beacon_trial_silver_shrink')">
      <template #header>
        <div v-html="t('BEACON_TRIAL_SILVER_SHRINK_HEADER')"></div>
      </template>
      <div class="relative px-2 -mt-5 text-center">
        <div class="silver-coin">
          <div class="absolute top-8">
            <div
              class="mb-1 text-sm"
              v-html="t('BEACON_TRIAL_SILVER_SHRINK_DESC_1')"
            ></div>
            <div
              class="mb-5 text-2xl font-bold"
              v-html="t('BEACON_TRIAL_SILVER_SHRINK_DESC_2')"
            ></div>
          </div>
          <Icon class="mt-12 size-[40%]" name="silver-coin" />
        </div>
        <div
          class="bg-[#091a3b] rounded p-3 flex flex-nowrap justify-center items-center gap-2 mb-5 -mt-10"
        >
          <div
            class="text-sm"
            v-html="
              t('BEACON_TRIAL_SILVER_SHRINK_DESC_3', {
                TIME: nextShrinkCountdown,
              })
            "
          ></div>
          <Icon name="question-mark" />
        </div>
        <div
          class="mb-5 text-sm frame-discount-powerup"
          v-html="t('BEACON_TRIAL_SILVER_SHRINK_DESC_4')"
        ></div>
        <SilverCoinPowerUpSelection class="mb-5 pointer-events-none" />
        <Button
          class="mb-5 mx-auto !w-[230px]"
          :title="t('BEACON_TRIAL_SILVER_SHRINK_BTN_USE')"
          :old-amount="100"
          :amount="90"
          @click="handleUseShrink"
        />
        <div
          class="text-sm"
          v-html="t('BEACON_TRIAL_SILVER_SHRINK_DESC_5')"
        ></div>
      </div>
    </Dialog>
  </TrialFrame>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
}
</style>
