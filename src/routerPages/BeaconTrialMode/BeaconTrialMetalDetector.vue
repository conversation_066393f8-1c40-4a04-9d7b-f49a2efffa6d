<script lang="ts" setup>
import { TrialFrame } from '@components';

const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();

function handleRequestMetalDetector() {
  closeDialog('beacon_trial_metal_detector');
  openDialog('beacon_trial_applied');
}
</script>
<template>
  <TrialFrame hidden-map>
    <Dialog @close="closeDialog('beacon_trial_metal_detector')">
      <template #icon-center>
        <Icon
          class="absolute -top-16 left-1/2 -translate-x-1/2 pointer-events-none"
          name="metal-detector"
          :size="85"
        />
      </template>
      <template #header>
        <div v-html="t('BEACON_TRIAL_METAL_DETECTOR_HEADER')"></div>
      </template>
      <div class="text-center">
        <div
          class="text-sm mb-5"
          v-html="t('BEACON_TRIAL_METAL_DETECTOR_DESC_1')"
        ></div>
        <Button
          class="mb-5 mx-auto !w-[230px]"
          :title="t('BEACON_TRIAL_METAL_DETECTOR_BTN_USE')"
          :old-amount="100"
          :amount="90"
          @click="handleRequestMetalDetector"
        />
      </div>
    </Dialog>
  </TrialFrame>
</template>
