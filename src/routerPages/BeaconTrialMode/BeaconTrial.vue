<script lang="ts" setup>
import {
  delay,
  useGlobalInstructor,
  useInventory,
  useMapHelpers,
} from '@composables';
import {
  ONGOING_COIN_FREE_FILL_LAYER,
  ONGOING_COIN_FREE_LINE_GLOW_LAYER,
  ONGOING_COIN_FREE_LINE_LAYER,
} from '@constants';
import { closeNotify, successNotify } from '@helpers';
import { useMapStore, useUserStore } from '@stores';
import {
  FillLayer,
  GeoJsonSource,
  LineLayer,
  LngLatBoundsLike,
  LngLatLike,
  MapCreationStatus,
  Marker,
} from 'vue3-maplibre-gl';
import { TrialFrame } from '@components';
import circle from '@turf/circle';
import bbox from '@turf/bbox';

interface Props {
  isUsed?: boolean;
}

const props = defineProps<Props>();

const storeUser = useUserStore();
const storeMap = useMapStore();

const { settings } = storeToRefs(storeUser);
const { lastLocations, _zoom } = storeToRefs(storeMap);
const { isFirstBeaconTrial } = useInventory();
const { t } = useI18n();
const { makeSource, mapTransform } = useMapHelpers();
const { openUnifyInstructor, closeUnifyInstructor } = useGlobalInstructor();
const { push } = useMicroRoute();

const SCALE_FACTOR = 1;
const MIN_ZOOM = 8;
const OFFSET = 0.0005; // ~0.0005 degrees is ~50m
const BASE_RADIUS = computed(() => settings.value?.beacon.radius || 500);
const trialFrameRef = ref<InstanceType<typeof TrialFrame>>();
const mapInstance = computed(() => trialFrameRef.value?.mapInstance);
const mapStatus = computed(() => trialFrameRef.value?.mapStatus);
const isUsed = ref(props.isUsed || false);

const circleProperties = computed(() => {
  const [lng, lat] = lastLocations.value;
  return [
    {
      lng: lng - OFFSET * 10,
      lat: lat - OFFSET,
      radius: BASE_RADIUS.value * 2,
    },
    {
      lng: lng + OFFSET * 10,
      lat: lat + OFFSET * 50,
      radius: BASE_RADIUS.value * 2.5,
    },
  ];
});

const beaconProperties = computed(() => {
  const [lng, lat] = lastLocations.value;
  return {
    lng,
    lat,
    radius: BASE_RADIUS.value,
  };
});

const beaconSource = computed(() => {
  const c = circle(
    [beaconProperties.value.lng, beaconProperties.value.lat],
    beaconProperties.value.radius,
    {
      units: 'meters',
    }
  );
  return makeSource([c]);
});

const beaconOutlineSources = computed(() => {
  const [lng, lat] = lastLocations.value;
  const outlines = [
    {
      multiplier: 1.025,
      properties: {
        opacity: 0.8,
        'min-zoom': 9,
        'max-zoom': 20,
      },
    },
    {
      multiplier: 1.05,
      properties: {
        opacity: 0.3,
        'min-zoom': 14,
        'max-zoom': 20,
      },
    },
  ];
  return makeSource(
    outlines.map(({ multiplier, properties }) =>
      circle([lng, lat], BASE_RADIUS.value * multiplier, {
        units: 'meters',
        properties,
      })
    )
  );
});

const circleSource = computed(() => {
  const c = circleProperties.value.map((c) => {
    return circle([c.lng, c.lat], c.radius, {
      units: 'meters',
    });
  });
  return makeSource(c);
});

const scaleMarkerBaseOnMapZoom = computed(() => {
  return 1 + (_zoom.value - MIN_ZOOM) * SCALE_FACTOR;
});

const beaconDecorateMarkers = computed(() => {
  const [lng, lat] = lastLocations.value;
  const radius = BASE_RADIUS.value;
  return [
    {
      pos: mapTransform(lng, lat, radius, 0) as LngLatLike,
      id: 'star',
      class: 'w-[40px]',
      anim: '_pulse-reverse',
    },
    {
      pos: mapTransform(lng, lat, radius, 10) as LngLatLike,
      id: 'star',
      class: 'w-[20px]',
      anim: '_pulse',
    },
    {
      pos: mapTransform(lng, lat, radius, 80) as LngLatLike,
      id: 'sqkii',
      class: 'w-[10px]',
      anim: '',
    },
    {
      pos: mapTransform(lng, lat, radius, 190) as LngLatLike,
      id: 'percent',
      class: 'w-[5px]',
      anim: '',
    },
    {
      pos: mapTransform(lng, lat, radius, 280) as LngLatLike,
      id: 'star',
      class: 'w-[40px]',
      anim: '_pulse',
    },
    {
      pos: mapTransform(lng, lat, radius - 10, 260) as LngLatLike,
      id: 'star',
      class: 'w-[20px]',
      anim: '_pulse-reverse',
    },
  ];
});

const fitBounds = (): void => {
  const [lng, lat] = lastLocations.value;
  const c = circle([lng, lat], BASE_RADIUS.value * 1.2, {
    units: 'meters',
  });
  const box = bbox(c);
  mapInstance.value?.fitBounds(box as LngLatBoundsLike);
};

const handleUseBeacon = (): void => {
  closeNotify();
  isUsed.value = true;
  openUnifyInstructor('timii', {
    sequences: [
      {
        hideClose: true,
        message: t('BEACON_TRIAL_IN_USE_MESSAGE'),
      },
    ],
  });
};

const handleGoToBag = async () => {
  closeUnifyInstructor();
  await delay(300);
  push('/beacon_inventory_trial');
};

watch(mapStatus, async (val) => {
  if (val === MapCreationStatus.Loaded) {
    if (isFirstBeaconTrial.value) {
      successNotify({
        message: t('BEACON_TRIAL_FIRST_MESSAGE'),
      });
      storeUser.updateOnboarding('first_beacon_trial');
    }
    await delay(2000);
    fitBounds();
  }
});

onMounted(() => {
  isUsed.value = props.isUsed || false;
});
</script>

<template>
  <TrialFrame ref="trialFrameRef">
    <div
      class="banner absolute w-max top-12 left-1/2 -translate-x-1/2 z-10"
      v-html="t('BEACON_TRIAL_BANNER_TEXT')"
    ></div>
    <Button
      v-if="!isUsed"
      class="absolute bottom-10 left-1/2 -translate-x-1/2 z-10 !w-[210px]"
      :label="t('BEACON_TRIAL_BTN_CONFIRM')"
      @click="handleUseBeacon"
    />
    <div
      class="dynamic_btn absolute right-0 z-10 bottom-20"
      v-if="isUsed"
      @click="handleGoToBag"
    >
      <div
        class="fit leading-normal gap-0.5 flex-center column relative bag-pulse"
      >
        <Icon name="bag" :size="20" class="mr-[-6px]" />
        <div
          class="font-bold text-[10px] text-center mr-[-6px]"
          v-html="t('BAG_DYNAMIC_BTN')"
        ></div>
      </div>
    </div>
    <GeoJsonSource id="trial_circle" :data="circleSource">
      <FillLayer
        id="trial_fill_layer"
        :style="{
          ...ONGOING_COIN_FREE_FILL_LAYER,
        }"
      />
      <LineLayer
        id="trial_line_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_LAYER,
        }"
      />
      <LineLayer
        id="trial_line_glow_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_GLOW_LAYER,
        }"
      />
    </GeoJsonSource>
    <GeoJsonSource id="trial_beacon" :data="beaconSource">
      <template v-if="!isUsed">
        <FillLayer
          id="trial_preview_beacon_fill_layer"
          :style="{
            ...ONGOING_COIN_FREE_FILL_LAYER,
            'fill-color': '#46FCF1',
          }"
        />
        <LineLayer
          id="trial_preview_beacon_line_layer"
          :style="{
            ...ONGOING_COIN_FREE_LINE_LAYER,
            'line-dasharray': [5, 2],
            'line-width': 3,
            'line-color': '#46FCF1',
          }"
        />
      </template>
      <template v-else>
        <FillLayer
          id="beacon_in_use_fill_layer"
          :style="{
            'fill-color': '#F3FF6C',
            'fill-opacity': 0.3,
          }"
        />
        <LineLayer
          id="beacon_in_use_line_layer"
          :style="{
            'line-color': '#F3FF6C',
            'line-width': [
              'interpolate',
              ['linear'],
              ['zoom'],
              9,
              1,
              12,
              10,
              15,
              0,
            ],
            'line-blur': 10,
          }"
        />
      </template>
    </GeoJsonSource>
    <template v-if="isUsed">
      <GeoJsonSource id="trial_beacon_outline" :data="beaconOutlineSources">
        <LineLayer
          id="beacon_outline"
          :style="{
            'line-color': '#F3FF6C',
            'line-width': [
              'interpolate',
              ['linear'],
              ['zoom'],
              12,
              1,
              14,
              3,
              20,
              10,
            ],
            'line-opacity': ['get', 'opacity'],
          }"
        />
      </GeoJsonSource>
      <Marker
        v-for="(marker, i) in beaconDecorateMarkers"
        :key="`${i}_${marker.id}_${marker.pos.toString()}`"
        :lnglat="marker.pos"
        :class="marker.class"
      >
        <img
          :id="marker.id"
          :src="`/imgs/bc_${marker.id}.png`"
          :class="[marker.class, marker.anim]"
          class="origin-center"
          :style="{
            transform: `scale(${scaleMarkerBaseOnMapZoom})`,
          }"
        />
      </Marker>
    </template>
  </TrialFrame>
</template>
<style lang="scss" scoped>
.banner {
  text-align: center;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid #b663e9;
  background: linear-gradient(261deg, #341c7c 1.58%, #1a0950 60.32%);
}
._pulse {
  animation: pulse infinite ease-in 2s;
}
._pulse-reverse {
  animation: pulse infinite ease-in 2s reverse;
}
@keyframes pulse {
  0% {
    transform: scale(0);
  }

  70% {
    transform: scale(1);
  }

  100% {
    transform: scale(0);
  }
}

.dynamic_btn {
  width: 68px;
  height: 68px;
  background: url(/imgs/button/dynamic.png);
  background-size: 100% 100%;
}

.bag-pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 35px;
  height: 35px;
  z-index: 2;
  transform: scale(1);
  background: rgba(52, 172, 224, 0.3);
  animation: pulse-blue 2s infinite;
  box-shadow: 0 0 0 0 rgba(52, 172, 224, 1);
  border-radius: 8px;
}

@keyframes pulse-blue {
  0% {
    transform: scale(1.05);
    box-shadow: 0 0 0 0 rgba(52, 172, 224, 0.7);
  }

  60% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(52, 172, 224, 0);
  }

  100% {
    transform: scale(1.05);
    box-shadow: 0 0 0 0 rgba(52, 172, 224, 0);
  }
}
</style>
