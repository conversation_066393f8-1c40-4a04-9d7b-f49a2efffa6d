<script lang="ts" setup>
import { TrialFrame } from '@components';
import { delay, useMapHelpers } from '@composables';
import {
  ONGOING_COIN_FREE_FILL_LAYER,
  ONGOING_COIN_FREE_LINE_GLOW_LAYER,
  ONGOING_COIN_FREE_LINE_LAYER,
} from '@constants';
import { useMapStore } from '@stores';
import {
  FillLayer,
  GeoJsonSource,
  LineLayer,
  LngLatBoundsLike,
} from 'vue3-maplibre-gl';
import circle from '@turf/circle';
import bbox from '@turf/bbox';

const storeMap = useMapStore();

const { lastLocations } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();
const { t } = useI18n();
const { openDialog, push } = useMicroRoute();

type Step = (typeof STEPS)[keyof typeof STEPS];

interface FitBoundsOptions {
  lng: number;
  lat: number;
  radius: number;
  padding?: number;
  duration?: number;
  animate?: boolean;
}

const STEPS = {
  SELECT_CIRCLE: 1,
  SELECT_RADIUS: 2,
} as const;
const RADIUS_OPTIONS = [
  {
    id: 1,
    radius: 25,
    price: 100,
  },
  {
    id: 2,
    radius: 50,
    price: 200,
  },
  {
    id: 3,
    radius: 100,
    price: 300,
  },
];
const OFFSET = 0.0005; // ~0.0005 degrees is ~50m
const BASE_RADIUS = 150;

const showDialog = ref(false);
const slide = ref('circle_1');
const step = ref<Step>(STEPS.SELECT_CIRCLE);
const selectedRadius = ref(RADIUS_OPTIONS[0].radius || 25);
const trialFrameRef = ref<InstanceType<typeof TrialFrame>>();

const mapInstance = computed(() => trialFrameRef.value?.mapInstance);

const amount = computed(() => {
  const radius = selectedRadius.value;
  const option = RADIUS_OPTIONS.find((r) => r.radius === radius);
  return option?.price || 0;
});

const circleProperties = computed(() => {
  const [lng, lat] = lastLocations.value;
  return [
    {
      id: 'circle_1',
      name: 'Silver Coin #1',
      lng: lng - OFFSET * 1.5,
      lat: lat - OFFSET,
      radius: BASE_RADIUS * 2,
    },
    {
      id: 'circle_2',
      name: 'Silver Coin #2',
      lng: lng + OFFSET * 1.5,
      lat: lat + OFFSET * 3,
      radius: BASE_RADIUS * 2.5,
    },
  ].map((c) => ({
    ...c,
    width: c.id === slide.value ? 3 : 1,
  }));
});

const circleSource = computed(() => {
  const c = circleProperties.value.map((c) => {
    return circle([c.lng, c.lat], c.radius, {
      units: 'meters',
      properties: c,
    });
  });
  return makeSource(c);
});

const sonarSource = computed(() => {
  if (step.value !== STEPS.SELECT_RADIUS) return makeSource([]);
  const [lng, lat] = lastLocations.value;
  return makeSource([
    circle([lng, lat], selectedRadius.value, {
      units: 'meters',
    }),
  ]);
});

const handleUseSonar = (): void => {
  openDialog('beacon_trial_applied');
};

const handleConfirmCircle = (): void => {
  step.value = STEPS.SELECT_RADIUS;
  fitBoundsWithRadius();
};

const fitBounds = (options: FitBoundsOptions): void => {
  const {
    lng,
    lat,
    radius,
    padding = 50,
    duration = 500,
    animate = true,
  } = options;
  const _circle = circle([lng, lat], radius, {
    units: 'meters',
  });
  const box = bbox(_circle) as LngLatBoundsLike;

  mapInstance.value?.fitBounds(box, {
    padding,
    duration,
    animate,
  });
};

const fitBoundsWithCircle = (): void => {
  const circle = circleProperties.value.find((c) => c.id === slide.value);
  if (!circle) return;
  fitBounds({
    lng: circle.lng,
    lat: circle.lat,
    radius: circle.radius * 1.2,
  });
};

const fitBoundsWithRadius = (): void => {
  const [lng, lat] = lastLocations.value;
  fitBounds({
    lng,
    lat,
    radius: selectedRadius.value * 1.2,
  });
};
watch(slide, fitBoundsWithCircle);

watch(selectedRadius, fitBoundsWithRadius);

onMounted(async () => {
  await delay(500);
  showDialog.value = true;
  await delay(1000);
  fitBoundsWithCircle();
});
</script>
<template>
  <TrialFrame ref="trialFrameRef">
    <div
      v-if="step === STEPS.SELECT_CIRCLE"
      class="banner absolute w-[70%] top-12 left-1/2 -translate-x-1/2 z-10"
      v-html="t('COIN_SONAR_BANNER_TEXT')"
    ></div>
    <Button
      class="fixed top-14 left-3 z-10"
      shape="square"
      variant="secondary"
      @click="push(-1)"
    >
      <Icon name="arrow-left" />
    </Button>
    <GeoJsonSource id="trial_circle" :data="circleSource">
      <FillLayer
        id="trial_fill_layer"
        :style="{
          ...ONGOING_COIN_FREE_FILL_LAYER,
        }"
      />
      <LineLayer
        id="trial_line_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_LAYER,
          'line-width': ['get', 'width'],
        }"
      />
      <LineLayer
        id="trial_line_glow_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_GLOW_LAYER,
        }"
      />
    </GeoJsonSource>
    <GeoJsonSource id="sonar_circle" :data="sonarSource">
      <FillLayer
        id="sonar_fill_layer"
        :style="{
          ...ONGOING_COIN_FREE_FILL_LAYER,
          'fill-color': '#46FCF1',
        }"
      />
      <LineLayer
        id="sonar_line_layer"
        :style="{
          ...ONGOING_COIN_FREE_FILL_LAYER,
          'line-color': '#46FCF1',
        }"
      />
      <LineLayer
        id="sonar_line_glow_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_LAYER,
          'line-dasharray': [5, 2],
          'line-width': 3,
          'line-color': '#46FCF1',
        }"
      />
    </GeoJsonSource>
    <q-dialog v-model="showDialog" position="bottom" seamless>
      <div class="beacon-trial-coin-sonar-dialog-container">
        <Button
          shape="square"
          variant="purple"
          class="absolute left-5 -top-3 z-20"
          v-show="step === STEPS.SELECT_RADIUS"
          @click="step = STEPS.SELECT_CIRCLE"
        >
          <Icon name="arrow-left" />
        </Button>
        <div
          v-show="step === STEPS.SELECT_CIRCLE"
          class="w-full h-full flex flex-col justify-center items-center"
        >
          <q-carousel
            v-model="slide"
            swipeable
            animated
            padding
            arrows
            class="bg-transparent mb-5"
          >
            <q-carousel-slide
              v-for="c in circleProperties"
              :key="c.id"
              :name="c.id"
            >
              <div class="text-sm p-4 bg-[#04081D] w-full rounded-xl">
                {{ c.name }}
              </div>
            </q-carousel-slide>
          </q-carousel>
          <Button
            label="Confirm circle"
            class="!w-[210px]"
            @click="handleConfirmCircle"
          />
        </div>
        <div
          v-show="step === STEPS.SELECT_RADIUS"
          class="w-full h-full flex flex-col justify-center items-center"
        >
          <div
            class="text-sm mb-5"
            v-html="t('BEACON_TRIAL_COIN_SONAR_DESC_1')"
          ></div>
          <q-btn-group class="mb-8 metal-group-prices">
            <div
              class="absolute rounded-lg -inset-1 border border-[#7b37e950]"
            ></div>
            <q-btn
              class="capitalize opacity-50 price"
              :class="{
                active: selectedRadius === r.radius,
              }"
              v-for="r in RADIUS_OPTIONS"
              :key="r.id"
              :label="`${r.radius}m`"
              @click="selectedRadius = r.radius"
            />
          </q-btn-group>
          <Button
            class="mb-5 mx-auto !w-[260px]"
            :title="
              t('BEACON_TRIAL_COIN_SONAR_BTN_USE', { RADIUS: selectedRadius })
            "
            :old-amount="amount"
            :amount="amount * 0.9"
            @click="handleUseSonar"
          />
        </div>
      </div>
    </q-dialog>
  </TrialFrame>
</template>
<style lang="scss">
.beacon-trial-coin-sonar-dialog-container {
  margin: 0 30px;
  width: 100%;
  height: 250px;
  margin-bottom: 8px;
  background-image: url(/imgs/bg-coin-sonar.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  overflow: visible !important;
  .q-carousel {
    height: unset;
    background-color: unset;
    padding: 0 !important;
    width: 100%;
  }
}
.metal-group-prices {
  box-shadow: unset;
  position: relative;
  .price {
    width: 70px;
    height: 42px;
    background: linear-gradient(
      180deg,
      rgba(147, 75, 218, 0.8) 0%,
      #511d85 100%
    );
    &.active {
      opacity: 1;
      border: 1px solid #b57eec;
      background: linear-gradient(
        180deg,
        rgba(147, 75, 218, 0.8) 0%,
        #511d85 100%
      );
    }
  }
}
</style>
<style lang="scss" scoped>
.banner {
  text-align: center;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid #b663e9;
  background: linear-gradient(261deg, #341c7c 1.58%, #1a0950 60.32%);
}
</style>
