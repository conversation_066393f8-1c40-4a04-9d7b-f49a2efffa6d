<script lang="ts" setup>
import { TrialFrame } from '@components';

const { closeDialog } = useMicroRoute();
const { t } = useI18n();
</script>
<template>
  <TrialFrame hidden-map>
    <Dialog @close="closeDialog('beacon_trial_applied')">
      <template #header>
        <div v-html="t('BEACON_TRIAL_APPLIED_HEADER')"></div>
      </template>
      <div class="text-center">
        <div
          class="text-sm mb-5"
          v-html="t('BEACON_TRIAL_APPLIED_DESC_1')"
        ></div>
        <div class="grid grid-cols-2 gap-5 mb-5">
          <div class="flex flex-col gap-2">
            <Icon name="beacon_applied_1" class="!w-full" />
            <div
              class="italic text-sm"
              v-html="t('BEACON_TRIAL_APPLIED_DESC_2')"
            ></div>
          </div>
          <div class="flex flex-col gap-2">
            <Icon name="beacon_applied_2" class="!w-full" />
            <div
              class="italic text-sm"
              v-html="t('BEACON_TRIAL_APPLIED_DESC_3')"
            ></div>
          </div>
        </div>
      </div>
    </Dialog>
  </TrialFrame>
</template>
