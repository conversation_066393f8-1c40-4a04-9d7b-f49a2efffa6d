<script lang="ts" setup>
import { TrialFrame } from '@components';
import {
  delay,
  useGlobalInstructor,
  useInventory,
  useMapHelpers,
} from '@composables';
import {
  ONGOING_COIN_FREE_FILL_LAYER,
  ONGOING_COIN_FREE_LINE_GLOW_LAYER,
  ONGOING_COIN_FREE_LINE_LAYER,
} from '@constants';
import { useMapStore, useUserStore } from '@stores';
import {
  FillLayer,
  GeoJsonSource,
  LineLayer,
  LngLatBoundsLike,
  MapCreationStatus,
  SymbolLayer,
} from 'vue3-maplibre-gl';
import { successNotify } from '@helpers';
import { point } from '@turf/helpers';
import circle from '@turf/circle';
import gsap, { Power2 } from 'gsap';
import destination from '@turf/destination';
import bbox from '@turf/bbox';

interface FitBoundsOptions {
  lng: number;
  lat: number;
  radius: number;
  padding?: number;
  duration?: number;
  animate?: boolean;
}

type Step = (typeof STEPS)[keyof typeof STEPS];

const STEPS = {
  SELECT_CIRCLE: 1,
  SELECT_RADIUS: 2,
  SCANNING: 3,
  RESULT_SUCCESS: 4,
  RESULT_FAIL: 5,
} as const;

const storeMap = useMapStore();
const storeUser = useUserStore();

const { settings } = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();
const { t } = useI18n();
const { openUnifyInstructor, closeUnifyInstructor } = useGlobalInstructor();
const { isFirstCoinSonarTrial } = useInventory();

const RADIUS_OPTIONS = computed(() => {
  const radius = settings.value?.metal_sonar?.radius || [];
  return radius.map((r, index) => ({
    id: index + 1,
    radius: r,
    price: 100 * (index + 1),
  }));
});
const OFFSET = 0.0005; // ~0.0005 degrees is ~50m
const BASE_RADIUS = 50;
const showDialog = ref(false);
const slide = ref('circle_1');
const step = ref<Step>(STEPS.SELECT_CIRCLE);
const selectedRadius = ref(RADIUS_OPTIONS.value[0].radius || 25);
const trialFrameRef = ref<InstanceType<typeof TrialFrame>>();

const mapInstance = computed(() => trialFrameRef.value?.mapInstance);
const mapStatus = computed(() => trialFrameRef.value?.mapStatus);

const amount = computed(() => {
  const radius = selectedRadius.value;
  const option = RADIUS_OPTIONS.value.find((r) => r.radius === radius);
  return option?.price || 0;
});

const fitBounds = (options: FitBoundsOptions): void => {
  const {
    lng,
    lat,
    radius,
    padding = 50,
    duration = 500,
    animate = true,
  } = options;
  const _circle = circle([lng, lat], radius, {
    units: 'meters',
  });
  const box = bbox(_circle) as LngLatBoundsLike;

  mapInstance.value?.fitBounds(box, {
    padding,
    duration,
    animate,
  });
};

const circleProperties = computed(() => {
  const [lng, lat] = lastLocations.value;
  return [
    {
      id: 'circle_1',
      name: 'Silver Coin #1',
      lng: lng - OFFSET * 1.25,
      lat: lat - OFFSET,
      radius: BASE_RADIUS * 2,
    },
    {
      id: 'circle_2',
      name: 'Silver Coin #2',
      lng: lng + OFFSET * 1.25,
      lat: lat + OFFSET * 0.25,
      radius: BASE_RADIUS * 2.5,
    },
  ].map((c) => ({
    ...c,
    width: c.id === slide.value ? 3 : 1,
  }));
});

const circleSource = computed(() => {
  const c = circleProperties.value.map((c) => {
    return circle([c.lng, c.lat], c.radius, {
      units: 'meters',
      properties: c,
    });
  });
  return makeSource(c);
});

const sonarSource = computed(() => {
  if (step.value !== STEPS.SELECT_RADIUS) return makeSource([]);
  const [lng, lat] = lastLocations.value;
  return makeSource([
    circle([lng, lat], selectedRadius.value, {
      units: 'meters',
    }),
  ]);
});

const sonarResultSource = computed(() => {
  if (step.value !== STEPS.RESULT_SUCCESS && step.value !== STEPS.RESULT_FAIL)
    return makeSource([]);

  const [lng, lat] = lastLocations.value;
  return makeSource([
    circle([lng, lat], selectedRadius.value, {
      units: 'meters',
      properties: {
        color: step.value === STEPS.RESULT_SUCCESS ? '#29D798' : '#DF3126',
      },
    }),
  ]);
});

const sonarOutlineSources = computed(() => {
  if (step.value !== STEPS.RESULT_SUCCESS && step.value !== STEPS.RESULT_FAIL)
    return makeSource([]);
  const [lng, lat] = lastLocations.value;
  const outlines = [
    {
      multiplier: 1.025,
      properties: {
        opacity: 0.8,
        'min-zoom': 9,
        'max-zoom': 20,
        color: step.value === STEPS.RESULT_SUCCESS ? '#29D798' : '#DF3126',
      },
    },
    {
      multiplier: 1.05,
      properties: {
        opacity: 0.3,
        'min-zoom': 14,
        'max-zoom': 20,
        color: step.value === STEPS.RESULT_SUCCESS ? '#29D798' : '#DF3126',
      },
    },
  ];
  return makeSource(
    outlines.map(({ multiplier, properties }) =>
      circle([lng, lat], selectedRadius.value * multiplier, {
        units: 'meters',
        properties,
      })
    )
  );
});

const coinLocation = computed(() => {
  const selectedCircle = circleProperties.value.find(
    (c) => c.id === slide.value
  );
  if (!selectedCircle) return [0, 0];
  const radius = selectedCircle.radius;
  const distance = Math.random() * radius;
  const bearing = Math.random() * 360;
  const dest = destination(
    point([selectedCircle.lng, selectedCircle.lat]),
    distance,
    bearing,
    {
      units: 'meters',
    }
  );
  return dest.geometry.coordinates;
});

const coinSource = computed(() => {
  if (step.value !== STEPS.RESULT_SUCCESS && step.value !== STEPS.RESULT_FAIL)
    return makeSource([]);
  const [lng, lat] = coinLocation.value;
  return makeSource([point([lng, lat], { icon: 'shiny-coin' })]);
});

const handleUseSonar = async (): Promise<void> => {
  step.value = STEPS.SCANNING;
  gsap.to('.sonar-control', {
    opacity: 1,
    duration: 0.5,
    ease: Power2.easeInOut,
  });
  openUnifyInstructor('sqkii', {
    agent: 'radar-scanning',
    bubbleAction: true,
    bubbleText: t('COIN_SONAR_TRIAL_ACTIVATED'),
    sequences: [
      {
        persistent: true,
        message: t('COIN_SONAR_TRIAL_SEARCHING'),
      },
    ],
  });
  await delay(5000);
  gsap.to('.sonar-control', {
    opacity: 0,
  });
  step.value = Math.random() > 0.5 ? STEPS.RESULT_SUCCESS : STEPS.RESULT_FAIL;
  if (step.value === STEPS.RESULT_SUCCESS) handleSuccess();
  else handleFailed();
};

const handleSuccess = async (): Promise<void> => {
  openUnifyInstructor('sqkii', {
    agent: 'radar-scanning',
    bubbleText: t('COIN_SONAR_TRIAL_ACTIVATED'),
    sequences: [
      {
        persistent: true,
        message: t('COIN_SONAR_TRIAL_SUCCESS_1'),
      },
    ],
  });
  await delay(3000);
  openUnifyInstructor('sqkii', {
    bubbleAction: false,
    sequences: [
      {
        persistent: true,
        message: t('COIN_SONAR_TRIAL_SUCCESS_2', {
          RADIUS: selectedRadius.value,
        }),
      },
    ],
  });
};

const fitBoundsWithCircle = (): void => {
  const circle = circleProperties.value.find((c) => c.id === slide.value);
  if (!circle) return;
  fitBounds({
    lng: circle.lng,
    lat: circle.lat,
    radius: circle.radius * 1.2,
  });
};

const fitBoundsWithRadius = (): void => {
  const [lng, lat] = lastLocations.value;
  fitBounds({
    lng,
    lat,
    radius: selectedRadius.value * 1.2,
  });
};
watch(slide, fitBoundsWithCircle);

watch(selectedRadius, fitBoundsWithRadius);

const handleFailed = async (): Promise<void> => {
  openUnifyInstructor('sqkii', {
    agent: 'radar-scanning',
    bubbleText: t('COIN_SONAR_TRIAL_ACTIVATED'),
    sequences: [
      {
        persistent: true,
        message: t('COIN_SONAR_TRIAL_FAIL_1'),
      },
    ],
  });
  await delay(3000);
  openUnifyInstructor('sqkii', {
    bubbleAction: false,
    sequences: [
      {
        persistent: true,
        message: t('COIN_SONAR_TRIAL_FAIL_2', {
          RADIUS: selectedRadius.value,
        }),
      },
    ],
  });
};

const handleConfirmCircle = (): void => {
  step.value = STEPS.SELECT_RADIUS;
  fitBoundsWithRadius();
};

const triggerFirstCoinSonarTrial = async (): Promise<void> => {
  if (!isFirstCoinSonarTrial.value) return;
  await delay(500);
  successNotify({
    message: t('SONAR_TRIAL_FIRST_MESSAGE'),
  });
  openUnifyInstructor('timii', {
    sequences: [
      {
        css: {
          bottom: '370px',
        },
        message: t('TIMII_FIRST_COIN_SONAR_TRIAL'),
      },
    ],
  });
  storeUser.updateOnboarding('first_trial_coin_sonar');
};

watch(mapStatus, async (val) => {
  if (val === MapCreationStatus.Loaded) {
    await delay(500);
    showDialog.value = true;
    triggerFirstCoinSonarTrial();
    await delay(1000);
    fitBoundsWithCircle();
  }
});

onBeforeUnmount(() => {
  closeUnifyInstructor();
});
</script>
<template>
  <TrialFrame ref="trialFrameRef" class="trial-frame-container">
    <div
      v-if="step === STEPS.SELECT_CIRCLE"
      class="banner absolute w-[70%] top-12 left-1/2 -translate-x-1/2 z-10"
      v-html="t('COIN_SONAR_BANNER_TEXT')"
    ></div>
    <div
      class="sonar-control absolute z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full opacity-0 pointer-events-none"
    >
      <div class="sonar-radar">
        <div class="circle"></div>
        <div class="shadow"></div>
      </div>
      <div class="pointer-events-none line-horizontal">
        <div class="absolute ml-2 text-xs left-3/4 -translate-x-3/4">
          {{ selectedRadius }}m
        </div>
      </div>
      <div class="pointer-events-none line-vertical"></div>
    </div>
    <GeoJsonSource id="trial_circle" :data="circleSource">
      <FillLayer
        id="trial_fill_layer"
        :style="{
          ...ONGOING_COIN_FREE_FILL_LAYER,
        }"
      />
      <LineLayer
        id="trial_line_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_LAYER,
          'line-width': ['get', 'width'],
        }"
      />
      <LineLayer
        id="trial_line_glow_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_GLOW_LAYER,
        }"
      />
    </GeoJsonSource>
    <GeoJsonSource id="sonar_circle" :data="sonarSource">
      <FillLayer
        id="sonar_fill_layer"
        :style="{
          ...ONGOING_COIN_FREE_FILL_LAYER,
          'fill-color': '#46FCF1',
        }"
      />
      <LineLayer
        id="sonar_line_layer"
        :style="{
          ...ONGOING_COIN_FREE_FILL_LAYER,
          'line-color': '#46FCF1',
        }"
      />
      <LineLayer
        id="sonar_line_glow_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_LAYER,
          'line-dasharray': [5, 2],
          'line-width': 3,
          'line-color': '#46FCF1',
        }"
      />
    </GeoJsonSource>
    <GeoJsonSource id="sonar_result_circle" :data="sonarResultSource">
      <FillLayer
        id="beacon_in_use_fill_layer"
        :style="{
          'fill-color': ['get', 'color'],
          'fill-opacity': 0.3,
        }"
      />
    </GeoJsonSource>
    <GeoJsonSource id="sonar_outline_circle" :data="sonarOutlineSources">
      <LineLayer
        id="sonar_outline_line_layer"
        :style="{
          'line-color': ['get', 'color'],
          'line-width': [
            'interpolate',
            ['linear'],
            ['zoom'],
            12,
            1,
            14,
            2,
            20,
            1,
          ],
          'line-opacity': ['get', 'opacity'],
        }"
      />
    </GeoJsonSource>
    <GeoJsonSource id="coin_location" :data="coinSource">
      <SymbolLayer
        id="coin_location_symbol_layer"
        :style="{
          'icon-image': ['get', 'icon'],
          'icon-size': 1,
          'icon-allow-overlap': true,
          'icon-anchor': 'bottom',
          'icon-opacity': [
            'interpolate',
            ['linear'],
            ['zoom'],
            15,
            0,
            15.5,
            0.5,
            16,
            1,
          ],
        }"
      />
    </GeoJsonSource>
    <q-dialog v-model="showDialog" position="bottom" seamless>
      <div
        class="coin-sonar-trial-dialog-container pt-10"
        :style="{
          backgroundImage: ([STEPS.SELECT_CIRCLE, STEPS.SELECT_RADIUS] as number[]).includes(
            step
          )
            ? 'url(/imgs/bg-coin-sonar.png)'
            : 'none',
        }"
      >
        <Button
          shape="square"
          variant="purple"
          class="absolute left-5 -top-3 z-20"
          v-show="step === STEPS.SELECT_RADIUS"
          @click="step = STEPS.SELECT_CIRCLE"
        >
          <Icon name="arrow-left" />
        </Button>

        <div class="flex flex-col justify-center items-center">
          <div
            v-if="step === STEPS.SELECT_RADIUS || step === STEPS.SELECT_CIRCLE"
            class="text-text-base font-bold text-center p-4 mb-5 w-full"
            :style="{
              background:
                'linear-gradient(270deg, rgba(135, 100, 242, 0.00) -37.14%, rgba(135, 100, 242, 0.30) 46.61%, rgba(141, 118, 211, 0.00) 125.87%)',
            }"
            v-html="
              step === STEPS.SELECT_CIRCLE
                ? t('SONAR_METAL_SELECTED_CIRCLE_TITLE')
                : t('SONAR_METAL_GUI_TITLE')
            "
          ></div>

          <section
            class="w-full h-full text-center"
            v-show="step === STEPS.SELECT_CIRCLE"
          >
            <div
              class="mb-2 text-sm text-center px-10"
              v-html="t('SONAR_METAL_SELECTED_CIRCLE_DESC')"
            ></div>
            <q-carousel
              v-model="slide"
              swipeable
              animated
              padding
              arrows
              class="bg-transparent mb-5"
            >
              <q-carousel-slide
                v-for="c in circleProperties"
                :key="c.id"
                :name="c.id"
              >
                <div class="text-sm p-4 bg-[#04081D] w-full rounded-xl">
                  {{ c.name }}
                </div>
              </q-carousel-slide>
            </q-carousel>
            <Button
              label="Confirm circle"
              class="!w-[210px]"
              @click="handleConfirmCircle"
            />
          </section>
        </div>
        <section
          v-show="step === STEPS.SELECT_RADIUS"
          class="w-full h-[calc(100%-80px)] flex flex-col justify-center items-center"
        >
          <div
            class="mb-4 text-sm text-center px-10"
            v-html="t('SONAR_METAL_GUI_DESC')"
          ></div>
          <q-btn-group class="mb-8 metal-group-prices">
            <div
              class="absolute rounded-lg -inset-1 border border-[#7b37e950]"
            ></div>
            <q-btn
              class="capitalize opacity-50 price"
              :class="{
                active: selectedRadius === r.radius,
              }"
              v-for="r in RADIUS_OPTIONS"
              :key="r.id"
              :label="`${r.radius}m`"
              @click="selectedRadius = r.radius"
            />
          </q-btn-group>
          <Button
            class="mb-5 mx-auto !w-[260px]"
            :title="
              t('BEACON_TRIAL_COIN_SONAR_BTN_USE', { RADIUS: selectedRadius })
            "
            :amount="amount"
            @click="handleUseSonar"
          />
        </section>
      </div>
    </q-dialog>
  </TrialFrame>
</template>
<style lang="scss">
.coin-sonar-trial-dialog-container {
  margin: 0 30px;
  width: 100%;
  height: 350px;
  margin-bottom: 8px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  overflow: visible !important;
  .q-carousel {
    height: unset;
    background-color: unset;
    padding: 0 !important;
    width: 100%;
  }
}
</style>
<style lang="scss" scoped>
.banner {
  text-align: center;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid #b663e9;
  background: linear-gradient(261deg, #341c7c 1.58%, #1a0950 60.32%);
}
.metal-group-prices {
  box-shadow: unset;
  position: relative;
  .price {
    width: 70px;
    height: 42px;
    background: linear-gradient(
      180deg,
      rgba(147, 75, 218, 0.8) 0%,
      #511d85 100%
    );
    &.active {
      opacity: 1;
      border: 1px solid #b57eec;
      background: linear-gradient(
        180deg,
        rgba(147, 75, 218, 0.8) 0%,
        #511d85 100%
      );
    }
  }
}
.trial-frame-container {
  --background: 205, 205, 205;
  --size: 60px;
  --duration: 3s;
  --scale: 3;
  --opacity: 0.4;
  .sonar-radar {
    width: 100%;
    height: 100%;
    animation: spin 4s linear infinite;
    .circle {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: var(--size);
      height: var(--size);
      background: rgba(var(--background), 0.5);
      border-radius: 100%;
      border: 1px solid #ffffff70;
      animation: pulse-shadow var(--duration) calc(2 * var(--duration) / 3)
        linear infinite;
      &::after,
      &::before {
        content: '';
        width: 100%;
        height: 100%;
        position: absolute;
        background: rgba(var(--background), 0.5);
        border-radius: 100%;
      }
      &::after {
        animation: pulse var(--duration) linear infinite;
      }
      &::before {
        animation: pulse var(--duration) calc(var(--duration) / 3) linear
          infinite;
      }
    }

    .shadow {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(
        0deg,
        rgb(var(--background)) 50%,
        transparent 100%
      );
      opacity: 0.6;
      padding: 50%;
      border-radius: 100%;
      clip-path: polygon(50% 0, 100% 0, 100% 50%, 50% 50%);
      z-index: 1;
    }
  }

  .line-vertical {
    z-index: 10;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1px;
    height: 50px;
    background-color: #ffffff;
  }

  .line-horizontal {
    z-index: 10;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 1px;
    background-color: #ffffff;
    &::before {
      position: absolute;
      content: '';
      left: 0;
      top: -5px;
      width: 1px;
      height: 5px;
      background-color: #ffffff;
    }
    &::after {
      position: absolute;
      content: '';
      right: 0;
      top: -5px;
      width: 1px;
      height: 5px;
      background-color: #ffffff;
    }
  }
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: var(--opacity);
    }

    100% {
      transform: scale(var(--scale));
      opacity: 0;
    }
  }

  @keyframes pulse-shadow {
    0% {
      box-shadow: 0 0 0 0 rgba(var(--background), var(--opacity));
    }

    100% {
      box-shadow: 0 0 0 calc((var(--scale) - 1) * var(--size) / 2)
        rgba(var(--background), 0);
    }
  }

  @keyframes spin {
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
