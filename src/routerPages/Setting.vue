<script setup lang="ts">
import { successNotify, getSocials } from '@helpers';
import { useUserStore } from '@stores';
import { copyToClipboard, Loading, Platform } from 'quasar';
import { useI18n } from 'vue-i18n';
import { USER } from '@repositories';
import { debounce } from 'lodash';
import {
  playSFX,
  useAsync,
  useBrandSov,
  useGlobal,
  useTrackData,
} from '@composables';
import type { IUserSetting, IUserLang, ILanguages } from '@types';

const storeUser = useUserStore();

const { push, openDialog } = useMicroRoute();
const {
  user,
  features,
  isSeasonStarting,
  isTriggerSurvey,
  canTriggerEndGameSurvey,
  settings,
  currentCountryCode,
} = storeToRefs(storeUser);
const { t, locale } = useI18n();
const { isLogged } = useGlobal();
const { track } = useTrackData();

const { randomResult, ready } = useBrandSov('safety_hints_pop_up_flag', {
  id: 'safety_hints_pop_up_character',
  track: false,
});

const APP_LANGUAGE_CODE = computed(
  () => process.env.APP_LANGUAGE_CODE as IUserLang
);

const lang = ref<IUserLang>(user.value?.lang || APP_LANGUAGE_CODE.value);
const expand = ref(false);
const BGM = ref(
  Number(user.value?.setting.background_music) > -1
    ? user.value?.setting.background_music
    : 100
);
const SFX = ref(
  Number(user.value?.setting?.sound_effect) > -1
    ? user.value?.setting?.sound_effect
    : 100
);

const languageData = computed(() => {
  if (!settings.value?.supported_languages.length) return {} as ILanguages;
  return settings.value.supported_languages.reduce((acc, lang) => {
    acc[lang] = {
      label: lang === 'en' ? t('LANG_EN') : t('LANG_TRANSLATE'),
      value: lang as IUserLang,
    };
    return acc;
  }, {} as ILanguages);
});

function handleCopyClipboard() {
  if (!user.value) return;
  copyToClipboard(user.value.hunter_id);
  successNotify({
    message: t('COPIED_HUNTER_ID'),
    timeout: 3000,
  });
}

async function updateSoundSetting(
  type: 'background_music' | 'sound_effect',
  value: number
) {
  USER.updateUserSettings({ type, value });
  track(type, {
    volume: value,
  });

  playSFX('button');
}

const debounceUpdate = debounce(updateSoundSetting, 500);

watch(BGM, (val) => {
  storeUser.updateUser({
    setting: {
      ...user.value?.setting,
      background_music: Number(val),
    } as IUserSetting,
  });

  debounceUpdate('background_music', Number(val));
});

watch(SFX, (val) => {
  storeUser.updateUser({
    setting: {
      ...user.value?.setting,
      sound_effect: Number(val),
    } as IUserSetting,
  });

  debounceUpdate('sound_effect', Number(val));
});

const { execute: handleChangeLanguage } = useAsync({
  async fn(l: IUserLang) {
    if (l === user.value?.lang) return;
    Loading.show();
    LocalStorage.set('lang', l);
    await USER.changeLang(l);
    storeUser.updateUser({ lang: l });
    locale.value = l;
    lang.value = l;
    expand.value = false;
    Loading.hide();
  },
});
</script>

<template>
  <div class="fullscreen setting">
    <div
      class="fixed top-0 left-0 z-10 flex items-center justify-center w-full h-20"
    >
      <Button
        class="absolute left-3"
        shape="square"
        variant="secondary"
        @click="
          track('settings_not_loggedin', {
            action: 'settings_back',
          });
          push(-1);
        "
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-extrabold" v-html="t('SETTINGS_HEADING')"></div>
    </div>
    <div
      class="flex flex-col items-center justify-start p-5 setting-content flex-nowrap"
      v-if="user"
    >
      <div
        class="mb-2 text-sm text-center"
        v-html="isLogged ? t('SETTINGS_LOGGED_AS') : t('SETTINGS_NOTLOGGED')"
      ></div>
      <div
        class="mb-1 text-lg font-bold text-center"
        @click="handleCopyClipboard"
        v-html="
          t(
            isLogged ? 'SETTINGS_HUNTERNAME' : 'SETTINGS_NOTLOGGEDIN_GUESTNAME',
            { HUNTER_ID: user.hunter_id }
          )
        "
      ></div>

      <template v-if="isLogged && features?.change_hunter_id">
        <div
          class="mt-1 mb-3 text-sm font-normal text-center"
          v-html="t('SETTINGS_LOGGEDIN_3')"
        ></div>
        <Button
          class="!w-[200px]"
          style="flex: 0 0 48px"
          :label="t('SETTINGS_BUTTON_CHANGEHUNTERID')"
          @click="
            track('settings_loggedin', {
              action: 'settings_change_hunterid',
            });
            openDialog('change_hunter_id');
          "
        />
      </template>

      <template v-if="!isLogged && isSeasonStarting">
        <div class="flex flex-col gap-3 mt-3">
          <Button
            class="!w-[280px]"
            :label="t('SETTINGS_BUTTON_SIGNUPFORANACCOUNT')"
            @click="
              openDialog('signup');
              track('sign_up_buttons', {
                screen: 'menu',
              });
              track('settings_not_loggedin', {
                action: 'settings_signup',
              });
            "
          />

          <Button
            variant="purple"
            class="!w-[280px]"
            :label="t('SETTINGS_BUTTON_IHAVEANACCOUNT')"
            @click="
              openDialog('login');
              track('login_buttons', {
                screen: 'menu',
              });
              track('settings_not_loggedin', {
                action: 'settings_login',
              });
            "
          />
        </div>
      </template>

      <div class="mt-5 w-[280px]">
        <div class="mb-2 font-bold" v-html="t('SETTINGS_BGM')"></div>

        <q-slider class="mb-3 slider-bgm" v-model="BGM" :min="0" :max="100" />
        <div class="mb-2 line"></div>
        <div class="mb-2 font-bold" v-html="t('SETTINGS_SFX')"></div>
        <q-slider class="mb-3 slider-sfx" v-model="SFX" :min="0" :max="100" />
      </div>

      <div
        class="px-5 text-sm text-center"
        v-if="currentCountryCode === 'VN'"
        v-html="t('SETTINGS_VN_URL')"
      ></div>

      <Expansion
        v-model="expand"
        group="language"
        class="language mt-5 !w-[250px]"
        :class="{
          'pointer-events-none':
            Number(settings?.supported_languages.length) < 2,
        }"
        v-if="Number(settings?.supported_languages.length) >= 2"
      >
        <template v-slot:header>
          <div class="flex-1 column flex-nowrap">
            <div
              class="text-[8px] transition-all"
              :class="{
                'mb-2': expand,
              }"
              v-html="t('SETTING_LANGUAGE')"
            ></div>
            <div
              class="text-sm"
              :class="{
                'text-[#00e0ff] font-bold': expand,
              }"
            >
              {{ t(languageData[user.lang].label) }}
            </div>
          </div>
        </template>
        <q-card style="background: transparent">
          <div
            @click="handleChangeLanguage(l.value)"
            class="pt-2 mx-4 mb-3 text-sm"
            style="border-top: 0.5px solid #00e0ff"
            v-for="l in Object.values(languageData).filter(
              (l) => l.value !== user?.lang
            )"
            :key="l.value"
            v-html="l.label"
          ></div>
        </q-card>
      </Expansion>

      <template v-if="isTriggerSurvey || canTriggerEndGameSurvey">
        <div class="flex flex-col items-center justify-center mt-5">
          <div
            class="px-10 mb-3 text-sm text-center"
            v-html="t('SETTINGS_NOTLOGGEDIN_COMPLETESURVEY')"
          ></div>

          <div class="relative">
            <Icon
              v-if="ready"
              class="mx-auto"
              :name="
                randomResult.safety_hints_pop_up_character.getAsset('_timii')
              "
              :size="45"
            />
            <Button
              :label="t('SETTINGS_BUTTON_STARTSURVEY')"
              class="!w-[200px]"
              @click="
                track('settings_loggedin', {
                  action: 'settings_survey',
                });
                openDialog(canTriggerEndGameSurvey ? 'survey' : 'mid_survey');
              "
            />
          </div>
        </div>
      </template>

      <div class="mt-5 text-center">
        <div
          class="px-10 mb-5 text-sm"
          v-html="t('SETTINGS_SOCIALMEDIA')"
        ></div>
        <div class="flex items-center justify-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
            @click="
              track('settings_not_loggedin', {
                action: 'settings_social',
                type: icon,
              })
            "
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>

      <div v-if="isLogged" class="mt-5">
        <Button
          class="!w-[200px]"
          variant="purple"
          :label="t('SETTINGS_BUTTON_LOGOUT')"
          @click="
            track('settings_loggedin', {
              action: 'log_out',
            });
            openDialog('logout');
          "
        />
      </div>

      <div class="flex flex-col items-center justify-center gap-2 mt-5">
        <a
          :href="
            Platform.is.ios
              ? 'https://support.apple.com/en-sg/HT207092'
              : 'https://support.google.com/accounts/answer/6179507?hl=en&ref_topic=7189122'
          "
          target="_blank"
          rel="noopener noreferrer"
          class="text-sm underline text-link"
          v-html="t('SETTINGS_GPS')"
          @click="
            track('settings_not_loggedin', {
              action: 'settings_troubleshootgps',
              link: Platform.is.ios
                ? 'https://support.apple.com/en-sg/HT207092'
                : 'https://support.google.com/accounts/answer/6179507?hl=en&ref_topic=7189122',
            })
          "
        >
        </a>
        <div class="flex items-center justify-center gap-5">
          <div
            class="text-sm underline text-link"
            v-html="t('SETTINGS_TACS')"
            @click="
              track('settings_not_loggedin', {
                action: 'settings_tncs',
              });
              openDialog('tac');
            "
          ></div>
          <div
            class="text-sm underline text-link"
            v-html="t('SETTINGS_FAQS')"
            @click="
              track('settings_not_loggedin', {
                action: 'settings_faqs',
              });
              push('faq');
            "
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.setting {
  background: url('/imgs/bg_dark_blur.png') center center no-repeat;
  background-size: 100% 100%;
  background-color: #000;
  backdrop-filter: blur(5px);
  padding-top: 80px;

  &-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .language {
    width: 70%;
    background: #04081d;
    border-radius: 10px;
    box-shadow: 2px 2px 10px rgba(#04081d, 0.1);
    border: 1px solid #00e0ff;
  }
}
</style>
