<script lang="ts" setup>
import {
  BeaconGUI,
  CoinSonarGUI,
  GuiV2,
  Map,
  Marquee,
  MetalDetectorGUI,
} from '@components';
import {
  playSFX,
  useBAStatus,
  useGlobalTriggerDialog,
  useSocket,
  useSystemMessage,
} from '@composables';
import { SOCKET_EVENT } from '@constants';
import { SilverCoinSelectCircle } from '@routerDialogs';
import { useBAStore, useDialogStore, useMapStore, useUserStore } from '@stores';
import type {
  CircleUpdatedData,
  IBeacon,
  IContentMsg,
  IGridEliminated,
  INotification,
  IUserLang,
} from '@types';
import { debounce, groupBy, last, mapValues, throttle } from 'lodash';
import { api } from 'src/boot/axios';

const storeMap = useMapStore();
const storeUser = useUserStore();
const storeBA = useBAStore();
const storeDialog = useDialogStore();

const { showNotiBaSatus } = useBAStatus();
const { query } = useRoute();
const { user } = storeToRefs(storeUser);
const code_query = ref<any>(query?.code || query?.c);
const state_query = ref<any>(query?.state);
const { push: vuePush } = useRouter();
const {
  showCoinSonarGUI,
  showBeaconGUI,
  showMetalDetectorGUI,
  showSilverCoinSelectCircle,
} = storeToRefs(storeDialog);
const { currentPath, openDialog, push, dialogs } = useMicroRoute();
const { socket } = useSocket();
const { setLocaleMessage, getLocaleMessage } = useI18n();

const { brand_actions } = storeToRefs(useBAStore());

useGlobalTriggerDialog();
useSystemMessage();

const isHomeScreen = computed(
  () => last(currentPath.value.split('/')) === 'home'
);

const activeDialog = computed(() => dialogs.value.some((d) => d.actived));

watchEffect(async () => {
  if (code_query.value) {
    await storeBA.fetchBrandAction();
    push('offer_wall', {
      code_query: code_query.value,
      state_query: state_query.value,
    });
    code_query.value = undefined;
    state_query.value = undefined;
  }
});

watch(
  () => user.value?.resources.crystal,
  () => {
    //fetch when crystal update
    storeUser.fetchListCrystalExpiring();
  }
);

const debouncedFetchSilverCoin = debounce(() => {
  storeMap.fetchSilverCoin();
}, 1000);

const throttledShrinkSfx = throttle(() => {
  playSFX('circle_shrinking');
}, 3000);

onMounted(async () => {
  await nextTick();
  document.addEventListener('click', (event: any) => {
    const classList = Array.from(event.target.classList) as string[];

    if (!classList?.some((cl) => cl === 'ba_tac')) return;

    const ba = brand_actions.value.find((item) =>
      classList.some((cl) => cl === item.unique_id)
    );
    if (ba) {
      openDialog('ba_tac', {
        ba_unique_id: ba.unique_id,
      });
    }
  });
  socket.on(SOCKET_EVENT.UPDATE_MAP, async () => {
    await debouncedFetchSilverCoin();
  });

  socket.on(SOCKET_EVENT.UPDATE_CIRCLE, async (data: CircleUpdatedData) => {
    if (['found', 'verifying', 'forfeited'].includes(data.status as string))
      return storeMap.updateCoinStatus(data);
    storeMap.updateCircle(data).then((success) => {
      if (success && isHomeScreen.value) throttledShrinkSfx();
    });
  });

  socket.on(SOCKET_EVENT.UPDATE_BUILD, () => {
    openDialog('build_update');
  });

  socket.on(SOCKET_EVENT.UPDATE_SETTING, () => {
    storeUser.fetchSetting();
  });

  socket.on(SOCKET_EVENT.ANNOUNCEMENT, (data: INotification) => {
    storeUser.setNotifications(data);
  });

  socket.on(SOCKET_EVENT.GRID_ELIMINATED, (data: IGridEliminated) => {
    const { geohashes = [] } = data;
    storeMap.addFreeEliminated(geohashes);
  });

  socket.on(SOCKET_EVENT.UPDATE_GOLDEN_COIN, () => {
    storeDialog.triggerGoldenSequence = false;
    storeUser.fetchSetting();
  });

  socket.on(SOCKET_EVENT.CONTENT_UPDATE, async (data: IContentMsg) => {
    const lang =
      (LocalStorage.getItem('lang') as IUserLang) ||
      process.env.APP_LANGUAGE_CODE;
    const localeMsg = getLocaleMessage(lang || process.env.APP_LANGUAGE_CODE);

    if (data) {
      const msg =
        data.key && data.lang === lang
          ? { [data.key]: data.value }
          : // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            data[lang] || {};
      setLocaleMessage(lang, { ...localeMsg, ...msg });

      return;
    }

    const { data: contents } = await api.get('/content/all');
    const messages = groupBy(contents, 'lang');

    setLocaleMessage(lang, {
      ...localeMsg,
      ...mapValues(messages, (items) => items[0].data)[lang],
    });
  });

  socket?.on(SOCKET_EVENT.BRAND_ACTION_UPDATE, async () => {
    await storeBA.fetchBrandAction();
    const path = last(currentPath.value.split('/')) || '';
    if (['home', 'menu', 'offer_wall'].includes(path)) showNotiBaSatus();
  });

  socket.on(SOCKET_EVENT.BRAND_ACTION_UPDATED, async () => {
    await storeBA.fetchBrandAction();
    // TO DO: Show Timed Misstion
  });

  socket.on(SOCKET_EVENT.BEACON_SPAWN, (data: IBeacon) => {
    storeUser.updateBeacon(data);
  });

  socket.on(SOCKET_EVENT.CRYSTAL_EXPIRED, () => {
    storeUser.fetchUser();
  });
  socket.on(SOCKET_EVENT.CRYSTAL_UPDATE, () => {
    storeUser.fetchUser();
  });

  // set referral code
  if (query.ref) storeUser.referralUser(String(query.ref));

  vuePush({ query: undefined });
});

const disableMap = computed(() => {
  return (
    showCoinSonarGUI.value ||
    showBeaconGUI.value ||
    showMetalDetectorGUI.value ||
    showSilverCoinSelectCircle.value
  );
});
</script>

<template>
  <div class="fullscreen">
    <Map
      :class="{
        'pointer-events-none': disableMap,
      }"
    />
    <template
      v-if="(!!storeMap.mapIns && !storeMap.loading) || storeMap.fromGrids"
    >
      <CoinSonarGUI v-if="showCoinSonarGUI" />
      <BeaconGUI v-if="showBeaconGUI" />
      <MetalDetectorGUI v-if="showMetalDetectorGUI" />
      <SilverCoinSelectCircle v-if="showSilverCoinSelectCircle" />
      <GuiV2 v-if="!activeDialog" />
      <Marquee v-if="!disableMap" />
      <UnifyInstructor />
    </template>
  </div>
</template>
