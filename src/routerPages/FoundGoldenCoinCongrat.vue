<script lang="ts" setup>
import { Stepper } from '@components';
import { useUserStore } from '@stores';

interface Props {
  serial_number: string;
}

defineProps<Props>();

const storeUser = useUserStore();

const { dataVerification } = storeToRefs(storeUser);
const { t } = useI18n();
const { closeAllDialog, push } = useMicroRoute();

const STEPS = computed(() => [
  t('GOLDENCOIN_PROCESS_1'),
  t('GOLDENCOIN_PROCESS_2'),
]);

function handleCalled() {
  closeAllDialog();
  push('/home');
}
</script>

<template>
  <div class="fit overflow-y-auto overflow-x-hidden bg-[#090422]">
    <div
      class="congrat p-5 text-center sentosa_crystal_coin"
      :class="dataVerification?.type"
    >
      <div
        class="text-2xl font-bold mb-5"
        v-html="t('GOLDENCOIN_FOUND_CONGRATS')"
      ></div>

      <div
        class="text-lg mb-[50vw]"
        v-html="
          t('SENTOSA_GOLDENCOIN_FOUND_YOUFOUND', {
            NAME:
              dataVerification?.coin_name || dataVerification?.coin?.coin_name,
          })
        "
      ></div>

      <div
        class="text-base mb-2"
        v-html="t('GOLDENCOIN_FOUND_WHATSNEXT')"
      ></div>
      <Icon class="mx-auto mb-7" name="arrows_double_down" :size="32" />
      <Stepper :steps="STEPS" text vertical :step="1" class="px-5 mb-10" />
      <div class="pb-10">
        <Button :label="t('GOLDENCOIN_FOUND_PROCEED')" @click="handleCalled" />
      </div>
    </div>
  </div>
</template>
<style lang="scss">
.congrat {
  width: 100%;
  height: 100%;

  &.silver_coin {
    background: url('/imgs/congrat-silver.png'), url('/imgs/rectangle-1.png');
    background-size: 100% auto, 100% 50%;
    background-repeat: no-repeat;
    background-position: top center, top center;
  }
  &.golden_coin {
    background: url('/imgs/congrat-golden.png'), url('/imgs/rectangle-1.png');
    background-size: 100% auto, 100% 50%;
    background-repeat: no-repeat;
    background-position: top center, top center;
  }
  &.sentosa_crystal_coin {
    background: url('/imgs/congrat-sentosa.png'), url('/imgs/rectangle-1.png');
    background-size: 100% auto, 100% 50%;
    background-repeat: no-repeat;
    background-position: top center, top center;
  }
}
</style>
