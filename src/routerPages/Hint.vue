<script lang="ts" setup>
import { useTrackData } from '@composables';
import { INDEX_RARITY } from '@constants';
import { formatName, getSocials } from '@helpers';
import { useUserStore } from '@stores';
import { onClickOutside } from '@vueuse/core';

const storeUser = useUserStore();

const { hints } = storeToRefs(storeUser);
const { push, openDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();

const FILTER_LIST = ['default', 'rarity', 'hint_number'];
const target = ref(null);
const search = ref('');
const showFilter = ref(false);
const filterBy = ref('default');

const filterHints = computed(() => {
  const searchValue = search.value.toLowerCase();
  const filteredHints = hints.value.filter((h) => {
    const content = h.content.toLowerCase();
    const id = h.hint_id.toLowerCase();
    return content.includes(searchValue) || id.includes(searchValue);
  });

  const sortedHints = filteredHints.sort((a, b) => {
    switch (filterBy.value) {
      case 'rarity':
        return INDEX_RARITY[b.rarity] - INDEX_RARITY[a.rarity];
      case 'hint_number':
        return (
          Number(a.hint_id.replace('#', '')) -
          Number(b.hint_id.replace('#', ''))
        );
      default:
        return 0;
    }
  });
  return sortedHints;
});

onClickOutside(target, () => {
  showFilter.value = false;
});

onMounted(async () => {
  await storeUser.fetchHints();
});
</script>
<template>
  <div
    class="hints"
    :class="{
      'bg-hints': !!hints.length,
    }"
  >
    <div class="relative flex justify-center items-center py-5 mb-5">
      <Button
        class="absolute top-2 left-2"
        shape="square"
        variant="secondary"
        @click="
          track('text_hint_inventory', {
            screen: 'text_hint_inventory',
            button: 'back',
          });
          push(-1);
        "
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-bold" v-html="t('MY_HINT_TITLE')"></div>
    </div>
    <template v-if="!hints.length">
      <div
        class="flex flex-col justify-center items-center gap-5 w-full h-full overflow-y-auto"
      >
        <Icon name="hint/rooster" :size="75" />
        <div class="text-lg font-bold" v-html="t('MY_HINT_EMPTY_DESC_1')"></div>
        <div class="text-sm" v-html="t('MY_HINT_EMPTY_DESC_2')"></div>
        <Button
          :label="t('BUTTON_EMPTY_GET_HINTS')"
          class="!w-[210px]"
          @click="
            track('text_hint_inventory', {
              screen: 'text_hint_inventory',
              button: 'go_to_shop',
            });
            track('myhints_empty', {
              action: 'get_hints',
            });
            push('shop');
          "
        />
        <div
          class="text-sm px-20 text-center"
          v-html="t('MY_HINT_EMPTY_SOCIAL')"
        ></div>
        <div class="flex justify-center items-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="overlay-bottom"></div>
      <div class="flex flex-nowrap gap-5 px-5 pb-5">
        <Input
          class="full-width input-search"
          type="search"
          :label="t('MY_HINT_SEARCH')"
          v-model="search"
        />
        <div class="relative" ref="target">
          <Button
            shape="medium-square"
            class="relative"
            @click="showFilter = !showFilter"
          >
            <Icon name="ic_fillter" :size="14" />
          </Button>
          <div
            class="flex flex-col items-end justify-center gap-4 filter-hint overflow-hidden"
            :class="{
              'filter-hint-open': showFilter,
            }"
          >
            <div
              v-for="item in FILTER_LIST"
              :key="item"
              class="text-base relative"
              @click="
                filterBy = item;
                showFilter = false;
              "
            >
              <span
                class="text-sm"
                :class="{
                  'font-bold': filterBy === item,
                }"
              >
                {{ formatName(item) }}
              </span>
              <Icon
                v-if="filterBy === item"
                name="arrow-left"
                :size="15"
                class="absolute top-1 -right-4"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="px-5 pb-[100px] full-height overflow-y-auto">
        <div class="flex flex-col gap-4">
          <div
            :class="`card-${h.rarity}`"
            v-for="h in filterHints"
            :key="h._id"
            @click="
              openDialog('hint_details', {
                hint: h,
              });
              track('text_hint_inventory', {
                screen: 'text_hint_inventory',
                button: 'view_hint',
                hint_id: h.hint_id,
              });
            "
          >
            <div class="text-black relative" :class="`card-${h.rarity}-top`">
              <span
                class="absolute top-[2px] left-[7%]"
                v-html="
                  t('MY_HINT_NUMBER', {
                    NUMBER: h.hint_id,
                  })
                "
              ></span>
            </div>
            <div :class="`card-${h.rarity}-body`" v-html="t(h.content)"></div>
            <div :class="`card-${h.rarity}-bottom`"></div>
          </div>
        </div>
      </div>

      <Button
        class="fixed z-30 bottom-5 right-5"
        @click="
          track('text_hint_inventory', {
            screen: 'text_hint_inventory',
            button: 'go_to_shop',
          });
          track('myhints', {
            action: 'myhints_more',
          });
          push('shop');
        "
      >
        <span class="text-sm mr-5" v-html="t('BUTTON_GET_MORE_HINT')"></span>
        <Icon name="arrow-long" :size="16" />
      </Button>
    </template>
  </div>
</template>
<style lang="scss" scoped>
.hints {
  position: relative;
  background: #15192c;
  display: flex;
  flex-flow: column;
  width: 100%;
  height: 100%;
  .overlay-bottom {
    background: linear-gradient(
      180deg,
      rgba(11, 24, 60, 0) 0%,
      rgba(11, 24, 60, 0.92) 71.99%
    );
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    z-index: 20;
  }
  &.bg-hints {
    background: linear-gradient(
        180.05deg,
        rgba(37, 25, 109, 0.3) 15.77%,
        rgba(29, 65, 137, 0.3) 98.5%
      ),
      url(/imgs/map.png);
    background-size: 100% 100%, cover;
  }
  .card-top {
    height: calc((100vw - 40px) * 0.09);
    width: 100%;
    background-size: 100% 100% !important;
  }
  .card-body {
    padding: 4px 12px;
    border-width: 0px 2px;
    border-style: solid;
    margin-top: -2px;
  }
  .card-bottom {
    height: calc((100vw - 40px) * 0.06);
    width: 100%;
    background-size: 100% 100% !important;
    margin-top: -2px;
  }
  .card {
    &-C {
      &-top {
        @extend .card-top;
        background-image: url(/imgs/hint/top/C.png);
      }
      &-body {
        @extend .card-body;
        background: linear-gradient(180deg, #318b81 0%, #2f7070 100%);
        border-color: #15d1c3;
      }
      &-bottom {
        @extend .card-bottom;
        background: url(/imgs/hint/btm/C.png);
      }
    }
    &-U {
      &-top {
        @extend .card-top;
        background-image: url(/imgs/hint/top/U.png);
      }
      &-body {
        @extend .card-body;
        background: linear-gradient(180deg, #d34882 0%, #be3e72 100%);
        border-color: #ffb6d4;
      }
      &-bottom {
        @extend .card-bottom;
        background: url(/imgs/hint/btm/U.png);
      }
    }
    &-R {
      &-top {
        @extend .card-top;
        background-image: url(/imgs/hint/top/R.png);
      }
      &-body {
        @extend .card-body;
        background: linear-gradient(180deg, #982832 0%, #79242c 100%);
        border-color: #ff555e;
      }
      &-bottom {
        @extend .card-bottom;
        background: url(/imgs/hint/btm/R.png);
      }
    }
    &-SR {
      &-top {
        @extend .card-top;
        background-image: url(/imgs/hint/top/SR.png);
      }
      &-body {
        @extend .card-body;
        background: linear-gradient(180deg, #3c67ae 0%, #384d91 100%);
        border-color: #27aae1;
      }
      &-bottom {
        @extend .card-bottom;
        background: url(/imgs/hint/btm/SR.png);
      }
    }
    &-SSR {
      &-top {
        @extend .card-top;
        background-image: url(/imgs/hint/top/SSR.png);
      }
      &-body {
        @extend .card-body;
        background: linear-gradient(180deg, #533d93 0%, #281657 100%);
        border-color: #9765e0;
      }
      &-bottom {
        @extend .card-bottom;
        background: url(/imgs/hint/btm/SSR.png);
      }
    }
  }
  .filter-hint {
    position: absolute;
    bottom: 0;
    right: 0;
    transform: translateY(calc(100% + 14px));
    width: max-content;
    background: linear-gradient(
      180.05deg,
      rgba(37, 25, 109, 0.9) 15.77%,
      rgba(29, 65, 137, 0.9) 98.5%
    );
    padding: 0;
    border: 1px solid #11d1f9;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
    border-radius: 10px 2px 10px 10px;
    opacity: 0;
    transition: all 0.3s;
    flex-wrap: nowrap;
    z-index: 9999999;
    height: 0px;
    &-open {
      height: 140px;
      transition: all 0.3s;
      opacity: 1;
      padding: 0 30px;
    }
  }
}
</style>
