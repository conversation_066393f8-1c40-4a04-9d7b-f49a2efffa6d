<script setup lang="ts">
import { useUserStore } from '@stores';
import { CarAnimation } from '@components';
import { FULL_DATE_TIME_24H_FORMAT, dateTimeFormat } from '@helpers';
import type { IEventUpdate } from '@types';

interface Props {
  data: IEventUpdate;
}

defineProps<Props>();

const storeUser = useUserStore();

const { seasonCode } = storeToRefs(storeUser);
const { push } = useMicroRoute();
const { t } = useI18n();
</script>
<template>
  <div class="fullscreen update overflow-y-auto overflow-x-hidden">
    <div
      class="absolute top-0 left-0 right-0 flex flex-center text-center relative-position z-20"
    >
      <Button
        class="absolute top-2.5 left-2.5 z-10"
        shape="square"
        variant="secondary"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <p class="mt-5 text-xl font-bold" v-html="t('update')"></p>
    </div>
    <div
      v-if="!data.image"
      class="h-[109vw] relative"
      :class="`season-${seasonCode.toLowerCase()}`"
    >
      <CarAnimation />
    </div>

    <Icon
      v-else
      class="!w-full aspect-video object-cover"
      :name="data.image"
      type="url"
      lazy
    />
    <div class="p-6 relative z-30">
      <p
        class="font-bold text-xl"
        :class="!data.image && '-mt-20'"
        v-html="t(data.title)"
      ></p>
      <p class="my-5 font-normal">
        {{ dateTimeFormat(data.date, FULL_DATE_TIME_24H_FORMAT) }}
      </p>
      <p class="text-base" v-html="t(data.description)"></p>
    </div>
  </div>
</template>
<style scoped lang="scss">
.update {
  background: linear-gradient(180deg, rgba(15, 19, 42, 0) 0%, #0f132a 14.18%);
  .season-sg {
    background-size: 100% 112vw !important;
    background: url('/imgs/kv/sg.png') center -5vw no-repeat;
  }
  .season-vn {
    background-size: 100% 112vw !important;
    background: url('/imgs/kv/vn.png') center -5vw no-repeat;
  }
}
</style>
