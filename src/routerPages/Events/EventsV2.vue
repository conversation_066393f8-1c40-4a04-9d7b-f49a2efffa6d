<script lang="ts" setup>
import {
  FULL_DATE_TIME_24H_FORMAT,
  dateTimeFormat,
  formatName,
} from '@helpers';
import { useI18n } from 'vue-i18n';
import { useUserStore } from '@stores';
import { EVENT } from '@repositories';
import { useAsync } from '@composables';
import { Loading } from 'quasar';
import type { IEventUpdate } from '@types';

const storeUser = useUserStore();

const { events } = storeToRefs(storeUser);
const { push } = useMicroRoute();
const { t } = useI18n();

const { loading, execute: handleAction } = useAsync({
  async fn(event: IEventUpdate) {
    if (!event.seen) {
      await EVENT.seen(event._id);
      await storeUser.fetchEvents();
    }

    switch (event.type) {
      case 'contest':
        Loading.show();
        await storeUser.getContestById(event.metadata.contest_id);
        push('contest');
        Loading.hide();
        break;
      case 'update':
        push('event_update', {
          data: event,
        });
        break;
    }
  },
});

onMounted(async () => {
  await storeUser.fetchEvents();
});
</script>

<template>
  <div class="events">
    <div class="relative flex justify-center items-center py-5 mb-5">
      <Button
        class="absolute top-2 left-2"
        shape="square"
        variant="secondary"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-bold" v-html="t('EVENTS_UPDATE_TITLE')"></div>
      <!-- <Button
        shape="square"
        variant="secondary"
        @click="push('event_setting')"
        class="absolute top-2 right-2"
      >
        <Icon name="settings" />
      </Button> -->
    </div>
    <div
      class="relative full-height overflow-y-auto flex flex-col justify-between z-10"
    >
      <div class="flex flex-col gap-5">
        <div
          class="item"
          :class="{
            'pointer-events-none': loading,
          }"
          v-for="event in events?.event_updates"
          :key="event._id"
          @click="handleAction(event)"
        >
          <div v-if="event.image" class="item-top relative">
            <Icon
              :name="event.image"
              lazy
              type="url"
              class="!w-full h-full"
              style="border-radius: inherit"
            />
            <div
              class="dot absolute top-[10px] right-[10px]"
              v-if="event.image && !event.seen"
            ></div>
          </div>
          <div
            class="item-bottom relative"
            :style="{
              borderRadius: event.image ? '0 0 16px 16px' : '16px',
            }"
          >
            <div
              class="dot absolute top-[10px] right-[10px]"
              v-if="!event.image && !event.seen"
            ></div>
            <div class="item-type" :class="`type-${event.type}`">
              {{ formatName(event.type) }}
            </div>
            <div
              class="text-2xl font-bold text-[#090422] mb-[6px]"
              v-html="t(event.title)"
            ></div>
            <div
              class="text-base text-[#090422] mb-4"
              v-html="t(event.description)"
            ></div>
            <div class="text-sm text-[#090422]">
              {{
                dateTimeFormat(
                  +new Date(event.updated_at),
                  FULL_DATE_TIME_24H_FORMAT
                )
              }}
            </div>
          </div>
        </div>
        <div
          class="mt-10 px-[62px] text-center"
          v-html="t('EVENTS_UPDATE_TEXT')"
        ></div>
      </div>
      <div class="relative">
        <Icon class="half-sqkii" name="half-sqkii" :size="73" />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.events {
  position: relative;
  background: #090422;
  display: flex;
  flex-flow: column;
  width: 100%;
  height: 100%;
  padding-bottom: 20px;
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(
      180deg,
      #1f7c90 -13.42%,
      rgba(145, 36, 254, 0) 100%
    );
  }
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 0 18px;
    &-top {
      width: 100%;
      height: 160px;
      background: #fff;
      border-radius: 12px 12px 0 0;
    }
    &-bottom {
      position: relative;
      padding: 40px 28px 20px;
      width: 100%;
      background-color: #ffffff;
      .item-type {
        position: absolute;
        width: max-content;
        padding: 4px 10px;
        left: -5px;
        top: 8px;

        &.type-contest {
          background: linear-gradient(108deg, #bc18d7 23.38%, #a150f1 87.39%);
          &::before {
            content: ' ';
            display: block;
            background: #a150f1;
            width: 20px;
            height: 100%;
            position: absolute;
            top: 0;
            right: -15px;
            transform: skew(10deg);
            border-radius: 0px 10px 4px 0;
          }
        }
        &.type-update {
          background: linear-gradient(90deg, #2e78e7 -0.34%, #2b99d7 99.59%);
          &::before {
            content: ' ';
            display: block;
            background: #2b99d7;
            width: 20px;
            height: 100%;
            position: absolute;
            top: 0;
            right: -15px;
            transform: skew(10deg);
            border-radius: 0px 10px 4px 0;
          }
        }
        &.type-performance_fix {
          background: linear-gradient(90deg, #5d3ac0 -0.34%, #2f7ae6 99.59%);
          &::before {
            content: ' ';
            display: block;
            background: #2f7ae6;
            width: 20px;
            height: 100%;
            position: absolute;
            top: 0;
            right: -15px;
            transform: skew(10deg);
            border-radius: 0px 10px 4px 0;
          }
        }
      }
    }
  }
  .half-sqkii {
    position: absolute;
    right: 0;
    bottom: 0;
  }
}
</style>
