<script lang="ts" setup>
import { useTick } from '@composables';
import {
  FULL_DATE_TIME_24H_FORMAT,
  convertTime,
  dateTimeFormat,
} from '@helpers';
import { useUserStore } from '@stores';

const storeUser = useUserStore();

const { user, contest, settings, seasonCode } = storeToRefs(storeUser);
const { t } = useI18n();
const { push, openDialog } = useMicroRoute();
const { now } = useTick();

const date = +new Date();

const leaderboards = computed(() => {
  if (!contest.value) return [];
  return contest.value.leaderboard;
});

const userVoted = computed(() => {
  if (!contest.value?.userContest) return '';
  const l = leaderboards.value.find(
    (l) => l.school === contest.value?.userContest?.school
  );
  return l?.school || '';
});

const timeCountdown = computed(() => {
  if (!contest.value) return null;
  const cd = +new Date(contest.value.end_at);
  return convertTime(cd - now.value);
});

const isEnd = computed(() => {
  if (!contest.value) return false;
  return +new Date(contest.value.end_at) <= now.value;
});

const bottomAction = computed(() => {
  if (isEnd.value || !contest.value || !user.value) return null;

  switch (true) {
    case !contest.value.userContest?.voted_at &&
      !!user.value.mobile_number &&
      !user.value.verified_mobile_number_at:
      return {
        text: t('BUTTON_VERIFY_NUMBER_VOTE'),
        action: () => {
          if (!!settings.value?.zalo_daily_quota?.remainingQuota) {
            openDialog('otp');
            return;
          }
          push('offer_wall');
        },
      };
    case !contest.value.userContest?.voted_at &&
      !!user.value.mobile_number &&
      !!user.value.verified_mobile_number_at:
      return {
        text: t('BUTTON_VOTE_NOW'),
        action: () => {
          openDialog('voting_contest', {
            contest,
          });
        },
      };
    case !contest.value.userContest?.voted_at && !user.value.mobile_number:
      return {
        text: t('BUTTON_SIGN_TO_VOTE'),
        action: () => {
          openDialog('signup', {
            contest,
          });
        },
      };
    case !!userVoted.value:
      return {
        text: t('BUTTON_REFER_VOTED'),
        action: () => {
          openDialog('contest_referral');
        },
      };
  }
});
</script>
<template>
  <div
    class="contest flex flex-col flex-nowrap fit"
    v-if="contest"
    :class="[
      `season-${seasonCode.toLowerCase()}`,
      !!bottomAction ? '!pb-[90px]' : '',
    ]"
  >
    <div class="relative flex justify-center items-center py-5">
      <Button
        class="absolute top-2 left-4"
        shape="square"
        variant="secondary"
        @click="push(-1)"
        style="position: absolute; top: 10px; left: 16px"
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-bold">
        {{ t(contest.title) }}
      </div>
      <Button
        v-if="user && !!user.verified_mobile_number_at"
        class="absolute top-2 right-4"
        shape="square"
        @click="openDialog('contest_referral')"
      >
        <Icon name="add-friend" />
      </Button>
    </div>
    <div
      class="h-full overflow-y-auto overflow-x-hidden relative z-10 flex flex-col flex-nowrap items-center px-6 text-center"
    >
      <Icon name="silver-coin" :size="106" class="mb-5" />

      <div class="text-2xl font-bold mb-5">{{ t('CONTEST_SUB_TITLE') }}</div>
      <div
        class="text-base mb-5"
        v-html="
          !!userVoted
            ? t('CONTEST_DESC_1_VOTED')
            : t(contest.description, {
                REWARD: contest?.reward,
              })
        "
      ></div>
      <div
        class="text-base font-bold mb-3"
        v-html="isEnd ? t('CONTEST_DESC_3') : t('CONTEST_DESC_2')"
      ></div>
      <div class="contest-time mb-5" v-if="!isEnd">
        <div>
          <div class="text">
            {{ timeCountdown?.days ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('CONTEST_DAYS') }}
          </div>
        </div>
        <div>
          <div class="text">
            {{ timeCountdown?.hours ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('CONTEST_HOURS') }}
          </div>
        </div>
        <div>
          <div class="text">
            {{ timeCountdown?.minutes ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('CONTEST_MINUTES') }}
          </div>
        </div>
        <div>
          <div class="text">
            {{ timeCountdown?.seconds ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('CONTEST_SECOND') }}
          </div>
        </div>
      </div>
      <div class="opacity-30 bg-white w-full h-[1px] mb-5"></div>

      <div class="text-2xl font-bold mb-2">
        {{ t('CONTEST_LEADERBOARD') }}
      </div>
      <div
        v-if="contest"
        class="text-base mb-5"
        v-html="
          t('CONTEST_LEADERBOARD_DESC', {
            DATE: dateTimeFormat(date, FULL_DATE_TIME_24H_FORMAT),
          })
        "
      ></div>
      <div class="flex flex-col gap-5 full-width pb-5">
        <div
          class="rank flex flex-col flex-nowrap gap-5"
          :class="`rank-${l.rank}`"
          v-for="l in leaderboards"
          :key="l.rank"
        >
          <div
            class="rank-reward flex flex-nowrap items-center gap-1"
            v-if="l.rank <= 3 && l.rank"
          >
            <b>{{ 3 - l.rank + 1 }}</b>
            <Icon name="silver-coin" :size="14" />

            <div class="text-sm">{{ t('VOTED_COIN_DROPPED_NEARBY') }}</div>
          </div>
          <div class="voted" v-if="userVoted === l.school">
            {{ t('VOTED_FOR') }}
          </div>
          <div class="flex flex-nowrap justify-between items-center gap-10">
            <div class="flex flex-nowrap gap-4">
              <div class="text-2xl" style="min-width: 14px">
                {{ l.rank || '-' }}
              </div>
              <div class="text-base text-left font-bold mt-1">
                {{ t(l.school) }}
              </div>
            </div>
            <div class="flex flex-nowrap gap-4">
              <Icon name="vote" />
              <div class="text-sm">{{ l.votes }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom" v-if="bottomAction">
        <Button
          class="mt-6"
          :label="bottomAction.text"
          @click="bottomAction.action"
        />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.contest {
  position: relative;
  background-size: contain !important;
  background-position: top center !important;
  background-repeat: no-repeat !important;

  &.season-sg {
    background: url('/imgs/kv/sg.png') #0f132a;
  }
  &.season-vn {
    background: url('/imgs/kv/vn.png') #0f132a;
  }

  &::before {
    position: absolute;
    content: '';
    background: linear-gradient(180deg, rgba(15, 19, 42, 0) 0%, #0f132a 14.18%);
    width: 100%;
    height: 100%;
    top: 20%;
    left: 0;
  }

  .contest-time {
    display: flex;
    justify-content: space-around;
    width: 100%;
    border-radius: 10px;
    background: linear-gradient(
      180deg,
      rgba(200, 107, 233, 0.8) 0%,
      #bb20c9 100%
    );
    padding: 10px 20px;
    .text {
      font-weight: 800;
      font-size: 44px;
      line-height: 44px;
      width: 60px;
    }
  }

  .rank {
    position: relative;
    padding: 16px 20px;
    background: #5d3ac04d;
    border-radius: 10px;
    &-1 {
      background: linear-gradient(
            180deg,
            rgba(56, 210, 231, 0.3) 0%,
            rgba(22, 90, 115, 0.5) 100%
          )
          padding-box,
        linear-gradient(260.59deg, #341c7c 1.58%, #1a0950 60.32%) padding-box,
        linear-gradient(180deg, #38d2e7 -2.36%, #165a73 77.7%) padding-box,
        linear-gradient(180deg, #38d2e7 -2.36%, #165a73 77.7%) border-box;
      border: 2px solid transparent;
    }
    &-2,
    &-3 {
      background: linear-gradient(260.59deg, #341c7c 1.58%, #1a0950 60.32%)
          padding-box,
        linear-gradient(180deg, #b663e9 -2.36%, #4f46c1 77.7%) padding-box,
        linear-gradient(180deg, #b663e9 -2.36%, #4f46c1 77.7%) border-box;
      border: 2px solid transparent;
    }
  }
  .bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 108px;
    background: linear-gradient(180deg, rgba(16, 20, 43, 0) 0%, #10142b 26.5%);
  }
  .rank-reward {
    position: relative;
    background: linear-gradient(90deg, #2e78e7 -0.34%, #2b99d7 99.59%);
    width: max-content;
    padding-right: 10px;
    padding-left: 4px;
    margin-left: -20px;
    &::before {
      content: ' ';
      display: block;
      background: #2b99d7;
      width: 20px;
      height: 100%;
      position: absolute;
      top: 0;
      right: -15px;
      transform: skew(10deg);
      border-radius: 0px 10px 4px 0;
    }
  }
  .voted {
    position: relative;
    background: linear-gradient(108deg, #bc18d7 23.38%, #a150f1 87.39%);
    width: max-content;
    padding: 0 5px;
    margin-left: -20px;
    margin-top: -10px;
    &::before {
      content: ' ';
      display: block;
      background: #a150f1;
      width: 20px;
      height: 100%;
      position: absolute;
      top: 0;
      right: -15px;
      transform: skew(10deg);
      border-radius: 0px 10px 4px 0;
    }
  }
}
</style>
