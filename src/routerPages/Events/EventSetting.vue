<script lang="ts" setup>
import { useI18n } from 'vue-i18n';

const { push } = useMicroRoute();
const { t } = useI18n();
const initValue = [
  {
    title: 'Silver coin alerts',
    unSelectAll: 'Unsubscribe from all silver coin alerts',
    subList: [
      {
        title: 'General alerts',
        list: [
          {
            title: 'All general alerts',
            checked: true,
            subList: [
              {
                title: 'A new silver coin has been put into play',
                checked: true,
              },
              {
                title:
                  'Winning silver coins can be claimed again<br/><span class="italic opacity-50">This is for hunters who have reached the temporary limit for the number of silver coins submitted for prize redemption, so that others can have a chance too!</span>',
                checked: true,
              },
            ],
          },
        ],
      },
      {
        title: 'Alerts for last viewed silver coin',
        selectAll: {
          title: 'Any updates to the silver coin circle I last viewed',
          checked: true,
        },
        list: [
          {
            title: 'Any updates to the silver coin circle I last viewed',
            checked: true,
            subList: [
              {
                title: 'Any time the silver coin circle has shrunk',
                checked: true,
              },
              {
                title:
                  'Silver coin circle has shrunk to its smallest public size',
                checked: true,
              },
              {
                title: 'Silver coin has been found',
                checked: true,
              },
              {
                title: 'Time is running out for the silver coin',
                checked: true,
              },
            ],
          },
        ],
      },
    ],
  },
  {
    title: 'Gold coin alerts',
    list: [
      {
        title: 'Public gold coin grids have been eliminated',
        checked: true,
      },
      {
        title: 'All public gold coin grids have been eliminated',
        checked: true,
      },
    ],
  },
  {
    title: 'Event alerts',
    list: [
      {
        title: 'A new event has started',
        checked: true,
      },
      {
        title: 'Your vote has dropped out of the top rankings',
        checked: true,
      },
      {
        title: 'The current event is ending soon',
        checked: true,
      },
    ],
  },
  {
    title: 'Brand action alerts',
    list: [
      {
        title:
          'Updates on limited time brand actions (e.g. new brand action, brand action is ending soon)',
        checked: true,
      },
    ],
  },
  {
    title: 'Crystals alerts',

    list: [
      {
        title: 'Alerts for all claimable bonus Crystals',
        checked: true,
        subList: [
          {
            title: 'Daily login bonuses',
            checked: true,
          },
          {
            title: '2h login bonuses',
            checked: true,
          },
        ],
      },
      {
        title: 'Expiring Crystals',
        checked: true,
      },
    ],
  },
  {
    title: 'General alerts',

    list: [
      {
        title:
          'New hunt updates (e.g. a new hunt is starting)<br/><span class="italic opacity-50">We will also notify hunters who have registered their interest via email!</span>',
        checked: true,
      },
    ],
  },
];

const event_setting = ref(initValue);
</script>

<template>
  <div class="events-setting fullscreen column justify-start flex-nowrap">
    <div class="relative flex justify-center items-center py-5">
      <Button
        class="absolute top-2 left-2"
        shape="square"
        variant="secondary"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-bold" v-html="t('Updates Settings')"></div>
    </div>
    <div class="relative overflow-auto" style="flex: 1">
      <Expansion v-for="item in event_setting" :key="item.title">
        <template #header>
          <div class="text-xl font-bold" v-html="item.title"></div>
        </template>
        <div class="px-5">
          <div class="w-full" v-if="!!item.subList">
            <div
              v-for="subItem in item.subList"
              :key="subItem.title"
              class="mb-[25px]"
            >
              <p
                v-html="subItem.title"
                class="font-bold mt-[15px] mb-[15px] text-lg"
              ></p>
              <div v-for="subItem2 in subItem.list" :key="subItem2.title">
                <q-checkbox v-model="subItem2.checked" :label="undefined">
                  <div class="text-sm" v-html="subItem2.title"></div>
                </q-checkbox>
                <div class="pl-6">
                  <q-checkbox
                    v-model="subItem3.checked"
                    :label="undefined"
                    v-for="subItem3 in subItem2.subList"
                    :key="subItem3.title"
                    class="mt-[15px]"
                  >
                    <div class="text-sm" v-html="subItem3.title"></div>
                  </q-checkbox>
                </div>
              </div>
            </div>
          </div>
          <div class="w-full" v-else>
            <div v-for="subItem in item.list" :key="subItem.title">
              <q-checkbox
                v-model="subItem.checked"
                :label="undefined"
                class="mb-[15px]"
              >
                <div class="text-sm" v-html="subItem.title"></div>
              </q-checkbox>
              <div class="pl-6">
                <div v-for="subItem2 in subItem.subList" :key="subItem2.title">
                  <q-checkbox
                    v-model="subItem2.checked"
                    :label="undefined"
                    class="mb-[15px]"
                  >
                    <div class="text-sm" v-html="subItem2.title"></div>
                  </q-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Expansion>
    </div>
  </div>
</template>
<style lang="scss">
.events-setting {
  background: #090422;
  .q-checkbox {
    align-items: flex-start;
    .q-checkbox__inner {
      background: #04081d;
      border: 1px solid #6e60cb;
      border-radius: 4px !important;
      width: 16px;
      height: 16px;
    }
  }

  .q-expansion-item .q-expansion-item__container .q-item__section {
    position: relative !important;
    transform: none !important;
  }
  .q-expansion-item {
    margin-bottom: 40px;
  }
}
</style>
