<script lang="ts" setup>
import { Stepper } from '@components';
import { useTrackData } from '@composables';
import { successNotify } from '@helpers';
import { useUserStore } from '@stores';

interface Props {
  serial_number: string;
}

defineProps<Props>();

const storeUser = useUserStore();

const { dataVerification } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog, push } = useMicroRoute();
const { track } = useTrackData();

const CAPITALAND_BRAND_UNIQUE_ID = [
  'capitaland_geneo',
  'capitaland_science_park',
];

const STEPS = computed(() => {
  if (
    dataVerification.value &&
    CAPITALAND_BRAND_UNIQUE_ID.includes(dataVerification.value.brand_unique_id)
  )
    return [
      t('SILVERCOIN_PROCESS_1'),
      t('SILVERCOIN_PROCESS_2'),
      t('SILVERCOIN_PROCESS_3'),
      // t('SILVERCOIN_PROCESS_4'),
      t('SILVERCOIN_PROCESS_5'),
      t('SILVERCOIN_PROCESS_6'),
      t('SILVERCOIN_PROCESS_7'),
    ];
  return [
    t('SILVERCOIN_PROCESS_1'),
    t('SILVERCOIN_PROCESS_2'),
    t('SILVERCOIN_PROCESS_3'),
    t('SILVERCOIN_PROCESS_4'),
    t('SILVERCOIN_PROCESS_5'),
    t('SILVERCOIN_PROCESS_6'),
    t('SILVERCOIN_PROCESS_7'),
  ];
});

onMounted(() => {
  successNotify({
    message:
      dataVerification.value?.brand_unique_id === 'sqkii' ||
      dataVerification.value?.brand_name === 'Sqkii'
        ? t('SILVERCOIN_ELIGIBILITY')
        : t('SILVERCOIN_CAPITALAND_ELIGIBILITY', {
            BRAND_NAME: dataVerification.value?.brand_name,
          }),
    actions: [
      {
        handler: () => {
          track('silvercoin_winner', {
            action: 'silvercoin_winner_notif_close',
          });
        },
      },
    ],
  });
});
</script>

<template>
  <div class="fit overflow-y-auto overflow-x-hidden bg-[#090422]">
    <div class="congrat p-5 text-center" :class="dataVerification?.type">
      <div
        class="text-2xl font-bold mb-5"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_CONGRATS')"
      ></div>

      <div
        class="text-lg mb-3"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_YOUFOUND')"
      ></div>

      <div
        class="text-xl font-bold mb-[50vw]"
        v-if="dataVerification?.type === 'capitaland_geneo'"
      >
        {{ dataVerification?.coin_name }}
      </div>
      <div class="text-xl font-bold mb-[50vw]" v-else>
        {{ dataVerification?.brand_name }}
        {{
          t('SILVERCOINPOPUP_COIN_1', {
            ORDER: dataVerification?.coin_number,
            BRAND_NAME:
              `${dataVerification?.prefix || ''} ${
                dataVerification?.brand_name
              }` || 'Silver',
          })
        }}
      </div>

      <div
        class="text-base mb-2"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_WHATSNEXT')"
      ></div>

      <div
        class="text-sm px-12 mb-5"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_DESC')"
      ></div>
      <Icon class="mx-auto mb-7" name="arrows_double_down" :size="32" />
      <Stepper :steps="STEPS" text vertical :step="1" class="px-5 mb-10" />
      <div class="pb-10">
        <Button
          :label="t('SILVERCOIN_BUTTON_PROCEED')"
          @click="
            openDialog('enter_verify_form', {
              serial_number,
            });
            track('silvercoin_winner', {
              action: 'silvercoin_winner_proceed',
            });
            push(-1);
          "
        />
      </div>
    </div>
  </div>
</template>
<style lang="scss">
.congrat {
  width: 100%;
  height: 100%;

  &.silver_coin,
  &.capitaland_science_park {
    background: url('/imgs/congrat-silver.png'), url('/imgs/rectangle-1.png');
    background-size: 100% auto, 100% 50%;
    background-repeat: no-repeat;
    background-position: top center, top center;
  }
  &.capitaland_geneo {
    background: url('/imgs/congrat-lyden-woods.png'),
      url('/imgs/rectangle-1.png');
    background-size: 100% auto, 100% 50%;
    background-repeat: no-repeat;
    background-position: top center, top center;
  }
  &.golden_coin {
    background: url('/imgs/congrat-golden.png'), url('/imgs/rectangle-1.png');
    background-size: 100% auto, 100% 50%;
    background-repeat: no-repeat;
    background-position: top center, top center;
  }
}
</style>
