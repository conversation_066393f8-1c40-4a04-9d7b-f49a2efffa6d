<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { TrialFrame } from '@components';
import { delay, useGlobalInstructor, useMapHelpers } from '@composables';
import {
  BRAND_SOV,
  ONGOING_COIN_FREE_FILL_LAYER,
  ONGOING_COIN_FREE_LINE_GLOW_LAYER,
  ONGOING_COIN_FREE_LINE_LAYER,
  ONGOING_COIN_PAID_FILL_LAYER,
  ONGOING_COIN_PAID_LINE_GLOW_LAYER,
  ONGOING_COIN_PAID_LINE_LAYER,
} from '@constants';
import { useMapStore } from '@stores';
import {
  FillLayer,
  GeoJsonSource,
  LineLayer,
  LngLatBoundsLike,
  MapCreationStatus,
  Marker,
} from 'vue3-maplibre-gl';
import circle from '@turf/circle';
import bbox from '@turf/bbox';
import { point } from '@turf/helpers';

type Step = (typeof TRIAL_STEPS)[keyof typeof TRIAL_STEPS];
type ShrinkType = 'smaller_public' | 'no_change' | 'smaller_private';

interface FitBoundsOptions {
  lng: number;
  lat: number;
  radius: number;
  padding?: number;
  duration?: number;
  animate?: boolean;
}

interface Circle {
  id: string;
  name: string;
  lng: number;
  lat: number;
  radius: number;
  width: number;
}

interface BottomMessage {
  id: number;
  type: ShrinkType;
  this_use: number;
  smallest: number;
}

interface CircleConfig {
  offsetMultiplier: number;
  radiusMultiplier: number;
  name: string;
}

const TRIAL_STEPS = {
  SELECT_CIRCLE: 1,
  SHRINK: 2,
} as const;

const SHRINK_MESSAGE_KEYS: Record<ShrinkType | 'default', string> = {
  smaller_public: 'SHRINKINGCIRCLE_SHRANKED_AHEAD',
  smaller_private: 'SHRINKINGCIRCLE_SHRANKED_AHEAD_1',
  no_change: 'SHRINKINGCIRCLE_SHRANKED_AHEAD_2',
  default: 'SHRINKINGCIRCLE_SHRANKED_AHEAD_2',
} as const;

const MAP_CONFIG = {
  OFFSET: 0.0005, // ~0.0005 degrees is ~50m
  BASE_RADIUS: 50,
  FIT_BOUNDS_PADDING: 25,
  FIT_BOUNDS_MULTIPLIER: 1.2,
  CIRCLE_WIDTH_SELECTED: 3,
  CIRCLE_WIDTH_DEFAULT: 1,
  DASH_ARRAY: [2, 2] as number[],
  DASH_LINE_WIDTH: 3,
} as const;

const CIRCLE_CONFIGS: CircleConfig[] = [
  {
    offsetMultiplier: -0.5,
    radiusMultiplier: 2,
    name: 'Silver Coin #1',
  },
  {
    offsetMultiplier: 0.5,
    radiusMultiplier: 2.5,
    name: 'Silver Coin #2',
  },
] as const;

const TIMING_CONFIG = {
  MAP_LOAD_DELAY: 500,
  DIALOG_SHOW_DELAY: 1000,
} as const;

const SHRINK_DATA: BottomMessage[] = [
  {
    id: 1,
    type: 'smaller_public',
    this_use: 20,
    smallest: 20,
  },
  {
    id: 2,
    type: 'smaller_private',
    this_use: 5,
    smallest: 25,
  },
  {
    id: 3,
    type: 'no_change',
    this_use: 10,
    smallest: 25,
  },
] as const;

const storeMap = useMapStore();

const { lastLocations } = storeToRefs(storeMap);
const { makeSource, transformPosition } = useMapHelpers();
const { openDialog, closeDialog } = useMicroRoute();
const { openUnifyInstructor } = useGlobalInstructor();
const { t } = useI18n();

const trialFrameRef = ref<InstanceType<typeof TrialFrame>>();
const showDialog = ref(false);
const slide = ref('circle_1');
const step = ref<Step>(TRIAL_STEPS.SELECT_CIRCLE);
const countingShrink = ref(0);
const bottomMessage = ref<BottomMessage | undefined>(undefined);
const showPaidCircle = ref(false);

const mapInstance = computed(() => trialFrameRef.value?.mapInstance);
const mapStatus = computed(() => trialFrameRef.value?.mapStatus);

const circleProperties = computed<Circle[]>(() => {
  const [lng, lat] = lastLocations.value;

  return CIRCLE_CONFIGS.map((config, index) => {
    const circleId = `circle_${index + 1}`;
    const isSelected = circleId === slide.value;

    return {
      id: circleId,
      name: config.name,
      lng: lng + MAP_CONFIG.OFFSET * config.offsetMultiplier,
      lat: lat + MAP_CONFIG.OFFSET * (index === 0 ? -1 : 2),
      radius: MAP_CONFIG.BASE_RADIUS * config.radiusMultiplier,
      width: isSelected
        ? MAP_CONFIG.CIRCLE_WIDTH_SELECTED
        : MAP_CONFIG.CIRCLE_WIDTH_DEFAULT,
    };
  });
});

const circleSource = computed(() => {
  const createCircleSource = (circles: Circle[]) => {
    return makeSource(
      circles.map((c) => {
        return circle([c.lng, c.lat], c.radius, {
          units: 'meters',
          properties: c,
        });
      })
    );
  };

  const filteredCircles =
    step.value === TRIAL_STEPS.SHRINK
      ? circleProperties.value.filter((c) => c.id === slide.value)
      : circleProperties.value;

  return createCircleSource(filteredCircles);
});

const circlePaidSource = computed(() => {
  const createPaidCircleSource = (circles: Circle[]) => {
    if (!bottomMessage.value) return makeSource([]);

    return makeSource(
      circles.map((c) => {
        if (!bottomMessage.value) return [];
        const radius = Math.max(0, c.radius - bottomMessage.value.smallest);
        return circle([c.lng, c.lat], radius, {
          units: 'meters',
          properties: c,
        });
      })
    );
  };

  return createPaidCircleSource(
    circleProperties.value.filter((c) => c.id === slide.value)
  );
});

const markerLngLat = computed(() => {
  const selectedCircle = circleProperties.value.find(
    (c) => c.id === slide.value
  );
  if (!selectedCircle || !bottomMessage.value)
    return [0, 0] as [number, number];
  const radius = selectedCircle.radius - (bottomMessage.value?.smallest || 0);
  const geo = transformPosition(
    point([selectedCircle.lng, selectedCircle.lat]),
    radius
  );
  const [lng, lat] = geo.geometry.coordinates;
  return [lng, lat] as [number, number];
});

const circlePaidDashSource = computed(() => {
  const createDashCircleSource = (circles: Circle[]) => {
    if (!bottomMessage.value || countingShrink.value < 3) return makeSource([]);

    return makeSource(
      circles.map((c) => {
        if (!bottomMessage.value) return [];
        const radius = Math.max(0, c.radius - bottomMessage.value.this_use);
        return circle([c.lng, c.lat], radius, {
          units: 'meters',
          properties: c,
        });
      })
    );
  };

  return createDashCircleSource(
    circleProperties.value.filter((c) => c.id === slide.value)
  );
});

const fitBounds = (options: FitBoundsOptions): void => {
  const {
    lng,
    lat,
    radius,
    padding = 50,
    duration = 500,
    animate = true,
  } = options;
  const _circle = circle([lng, lat], radius, {
    units: 'meters',
  });
  const box = bbox(_circle) as LngLatBoundsLike;

  mapInstance.value?.fitBounds(box, {
    padding,
    duration,
    animate,
  });
};

const fitBoundsWithCircle = (): void => {
  const circle = circleProperties.value.find((c) => c.id === slide.value);
  if (!circle) return;
  fitBounds({
    lng: circle.lng,
    lat: circle.lat,
    radius: circle.radius * 1.2,
  });
};

watch(slide, fitBoundsWithCircle);

const getNextShrinkData = (index: number): BottomMessage | undefined => {
  return SHRINK_DATA[index] || undefined;
};

const getShrinkTranslationKey = (type: ShrinkType): string => {
  return SHRINK_MESSAGE_KEYS[type] || SHRINK_MESSAGE_KEYS.default;
};

const handleConfirmCircle = (): void => {
  step.value = TRIAL_STEPS.SHRINK;
};

const handleMapClick = (): void => {
  if (step.value === TRIAL_STEPS.SELECT_CIRCLE) return;

  openDialog('silver_coin_shrink_trial', {
    bottomMessage,
    radius: MAP_CONFIG.BASE_RADIUS,
    onClose: () => {
      closeDialog('silver_coin_shrink_trial');
      countingShrink.value = Math.min(countingShrink.value + 1, 3);

      const nextShrinkData = getNextShrinkData(countingShrink.value - 1);
      bottomMessage.value = nextShrinkData;

      if (!nextShrinkData) return;

      const selectedCircle = circleProperties.value.find(
        (c) => c.id === slide.value
      );
      if (!selectedCircle) return;
      fitBounds({
        lng: selectedCircle.lng,
        lat: selectedCircle.lat,
        radius: (selectedCircle.radius - nextShrinkData.smallest) * 1.2,
      });

      const { type, this_use, smallest } = nextShrinkData;
      const translationKey = getShrinkTranslationKey(type);

      openUnifyInstructor('nancii', {
        tag: `/sov/brand_tag/${BRAND_SOV.DBS}`,
        sequences: [
          {
            backdropCss: true,
            message: t(translationKey, {
              USED: this_use,
              SMALLEST: smallest,
            }),
          },
        ],
      });
      showPaidCircle.value = true;
    },
  });
};

watch(mapStatus, async (val) => {
  if (val === MapCreationStatus.Loaded) {
    await delay(TIMING_CONFIG.MAP_LOAD_DELAY);
    showDialog.value = true;
    await delay(TIMING_CONFIG.DIALOG_SHOW_DELAY);
    // fitBounds(MAP_CONFIG.BASE_RADIUS);
    fitBoundsWithCircle();
  }
});
</script>
<template>
  <TrialFrame ref="trialFrameRef">
    <div
      v-if="step === TRIAL_STEPS.SELECT_CIRCLE"
      class="banner absolute w-[70%] top-12 left-1/2 -translate-x-1/2 z-10"
      v-html="t('SILVERCOIN_BANNER_TEXT')"
    ></div>
    <GeoJsonSource id="trial_circle" :data="circleSource">
      <FillLayer
        id="trial_fill_layer"
        :style="{
          ...ONGOING_COIN_FREE_FILL_LAYER,
        }"
        @click="handleMapClick"
      />
      <LineLayer
        id="trial_line_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_LAYER,
          'line-width': ['get', 'width'],
        }"
      />
      <LineLayer
        id="trial_line_glow_layer"
        :style="{
          ...ONGOING_COIN_FREE_LINE_GLOW_LAYER,
        }"
      />
    </GeoJsonSource>
    <GeoJsonSource id="paid_circle" :data="circlePaidSource">
      <FillLayer
        id="paid_fill_layer"
        :style="{
          ...ONGOING_COIN_PAID_FILL_LAYER,
        }"
      />
      <LineLayer
        id="paid_line_layer"
        :style="{
          ...ONGOING_COIN_PAID_LINE_LAYER,
        }"
      />
      <LineLayer
        id="paid_line_glow_layer"
        :style="{
          ...ONGOING_COIN_PAID_LINE_GLOW_LAYER,
        }"
      />
    </GeoJsonSource>
    <Marker :lnglat="markerLngLat" anchor="bottom">
      <div
        class="paid-popup absolute left-1/2 -translate-x-1/2 -top-1 transition-all duration-300 ease-in-out"
        v-html="t('POPUP_COIN_PAID')"
      ></div>
    </Marker>
    <GeoJsonSource id="paid_dash_circle" :data="circlePaidDashSource">
      <FillLayer
        id="paid_dash_fill_layer"
        :style="{
          ...ONGOING_COIN_PAID_FILL_LAYER,
        }"
      />
      <LineLayer
        id="paid_dash_line_layer"
        :style="{
          ...ONGOING_COIN_PAID_LINE_LAYER,
          'line-dasharray': MAP_CONFIG.DASH_ARRAY,
          'line-width': MAP_CONFIG.DASH_LINE_WIDTH,
        }"
      />
      <LineLayer
        id="paid_dash_line_glow_layer"
        :style="{
          ...ONGOING_COIN_PAID_LINE_GLOW_LAYER,
          'line-dasharray': MAP_CONFIG.DASH_ARRAY,
          'line-width': MAP_CONFIG.DASH_LINE_WIDTH,
        }"
      />
    </GeoJsonSource>
    <q-dialog v-model="showDialog" position="bottom" seamless>
      <div
        v-show="step === TRIAL_STEPS.SELECT_CIRCLE"
        class="silver-coin-trial-dialog-container"
        :style="{
          backgroundImage:
            step === TRIAL_STEPS.SELECT_CIRCLE
              ? 'url(/imgs/bg-coin-sonar.png)'
              : 'none',
        }"
      >
        <div class="w-full h-full flex flex-col justify-center items-center">
          <q-carousel
            v-model="slide"
            swipeable
            animated
            padding
            arrows
            class="bg-transparent mb-5"
          >
            <q-carousel-slide
              v-for="circle in circleProperties"
              :key="circle.id"
              :name="circle.id"
            >
              <div class="text-sm p-4 bg-[#04081D] w-full rounded-xl">
                {{ circle.name }}
              </div>
            </q-carousel-slide>
          </q-carousel>
          <Button
            :label="t('TRIAL_CONFIRM_CIRCLE_BTN')"
            class="!w-[210px]"
            @click="handleConfirmCircle"
          />
        </div>
      </div>
    </q-dialog>
  </TrialFrame>
</template>
<style lang="scss" scoped>
.paid-popup {
  font-size: 14px;
  padding: 4px 8px;
  text-align: center;
  border-radius: 4px;
  min-width: 85px;
  background-image: url('/imgs/map/paid-popup.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-color: unset;
}

.banner {
  text-align: center;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid #b663e9;
  background: linear-gradient(261deg, #341c7c 1.58%, #1a0950 60.32%);
}
</style>
<style lang="scss">
.silver-coin-trial-dialog-container {
  margin: 0 30px;
  width: 100%;
  height: 250px;
  margin-bottom: 8px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  overflow: visible !important;
  .q-carousel {
    height: unset;
    background-color: unset;
    padding: 0 !important;
    width: 100%;
  }
}
</style>
