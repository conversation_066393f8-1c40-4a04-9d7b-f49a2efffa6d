<script lang="ts" setup>
import { TrialFrame } from '@components';
import { useTick } from '@composables';
import { timeCountDown } from '@helpers';
import dayjs from 'dayjs';

interface BottomMessage {
  id: number;
  type: 'smaller_public' | 'no_change' | 'smaller_private';
  this_use: number;
  smallest: number;
}

interface Props {
  radius: number;
  bottomMessage?: BottomMessage;
}

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();
defineProps<Props>();

const { t } = useI18n();
const { now } = useTick();
const { closeDialog } = useMicroRoute();

const TIME = dayjs().add(1, 'hour').toISOString();

const nextShrinkCountdown = computed(() => {
  const cd = +new Date(TIME) - now.value;
  return timeCountDown(cd);
});

function handleUseShrink() {
  emits('close');
}
</script>
<template>
  <TrialFrame hidden-map>
    <Dialog @close="closeDialog('silver_coin_shrink_trial')">
      <template #header>
        <div v-html="t('BEACON_TRIAL_SILVER_SHRINK_HEADER')"></div>
      </template>
      <div class="relative px-2 -mt-5 text-center">
        <div class="silver-coin">
          <div class="absolute top-8">
            <div
              class="mb-1 text-sm"
              v-html="t('BEACON_TRIAL_SILVER_SHRINK_DESC_1')"
            ></div>
            <div
              class="mb-5 text-2xl font-bold"
              v-html="t('BEACON_TRIAL_SILVER_SHRINK_DESC_2')"
            ></div>
          </div>
          <Icon class="mt-12 size-[40%]" name="silver-coin" />
        </div>
        <div
          v-if="!bottomMessage"
          class="bg-[#091a3b] rounded p-3 flex flex-nowrap justify-center items-center gap-2 mb-5 -mt-10"
        >
          <div
            class="text-sm"
            v-html="
              t('BEACON_TRIAL_SILVER_SHRINK_DESC_3', {
                TIME: nextShrinkCountdown,
              })
            "
          ></div>
          <Icon name="question-mark" />
        </div>
        <div v-if="bottomMessage">
          <div
            class="bg-[#2E3B54] rounded-t-md p-3 flex flex-nowrap justify-center items-center gap-2 -mt-10"
          >
            <div
              class="text-sm"
              v-html="
                t('SILVERCOINPOPUP_COIN_TIMER', {
                  COUNTDOWN: nextShrinkCountdown?.toLowerCase(),
                })
              "
            ></div>
            <Icon name="question-mark" />
          </div>
          <div
            class="bg-[#091A3B] rounded-b-md p-3 flex flex-nowrap justify-center items-center gap-2 mb-5"
          >
            <div
              v-html="
                t('SILVERCOINPOPUP_COIN_PRIVATELY', {
                  DISTANCE: bottomMessage.smallest,
                })
              "
            ></div>
          </div>
        </div>
        <SilverCoinPowerUpSelection class="mb-5 pointer-events-none" />
        <Button
          class="mb-5 mx-auto !w-[230px]"
          :title="t('BEACON_TRIAL_SILVER_SHRINK_BTN_USE')"
          :amount="100"
          @click="handleUseShrink"
        />
        <div
          class="text-sm"
          v-html="t('BEACON_TRIAL_SILVER_SHRINK_DESC_5')"
        ></div>
      </div>
    </Dialog>
  </TrialFrame>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
}
</style>
