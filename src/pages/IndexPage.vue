<script setup lang="ts">
import { useMicroStore, useTrackData, useTicker } from '@composables';
import { dialogs, routes } from '@microRouter';
import type { IGlobeState } from '@types';
import type { MicroDialog } from 'vue-micro-route';

useTicker();

const routesGenerated = {
  routes,
  dialogs: dialogs as unknown as MicroDialog[],
};

useMicroStore<IGlobeState>({
  show: false,
  brand_filter: {
    new: 0,
    pending: 0,
  },
});

const { track } = useTrackData();

function trackClickClientLink(e: MouseEvent) {
  const target = e.target as HTMLElement;
  if (target.tagName === 'A' && target.classList.contains('client-link')) {
    track('click_client_link', {
      url: target.getAttribute('href'),
    });
  }
}

function trackClickUXLink(e: MouseEvent) {
  const target = e.target as HTMLElement;
  if (target.classList.contains('ux-link')) {
    const action = target.getAttribute('data-action');
    const type = target.getAttribute('data-type');
    if (!action || !type) return;
    track(type, {
      action,
    });
  }
}

onMounted(() => {
  document.body.addEventListener('click', trackClickClientLink);
  document.body.addEventListener('click', trackClickUXLink);
});

onUnmounted(() => {
  document.body.removeEventListener('click', trackClickClientLink);
  document.body.removeEventListener('click', trackClickUXLink);
});
</script>
<template>
  <MicroRouterView
    default-path="/home"
    :routes-getter="routesGenerated.routes"
    :dialogs="routesGenerated.dialogs"
    default-bgm=""
  />
</template>
