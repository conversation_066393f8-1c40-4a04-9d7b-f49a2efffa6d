<script lang="ts" setup>
import type { IActivatingTimeMission } from '@types';

interface Props {
  pendingMission: IActivatingTimeMission;
}
defineProps<Props>();
const { t } = useI18n();
const { openDialog } = useMicroRoute();
</script>
<template>
  <div class="pending-card flex flex-col justify-center items-center gap-2">
    <div class="flex items-center gap-2 w-full">
      <div
        class="text-left text-sm flex-1"
        v-html="t(pendingMission.mission.description)"
      ></div>

      <Button
        size="small"
        variant="purple"
        @click="
          openDialog('mission_pending', {
            pendingMission,
          })
        "
        >{{ t('PENDING') }}
        <Icon class="ml-1" name="question-mark-p" :size="20"
      /></Button>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.pending-card {
  width: 100%;
  padding: 12px;
  border-radius: 10px;
  border: 2px solid #51178c;
  background: #320b5b;
}
</style>
