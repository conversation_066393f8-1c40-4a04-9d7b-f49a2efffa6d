<script setup lang="ts">
import { useBAStore, useMapStore } from '@stores';

const mapStore = useMapStore();
const baStore = useBAStore();
const { devTools, lastLocations, mapIns } = storeToRefs(mapStore);
const { push } = useMicroRoute();

const { timeMissionData } = storeToRefs(baStore);

function turnOffPickLocationMode() {
  if (devTools.value.pickLocationMode && devTools.value.fakeGps) {
    devTools.value.location = [...devTools.value.targetLocation];
    lastLocations.value = [...devTools.value.targetLocation];
  }
  devTools.value.pickLocationMode = false;
}

const nearestValidOutlet = computed(() => {
  if (timeMissionData.value?.type !== 'location_based') return null;
  return mapStore.getNearestOutlet(
    timeMissionData.value.brand_unique_id,
    timeMissionData.value.data?.location_based?.sv_client
  );
});

function flyToNearestOutlet() {
  if (!nearestValidOutlet.value) return;
  if (devTools.value.fakeGps) {
    devTools.value.location = [
      nearestValidOutlet.value.location.lng,
      nearestValidOutlet.value.location.lat,
    ];
    lastLocations.value = [
      nearestValidOutlet.value.location.lng,
      nearestValidOutlet.value.location.lat,
    ];
  }
  mapIns.value?.flyTo({
    center: [
      nearestValidOutlet.value.location.lng,
      nearestValidOutlet.value.location.lat,
    ],
    zoom: 14,
    duration: 2500,
    minZoom: 8,
  });
}
</script>

<template>
  <Button shape="square" @click="push('dev_tool')"> 🐞 </Button>
  <Button
    shape="square"
    v-if="devTools.pickLocationMode"
    @click="turnOffPickLocationMode"
  >
    <Icon name="top-up-success" />
  </Button>
  <Button
    shape="square"
    v-if="nearestValidOutlet"
    class="relative"
    @click="flyToNearestOutlet"
  >
    <Icon name="crystal" />
    <p
      class="absolute bottom-0 text-[8px] text-nowrap whitespace-nowrap text-center translate-y-full"
    >
      Nearest Store
    </p>
  </Button>
</template>
