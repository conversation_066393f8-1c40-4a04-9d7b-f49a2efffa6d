<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useUserStore } from '@stores';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { push } = useMicroRoute();
const { settings } = storeToRefs(useUserStore());

const showFAQ = (event: any) => {
  if (event.target.id === 'faq_h') {
    push('faq');
    emits('close');
  }
};

onMounted(() => {
  document.addEventListener('click', showFAQ);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', showFAQ);
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div class="flex flex-nowrap items-center gap-5">
        <div>
          <Icon
            name="logo_htm"
            class="pointer-none mt-1 targetAnim"
            style="height: 44px; width: auto"
          />
        </div>
      </div>
    </template>
    <div class="text-center">
      <div
        class="text-sm targetAnim mb-[3px]"
        v-html="t('holding_content_1')"
      ></div>
      <div class="flex items-end justify-center">
        <Icon
          name="coin/gold-coin"
          :size="65"
          style="z-index: 1; margin-right: -45px"
          v-if="settings?.holding_page_state.includes('gold')"
        />
        <Icon name="coin/sqkii" :size="127" style="z-index: 2" />
        <Icon
          name="coin/silver-coin"
          :size="65"
          style="z-index: 3; margin-left: -20px"
        />
      </div>
      <div
        class="text-sm targetAnim my-3"
        v-html="
          t(
            settings?.holding_page_state.includes('ongoing')
              ? 'holding_content_2_ongoing'
              : 'holding_content_2'
          )
        "
      ></div>
      <a
        :href="settings.holding_page_target_url"
        style="text-decoration: none; color: white"
        class="mx-auto w-max flex flex-center"
        v-if="settings?.holding_page_state.includes('ongoing')"
      >
        <Button @click="emits('close')">
          {{ t('LET_GO') }}
        </Button>
      </a>
      <div
        class="text-sm targetAnim my-4"
        v-html="
          t('holding_content_3', {
            faq: `<span style='color:#58E9E0' id='faq_h'>${t(
              'SETTINGS_FAQS'
            )}</span>`,
          })
        "
      ></div>
    </div>
  </Dialog>
</template>
