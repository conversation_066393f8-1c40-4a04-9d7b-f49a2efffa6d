<script setup lang="ts">
import { useTrackData } from '@composables';
import { useWindowSize } from '@vueuse/core';
import QrcodeVue from 'qrcode.vue';
const { width } = useWindowSize();

const { t } = useI18n();
const { track } = useTrackData();

const URL = computed(() => process.env.APP_END_POINT);

track('page_blocker');
</script>
<template>
  <div class="fixed top-0 left-0 right-0 bottom-0 blocker flex flex-center">
    <img
      :src="`/imgs/kv/dbs_blocker${width > 1024 ? '' : '_tablet'}.png`"
      class="h-full max-w-max min-w-[100%]"
    />
    <section v-show="width > 1024">
      <Icon
        name="kv/dbs-logo"
        class="absolute left-[44px] bottom-[40px] w-[380px] h-auto"
      />

      <div class="absolute right-[30px] bottom-[40px] flex items-center gap-6">
        <p
          style="border-radius: 8px; background: rgba(0, 0, 0, 0.5)"
          class="text-xl px-4 py-[18px]"
        >
          <b>The easiest #HuntTheMouse yet!</b><br />
          <span>Scan here to hunt on your mobile.</span>
        </p>
        <div class="bg-[#EF072A] rounded-3xl p-2 border-4 border-white">
          <QrcodeVue
            class="rounded-2xl bg-white p-2"
            :value="URL"
            :size="123"
            level="H"
          />
        </div>
      </div>
    </section>
    <section v-show="width <= 1024">
      <Icon
        name="kv/dbs-logo"
        class="absolute left-[50%] -translate-x-1/2 top-[50px] w-[370px] h-auto"
      />

      <div
        class="absolute left-[50%] -translate-x-1/2 bottom-[40px] w-full flex flex-center gap-6 flex-nowrap"
      >
        <div class="bg-[#EF072A] rounded-3xl p-2 border-4 border-white">
          <QrcodeVue
            class="rounded-2xl bg-white p-2"
            :value="URL"
            :size="123"
            level="H"
          />
        </div>
        <p
          style="border-radius: 8px; background: rgba(0, 0, 0, 0.5)"
          class="text-xl px-4 py-[18px]"
        >
          <b>The easiest #HuntTheMouse yet!</b><br />
          <span>Scan here to hunt on your mobile.</span>
        </p>
      </div>
    </section>
    <!-- <section v-show="width > 1024">
      <div class="desktop-background"></div>
      <div class="desktop-middle-ground"></div>
      <div class="desktop-far-ground"></div>
      <Icon
        name="sentosa-blocker/desktop-beach-objects"
        class="!w-[65vw] absolute left-[15vw] top-[50vh] z-10"
      />
      <Icon
        name="sentosa-blocker/desktop-characters"
        class="!w-[60vw] absolute top-[30vh] left-1/2 -translate-x-1/2 z-20"
      />
      <Icon
        name="sentosa-blocker/desktop-foreground"
        class="!w-full absolute bottom-0 z-[9999]"
      />
      <div
        class="left-1/2 -translate-x-1/2 w-full absolute bottom-0 z-50 flex justify-between items-center px-20"
      >
        <Icon name="sentosa-blocker/logo" class="!w-[23vw]" />
        <div class="flex items-center gap-5">
          <div>
            <div
              class="text-2xl font-bold text-[#481700]"
              v-html="t('BLOCKER_CTA')"
            ></div>
            <div
              class="text-2xl font-400 text-[#481700]"
              v-html="t('BLOCKER_CTA_DESC')"
            ></div>
          </div>
          <div class="qr flex justify-center items-center mb-5">
            <QrcodeVue class="rounded-lg" :value="URL" :size="140" level="H" />
          </div>
        </div>
      </div>
    </section>
    <section v-show="width <= 1024">
      <Icon
        name="sentosa-blocker/logo"
        class="!w-[40vw] absolute left-1/2 -translate-x-1/2 top-10"
      />
      <div class="tablet-background"></div>
      <div class="tablet-middle-ground"></div>
      <div class="tablet-far-ground"></div>
      <Icon
        name="sentosa-blocker/tablet-beach-objects"
        class="!w-[75vw] absolute left-[15vw] top-[50vh] z-10"
      />
      <Icon
        name="sentosa-blocker/tablet-characters"
        class="!w-[85vw] absolute top-[35vh] left-1/2 -translate-x-1/2 z-20"
      />
      <Icon
        name="sentosa-blocker/tablet-foreground"
        class="!w-full absolute bottom-0 z-[9999]"
      />
      <div
        class="left-1/2 -translate-x-1/2 absolute bottom-10 z-50 flex flex-col justify-center items-center"
      >
        <div class="qr flex justify-center items-center mb-5">
          <QrcodeVue class="rounded-lg" :value="URL" :size="140" level="H" />
        </div>
        <div
          class="text-2xl font-bold text-[#481700]"
          v-html="t('BLOCKER_CTA')"
        ></div>
        <div
          class="text-2xl font-400 text-[#481700]"
          v-html="t('BLOCKER_CTA_DESC')"
        ></div>
      </div>
    </section> -->
  </div>
</template>
<style lang="scss" scoped>
.blocker {
}

.qr {
  width: 175px;
  height: 175px;
  background: linear-gradient(90deg, #f39100 0%, #ff7c24 100%);
  clip-path: polygon(
    10% 0%,
    90% 0%,
    100% 10%,
    100% 90%,
    90% 100%,
    10% 100%,
    0% 90%,
    0% 10%
  );
}
</style>
