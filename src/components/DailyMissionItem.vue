<script lang="ts" setup>
import { delay, useAsync, useGlobalInstructor } from '@composables';
import { numeralFormat } from '@helpers';
import { useMapStore, useUserStore } from '@stores';
import { DAILYMISSION } from '@repositories';
import type { IDailyMission } from '@types';
import turfDistance from '@turf/distance';

interface Props {
  mission: IDailyMission;
  hideBtn?: boolean;
}

const props = defineProps<Props>();

const storeUser = useUserStore();
const storeMap = useMapStore();

const { silverCoins, lastLocations } = storeToRefs(storeMap);
const { t } = useI18n();
const { openUnifyInstructor, closeUnifyInstructor } = useGlobalInstructor();
const { push, openDialog } = useMicroRoute();
const { mission } = toRefs(props);

const distanceSliverCoin = computed(() => {
  return silverCoins.value.map((s) => {
    const { circle } = s || {};
    const { center } = circle || {};
    const lng = center?.lng || 0;
    const lat = center?.lat || 0;
    const radius = Number(circle?.radius || 0);

    const distance =
      turfDistance(lastLocations.value, [lng, lat], { units: 'meters' }) -
      radius;

    return {
      ...s,
      distance: +distance.toFixed(0),
    };
  });
});

const nearestCoin = computed(() =>
  distanceSliverCoin.value.reduce((nearest, current) => {
    return current.distance < nearest.distance ? current : nearest;
  })
);

function handleSubmitMission(mission: IDailyMission) {
  if (mission.progress >= mission.required) claimMission(mission);
  else goToMission(mission);
}

const { loading, execute: claimMission } = useAsync({
  async fn(mission: IDailyMission) {
    const { data } = await DAILYMISSION.claim(mission.unique_id);
    return data;
  },
  async onSuccess({ crystal }) {
    await storeUser.fetchDailyMission();
    storeUser.updateCrystals(crystal);
    openDialog('promo_success', {
      crystal,
      buttonType: 'fromDailyMission',
    });
  },
});

async function goToMission(mission: IDailyMission) {
  switch (mission.type) {
    case 'walk':
      push('/home');
      openUnifyInstructor('timii', {
        sequences: [
          {
            message: t('DIALOGUE_BA_WALK', {
              PROGRESS: (mission.required - mission.progress).toFixed(0),
            }),
          },
        ],
      });
      await delay(15000);
      closeUnifyInstructor();
      break;
    case 'use_shrink_pu':
      push('/home');
      openDialog('silver_coin', {
        coin: nearestCoin.value,
      });
      break;
    case 'complete_other_missions':
    case 'perform_brand_action':
      push('offer_wall');
      break;
    default:
      break;
  }
}
</script>
<template>
  <div
    class="card flex flex-col justify-center items-center gap-2"
    :class="{
      'pointer-events-none opacity-50': mission.claimed_at,
    }"
  >
    <div class="flex flex-nowrap items-start gap-2 w-full">
      <div
        class="rewards relative size-12 flex justify-center items-center shrink-0"
      >
        <Icon name="dbs_crystal" :size="40" />
        <div class="text-sm absolute bottom-0 right-1 text-border">
          {{ mission.reward }}
        </div>
      </div>
      <div class="flex flex-col gap-2 w-full">
        <div
          class="text-left text-sm line-clamp-2"
          v-html="t(mission.name)"
        ></div>
        <div class="process-bar">
          <template v-if="mission.claimed_at">
            <div class="relative">
              <div
                class="relative text-sm text-border z-20"
                v-html="'Reward claimed'"
              ></div>
              <div class="process top-0 left-0 w-full !rounded-md z-10"></div>
            </div>
          </template>
          <template v-else>
            <div class="process-label text-border">
              {{ numeralFormat(mission.progress)
              }}<span v-if="mission.type === 'walk'">m</span>
              /
              {{ numeralFormat(mission.required)
              }}<span v-if="mission.type === 'walk'">m</span>
            </div>
            <div
              class="process"
              :style="{
                width: `${(mission.progress / mission.required) * 100}%`,
              }"
            ></div>
          </template>
        </div>
        <Button
          v-if="!mission.claimed_at && !hideBtn"
          class="!w-[210px]"
          size="small"
          :variant="
            mission.progress >= mission.required ? 'secondary' : 'primary'
          "
          :label="
            mission.progress >= mission.required
              ? t('DAILY_MISSIONS_BTN_CLAIM')
              : t('DAILY_MISSIONS_BTN_GO')
          "
          :loading="loading"
          @click="handleSubmitMission(mission)"
        />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.card {
  width: 100%;
  padding: 12px;
  border-radius: 10px;
  border: 2px solid #51178c;
  background: #320b5b;
  .rewards {
    background-image: url(/imgs/daily-rewards-frame.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .process-bar {
    position: relative;
    background: rgba($color: #4f0649, $alpha: 0.9);
    box-shadow: 0px -3px 5px 0px #00000080;
    width: 100%;
    height: 20px;
    border-radius: 6px;
    .process {
      position: absolute;
      height: 100%;
      background: linear-gradient(90deg, #7147cd 0%, #d834cf 100%);
      border-radius: inherit;
      max-width: 100%;
    }
    .process-label {
      position: absolute;
      z-index: 2;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .text-border {
    text-shadow: rgb(65, 6, 60) 2px 0px 0px,
      rgb(65, 6, 60) 1.75517px 0.958851px 0px,
      rgb(65, 6, 60) 1.0806px 1.68294px 0px,
      rgb(65, 6, 60) 0.141474px 1.99499px 0px,
      rgb(65, 6, 60) -0.832294px 1.81859px 0px,
      rgb(65, 6, 60) -1.60229px 1.19694px 0px,
      rgb(65, 6, 60) -1.97998px 0.28224px 0px,
      rgb(65, 6, 60) -1.87291px -0.701566px 0px,
      rgb(65, 6, 60) -1.30729px -1.5136px 0px,
      rgb(65, 6, 60) -0.421592px -1.95506px 0px,
      rgb(65, 6, 60) 0.567324px -1.91785px 0px,
      rgb(65, 6, 60) 1.41734px -1.41108px 0px,
      rgb(65, 6, 60) 1.92034px -0.558831px 0px;
  }
}
</style>
