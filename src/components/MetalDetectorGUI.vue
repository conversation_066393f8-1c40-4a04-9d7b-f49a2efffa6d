<script lang="ts" setup>
import gsap, { Bounce } from 'gsap';
import TextPlugin from 'gsap/TextPlugin';
import {
  playSFX,
  useMetalDetector,
  useSound,
  useTrackData,
} from '@composables';
import { errorNotify } from '@helpers';
import { MAP } from '@repositories';
import { useUserStore } from '@stores';
import type {
  IAPIResponseError,
  IMetalDetectorResult,
  IMetalDetectorScanResult,
} from '@types';

gsap.registerPlugin(TextPlugin);

type ScanStatus = 'red' | 'yellow' | 'green';
type AccuracyLevel = 'low' | 'medium' | 'high';

const SCAN_CONFIG = {
  SCAN_INTERVAL: 2000,
  COUNTDOWN_INTERVAL: 1000,
  SOUND_INTERVALS: {
    red: 1000,
    yellow: 500,
    green: 250,
  },
  VIBRATE_RANGES: {
    red: { min: 0.1, max: 0.3 },
    yellow: { min: 0.3, max: 0.85 },
    green: { min: 0.85, max: 1 },
  },
} as const;

const storeUser = useUserStore();

const { settings } = storeToRefs(storeUser);
const { lastLocations, itemId, fitBoundsWithLocation, handleBackToMain } =
  useMetalDetector();
const { openDialog, closeDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();
const sound = useSound();

const startScan = ref(false);
const status = ref<ScanStatus>();
const accuracy = ref<AccuracyLevel>('low');
const scanningResult = ref<IMetalDetectorScanResult>();
const vibrateProgress = ref(0);
const isScanning = ref(false);
const isCompleted = ref(false);
const currentDuration = ref(0);

let durationInterval: NodeJS.Timeout;
let scanTimeout: NodeJS.Timeout;
let soundInterval: NodeJS.Timeout | null = null;

const scanDuration = computed(() => {
  return Number(settings.value?.metal_detector?.duration_in_sec || 60);
});

const vibrateColor = computed(() => {
  const colorMap: Record<ScanStatus, string> = {
    red: 'linear-gradient(to right, rgb(238, 77, 77), rgb(243, 8, 8))',
    yellow:
      'linear-gradient(to right, rgb(238, 77, 77), rgb(243, 8, 8), rgb(239, 173, 50), rgb(242, 242, 59))',
    green:
      'linear-gradient(to right, rgb(238, 77, 77), rgb(243, 8, 8), rgb(239, 173, 50), rgb(242, 242, 59), rgb(17, 174, 17), rgb(22, 219, 22))',
  };
  return status.value ? colorMap[status.value] : '';
});

const progressValue = computed(() => {
  return 100 - (100 * currentDuration.value) / scanDuration.value;
});

const timeLeftText = computed(() => {
  return `${currentDuration.value}s`;
});

const clearSoundInterval = (): void => {
  if (soundInterval) {
    clearInterval(soundInterval);
    soundInterval = null;
  }
};

const playSoundEffect = (interval: number): void => {
  clearSoundInterval();
  soundInterval = setInterval(() => {
    playSFX('metal_detector');
  }, interval);
};

watchEffect(() => {
  if (!status.value) {
    clearSoundInterval();
    return;
  }

  const soundIntervals = SCAN_CONFIG.SOUND_INTERVALS;
  playSoundEffect(soundIntervals[status.value]);
});

const getRandomInRange = (min: number, max: number): number => {
  return Number(Math.random() * (max - min) + min);
};

const getVibrateProgress = (): void => {
  if (!status.value) return;

  const ranges = SCAN_CONFIG.VIBRATE_RANGES;
  const range = ranges[status.value];
  vibrateProgress.value = getRandomInRange(range.min, range.max);
};

watch(lastLocations, fitBoundsWithLocation);
watch(status, getVibrateProgress);

async function startMetalDetector() {
  try {
    const [lng, lat] = lastLocations.value;
    const { data } = await MAP.useMetalDetector({
      item_id: itemId.value,
      lng,
      lat,
    });
    await storeUser.fetchUser();
    storeUser.metalDetectorItemId = data.item_id;
    startScan.value = true;
  } catch (error) {
    const { error_message } = error as IAPIResponseError;
    errorNotify({
      message: error_message,
    });
    resetMetalDetector();
  }
}

async function scanMetalDetector() {
  try {
    const [lng, lat] = lastLocations.value;
    const { data } = await MAP.scanMetalDetector({
      item_id: storeUser.metalDetectorItemId,
      lng,
      lat,
    });

    gsap.set('.metal-detector-gui', { pointerEvents: 'none' });
    scanningResult.value = data;
    status.value = data.scan_result;
    accuracy.value = data.accuracy;
    getVibrateProgress();
    track('metal_detector_use', {
      scan_result: data.scan_result,
      duration: scanDuration.value,
    });
    scanTimeout = setTimeout(() => {
      scanMetalDetector();
    }, SCAN_CONFIG.SCAN_INTERVAL);
  } catch (error) {
    const { error_message } = error as IAPIResponseError;
    switch (error_message) {
      case 'speed_limit_exceeded':
        const dialog = openDialog('metal_detector_overload', {
          duration: scanDuration.value,
          onClose(data: IMetalDetectorResult) {
            // Update scan duration from server response
            if (settings.value?.metal_detector) {
              settings.value.metal_detector.duration_in_sec = data.duration;
            }
            startScan.value = true;
            dialog.close();
          },
        });
        break;
      case 'cancelled':
        handleBackToMain();
        errorNotify({
          message: t('METAL_DETECTOR_ERROR_CANCELLED'),
        });
        break;
      case 'metal_detector_timeout':
        openDialog('metal_detector_result', {
          data: scanningResult.value,
          itemId: storeUser.metalDetectorItemId,
          // ttl: settings.value?.metal_detector?.extend_offer_ttl || 30,
          onExtended() {
            currentDuration.value = Number(
              settings.value?.metal_detector?.extend_duration_in_sec || 0
            );
            closeDialog('metal_detector_result');
            startScan.value = true;
          },
        });
        break;
      default:
        handleBackToMain();
        break;
    }
    resetMetalDetector();
  }
}

const resetMetalDetector = (): void => {
  status.value = undefined;
  startScan.value = false;
  isScanning.value = false;
  isCompleted.value = false;
  clearInterval(durationInterval);
  clearTimeout(scanTimeout);
  clearSoundInterval();
  gsap.set('.metal-detector-gui', { pointerEvents: 'all' });
};

const startCooldown = (): void => {
  currentDuration.value = scanDuration.value;
  durationInterval = setInterval(() => {
    if (currentDuration.value > 0) {
      currentDuration.value -= 1;
    } else {
      resetMetalDetector();
      clearSoundInterval();
      openDialog('metal_detector_result', {
        data: scanningResult.value,
        itemId: storeUser.metalDetectorItemId,
        ttl: settings.value?.metal_detector?.extend_offer_ttl || 30,
        onExtended() {
          currentDuration.value = Number(
            settings.value?.metal_detector?.extend_duration_in_sec || 0
          );
          closeDialog('metal_detector_result');
          startScan.value = true;
        },
      });
    }
  }, SCAN_CONFIG.COUNTDOWN_INTERVAL);
};

watch(startScan, (val) => {
  if (val) {
    scanMetalDetector();
    startCooldown();
    bannerAnim();
  } else {
    resetMetalDetector();
  }
});

watch(lastLocations, fitBoundsWithLocation);
watch(status, getVibrateProgress);

onBeforeUnmount(() => {
  clearSoundInterval();
  if (durationInterval) clearInterval(durationInterval);
  if (scanTimeout) clearTimeout(scanTimeout);
});

const bannerAnim = (): void => {
  gsap
    .timeline()
    .to('.mapbanner', {
      opacity: 1,
      duration: 2,
      ease: Bounce.easeInOut,
    })
    .fromTo(
      '.mapbanner-text',
      { text: '' },
      {
        duration: 1,
        text: 'Metal Detector is starting...',
      },
      '-=1'
    )
    .to('.mapbanner', {
      opacity: 0,
      duration: 2,
      ease: Bounce.easeInOut,
    });
};
onMounted(async () => {
  await nextTick();
  // Initialize component state
  currentDuration.value = scanDuration.value;

  // Stop any existing sounds and fit map bounds
  sound.stop();
  fitBoundsWithLocation();

  // Start the metal detector
  await startMetalDetector();
});

onBeforeUnmount(() => {
  sound.play('default');
  clearInterval(durationInterval);
  if (soundInterval) {
    clearInterval(soundInterval);
    soundInterval = null;
  }
});
</script>
<template>
  <div class="metal-detector-gui fixed fullscreen">
    <div
      class="mapbanner text-center absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
    >
      <Icon name="mapbanner" :size="200"></Icon>
      <div
        class="absolute text-center top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-[14px] w-full mapbanner-text"
      ></div>
    </div>
    <div :class="[status]"></div>
    <div class="absolute left-1/2 -translate-x-1/2 top-3 w-[68px] h-[80px]">
      <Icon class="w-full relative z-10" name="mdbattery" />
      <div
        class="w-full h-full absolute top-[6px] left-0 flex justify-center items-center z-20"
      >
        <q-circular-progress
          :value="progressValue"
          :thickness="1"
          size="62px"
          color="dark"
          center-color="transparent"
          track-color="transparent"
        />

        <div
          class="text-[18px] text-center leading-none absolute -translate-x-1/2 -translate-y-full top-[58px] left-1/2 z-20"
        >
          <b>{{ timeLeftText }}</b
          ><br />
          left
        </div>
      </div>
    </div>
    <div class="absolute left-1/2 -translate-x-1/2 bottom-10 w-4/5 h-5">
      <div class="signal-accuracy flex justify-center items-center">
        <span class="mt-1.5">
          Accuracy:
          <span
            class="font-extrabold capitalize"
            :class="{
              'text-[#FF4B4B]': accuracy === 'low',
              'text-[#FFD325]': accuracy === 'medium',
              'text-[#41CF5B]': accuracy === 'high',
            }"
          >
            {{ accuracy }}
          </span>
        </span>
      </div>
      <q-linear-progress
        rounded
        class="metal-detector-progress w-full h-full"
        :value="vibrateProgress"
      />
      <Icon
        class="absolute top-1/2 -translate-y-1/2 -left-4"
        name="metal-detector"
        :size="42"
      />
      <Icon
        class="absolute top-1/2 -translate-y-1/2 right-1"
        name="coin_detector"
        :size="19"
      />
    </div>
  </div>
</template>
<style lang="scss">
.mapbanner {
  opacity: 0;
}
.metal-detector-gui {
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;

  .green {
    width: calc(100svw - 20px);
    height: calc(100svh - 20px);
    position: relative;
    animation: glowGreen 2s infinite;
  }

  @keyframes glowGreen {
    0% {
      box-shadow: 0 0 30px rgb(49, 248, 49);
    }
    50% {
      box-shadow: 0 0 30px rgb(164, 172, 164);
    }
    100% {
      box-shadow: 0 0 30px rgb(49, 248, 49);
    }
  }

  .red {
    width: calc(100svw - 20px);
    height: calc(100svh - 20px);
    position: relative;
    animation: glowRed 2s infinite;
  }

  @keyframes glowRed {
    0% {
      box-shadow: 0 0 30px rgba(247, 55, 55);
    }
    50% {
      box-shadow: 0 0 30px rgb(164, 172, 164);
    }
    100% {
      box-shadow: 0 0 30px rgba(247, 55, 55);
    }
  }

  .yellow {
    width: calc(100svw - 20px);
    height: calc(100svh - 20px);
    position: relative;
    animation: glowYellow 2s infinite;
  }

  @keyframes glowYellow {
    0% {
      box-shadow: 0 0 30px rgb(244, 244, 82);
    }
    50% {
      box-shadow: 0 0 30px rgb(164, 172, 164);
    }
    100% {
      box-shadow: 0 0 30px rgb(244, 244, 82);
    }
  }

  .metal-detector-progress {
    border-radius: 4px;
    border: 1.5px solid #742ebb;
    .q-linear-progress__track {
      background-color: #08142d;
      opacity: unset;
    }
    .q-linear-progress__model--determinate {
      border-radius: 0 4px 4px 0;
      background: v-bind(vibrateColor);
    }
  }

  .signal-accuracy {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-image: url(/imgs/signal-accuracy.png);
    width: 165px;
    height: 30px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
</style>
