<template>
  <div class="relative pointer-events-none w-screen h-screen">
    <div class="background"></div>
    <div class="middle-ground"></div>
    <div class="far-ground"></div>
    <Icon
      name="sentosa-kv/beach-objects"
      class="!w-full absolute left-[5vw] top-[45vh] z-10"
    />
    <Icon
      name="sentosa-kv/characters"
      class="!w-[92vw] absolute top-[37vh] left-1/2 -translate-x-1/2 z-20"
    />
    <Icon
      name="sentosa-kv/foreground"
      class="!w-full absolute bottom-0 z-[9999]"
    />
  </div>
</template>
<style lang="scss" scoped>
.middle-ground {
  width: 100%;
  height: 80vh;
  background: url('/imgs/sentosa-kv/middle-ground.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 5;
}
.background {
  width: 100%;
  height: 60vh;
  background: url('/imgs/sentosa-kv/background.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.far-ground {
  width: 100%;
  height: 35vh;
  background: url('/imgs/sentosa-kv/far-ground.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 21vh;
  left: 0;
  z-index: 2;
}
</style>
