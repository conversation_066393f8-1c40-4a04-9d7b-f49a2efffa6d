<script lang="ts" setup>
import { useMapStore } from '@stores';
import { useMapHelpers, useViewportBounds } from '@composables';
import { Marker } from 'vue3-maplibre-gl';
import { point } from '@turf/helpers';
import circle from '@turf/circle';
import { BasePopup } from '@components';
import type { MetalSonarMarkerData } from '@types';

const storeMap = useMapStore();

const { _zoom, coinSonarLayers } = storeToRefs(storeMap);
const { t } = useI18n();
const { transformPosition } = useMapHelpers();
const { isInViewport } = useViewportBounds();

const shouldShowPopups = computed(() => {
  return _zoom.value > 15.5 && _zoom.value <= 17;
});

const metalSonarMarkers = computed<MetalSonarMarkerData[]>(() => {
  if (!shouldShowPopups.value || !coinSonarLayers.value) return [];

  return (
    coinSonarLayers.value
      .map((c) => {
        const { location, radius } = c;
        return circle([location.lng, location.lat], radius, {
          properties: c,
          units: 'meters',
        });
      })
      // .filter((c) => {
      //   const pt = point(lastLocations.value);
      //   return booleanPointInPolygon(pt, c);
      // })
      .map((c) => {
        const { location, radius, found_coin, _id } = c.properties;

        const geo = transformPosition(
          point([location.lng, location.lat]),
          radius
        );
        const [lng, lat] = geo.geometry.coordinates;

        return {
          id: _id || `sonar-${location.lng}-${location.lat}`,
          lngLat: [lng, lat] as [number, number],
          foundCoin: !!found_coin,
        };
      })
      .filter((marker) => isInViewport(marker.lngLat))
  );
});
</script>
<template>
  <template v-for="marker in metalSonarMarkers" :key="`sonar-${marker.id}`">
    <Marker v-if="shouldShowPopups" :lnglat="marker.lngLat" anchor="bottom">
      <BasePopup
        variant="metal-sonar"
        :content="
          marker.foundCoin
            ? t('METAL_SONAR_POPUP_POSITIVE')
            : t('METAL_SONAR_POPUP_NEGATIVE')
        "
      />
    </Marker>
  </template>
</template>
