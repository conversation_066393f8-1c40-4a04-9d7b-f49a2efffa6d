<script lang="ts" setup>
import { useMapStore } from '@stores';
import { Marker } from 'vue3-maplibre-gl';
import { point } from '@turf/helpers';
import { useMapHelpers, useViewportBounds } from '@composables';
import { BasePopup } from '@components';
import type { TimeUpMarkerData } from '@types';

const storeMap = useMapStore();

const { groupedSilverCoins, _zoom } = storeToRefs(storeMap);
const { t } = useI18n();
const { transformPosition } = useMapHelpers();
const { isInViewport } = useViewportBounds();

const timeUpMarkers = computed<TimeUpMarkerData[]>(() => {
  if (!shouldShowPopups.value || !groupedSilverCoins.value.time_up) return [];

  return groupedSilverCoins.value.time_up
    .map((c) => {
      const { circle, _id } = c.properties;
      const { center, radius } = circle;

      const geo = transformPosition(point([center.lng, center.lat]), radius);
      const [lng, lat] = geo.geometry.coordinates;

      return {
        id: _id,
        lngLat: [lng, lat] as [number, number],
      };
    })
    .filter((marker) => isInViewport(marker.lngLat));
});

const shouldShowPopups = computed(() => {
  return (
    _zoom.value > 13 &&
    _zoom.value < 15 &&
    groupedSilverCoins.value.time_up.length > 0
  );
});
</script>

<template>
  <template v-for="marker in timeUpMarkers" :key="`timeup-${marker.id}`">
    <Marker v-if="shouldShowPopups" :lnglat="marker.lngLat" anchor="bottom">
      <BasePopup variant="timeup" :content="t('POPUP_TIME_UP')" />
    </Marker>
  </template>
</template>
