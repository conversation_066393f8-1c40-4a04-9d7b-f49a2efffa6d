<script lang="ts" setup>
import { type PopupVariant, type PopupStyleConfig, POPUP_STYLES } from '@types';

interface Props {
  variant: PopupVariant;
  content: string;
  customMinWidth?: string;
  customTopOffset?: string;
}

const props = withDefaults(defineProps<Props>(), {
  customMinWidth: undefined,
  customTopOffset: undefined,
});

const styleConfig = computed((): PopupStyleConfig => {
  const baseConfig = POPUP_STYLES[props.variant];
  return {
    ...baseConfig,
    minWidth: props.customMinWidth || baseConfig.minWidth,
    topOffset: props.customTopOffset || baseConfig.topOffset || '-1px',
  };
});

const popupClasses = computed(() => [
  'base-popup',
  'absolute',
  'left-1/2',
  '-translate-x-1/2',
  'transition-all',
  'duration-300',
  'ease-in-out',
  `popup-${props.variant}`,
]);

const popupStyles = computed(() => ({
  fontSize: '14px',
  padding: '4px 8px',
  textAlign: 'center' as const,
  borderRadius: '4px',
  width: 'max-content',
  minWidth: styleConfig.value.minWidth,
  backgroundImage: styleConfig.value.backgroundImage,
  backgroundSize: '100% 100%',
  backgroundRepeat: 'no-repeat',
  backgroundColor: 'unset',
  top: styleConfig.value.topOffset,
}));
</script>

<template>
  <div :class="popupClasses" :style="popupStyles" v-html="content"></div>
</template>

<style lang="scss" scoped>
.base-popup {
  font-size: 14px;
  padding: 4px 8px;
  text-align: center;
  border-radius: 4px;
  width: max-content;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-color: unset;
  pointer-events: none;
}
</style>
