<script lang="ts" setup>
import { useMapStore } from '@stores';
import {
  useBeacon,
  useMapHelpers,
  useTick,
  useViewportBounds,
} from '@composables';
import { Marker } from 'vue3-maplibre-gl';
import { point } from '@turf/helpers';
import { timeCountDown } from '@helpers';
import { BasePopup } from '@components';
import type { BeaconMarkerData } from '@types';

const storeMap = useMapStore();

const { _zoom } = storeToRefs(storeMap);
const { inUsingBeacon } = useBeacon();
const { t } = useI18n();
const { now } = useTick();
const { transformPosition } = useMapHelpers();
const { isInViewport } = useViewportBounds();

const shouldShowPopups = computed(() => {
  return _zoom.value > 16 && _zoom.value < 19 && !!inUsingBeacon.value;
});

const beaconMarkers = computed<BeaconMarkerData[]>(() => {
  if (!inUsingBeacon.value || !shouldShowPopups.value) return [];

  const geo = transformPosition(
    point([inUsingBeacon.value.location.lng, inUsingBeacon.value.location.lat]),
    inUsingBeacon.value.radius
  );
  const [lng, lat] = geo.geometry.coordinates;

  const marker = {
    id: `beacon-${inUsingBeacon.value.location.lng}-${inUsingBeacon.value.location.lat}`,
    lngLat: [lng, lat] as [number, number],
    expireAt: inUsingBeacon.value.expire_at,
  };

  return isInViewport(marker.lngLat) ? [marker] : [];
});

const getBeaconPopupContent = computed(() => {
  return (marker: BeaconMarkerData) => {
    return t('BEACON_POPUP_1', {
      TIME: timeCountDown(+new Date(marker.expireAt) - now.value),
    });
  };
});
</script>

<template>
  <template v-for="marker in beaconMarkers" :key="`beacon-${marker.id}`">
    <Marker v-if="shouldShowPopups" :lnglat="marker.lngLat" anchor="bottom">
      <BasePopup variant="beacon" :content="getBeaconPopupContent(marker)" />
    </Marker>
  </template>
</template>
