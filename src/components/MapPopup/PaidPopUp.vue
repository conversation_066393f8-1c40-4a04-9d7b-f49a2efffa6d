<script lang="ts" setup>
import { useMapStore } from '@stores';
import { Marker } from 'vue3-maplibre-gl';
import { point } from '@turf/helpers';
import { useMapHelpers, useViewportBounds } from '@composables';
import { BasePopup } from '@components';
import type { PaidMarkerData } from '@types';

const storeMap = useMapStore();

const { groupedSilverCoins, _zoom } = storeToRefs(storeMap);
const { t } = useI18n();
const { transformPosition } = useMapHelpers();
const { isInViewport } = useViewportBounds();

const paidMarkers = computed<PaidMarkerData[]>(() => {
  if (!shouldShowPopups.value || !groupedSilverCoins.value.ongoing_paid)
    return [];

  return groupedSilverCoins.value.ongoing_paid
    .map((c) => {
      const { paidCircle, canUsePowerUp, _id } = c.properties;
      const { center, radius } = paidCircle;

      const geo = transformPosition(point([center.lng, center.lat]), radius);
      const [lng, lat] = geo.geometry.coordinates;

      return {
        id: _id,
        lngLat: [lng, lat] as [number, number],
        canUsePowerUp,
      };
    })
    .filter((marker) => isInViewport(marker.lngLat));
});

const shouldShowPopups = computed(() => {
  return (
    _zoom.value > 13 &&
    _zoom.value < 15 &&
    groupedSilverCoins.value.ongoing_paid.length > 0
  );
});
</script>

<template>
  <template v-for="marker in paidMarkers" :key="`paid-${marker.id}`">
    <Marker v-if="shouldShowPopups" :lnglat="marker.lngLat" anchor="bottom">
      <BasePopup
        variant="paid"
        :content="
          !marker.canUsePowerUp
            ? t('POPUP_COIN_MAX_PAID')
            : t('POPUP_COIN_PAID')
        "
      />
    </Marker>
  </template>
</template>
