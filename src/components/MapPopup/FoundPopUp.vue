<script lang="ts" setup>
import { useMapStore } from '@stores';
import { Marker } from 'vue3-maplibre-gl';
import { BasePopup } from '@components';
import { useViewportBounds } from '@composables';
import type { FoundMarkerData } from '@types';

const storeMap = useMapStore();

const { groupedSilverCoins, _zoom } = storeToRefs(storeMap);
const { t } = useI18n();
const { isInViewport } = useViewportBounds();

const foundSilverMarkers = computed<FoundMarkerData[]>(() => {
  if (!shouldShowPopups.value || !groupedSilverCoins.value?.found) return [];

  return groupedSilverCoins.value.found
    .map((c) => {
      const { circle, winner_info, coin_number, brand_name, _id } =
        c.properties;
      const { center } = circle;

      const name = `${winner_info.first_name} ${winner_info.last_name}`;

      return {
        id: _id,
        lngLat: [center.lng, center.lat] as [number, number],
        name,
        brandName: brand_name,
        coinNumber: String(coin_number),
      };
    })
    .filter((marker) => isInViewport(marker.lngLat));
});

const shouldShowPopups = computed(() => {
  return _zoom.value > 18 && groupedSilverCoins.value.found.length > 0;
});
</script>
<template>
  <template v-for="marker in foundSilverMarkers" :key="`found-${marker.id}`">
    <Marker v-if="shouldShowPopups" :lnglat="marker.lngLat" anchor="bottom">
      <BasePopup
        variant="found"
        :content="
          t('POPUP_FOUND', {
            NAME: marker.name,
            BRAND_NAME: marker.brandName,
            NUMBER: marker.coinNumber,
          })
        "
      />
    </Marker>
  </template>
</template>
