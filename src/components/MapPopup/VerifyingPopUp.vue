<script lang="ts" setup>
import { useMapStore } from '@stores';
import { Marker } from 'vue3-maplibre-gl';
import { point } from '@turf/helpers';
import { useMapHelpers, useViewportBounds } from '@composables';
import { BasePopup } from '@components';
import type { VerifyingMarkerData } from '@types';

const storeMap = useMapStore();

const { groupedSilverCoins, _zoom } = storeToRefs(storeMap);
const { t } = useI18n();
const { transformPosition } = useMapHelpers();
const { isInViewport } = useViewportBounds();

const verifyingMarkers = computed<VerifyingMarkerData[]>(() => {
  if (!shouldShowPopups.value || !groupedSilverCoins.value.verifying) return [];

  return groupedSilverCoins.value.verifying
    .map((c) => {
      const { circle, _id } = c.properties;
      const { center, radius } = circle;

      const geo = transformPosition(point([center.lng, center.lat]), radius);
      const [lng, lat] = geo.geometry.coordinates;

      return {
        id: _id,
        lngLat: [lng, lat] as [number, number],
      };
    })
    .filter((marker) => isInViewport(marker.lngLat));
});

const shouldShowPopups = computed(() => {
  return (
    _zoom.value > 13 &&
    _zoom.value < 15 &&
    groupedSilverCoins.value.verifying.length > 0
  );
});
</script>
<template>
  <template v-for="marker in verifyingMarkers" :key="`verifying-${marker.id}`">
    <Marker v-if="shouldShowPopups" :lnglat="marker.lngLat" anchor="bottom">
      <BasePopup variant="verifying" :content="t('POPUP_COIN_VERIFYING')" />
    </Marker>
  </template>
</template>
