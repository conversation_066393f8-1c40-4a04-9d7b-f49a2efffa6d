<script lang="ts" setup>
import { useUserStore } from '@stores';
import gsap, { Linear } from 'gsap';

interface Props {
  durations?: number; // seconds
  multipleLang?: boolean;
  illustrations: {
    name: string;
    type: 'gif' | 'png' | 'svg';
  }[];
}

const props = withDefaults(defineProps<Props>(), {
  durations: 8,
});
let holdingTime = 0;
let tween: gsap.core.Tween | null = null;

const storeUser = useUserStore();

const { user } = storeToRefs(storeUser);

const slide = ref<number>(1);
const progress = ref<number[]>(props.illustrations.map(() => 0));

const timeSlide = ref<number>(props.durations);
const slideRef = ref();

function startHolding() {
  holdingTime = Date.now();
  timeSlide.value = 0;
  tween?.kill();
}

function endHolding() {
  const isShortHold = Date.now() - holdingTime <= 200;
  if (isShortHold) slideRef.value?.next();
  else {
    calculateTimeSlide();
    animateProgress(slide.value);
  }
  holdingTime = 0;
}

function calculateTimeSlide() {
  timeSlide.value =
    (props.durations / 100) * (100 - progress.value[slide.value - 1]);
}

watch(slide, (val) => {
  resetTimeSlideAndProgress();
  animateProgress(val);
});

function resetTimeSlideAndProgress() {
  timeSlide.value = props.durations;
  progress.value = progress.value.map((_, idx) =>
    idx + 1 < slide.value ? 100 : 0
  );
}

function animateProgress(val: number) {
  tween?.kill();
  tween = gsap.to(progress.value, {
    [val - 1]: 100,
    duration: (props.durations / 100) * (100 - progress.value[val - 1]),
    ease: Linear.easeNone,
  });
}

onMounted(async () => {
  await nextTick();
  animateProgress(1);
});

onBeforeUnmount(() => {
  tween?.kill();
});
</script>
<template>
  <div class="relative w-full">
    <slot />
    <q-carousel
      animated
      v-model="slide"
      :autoplay="timeSlide ? timeSlide * 1000 : false"
      infinite
      ref="slideRef"
      class="rounded-xl w-full !h-auto"
    >
      <q-carousel-slide
        :name="index + 1"
        v-for="(item, index) in illustrations"
        :key="`slide_${index + 1}`"
        @pointerdown="startHolding"
        @pointerup="endHolding"
        class="p-0 !w-full"
      >
        <Icon
          v-if="multipleLang"
          class="!w-full"
          :name="`${item.name}${user?.lang || 'en'}`"
          :type="item.type"
          lazy
        />
        <Icon v-else class="!w-full" :name="item.name" :type="item.type" lazy />
      </q-carousel-slide>
    </q-carousel>
    <div
      class="grid grid-flow-col grid-col-3 gap-1 mt-2"
      v-if="illustrations.length > 1"
    >
      <div
        v-for="index in illustrations.length"
        :key="`slide_progress_${index}`"
        class="h-1 rounded relative overflow-hidden bg-[#979797]"
      >
        <div
          class="w-full h-full bg-[#fd2b84] rounded"
          :style="{
            transform: `translateX(-${100 - progress[index - 1]}%)`,
          }"
        ></div>
      </div>
    </div>
  </div>
</template>
