<script setup lang="ts">
import { useTrackData } from '@composables';
import QrcodeVue from 'qrcode.vue';
const { t } = useI18n();
const URL = computed(() => process.env.APP_END_POINT);
const userAgent = navigator.userAgent.toLowerCase();
const isTablet = computed(() =>
  /(ipad|tablet|(android(?!.*mobile))|(windows(?!.*phone)(.*touch))|kindle|playbook|silk|(puffin(?!.*(IP|AP|WP))))/.test(
    userAgent
  )
);
const { track } = useTrackData();
track('page_blocker');
</script>
<template>
  <div class="text-center fullscreen blocker">
    <div class="flex w-full logo flex-center">
      <Icon
        name="logo_htm"
        class="w-auto mx-auto max-h-[132px] blocker__logo"
        style="height: 15vh"
      />
    </div>
    <img src="/imgs/blocker/new_car_silver.png" class="object-cover kv" />
    <div
      class="items-center gap-5 mx-auto bottom-8 right-8 flex-nowrap blocker_info"
    >
      <div class="pb-5">
        <div
          class="text-2xl font-bold whitespace-nowrap"
          :class="{ 'text-left': !isTablet }"
          v-html="t('BLOCKER_CTA')"
        ></div>
        <div
          class="text-2xl font-400 whitespace-nowrap"
          v-html="t('BLOCKER_CTA_DESC')"
        ></div>
      </div>
      <div class="p-2 bg-white rounded-md">
        <QrcodeVue :value="URL" :size="155" level="H" />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.blocker {
  background: url('/imgs/blocker/new_kv_tablet.png') no-repeat;
  background-size: cover;
  .kv {
    position: absolute;
    bottom: 2vh;
    left: 50%;
    transform: translateX(-50%);
    height: 45vh;
  }
  .blocker__logo {
    margin-top: 60px;
  }
  .blocker_info {
    display: flex;
    flex-direction: column-reverse;
    margin-top: 30px;
    img {
      margin-bottom: 20px;
      height: 120px !important;
    }
  }
  @media screen and (min-width: 1025px) {
    background: url('/imgs/blocker/new_kv.png') no-repeat !important;
    background-size: cover !important;
    .kv {
      position: absolute;
      bottom: 12%;
      left: 50%;
      transform: translateX(-50%);
      height: 60vh !important;
      width: auto !important;
    }
    .blocker__logo {
      margin-top: 40px;
    }
    .blocker_info {
      position: absolute;
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      margin-top: 30px;
      img {
        margin-bottom: 20px;
        height: 120px !important;
      }
    }
  }
}
// .blocker-tablet {
//   background-size: 100% 10%, 100% auto;
//   background-color: #0f132a;
//   &.season-vn {
//     background: url('/imgs/blocker/road-tablet.png') bottom 0px center no-repeat,
//       url('/imgs/blocker/hcm-bg-tablet.png') top 0 center no-repeat;
//   }
//   .kv {
//     position: absolute;
//     bottom: 18vh;
//     left: 50%;
//     transform: translateX(-45%);
//     height: 300px;
//   }
//   .logo {
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     flex-direction: row-reverse;
//     gap: 20px;
//     margin-top: 40px;
//     .blocker__logo {
//       position: relative;
//     }
//   }
//   .blocker_info {
//     display: flex;
//     flex-direction: column-reverse;
//     bottom: 30px;
//     left: 50%;
//     transform: translateX(-50%);
//     img {
//       margin-bottom: 20px;
//     }
//   }
//   .driven-wheel-light {
//     position: absolute;
//     bottom: 165px;
//     z-index: 4;
//     width: 30px;
//     &.left {
//       left: 160px;
//     }
//     &.right {
//       right: 245px;
//     }
//   }
//}
</style>
