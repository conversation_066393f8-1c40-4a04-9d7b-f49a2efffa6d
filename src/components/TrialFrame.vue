<script lang="ts" setup>
import { delay } from '@composables';
import { useMapStore } from '@stores';
import { MapOptions } from 'maplibre-gl';
import { Loading } from 'quasar';
import {
  GeolocateSuccess,
  Mapbox,
  MapCreationStatus,
  useMapbox,
  GeolocateControls,
  GeolocateControl,
  Image,
} from 'vue3-maplibre-gl';
import 'vue3-maplibre-gl/dist/style.css';

interface Props {
  hiddenMap?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  hiddenMap: false,
});

const storeMap = useMapStore();

const { lastLocations } = storeToRefs(storeMap);
const { register: registerMap, mapStatus, mapInstance } = useMapbox();
const { push } = useMicroRoute();
const { t } = useI18n();

const showDialog = ref(false);

const mapOptions = computed<Partial<MapOptions>>(() => {
  return {
    container: 'trial-map',
    style: process.env.MAP_STYLE,
    minZoom: 12,
    maxZoom: 22,
    zoom: 12,
    center: lastLocations.value,
  };
});

const images = computed(() => {
  return ['shiny-coin'].map((name) => {
    return {
      id: name,
      image: `imgs/map/${name}.png`,
    };
  });
});

async function handleSuccessGPS(tt: GeolocateSuccess) {
  const { coords } = tt;
  storeMap.setUserLocation([coords.longitude, coords.latitude]);
}

async function registerGeoState(geo: GeolocateControl) {
  mapInstance.value?.touchZoomRotate.disableRotation();
  mapInstance.value?.doubleClickZoom.disable();
  mapInstance.value?.dragRotate.disable();
  mapInstance.value?.touchPitch.disable();
  mapInstance.value?.removeSprite('default');
  await delay(500);
  geo.trigger();
}

watch(
  () => mapStatus.value,
  (val) => {
    if (val === MapCreationStatus.Loaded) {
      Loading.hide();
    }
  }
);

onMounted(() => {
  if (!props.hiddenMap) Loading.show();
});

defineExpose({
  mapInstance,
  mapStatus,
});
</script>

<template>
  <div
    id="trial-frame"
    class="w-full h-full border-8 border-[#16A3E9] relative z-20"
  >
    <div
      class="text-xl font-bold absolute -top-0.5 -left-0.5 z-10 py-2 px-[14px] bg-[#16A3E9] rounded-br-md"
      v-html="t('TRIAL_FRAME_TITLE')"
    ></div>
    <div class="absolute -top-2 -right-2 z-10 p-2 bg-[#16A3E9] rounded-bl-md">
      <Button
        :label="t('TRIAL_FRAME_BTN_EXIT')"
        variant="purple"
        size="small"
        @click="showDialog = true"
      />
    </div>
    <template v-if="!hiddenMap">
      <Mapbox :options="mapOptions" @register="registerMap">
        <Image :images="images" />
        <template v-if="mapStatus === MapCreationStatus.Loaded">
          <GeolocateControls
            :options="{
              positionOptions: {
                enableHighAccuracy: true,
              },
              trackUserLocation: true,
              showAccuracyCircle: true,
            }"
            @geolocate="handleSuccessGPS"
            @register="registerGeoState"
          />
        </template>

        <div class="trial-frame-container">
          <slot v-if="mapStatus === MapCreationStatus.Loaded" />
        </div>
      </Mapbox>
    </template>
    <slot v-if="hiddenMap" />
    <UnifyInstructor />
    <q-dialog v-model="showDialog" persistent>
      <Dialog @close="showDialog = false">
        <template #header>
          <div v-html="t('TRIAL_FRAME_EXIT_TITLE')"></div>
        </template>
        <div class="text-center">
          <div class="text-sm mb-5" v-html="t('TRIAL_FRAME_EXIT_DESC')"></div>
          <div class="flex gap-4 flex-nowrap">
            <Button
              class="flex-1"
              size="max-content"
              :label="t('TRIAL_FRAME_EXIT_BTN_YES')"
              variant="purple"
              @click="push('/home')"
            />
            <Button
              size="max-content"
              class="flex-1"
              :label="t('TRIAL_FRAME_EXIT_BTN_NO')"
              block
              @click="showDialog = false"
            />
          </div>
        </div>
      </Dialog>
    </q-dialog>
  </div>
</template>
