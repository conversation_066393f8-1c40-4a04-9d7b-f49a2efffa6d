<script setup lang="ts">
const { t } = useI18n();
</script>
<template>
  <div class="toolkit_btn">
    <div class="relative fit flex-center column">
      <div class="fit leading-normal gap-0.5 flex-center column relative">
        <Icon
          :name="'/imgs/icons/ic_toolkit.png'"
          :size="20"
          type="url"
          class="mr-[-6px]"
        />
        <p
          class="font-bold text-[10px] text-center mr-[-6px]"
          v-html="t('BTN_TOOLKIT')"
        ></p>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.toolkit_btn {
  width: 68px;
  height: 68px;
  background: url(/imgs/button/hunter_tool.png);
  background-size: 100% 100%;
}
</style>
