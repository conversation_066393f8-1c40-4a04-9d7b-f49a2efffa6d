<script setup lang="ts">
import { useUserStore } from '@stores';
import gsap, { Bounce, Linear } from 'gsap';
import QrcodeVue from 'qrcode.vue';

const storeUser = useUserStore();
const tl = gsap.timeline({ repeat: -1 });

const { seasonCode } = storeToRefs(storeUser);
const { t } = useI18n();

const URL = computed(() => process.env.APP_END_POINT);
const userAgent = navigator.userAgent.toLowerCase();
const isTablet = computed(() =>
  /(ipad|tablet|(android(?!.*mobile))|(windows(?!.*phone)(.*touch))|kindle|playbook|silk|(puffin(?!.*(IP|AP|WP))))/.test(
    userAgent
  )
);

function startAnimation() {
  gsap.fromTo(
    '.driven-wheel-light',
    {
      opacity: 0,
    },
    { opacity: 1, delay: 0.5, duration: 0.5 }
  );
  tl.to('.driven-wheel', {
    rotation: '-=360_cw',
    ease: Linear.easeNone,
    duration: 1.5,
    repeat: -1,
  })
    .fromTo(
      '.car',
      {
        y: 8,
      },
      {
        y: 0,
        yoyo: true,
        repeat: -1,
        duration: 1.5,
        ease: Bounce.easeIn,
      }
    )
    .to(
      '.city',
      {
        ease: Linear.easeNone,
        duration: 0.8,
        repeat: -1,
      },
      '-=0.5'
    );
}

onMounted(async () => {
  await nextTick();
  startAnimation();
});

onBeforeUnmount(() => {
  tl && tl.kill();
});
</script>

<template>
  <div
    class="text-center pointer-events-none fullscreen"
    :class="[
      isTablet
        ? `blocker-tablet season-${seasonCode.toLowerCase()}`
        : `blocker-desktop season-${seasonCode.toLowerCase()}`,
    ]"
  >
    <div class="kv">
      <div class="car"></div>
      <div class="city"></div>
      <div class="driven-wheel left"><div class="blur"></div></div>
      <Icon class="driven-wheel-light left" name="kv/driven-wheel-light" />
      <div class="driven-wheel right"><div class="blur"></div></div>
      <Icon class="driven-wheel-light right" name="kv/driven-wheel-light" />
    </div>

    <div class="logo">
      <Icon
        name="logo_htm"
        class="absolute w-auto h-20 pointer-events-none blocker__logo"
      />
    </div>

    <div
      class="absolute items-center gap-5 bottom-8 right-8 flex-nowrap blocker__bottom"
    >
      <div>
        <div
          class="text-2xl font-bold whitespace-nowrap"
          :class="{ 'text-left': !isTablet }"
          v-html="t('BLOCKER_CTA')"
        ></div>
        <div
          class="text-2xl font-400 whitespace-nowrap"
          v-html="t('BLOCKER_CTA_DESC')"
        ></div>
      </div>
      <QrcodeVue :value="URL" :size="155" :margin="2" level="H" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.blocker-desktop {
  background-color: #0f132a;
  &.season-vn {
    background: url('/imgs/blocker/hcm-bg.png') top 0 center no-repeat;
    background-size: cover;
  }
  &.season-sg {
    background: url('/imgs/blocker/bg.png') top 0 center no-repeat;
    background-size: cover;
  }

  .kv {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 1000px;
    height: 1000px;
    .car {
      position: absolute;
      top: -40px;
      z-index: 3;
      width: 100%;
      height: 100%;
      background-image: url('/imgs/kv/car.png');
      background-size: cover;
      background-repeat: no-repeat;
      transform: scaleX(1);
    }

    .driven-wheel {
      position: absolute;
      width: 120px;
      height: 120px;
      background-image: url('/imgs/kv/driven-wheel.png');
      background-size: contain;
      background-repeat: no-repeat;
      bottom: 150px;
      z-index: 3;

      .blur {
        background-image: radial-gradient(
          46.65% 46.43% at 50.05% 54%,
          #864dce 0%,
          #7c47bf 5%,
          #502e7c 31%,
          #2e1a46 54%
        );
        opacity: 0.4;
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 4;
        border-radius: 50%;
      }
      &.left {
        left: 145px;
      }
      &.right {
        right: 230px;
      }
    }

    .shadow {
      position: absolute;
      bottom: 135px;
      z-index: 1;
      width: 500px;
      left: 115px;
    }
  }

  .driven-wheel-light {
    position: absolute;
    bottom: 160px;
    z-index: 4;
    width: 40px;

    &.left {
      left: 206px;
    }
    &.right {
      right: 249px;
    }
  }

  .blocker__logo {
    bottom: 30px;
    left: 30px;
  }
  .untame-logo {
    bottom: 120px;
    left: 30px;
  }
  .blocker__bottom {
    display: flex;
    flex-direction: row;
  }
}

@media screen and (max-width: 900px) {
  .blocker-desktop {
    background-size: 100% 10%, 100% 100%;
    background-color: #0f132a;
    &.season-vn {
      background: url('/imgs/blocker/road-tablet.png') bottom 0px center
          no-repeat,
        url('/imgs/blocker/hcm-bg-tablet.png') bottom 0 center no-repeat;
    }
    &.season-sg {
      background: url('/imgs/blocker/road-tablet.png') bottom 0px center
          no-repeat,
        url('/imgs/blocker/bg-tablet.png') bottom 0 center no-repeat;
    }
    .kv {
      bottom: 18vh;
      left: 50%;
      transform: translateX(-47%);
      width: 800px;
      height: 800px;
      .car {
        top: 0;
      }
      .driven-wheel {
        width: 95px;
        height: 95px;
        bottom: 90px;

        .blur {
          background-image: radial-gradient(
            46.65% 46.43% at 50.05% 54%,
            #864dce 0%,
            #7c47bf 5%,
            #502e7c 31%,
            #2e1a46 54%
          );
          opacity: 0.4;
          position: absolute;
          width: 100%;
          height: 100%;
          z-index: 4;
          border-radius: 50%;
        }
        &.left {
          left: 115px;
        }
        &.right {
          right: 187px;
        }
      }

      .shadow {
        position: absolute;
        bottom: 125px;
        z-index: 1;
        width: 450px;
        left: 100px;
      }
    }

    .blocker__logo {
      top: 80px;
      left: 50% !important;
      transform: translateX(-50%);
    }
    .blocker__bottom {
      display: flex;
      flex-direction: column-reverse;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);

      img {
        margin-bottom: 20px;
        height: 120px !important;
      }
    }

    .driven-wheel-light {
      position: absolute;
      bottom: 100px;
      z-index: 4;
      width: 30px;

      &.left {
        left: 165px;
      }
      &.right {
        right: 200px;
      }
    }
  }
}

.blocker-tablet {
  background-size: 100% 10%, 100% auto;
  background-color: #0f132a;
  &.season-vn {
    background: url('/imgs/blocker/road-tablet.png') bottom 0px center no-repeat,
      url('/imgs/blocker/hcm-bg-tablet.png') top 0 center no-repeat;
  }
  .kv {
    position: absolute;
    bottom: 18vh;
    left: 50%;
    transform: translateX(-45%);
    width: 800px;
    height: 800px;
    .car {
      position: absolute;
      top: 0;
      left: 8px;
      z-index: 3;
      width: 100%;
      height: 100%;
      background-image: url('/imgs/kv/car.png');
      background-size: contain;
      background-repeat: no-repeat;
      transform: scaleX(1);
    }

    .driven-wheel {
      position: absolute;
      width: 90px;
      height: 90px;
      background-image: url('/imgs/kv/driven-wheel.png');
      background-size: contain;
      background-repeat: no-repeat;
      bottom: 154px;
      z-index: 3;
      .blur {
        background-image: radial-gradient(
          46.65% 46.43% at 50.05% 54%,
          #864dce 0%,
          #7c47bf 5%,
          #502e7c 31%,
          #2e1a46 54%
        );
        opacity: 0.4;
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 4;
        border-radius: 50%;
      }
      &.left {
        left: 110px;
      }
      &.right {
        right: 235px;
      }
    }

    .shadow {
      position: absolute;
      bottom: 125px;
      z-index: 1;
      width: 450px;
      left: 100px;
    }
  }

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row-reverse;
    gap: 20px;
    margin-top: 40px;
    .blocker__logo {
      position: relative;
    }
  }

  .blocker__bottom {
    display: flex;
    flex-direction: column-reverse;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);

    img {
      margin-bottom: 20px;
    }
  }

  .driven-wheel-light {
    position: absolute;
    bottom: 165px;
    z-index: 4;
    width: 30px;

    &.left {
      left: 160px;
    }
    &.right {
      right: 245px;
    }
  }
}
</style>
