<script lang="ts" setup>
import { FOUND_COIN_SYMBOL_LAYER } from '@constants';
import { useUserStore } from '@stores';
import { useMapHelpers } from '@composables';
import { SymbolLayer, GeoJsonSource, MapMouseEvent } from 'vue3-maplibre-gl';
import { point } from '@turf/helpers';
import dayjs from 'dayjs';
import { SentosaGoldenCoin } from '@types';

interface Emits {
  (event: 'click', data: MapMouseEvent): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const { capitalandGeneoCoin } = storeToRefs(storeUser);
const { makeSource } = useMapHelpers();

const isWithinLast24Hours = (foundAt: string): boolean => {
  return dayjs().diff(dayjs(foundAt), 'hour') <= 24;
};

const transformCoinToGeoPoint = (coin: SentosaGoldenCoin) => {
  return point([coin.location.lng, coin.location.lat], {
    ...coin,
    icon: 'silver-flag-2',
    iconSizeMax: 0.45,
    iconSizeMin: 0.2,
    status: 'found',
    type: 'geneo-coin',
    circle: {},
    winner_info: coin.winner_info,
    videos: [coin.video_link],
  });
};

const groupedCoins = computed(() => {
  const foundCoins = capitalandGeneoCoin.value.filter(
    (coin) => coin.status === 'found'
  );

  return foundCoins.reduce(
    (acc, coin) => {
      const array = isWithinLast24Hours(coin.found_at) ? acc.recent : acc.past;
      array.push(coin);
      return acc;
    },
    { recent: [] as SentosaGoldenCoin[], past: [] as SentosaGoldenCoin[] }
  );
});

const createCoinSources = (coins: SentosaGoldenCoin[]) => {
  const sources = coins.map((coin) => transformCoinToGeoPoint(coin));
  return makeSource(sources);
};

const foundCoinSources = computed(() =>
  createCoinSources(groupedCoins.value.recent)
);

const pastFoundCoinSources = computed(() =>
  createCoinSources(groupedCoins.value.past)
);

const layerStyle = computed(() => ({
  ...FOUND_COIN_SYMBOL_LAYER,
}));
</script>
<template>
  <GeoJsonSource id="found_crystal_coin" :data="foundCoinSources">
    <SymbolLayer
      @click="emits('click', $event)"
      id="found_crystal_coin_symbol_layer"
      :style="layerStyle"
      :minzoom="14"
    />
  </GeoJsonSource>
  <GeoJsonSource id="past_found_crystal_coin" :data="pastFoundCoinSources">
    <SymbolLayer
      @click="emits('click', $event)"
      id="past_found_crystal_coin_symbol_layer"
      :style="layerStyle"
      :minzoom="22"
    />
  </GeoJsonSource>
</template>
