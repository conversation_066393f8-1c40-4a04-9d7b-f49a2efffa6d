<script lang="ts" setup>
import { FillLayer, GeoJsonSource, LineLayer } from 'vue3-maplibre-gl';
import { useBAStore, useMapStore } from '@stores';
import { useMapHelpers } from '@composables';
import circle from '@turf/circle';

const mapStore = useMapStore();
const baStore = useBAStore();
const { makeSource } = useMapHelpers();

const { timeMissionData } = storeToRefs(baStore);

const nearestValidOutlet = computed(() => {
  if (timeMissionData.value?.type !== 'location_based') return null;
  return mapStore.getNearestOutlet(
    timeMissionData.value.brand_unique_id,
    timeMissionData.value.data?.location_based?.sv_client
  );
});

const nearestValidOutletSource = computed(() => {
  if (!nearestValidOutlet.value) return makeSource([]);
  return makeSource([
    circle(
      [
        nearestValidOutlet.value.location.lng,
        nearestValidOutlet.value.location.lat,
      ],
      100,
      {
        units: 'meters',
      }
    ),
  ]);
});
</script>
<template>
  <GeoJsonSource v-if="nearestValidOutlet" :data="nearestValidOutletSource">
    <FillLayer
      :style="{
        'fill-color': '#DA3799',
        'fill-opacity': 0.4,
      }"
    />
    <LineLayer
      :style="{
        'line-color': '#DA3799',
        'line-width': 20,
        'line-blur': 50,
      }"
    />
  </GeoJsonSource>
</template>
