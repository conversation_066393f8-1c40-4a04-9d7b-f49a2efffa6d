<script lang="ts" setup>
import { useMapHelpers } from '@composables';
import { LAYERS } from '@constants';
import { useMapStore } from '@stores';
import { point } from '@turf/helpers';
import { groupBy } from 'lodash';
import { GeoJsonSource, MapMouseEvent, SymbolLayer } from 'vue3-maplibre-gl';

interface Emits {
  (event: 'click', data: MapMouseEvent): void;
}

const emits = defineEmits<Emits>();

const storeMap = useMapStore();

const { mapIcons } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();

const groupedByLayerId = computed(() => {
  const groups = groupBy(mapIcons.value, 'display.prefer_layer');
  return groups;
});

const mapIconSources = computed(() => {
  const pairs = [];
  for (const [preferLayerId, brandIcons] of Object.entries(
    groupedByLayerId.value
  )) {
    const data = brandIcons.map((d) =>
      point([d.location.lng, d.location.lat], {
        ...d,
        ...d.display,
        sponsor: true,
      })
    );
    pairs.push([preferLayerId, makeSource(data)] as const);
  }
  return pairs;
});
</script>
<template>
  <template v-for="[layerId, source] in mapIconSources" :key="layerId">
    <GeoJsonSource
      :data="source"
      v-if="LAYERS[layerId] !== undefined"
      :options="{
        cluster: false,
      }"
    >
      <SymbolLayer
        @click="emits('click', $event)"
        :id="layerId"
        :style="{
          ...LAYERS[layerId].layout,
          ...LAYERS[layerId].paint,
          'icon-image': ['get', 'brand_icon'],
        }"
        :minzoom="LAYERS[layerId].minzoom"
        :maxzoom="LAYERS[layerId].maxzoom"
      />
    </GeoJsonSource>
  </template>
</template>
