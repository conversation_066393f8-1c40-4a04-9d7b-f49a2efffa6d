<script lang="ts" setup>
import {
  ONGOING_COIN_FREE_FILL_LAYER,
  ONGOING_COIN_FREE_LINE_LAYER,
  ONGOING_COIN_FREE_LINE_GLOW_LAYER,
} from '@constants';
import { useMapStore } from '@stores';
import { useMapHelpers } from '@composables';
import { GeoJsonSource, FillLayer, LineLayer } from 'vue3-maplibre-gl';

const storeMap = useMapStore();
const { groupedSilverCoins, coinSonar } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();

const ongoingCoinFreeSources = computed(() => {
  const coins = groupedSilverCoins.value.ongoing_free.map((coin) => ({
    ...coin,
    properties: {
      ...coin.properties,
      lineWidth:
        coinSonar.value.selectedCircle?.properties?._id === coin.properties._id
          ? 4
          : 1,
    },
  }));

  return makeSource(coins);
});
</script>

<template>
  <GeoJsonSource id="ongoing_coin_free" :data="ongoingCoinFreeSources">
    <FillLayer
      id="ongoing_coin_free_fill_layer"
      :style="{
        ...ONGOING_COIN_FREE_FILL_LAYER,
      }"
    />
    <LineLayer
      id="ongoing_coin_free_line_layer"
      :style="{
        ...ONGOING_COIN_FREE_LINE_LAYER,
        'line-width': ['get', 'lineWidth'],
      }"
    />
    <LineLayer
      id="ongoing_coin_free_line_glow_layer"
      :style="{
        ...ONGOING_COIN_FREE_LINE_GLOW_LAYER,
      }"
    />
  </GeoJsonSource>
</template>
