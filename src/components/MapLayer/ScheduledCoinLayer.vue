<script lang="ts" setup>
import {
  SCHEDULED_FILL_LAYER,
  SCHEDULED_LINE_LAYER,
  SCHEDULED_LINE_GLOW_LAYER,
} from '@constants';
import { useMapStore } from '@stores';
import { useMapHelpers } from '@composables';
import { GeoJsonSource, FillLayer, LineLayer } from 'vue3-maplibre-gl';

const storeMap = useMapStore();
const { groupedSilverCoins, coinSonar } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();

const sources = computed(() => {
  const coins = groupedSilverCoins.value.scheduled.map((coin) => ({
    ...coin,
    properties: {
      ...coin.properties,
      lineWidth:
        coinSonar.value.selectedCircle?.properties?._id === coin.properties._id
          ? 4
          : 1,
    },
  }));

  return makeSource(coins);
});
</script>

<template>
  <GeoJsonSource id="scheduled_coin" :data="sources">
    <FillLayer
      id="scheduled_coin_fill_layer"
      :style="{
        ...SCHEDULED_FILL_LAYER,
      }"
    />
    <LineLayer
      id="scheduled_coin_line_layer"
      :style="{
        ...SCHEDULED_LINE_LAYER,
        'line-width': ['get', 'lineWidth'],
      }"
    />
    <LineLayer
      id="scheduled_coin_line_glow_layer"
      :style="{
        ...SCHEDULED_LINE_GLOW_LAYER,
      }"
    />
  </GeoJsonSource>
</template>
