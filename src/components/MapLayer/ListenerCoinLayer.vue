<script lang="ts" setup>
import { useMapStore } from '@stores';
import { useMapHelpers } from '@composables';
import { GeoJsonSource, FillLayer, MapMouseEvent } from 'vue3-maplibre-gl';

interface Emits {
  (event: 'click', data: MapMouseEvent): void;
}

const emits = defineEmits<Emits>();

const storeMap = useMapStore();

const { groupedSilverCoins } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();

const listenerCoinSources = computed(() => {
  return makeSource(
    [
      ...groupedSilverCoins.value.time_up,
      ...groupedSilverCoins.value.forfeited,
      ...groupedSilverCoins.value.verifying,
      ...groupedSilverCoins.value.ongoing_free,
      ...groupedSilverCoins.value.ongoing_paid,
      ...groupedSilverCoins.value.scheduled,
    ].sort((a, b) => +b.properties.radius - +a.properties.radius)
  );
});
</script>
<template>
  <GeoJsonSource :data="listenerCoinSources">
    <FillLayer
      @click="emits('click', $event)"
      id="listener_fill_layer"
      :style="{
        'fill-color': '#FFFFFF',
        'fill-opacity': 0,
      }"
    />
  </GeoJsonSource>
</template>
