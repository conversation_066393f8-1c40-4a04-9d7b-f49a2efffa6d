<script lang="ts" setup>
import { FOUND_COIN_SYMBOL_LAYER } from '@constants';
import { useMapStore, useUserStore } from '@stores';
import { useMapHelpers } from '@composables';
import { SymbolLayer, GeoJsonSource, MapMouseEvent } from 'vue3-maplibre-gl';
import { point } from '@turf/helpers';

interface Emits {
  (event: 'click', data: MapMouseEvent): void;
}

const emits = defineEmits<Emits>();

const storeMap = useMapStore();
const storeUser = useUserStore();

const { isFoundGoldenCoin, settings } = storeToRefs(storeUser);
const { groupedSilverCoins } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();

const foundCoinSources = computed(() => {
  if (isFoundGoldenCoin.value && !!settings.value?.golden_coin.location) {
    const { lng, lat } = settings.value?.golden_coin.location;
    const sources = [
      ...groupedSilverCoins.value.found,
      point([lng, lat], {
        icon: 'golden-flag',
        iconSizeMax: 0.5,
        iconSizeMin: 0.2,
        status: 'found',
        type: 'golden',
        circle: {},
        winner_info: {},
        videos: [],
      }),
    ];
    return makeSource(sources);
  }
  return makeSource(groupedSilverCoins.value.found);
});

const pastFoundCoinSources = computed(() => {
  return makeSource(groupedSilverCoins.value.past_found);
});
</script>
<template>
  <GeoJsonSource id="found_coin" :data="foundCoinSources">
    <SymbolLayer
      @click="emits('click', $event)"
      id="found_coin_symbol_layer"
      :style="{
        ...FOUND_COIN_SYMBOL_LAYER,
      }"
      :minzoom="14"
    />
  </GeoJsonSource>
  <GeoJsonSource id="past_found_coin" :data="pastFoundCoinSources">
    <SymbolLayer
      @click="emits('click', $event)"
      id="past_found_coin_symbol_layer"
      :style="{
        ...FOUND_COIN_SYMBOL_LAYER,
      }"
      :minzoom="22"
    />
  </GeoJsonSource>
</template>
