<script lang="ts" setup>
import {
  VERIFYING_FILL_LAYER,
  VERIFYING_LINE_LAYER,
  VERIFYING_LINE_GLOW_LAYER,
} from '@constants';
import { useMapStore } from '@stores';
import { useMapHelpers } from '@composables';
import { GeoJsonSource, FillLayer, LineLayer } from 'vue3-maplibre-gl';

const storeMap = useMapStore();

const { groupedSilverCoins } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();

const verifyingCoinSources = computed(() => {
  return makeSource(groupedSilverCoins.value.verifying);
});
</script>

<template>
  <GeoJsonSource :data="verifyingCoinSources">
    <FillLayer
      :style="{
        ...VERIFYING_FILL_LAYER,
      }"
    />
    <LineLayer
      :style="{
        ...VERIFYING_LINE_LAYER,
      }"
    />
    <LineLayer
      :style="{
        ...VERIFYING_LINE_GLOW_LAYER,
      }"
    />
  </GeoJsonSource>
</template>
