<script lang="ts" setup>
import {
  TIME_UP_FILL_LAYER,
  TIME_UP_LINE_LAYER,
  TIME_UP_BLOCK_LAYER,
} from '@constants';
import { useMapStore } from '@stores';
import { useMapHelpers } from '@composables';
import {
  GeoJSONSourceSpecification,
  GeoJsonSource,
  FillLayer,
  LineLayer,
} from 'vue3-maplibre-gl';
import { lineString } from '@turf/helpers';
import transformRotate from '@turf/transform-rotate';
import circle from '@turf/circle';

const storeMap = useMapStore();

const { groupedSilverCoins } = storeToRefs(storeMap);
const { makeSource, mapTransform } = useMapHelpers();

const timeUpCoinSources = computed(() => {
  return makeSource(groupedSilverCoins.value.time_up);
});

const timeUpBlockCoinSources = computed(() => {
  const sources: GeoJSONSourceSpecification['data'][] = [];
  groupedSilverCoins.value.time_up.forEach((s) => {
    const { center, radius } = s.properties.circle;
    const delta = (radius * 20) / 100;
    const baseLine = lineString([
      mapTransform(center.lng, center.lat, radius - delta, 0),
      mapTransform(center.lng, center.lat, radius - delta, 180),
    ]);
    const line = transformRotate(baseLine, 45);
    const circleFill = circle([center.lng, center.lat], radius - delta, {
      units: 'meters',
    });
    sources.push(makeSource([line, circleFill]));
  });
  return sources;
});
</script>
<template>
  <GeoJsonSource :data="timeUpCoinSources">
    <FillLayer
      :style="{
        ...TIME_UP_FILL_LAYER,
      }"
    />
    <LineLayer
      :style="{
        ...TIME_UP_LINE_LAYER,
      }"
    />
  </GeoJsonSource>
  <GeoJsonSource
    v-for="s in timeUpBlockCoinSources"
    :data="s"
    :key="s.toString()"
  >
    <LineLayer
      :style="{
        ...TIME_UP_BLOCK_LAYER,
      }"
    />
  </GeoJsonSource>
</template>
