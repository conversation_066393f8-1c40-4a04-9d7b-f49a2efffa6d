<script lang="ts" setup>
import {
  BOUNDARY_FUTURE_LINE_LAYER,
  BOUNDARY_FUTURE_OUTLINE_LAYER,
  BOUNDARY_FUTURE_OUTLINE_GLOW_LAYER,
} from '@constants';
import {
  LineLayer,
  GeoJsonSource,
  FillLayer,
  SymbolLayer,
} from 'vue3-maplibre-gl';
import { useMapHelpers } from '@composables';
import { useUserStore } from '@stores';
import boundary from '../boundary.json';

const storeUser = useUserStore();

const { currentSeason } = storeToRefs(storeUser);
const { makeSource } = useMapHelpers();
</script>
<template>
  <GeoJsonSource id="boundary" :data="makeSource(boundary.features)">
    <template
      v-if="
        !!currentSeason && ['ongoing', 'ended'].includes(currentSeason?.status)
      "
    >
      <LineLayer
        :style="{
          'line-color': '#CC0000',
          'line-width': [
            'interpolate',
            ['linear'],
            ['zoom'],
            9,
            8,
            14,
            12,
            20,
            14,
          ],

          'line-offset': [
            'interpolate',
            ['linear'],
            ['zoom'],
            9,
            3,
            14,
            7,
            20,
            8,
          ],
        }"
      />

      <LineLayer
        :style="{
          'line-color': '#CC0000',
          'line-width': [
            'interpolate',
            ['linear'],
            ['zoom'],
            9,
            8,
            14,
            12,
            20,
            14,
          ],
          'line-offset': [
            'interpolate',
            ['linear'],
            ['zoom'],
            1,
            0,
            9,
            -3,
            14,
            -7,
            20,
            -8,
          ],
        }"
      />
      <LineLayer
        :style="{
          'line-color': '#ffffff',
          'line-width': [
            'interpolate',
            ['linear'],
            ['zoom'],
            9,
            9,
            14,
            14,
            20,
            20,
          ],
        }"
      />
      <SymbolLayer
        :style="{
          // 'text-field': 'DBS',
          // 'text-size': 12,
          // 'text-color': '#000000',
          // 'text-halo-color': '#000000',
          // 'text-halo-width': 0.2,
          // 'text-anchor': 'center',
          'symbol-placement': 'line',
          'icon-allow-overlap': true,
          'symbol-spacing': [
            'interpolate',
            ['linear'],
            ['zoom'],
            9,
            50,
            14,
            100,
          ],
          'icon-rotation-alignment': 'map',
          'icon-image': 'boundary_dbs',
          'icon-size': ['interpolate', ['linear'], ['zoom'], 9, 0.3, 14, 0.45],
          'icon-keep-upright': true,
          'symbol-avoid-edges': true,
        }"
      />
    </template>
    <template v-else>
      <FillLayer
        :style="{
          ...BOUNDARY_FUTURE_LINE_LAYER,
        }"
      />

      <LineLayer
        :style="{
          ...BOUNDARY_FUTURE_OUTLINE_LAYER,
        }"
      />

      <LineLayer
        :style="{
          ...BOUNDARY_FUTURE_OUTLINE_GLOW_LAYER,
        }"
      />
    </template>
  </GeoJsonSource>
</template>
