<script lang="ts" setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useMapHelpers } from '@composables';
import { METAL_SONAR_NEGATIVE_FILL_LAYER } from '@constants';
import { useDialogStore, useMapStore } from '@stores';
import { FillLayer, GeoJsonSource, LineLayer } from 'vue3-maplibre-gl';
import circle from '@turf/circle';
import { Feature, Polygon } from '@turf/helpers';

const storeDialog = useDialogStore();
const storeMap = useMapStore();

const { showCoinSonarGUI } = storeToRefs(storeDialog);
const { coinSonar, lastLocations, coinSonarLayers } = storeToRefs(storeMap);

const { makeSource } = useMapHelpers();

const coinSonarSources = computed(() => {
  if (!coinSonarLayers.value.length) return makeSource([]);
  const circles = coinSonarLayers.value.map((n) => {
    const { location, radius } = n;
    return circle([location.lng, location.lat], radius, {
      properties: {
        ...n,
        color: n.found_coin ? '#29D798' : '#DF3126',
      },
      units: 'meters',
    });
  });
  return makeSource(circles);
});

const sonarOutlineSources = computed(() => {
  if (!coinSonarLayers.value.length) return makeSource([]);

  const outlines = [
    {
      multiplier: 1.025,
      properties: {
        opacity: 0.8,
        width: 2,
        'min-zoom': 9,
        'max-zoom': 20,
      },
    },
    {
      multiplier: 1.05,
      properties: {
        opacity: 0.3,
        width: 1,
        'min-zoom': 14,
        'max-zoom': 20,
      },
    },
  ];

  const allOutlines: Feature<Polygon>[] = [];

  coinSonarLayers.value.forEach((coinSonar) => {
    const { location, radius } = coinSonar;
    outlines.forEach(({ multiplier, properties }) => {
      const outline = circle(
        [location.lng, location.lat],
        radius * multiplier,
        {
          units: 'meters',
          properties: {
            ...properties,
            color: coinSonar.found_coin ? '#29D798' : '#DF3126',
          },
        }
      );
      allOutlines.push(outline);
    });
  });

  return makeSource(allOutlines);
});

const previewSources = computed(() => {
  if (!coinSonar.value.selectedPrice) return makeSource([]);

  const radius = coinSonar.value.selectedPrice.radius;
  const [lng, lat] = lastLocations.value;
  const c = circle([lng, lat], radius, {
    units: 'meters',
  });

  return makeSource([c]);
});
</script>
<template>
  <GeoJsonSource id="coin_sonar_circles" :data="coinSonarSources">
    <FillLayer
      id="coin_sonar_fill_layer"
      :style="{
        ...METAL_SONAR_NEGATIVE_FILL_LAYER,
        'fill-color': ['get', 'color'],
      }"
    />
  </GeoJsonSource>

  <GeoJsonSource id="sonar_outline_circles" :data="sonarOutlineSources">
    <LineLayer
      id="sonar_outline_line_layer"
      :style="{
        'line-color': ['get', 'color'],
        'line-width': [
          'interpolate',
          ['linear'],
          ['zoom'],
          12,
          1,
          14,
          2,
          20,
          1,
        ],
        'line-opacity': ['get', 'opacity'],
      }"
    />
  </GeoJsonSource>

  <template v-if="showCoinSonarGUI">
    <GeoJsonSource id="preview_sonar_circle" :data="previewSources">
      <LineLayer
        id="preview_sonar_line_layer"
        :style="{
          'line-color': '#ffffff',
          'line-dasharray': [2, 1],
        }"
      />
      <FillLayer
        id="preview_sonar_fill_layer"
        :style="{
          'fill-color': '#46FCF1',
          'fill-opacity': 0.2,
        }"
      />
    </GeoJsonSource>
  </template>
</template>
