<script lang="ts" setup>
import {
  ONGOING_COIN_PAID_FILL_LAYER,
  ONGOING_COIN_PAID_LINE_LAYER,
  ONGOING_COIN_PAID_LINE_GLOW_LAYER,
} from '@constants';
import { useMapStore } from '@stores';
import { useMapHelpers } from '@composables';
import { GeoJsonSource, FillLayer, LineLayer } from 'vue3-maplibre-gl';

const storeMap = useMapStore();

const { groupedSilverCoins } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();

const ongingCoinPaidSources = computed(() => {
  return makeSource(groupedSilverCoins.value.ongoing_paid);
});
</script>
<template>
  <GeoJsonSource :data="ongingCoinPaidSources">
    <FillLayer
      :style="{
        ...ONGOING_COIN_PAID_FILL_LAYER,
      }"
    />
    <LineLayer
      :style="{
        ...ONGOING_COIN_PAID_LINE_LAYER,
      }"
    />
    <LineLayer
      :style="{
        ...ONGOING_COIN_PAID_LINE_GLOW_LAYER,
      }"
    />
  </GeoJsonSource>
</template>
