<script setup lang="ts">
import {
  CircleLayer,
  GeoJsonSource,
  Image,
  SymbolLayer,
} from 'vue3-maplibre-gl';
import { useMapStore } from '@stores';
import {
  FeatureCollection,
  featureCollection,
  point,
  Properties,
  Point,
} from '@turf/helpers';
import { round, throttle } from 'lodash';

const mapStore = useMapStore();
const { devTools, mapIns } = storeToRefs(mapStore);

const fakeLocationSource = computed(
  (): FeatureCollection<Point, Properties> => {
    return featureCollection([
      point(devTools.value.location, {
        location: `${round(devTools.value.location[0], 6)}, ${round(
          devTools.value.location[1],
          6
        )}`,
      }),
    ]);
  }
);
const targetLocationSource = computed(
  (): FeatureCollection<Point, Properties> => {
    if (!devTools.value.targetLocation) return featureCollection([]);
    return featureCollection([point(devTools.value.targetLocation)]);
  }
);

function updateLocation(e: any) {
  if (!devTools.value.fakeGps || !devTools.value.pickLocationMode) return;
  devTools.value.targetLocation = [
    e.target.getCenter().lng,
    e.target.getCenter().lat,
  ];
}

const throttledOnMove = throttle(updateLocation, 20);

watch(
  () => devTools.value.pickLocationMode,
  (val) => {
    if (!mapIns.value) return;
    if (!val) {
      mapIns.value.off('move', throttledOnMove);
      mapIns.value.off('moveend', updateLocation);
    } else {
      mapIns.value.on('move', throttledOnMove);
      mapIns.value.on('moveend', updateLocation);
    }
  }
);

onMounted(() => {
  if (!mapIns.value) return;
  mapIns.value.on('moveend', updateLocation);
  mapIns.value.on('move', throttledOnMove);
  updateLocation({ target: mapIns.value });
});

onUnmounted(() => {
  if (!mapIns.value) return;
  mapIns.value.off('moveend', updateLocation);
  mapIns.value.off('move', throttledOnMove);
});
</script>

<template>
  <GeoJsonSource :data="fakeLocationSource">
    <CircleLayer
      :style="{
        'circle-radius': ['interpolate', ['linear'], ['zoom'], 1, 7, 24, 5],
        'circle-color': 'white',
        'circle-opacity': 1,
      }"
    />
    <CircleLayer
      :style="{
        'circle-radius': ['interpolate', ['linear'], ['zoom'], 1, 5, 24, 4],
        'circle-color': '#1ea1f1',
        'circle-opacity': 1,
      }"
    />
  </GeoJsonSource>
  <Image :images="[{ id: 'location', image: 'imgs/map/location.png' }]" />
  <GeoJsonSource v-if="devTools.pickLocationMode" :data="targetLocationSource">
    <SymbolLayer
      :style="{
        'icon-image': 'location',
        'icon-size': 1,
        'icon-allow-overlap': true,
        'icon-opacity': 1,
        'icon-anchor': 'bottom',
      }"
    />
    <SymbolLayer
      :style="{
        'text-anchor': 'top',
        'text-field': ['get', 'location'],
        'text-font': ['Open Sans Semibold', 'Arial Unicode MS Bold'],
        'text-offset': [0, 1.25],
      }"
    />
  </GeoJsonSource>
</template>
