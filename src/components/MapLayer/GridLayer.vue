<script lang="ts" setup>
import { useMapStore, useUserStore } from '@stores';
import { useMapHelpers } from '@composables';
import { MAP } from '@repositories';
import {
  GRID_GOLDEN_FILL_LAYER,
  GRID_GOLDEN_LINE_LAYER,
  GRID_ELIMINATED_FILL_LAYER,
  GRID_ELIMINATED_LINE_LAYER,
  GRID_PAID_ELIMINATED_FILL_LAYER,
  GRID_PAID_ELIMINATED_LINE_LAYER,
} from '@constants';
import { difference } from 'lodash';
import {
  GeoJsonSource,
  FillLayer,
  LineLayer,
  MapMouseEvent,
} from 'vue3-maplibre-gl';

interface Props {
  visibility: boolean;
}

interface Emits {
  (event: 'click', data: MapMouseEvent): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeMap = useMapStore();
const storeUser = useUserStore();

const { sourceGeoHashes, mapGeoHashes } = storeToRefs(storeMap);
const { isFoundGoldenCoin, settings } = storeToRefs(storeUser);
const { makeSource } = useMapHelpers();
// const { t } = useI18n();

const visibility = computed(() => props.visibility);

const goldenSources = computed(() => {
  if (!props.visibility) return makeSource([]);
  return makeSource(sourceGeoHashes.value.golden?.features);
});

const eliminatedSources = computed(() => {
  if (!props.visibility) return makeSource([]);
  return makeSource(sourceGeoHashes.value.eliminated?.features);
});

const paidEliminatedSources = computed(() => {
  if (!props.visibility) return makeSource([]);
  return makeSource(sourceGeoHashes.value.paidEliminated?.features);
});

const listenerGridSources = computed(() => {
  if (!props.visibility) return makeSource([]);
  return makeSource([
    ...(sourceGeoHashes.value.golden?.features || []),
    ...(sourceGeoHashes.value.eliminated?.features || []),
    ...(sourceGeoHashes.value.paidEliminated?.features || []),
  ]);
});

const goldenHash = computed(() => {
  return settings.value?.golden_coin.geohash;
});

// function renderPopup() {
//   removePopup('.golden-found-popup');

//   const location = settings.value?.golden_coin.location;
//   if (!location?.lat || !location.lng) return;

//   const { lat, lng } = location;

//   return newPopUp({
//     closeOnClick: false,
//     closeButton: false,
//     offset: [0, -90],
//     anchor: 'center',
//     className: 'golden-found-popup',
//     lngLat: [lng, lat],
//     html: t('POPUP_FOUND_GOLDEN', {
//       NAME: settings.value?.golden_coin?.winner_name,
//     }),
//   });
// }

async function makeFinalSources(geohash: string) {
  const { data } = await MAP.getGeoHashes();
  storeMap.mapGeoHashes = {
    golden: [geohash],
    eliminated: difference(data, [geohash]),
    paidEliminated: [],
  };
  const sources = storeMap.makeSourceFromMapGeoHashes(mapGeoHashes.value, {
    isGoldenCoin: true,
  });
  storeMap.sourceGeoHashes = sources;
}

watchEffect(() => {
  if (isFoundGoldenCoin.value && goldenHash.value)
    makeFinalSources(goldenHash.value);
});

// watch(
//   () => user.value?.lang,
//   () => {
//     removePopup('.golden-found-popup');
//     renderPopup();
//   }
// );

// watch(
//   () => props.visibility,
//   () => {
//     if (!props.visibility) removePopup('.golden-found-popup');
//     else renderPopup();
//   }
// );

onMounted(async () => {
  await nextTick();

  // storeMap.mapIns?.on('zoomend', () => {
  //   if (!isFoundGoldenCoin.value || !props.visibility) return;

  //   const target = zoom.value > 12 && zoom.value < 15;

  //   // if (target) renderPopup();
  //   // else removePopup('.golden-found-popup');
  // });
});
</script>
<template>
  <div id="golden-found-popup"></div>
  <GeoJsonSource :data="listenerGridSources">
    <FillLayer
      id="listener_grids_golden_fill_layer"
      :style="{
        ...GRID_GOLDEN_FILL_LAYER,
        visibility: visibility ? 'visible' : 'none',
        'fill-opacity': 0,
      }"
      @click="emits('click', $event)"
    />
  </GeoJsonSource>

  <GeoJsonSource :data="goldenSources">
    <FillLayer
      :style="{
        ...GRID_GOLDEN_FILL_LAYER,
        visibility: visibility ? 'visible' : 'none',
      }"
    />
    <LineLayer
      :style="{
        ...GRID_GOLDEN_LINE_LAYER,
        visibility: visibility ? 'visible' : 'none',
      }"
    />
  </GeoJsonSource>
  <GeoJsonSource :data="eliminatedSources">
    <FillLayer
      :style="{
        ...GRID_ELIMINATED_FILL_LAYER,
        visibility: visibility ? 'visible' : 'none',
      }"
    />
    <LineLayer
      :style="{
        ...GRID_ELIMINATED_LINE_LAYER,
        visibility: visibility ? 'visible' : 'none',
      }"
    />
  </GeoJsonSource>
  <GeoJsonSource :data="paidEliminatedSources">
    <FillLayer
      :style="{
        ...GRID_PAID_ELIMINATED_FILL_LAYER,
        visibility: visibility ? 'visible' : 'none',
      }"
    />
    <LineLayer
      :style="{
        ...GRID_PAID_ELIMINATED_LINE_LAYER,
        visibility: visibility ? 'visible' : 'none',
      }"
    />
  </GeoJsonSource>
</template>
