<script lang="ts" setup>
import { useCapitalandHooks, useMapHelpers } from '@composables';
import { LW_LAYERS } from '@constants';
import { point } from '@turf/helpers';
import { IAmenities } from '@types';
import { groupBy } from 'lodash';
import { GeoJsonSource, MapMouseEvent, SymbolLayer } from 'vue3-maplibre-gl';

interface Emits {
  (event: 'click', data: MapMouseEvent): void;
}

const emits = defineEmits<Emits>();

const LYDENWOODS_POINT = {
  coordinates: [103.788313, 1.291187],
  properties: {
    icon: 'lynden_woods',
    amenities: true,
  },
};

const { makeSource } = useMapHelpers();
const { amenitiesMissions } = useCapitalandHooks();

const groupedByLayerId = computed(() => {
  const groups = groupBy(amenitiesMissions.value, 'layer_id');
  return groups;
});

const amenitiesIconsSources = computed(() => {
  const pairs = [];
  for (const [preferLayerId, amenities] of Object.entries(
    groupedByLayerId.value
  )) {
    const data = amenities.map((amenity) =>
      point(amenity.location.coordinates, {
        ...amenity,
      })
    );
    if (preferLayerId === 'LyndenWoods') {
      data.push(
        point(
          LYDENWOODS_POINT.coordinates,
          LYDENWOODS_POINT.properties as IAmenities
        )
      );
    }

    pairs.push([preferLayerId, makeSource(data)] as const);
  }
  return pairs;
});
</script>
<template>
  <template v-for="[layerId, source] in amenitiesIconsSources" :key="layerId">
    <GeoJsonSource
      :data="source"
      v-if="LW_LAYERS[layerId] !== undefined"
      :options="{
        cluster: false,
      }"
    >
      <SymbolLayer
        @click="emits('click', $event)"
        :id="layerId"
        :style="{
          ...LW_LAYERS[layerId].layout,
          ...LW_LAYERS[layerId].paint,
          'icon-image': ['get', 'icon'],
          'icon-anchor': 'bottom',
        }"
        :minzoom="LW_LAYERS[layerId].minzoom"
        :maxzoom="LW_LAYERS[layerId].maxzoom"
      />
    </GeoJsonSource>
  </template>
</template>
