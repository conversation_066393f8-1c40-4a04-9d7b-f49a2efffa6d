<script lang="ts" setup>
import {
  FORFEITED_FILL_LAYER,
  FORFEITED_LINE_LAYER,
  FORFEITED_LINE_GLOW_LAYER,
} from '@constants';
import { useMapStore } from '@stores';
import { useMapHelpers } from '@composables';
import { GeoJsonSource, FillLayer, LineLayer } from 'vue3-maplibre-gl';

const storeMap = useMapStore();

const { groupedSilverCoins } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();

const forfeitedCoinSources = computed(() => {
  return makeSource(groupedSilverCoins.value.forfeited);
});
</script>
<template>
  <GeoJsonSource :data="forfeitedCoinSources">
    <FillLayer
      :style="{
        ...FORFEITED_FILL_LAYER,
      }"
    />
    <LineLayer
      :style="{
        ...FORFEITED_LINE_LAYER,
      }"
    />
    <LineLayer
      :style="{
        ...FORFEITED_LINE_GLOW_LAYER,
      }"
    />
  </GeoJsonSource>
</template>
