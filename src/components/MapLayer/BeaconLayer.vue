<script lang="ts" setup>
import { useBeacon, useMapHelpers } from '@composables';
import {
  Fill<PERSON>ayer,
  GeoJsonSource,
  LineLayer,
  LngLatLike,
  Marker,
} from 'vue3-maplibre-gl';
import { useDialogStore, useMapStore } from '@stores';
import circle from '@turf/circle';

interface BeaconDecorateMarker {
  pos: LngLatLike;
  id: string;
  class: string;
  anim: string;
}

const BEACON_COLORS = {
  OWNER: '#F3FF6C',
  NON_OWNER: '#00f5b4',
  GUI_FILL: '#46FCF1',
  GUI_LINE: '#ffffff',
} as const;

const ZOOM_THRESHOLDS = {
  MIN_MARKER_ZOOM: 12,
  MIN_ZOOM: 8,
} as const;

const SCALE_CONFIG = {
  FACTOR: 1,
  PADDING_MULTIPLIER: 1.2,
  OUTLINE_MULTIPLIERS: [1.025, 1.05],
} as const;

const storeDialog = useDialogStore();
const { showBeaconGUI } = storeToRefs(storeDialog);
const { beaconRadius, lastLocations, inUsingBeacon } = useBeacon();
const { makeSource, mapTransform } = useMapHelpers();
const { _zoom } = storeToRefs(useMapStore());

const createBeaconCircle = (
  lng: number,
  lat: number,
  radius: number,
  properties = {}
) => {
  return circle([lng, lat], radius, {
    units: 'meters',
    properties,
  });
};

const getBeaconColor = (isOwner: boolean): string => {
  return isOwner ? BEACON_COLORS.OWNER : BEACON_COLORS.NON_OWNER;
};

const scaleMarkerBaseOnMapZoom = computed(() => {
  return 1 + (_zoom.value - ZOOM_THRESHOLDS.MIN_ZOOM) * SCALE_CONFIG.FACTOR;
});

const userBeaconSources = computed(() => {
  if (!lastLocations.value?.length || !beaconRadius.value) {
    return makeSource([]);
  }

  const [lng, lat] = lastLocations.value;
  const beaconCircle = createBeaconCircle(lng, lat, beaconRadius.value);
  return makeSource([beaconCircle]);
});

const activeBeaconSources = computed(() => {
  if (!inUsingBeacon.value) {
    return makeSource([]);
  }

  const { location, radius, is_owner } = inUsingBeacon.value;
  const color = getBeaconColor(!!is_owner);

  const beaconCircle = createBeaconCircle(location.lng, location.lat, radius, {
    color,
  });

  return makeSource([beaconCircle]);
});

const BEACON_MARKER_CONFIG = [
  {
    id: 'star',
    class: 'w-[40px]',
    anim: '_pulse-reverse',
    degrees: 0,
    radiusOffset: 0,
  },
  {
    id: 'star',
    class: 'w-[20px]',
    anim: '_pulse',
    degrees: 10,
    radiusOffset: 0,
  },
  { id: 'sqkii', class: 'w-[10px]', anim: '', degrees: 80, radiusOffset: 0 },
  { id: 'percent', class: 'w-[5px]', anim: '', degrees: 190, radiusOffset: 0 },
  {
    id: 'star',
    class: 'w-[40px]',
    anim: '_pulse',
    degrees: 280,
    radiusOffset: 0,
  },
  {
    id: 'star',
    class: 'w-[20px]',
    anim: '_pulse-reverse',
    degrees: 260,
    radiusOffset: -10,
  },
] as const;

const beaconDecorateMarkers = computed<BeaconDecorateMarker[]>(() => {
  if (!inUsingBeacon.value?.is_owner) {
    return [];
  }

  const { lng, lat } = inUsingBeacon.value.location;
  const { radius } = inUsingBeacon.value;

  return BEACON_MARKER_CONFIG.map(
    ({ id, class: className, anim, degrees, radiusOffset }) => ({
      pos: mapTransform(lng, lat, radius + radiusOffset, degrees) as LngLatLike,
      id,
      class: className,
      anim,
    })
  );
});

const beaconOutlineSources = computed(() => {
  if (!inUsingBeacon.value) {
    return makeSource([]);
  }

  const { location, radius } = inUsingBeacon.value;
  const [innerMultiplier, outerMultiplier] = SCALE_CONFIG.OUTLINE_MULTIPLIERS;

  return makeSource([
    createBeaconCircle(location.lng, location.lat, radius * innerMultiplier, {
      opacity: 0.8,
      'min-zoom': 9,
      'max-zoom': 20,
    }),
    createBeaconCircle(location.lng, location.lat, radius * outerMultiplier, {
      opacity: 0.3,
      'min-zoom': 14,
      'max-zoom': 20,
    }),
  ]);
});
</script>
<template>
  <template v-if="inUsingBeacon && _zoom > ZOOM_THRESHOLDS.MIN_MARKER_ZOOM">
    <Marker
      v-for="(marker, index) in beaconDecorateMarkers"
      :key="`beacon-marker-${index}-${marker.id}-${marker.pos.toString()}`"
      :lnglat="marker.pos"
      :class="marker.class"
    >
      <img
        :id="marker.id"
        :src="`/imgs/bc_${marker.id}.png`"
        :class="[marker.class, marker.anim]"
        class="origin-center"
        :style="{
          transform: `scale(${scaleMarkerBaseOnMapZoom})`,
        }"
        :alt="`Beacon ${marker.id} decoration`"
      />
    </Marker>
  </template>

  <GeoJsonSource :data="userBeaconSources" v-if="showBeaconGUI">
    <LineLayer
      :style="{
        'line-color': BEACON_COLORS.GUI_LINE,
        'line-dasharray': [5, 2],
        'line-width': 3,
      }"
      before-id="beacon_pivot"
    />
    <FillLayer
      :style="{
        'fill-color': BEACON_COLORS.GUI_FILL,
        'fill-opacity': 0.2,
      }"
      before-id="beacon_pivot"
    />
  </GeoJsonSource>
  <GeoJsonSource :data="activeBeaconSources">
    <FillLayer
      id="beacon_in_use_fill"
      :style="{
        'fill-color': BEACON_COLORS.OWNER,
        'fill-opacity': 0.3,
      }"
    />
    <LineLayer
      id="beacon_in_use_outline"
      :style="{
        'line-color': BEACON_COLORS.OWNER,
        'line-width': [
          'interpolate',
          ['linear'],
          ['zoom'],
          9,
          1,
          12,
          10,
          15,
          0,
        ],
        'line-blur': 10,
      }"
    />
  </GeoJsonSource>

  <GeoJsonSource :data="beaconOutlineSources">
    <LineLayer
      id="beacon_outline"
      :style="{
        'line-color': BEACON_COLORS.OWNER,
        'line-width': [
          'interpolate',
          ['linear'],
          ['zoom'],
          12,
          1,
          14,
          3,
          20,
          10,
        ],
        'line-opacity': ['get', 'opacity'],
      }"
    />
  </GeoJsonSource>
</template>

<style lang="scss" scoped>
// Beacon marker animations
._pulse {
  animation: beacon-pulse 2s infinite ease-in;
}

._pulse-reverse {
  animation: beacon-pulse 2s infinite ease-in reverse;
}

@keyframes beacon-pulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  70% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0);
    opacity: 0;
  }
}

// Ensure markers are properly centered and responsive
.origin-center {
  transform-origin: center;
  transition: transform 0.2s ease-in-out;
}
</style>
