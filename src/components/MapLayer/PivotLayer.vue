<script lang="ts" setup>
import { GeoJsonSource, SymbolLayer } from 'vue3-maplibre-gl';
import { useMapStore } from '@stores';
import { useMapHelpers } from '@composables';
import { throttle } from 'lodash';

const props = defineProps<{
  pivotId: string;
  targetLayerIds: string[];
}>();

const storeMap = useMapStore();

const { mapIns } = storeToRefs(storeMap);
const { makeSource } = useMapHelpers();

function moveTargetLayers() {
  if (!mapIns.value) return;
  const layers = mapIns.value.getLayersOrder();
  const thisPivotIndex = layers.findIndex((layer) => layer === props.pivotId);
  const layersNeedToMove = layers.filter(
    (layer, index) =>
      props.targetLayerIds.includes(layer) && index < thisPivotIndex
  );
  for (const layerId of layersNeedToMove) {
    mapIns.value.moveLayer(layerId, props.pivotId);
    mapIns.value.moveLayer(props.pivotId, layerId);
  }
}

const debouncedMoveTargetLayers = throttle(moveTargetLayers, 1000);

function onAdded() {
  mapIns.value?.on('styledata', debouncedMoveTargetLayers);
}

onUnmounted(() => {
  mapIns.value?.off('styledata', debouncedMoveTargetLayers);
});
</script>
<template>
  <GeoJsonSource :data="makeSource([])">
    <SymbolLayer :id="pivotId" :minzoom="1" :maxzoom="24" @register="onAdded" />
  </GeoJsonSource>
</template>
