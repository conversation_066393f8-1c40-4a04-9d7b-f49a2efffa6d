<script lang="ts" setup>
import { FOUND_COIN_SYMBOL_LAYER } from '@constants';
import { useMapHelpers } from '@composables';
import { SymbolLayer, GeoJsonSource, MapMouseEvent } from 'vue3-maplibre-gl';
import { point } from '@turf/helpers';
import { useUserStore } from '@stores';

interface Props {
  visibility: boolean;
}

interface Emits {
  (event: 'click', data: MapMouseEvent): void;
}

const emits = defineEmits<Emits>();
const props = defineProps<Props>();

const storeUser = useUserStore();

const { settings } = storeToRefs(storeUser);
const { makeSource } = useMapHelpers();

const foundCoinSources = computed(() => {
  if (!props.visibility || !!settings.value?.flags?.hide_beach_station_map_icon)
    return makeSource([]);

  const s = point([103.81781256928298, 1.251313232529867], {
    icon: 'special-event',
    status: 'found',
    special_event: true,
    circle: {},
    winner_info: {},
    videos: [],
  });

  return makeSource([s]);
});
</script>
<template>
  <GeoJsonSource id="silver_special_event" :data="foundCoinSources">
    <SymbolLayer
      @click="emits('click', $event)"
      id="silver_special_event_symbol_layer"
      :style="{
        ...FOUND_COIN_SYMBOL_LAYER,
        visibility: visibility ? 'visible' : 'none',
        'icon-size': 0.35,
      }"
      :minzoom="14"
    />
  </GeoJsonSource>
  <!-- <Marker
    :lnglat="[103.81781256928298, 1.251313232529867]"
    anchor="bottom"
    class="special-event-marker"
    v-if="!settings?.flags?.hide_beach_station_map_icon"
  >
    <div
      class="flex items-center justify-center px-3 py-1 -mt-4 rounded opacity-0 pointer-events-none bg-cd"
      :style="{
        width: '85px',
      }"
      :class="{
        '!opacity-0 transition-opacity duration-500': zoom <= 14,
        '!opacity-100 transition-opacity duration-500': zoom >= 14,
        'bg-30m': currentState === 3,
        'bg-active': currentState === 4,
        'bg-end': currentState > 4,
      }"
    >
      <span v-if="[0, 1, 2, 3].includes(currentState)">
        in&nbsp;<b>{{ countdownContext?.countdown }}</b>
      </span>
      <span
        v-else-if="currentState === 4"
        class="font-extrabold"
        v-html="t('SENTOSA_SPECIAL_EVENT_ONGOING')"
      ></span>
      <span
        v-else
        class="font-extrabold"
        v-html="t('SENTOSA_SPECIAL_EVENT_ENDED')"
      ></span>
    </div>
  </Marker> -->
</template>
<style scoped lang="scss">
.bg-cd,
.bg-end {
  background-color: rgba(9, 9, 9, 0.7);
}

.bg-30m,
.bg-active {
  background: rgba(185, 0, 0, 0.7);
}

.special-event-marker {
  width: 85px;
}
</style>
