<script lang="ts" setup>
import { LineLayer, GeoJsonSource } from 'vue3-maplibre-gl';
import { useMapHelpers } from '@composables';
import geneo from '../geneo.json';

const { makeSource } = useMapHelpers();
</script>
<template>
  <GeoJsonSource id="geneo-boundaries" :data="makeSource(geneo.features)">
    <LineLayer
      :style="{
        'line-color': '#86DADE',
        'line-width': [
          'interpolate',
          ['linear'],
          ['zoom'],
          1,
          0.1,
          12,
          1,
          20,
          5,
        ],
        'line-opacity': [
          'interpolate',
          ['linear'],
          ['zoom'],
          1,
          0,
          9,
          0.3,
          12,
          1,
        ],
        'line-offset': [
          'interpolate',
          ['linear'],
          ['zoom'],
          1,
          0,
          12,
          1,
          13,
          2,
          20,
          10,
        ],
      }"
    />
    <LineLayer
      :style="{
        'line-color': '#F79B26',
        'line-width': ['interpolate', ['linear'], ['zoom'], 13, 0, 20, 5],
        'line-opacity': ['interpolate', ['linear'], ['zoom'], 10, 0, 13, 1],
        'line-offset': [
          'interpolate',
          ['linear'],
          ['zoom'],
          1,
          0,
          12,
          -1,
          13,
          -2,
          20,
          -10,
        ],
      }"
    />
    <LineLayer
      :style="{
        'line-color': '#8983D5',
        'line-width': [
          'interpolate',
          ['linear'],
          ['zoom'],
          9,
          0,
          12,
          1,
          13,
          3,
          20,
          14,
        ],
      }"
    />
    <LineLayer
      :style="{
        'line-color': '#ffffff',
        'line-width': [
          'interpolate',
          ['linear'],
          ['zoom'],
          1,
          0.1,
          12,
          0.2,
          20,
          4,
        ],
      }"
    />
  </GeoJsonSource>
</template>
