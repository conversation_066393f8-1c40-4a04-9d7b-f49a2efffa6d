<script setup lang="ts">
import { WinnerAgreementEN, WinnerAgreementVN } from '@components';
import { useUserStore } from '@stores';

defineProps<{
  reward: string;
}>();
const storeUser = useUserStore();
const { settings } = storeToRefs(storeUser);
const { t } = useI18n();

const panel = ref('en');
</script>

<template>
  <div class="relative winner-agreement-wrapper">
    <div
      class="tab-nav flex flex-nowrap justify-between"
      v-if="Number(settings?.supported_languages.length) > 1"
    >
      <div
        v-if="settings?.supported_languages.includes('en')"
        class="tab-nav__item p-2"
        :class="[panel === 'en' && 'active']"
        @click="panel = 'en'"
      >
        {{ t('WINNERSAGREEMENT_ENGTAB') }}
      </div>
      <div
        v-if="settings?.supported_languages.includes('vi')"
        class="tab-nav__item p-2"
        :class="[panel === 'vi' && 'active']"
        @click="panel = 'vi'"
      >
        {{ t('WINNERSAGREEMENT_TRANSLATEDTAB') }}
      </div>
    </div>
    <q-tab-panels
      v-model="panel"
      animated
      class="rounded-borders tab-content"
      style="height: calc(100vh - 500px)"
    >
      <q-tab-panel
        name="en"
        v-if="settings?.supported_languages.includes('en')"
      >
        <div class="overflow-auto pb-4" style="max-height: calc(100vh - 500px)">
          <WinnerAgreementEN :reward="reward" />
        </div>
      </q-tab-panel>

      <q-tab-panel
        name="vi"
        v-if="settings?.supported_languages.includes('vi')"
      >
        <div class="overflow-auto pb-4" style="max-height: calc(100vh - 500px)">
          <WinnerAgreementVN />
        </div>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<style lang="scss">
.winner-agreement-wrapper {
  .q-tab-panel {
    padding: 0;
  }
}
</style>

<style scoped lang="scss">
.winner-agreement-wrapper {
  margin-left: -20px;
  margin-right: -20px;
}
.tab-nav {
  &__item {
    background: rgba(91, 70, 116, 0.8);
    border-radius: 8px 8px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 49.5%;
    transition: all 0.75s;
    &.active {
      background: rgba(81, 29, 133, 0.8);
    }
  }
}

.tab-content {
  background: linear-gradient(
    180deg,
    rgba(81, 29, 133, 0.8) 0%,
    rgba(32, 13, 55, 0.8) 56%
  );
  border-radius: 0px 0px 8px 8px;
  position: relative;
  padding: 16px 16px 0px 16px !important;
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(
      180deg,
      rgba(71, 31, 127, 0) 0%,
      #211746 93.35%
    );
  }
}
</style>
