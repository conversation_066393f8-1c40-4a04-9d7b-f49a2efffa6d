<script setup lang="ts">
import {
  playSFX,
  useMetalDetector,
  useCoinSonar,
  useState,
  useTrackData,
  useBeacon,
} from '@composables';
import { errorNotify } from '@helpers';
import { useMapStore, useUserStore, useBAStore, useDialogStore } from '@stores';
import type { IGlobeState } from '@types';

const storeMap = useMapStore();
const storeUser = useUserStore();
const storeBA = useBAStore();
const storeDialog = useDialogStore();

const { t } = useI18n();
const { isEnabledGPS, user, onboarding, features } = storeToRefs(storeUser);
const { user_brand_actions } = storeToRefs(storeBA);
const { geoState } = storeToRefs(storeMap);
const { openDialog, push } = useMicroRoute();
const { canTriggerCoinSonar, handleRequestCoinSonar } = useCoinSonar();
const { isTriggerMetalDetector, metalDetectorAction } = useMetalDetector();
const { state } = useState<IGlobeState>();
const { track } = useTrackData();
const { canTriggerBeacon } = useBeacon();

const showDrawer = ref(false);

function triggerGPS() {
  if (isEnabledGPS.value) storeMap.triggerGPS();
  else if (geoState.value === 'UNAVAILABLE')
    errorNotify({
      message: t('GPS_ERROR_NOT_DETECTED'),
    });
  else openDialog('gps');
}

const beaconAction = () => {
  if (!isEnabledGPS.value)
    return errorNotify({
      message: t('GPS_ERROR_NOT_DETECTED'),
    });
  if (!onboarding.value?.first_beacon) return openDialog('beacon_welcome');
  storeDialog.showBeaconGUI = true;
};

const MAP_DYNAMIC_BUTTON = computed<
  Record<
    string,
    {
      icon: string;
      content: string;
      action: () => void;
      tick?: boolean;
      size?: number;
    }
  >
>(() => ({
  power_up: {
    icon: 'imgs/powerup.png',
    content: t('BTN_POWER_UP'),
    size: 12.5,
    action: () => {
      showDrawer.value = !showDrawer.value;
    },
  },
  beacon: {
    icon: 'imgs/beacon.png',
    content: t('BTN_BEACON'),
    action: beaconAction,
  },
  gps: {
    icon: 'imgs/icons/location_locked.png',
    content: t('TOOLKIT_BTN_GPS'),
    action: triggerGPS,
  },
  step_pedometor: {
    icon: '/icons/step.png',
    content: t('BTN_STEP'),
    action: () => {
      state.value.pedometer_onboarding_success = false;
      openDialog('track_step');
    },
  },

  claim_crystal: {
    icon: '/icons/plus_crystal.png',
    content: t('BTN_CLAIM_CRYSTAL'),
    action: () => {
      push('offer_wall');
    },
  },

  metal_sonar: {
    icon: '/imgs/metal-sonar.png',
    content: t('BTN_METAL_SONAR'),
    size: 18,
    action: () => handleRequestCoinSonar(true),
  },
  metal_detector: {
    icon: '/imgs/metal-detector.png',
    content: t('BTN_METAL_DETECTOR'),
    action: metalDetectorAction,
  },
}));

const power_ups = computed(() => [
  {
    name: 'metal-sonar',
    icon: '/imgs/metal-sonar.png',
    label: t('BTN_POWERUP_METAL_SONAR'),
    isActive: canTriggerCoinSonar.value,
    action: () => handleRequestCoinSonar(true),
  },
  {
    name: 'metal_detector',
    icon: '/imgs/metal-detector.png',
    label: t('BTN_POWERUP_METAL_DETECTOR'),
    isActive: isTriggerMetalDetector.value,
    isBeta: true,
    action: metalDetectorAction,
  },
  {
    name: 'beacon',
    icon: 'imgs/beacon.png',
    label: t('BTN_BEACON'),
    isActive: canTriggerBeacon.value,
    action: beaconAction,
  },
]);

const dynamic_btn = computed<string | undefined>(() => {
  switch (true) {
    case !!state.value.pedometer_onboarding_success:
      return 'step_pedometor';
    case ['UNAVAILABLE', 'OFF'].includes(geoState.value):
      return 'gps';
    case power_ups.value.filter((item) => item.isActive).length > 1:
      return 'power_up';
    case canTriggerCoinSonar.value:
      return 'metal_sonar';
    case isTriggerMetalDetector.value:
      return 'metal_detector';
    case !!user.value?.resources.beacon && !!features.value?.beacon:
      return 'beacon';
    case user_brand_actions.value.some((item) => item.status === 'verified'):
      return 'claim_crystal';
    default:
      return undefined;
  }
});

// watch(
//   () => showDrawer.value,
//   (val) => {
//     gsap.to('.power-up-drawer', {
//       // width: val ? 'auto' : '0px',
//       opacity: val ? 1 : 0,
//       duration: 0.3,
//       onComplete: () => {
//         gsap.killTweensOf('.power-up-drawer');
//       },
//     });
//   }
// );
</script>
<template>
  <div
    v-if="!!dynamic_btn"
    class="dynamic_btn"
    :class="[geoState === 'WAITING_ACTIVE' && 'pointer-events-none']"
    @click.stop="
      playSFX('button');
      MAP_DYNAMIC_BUTTON[dynamic_btn].action();
      track('home_screen', {
        button: 'home_dynamicbutton',
        action: dynamic_btn,
      });
    "
  >
    <div class="relative fit flex w-full">
      <div
        class="power-up-drawer pointer-events-none flex flex-nowrap items-center gap-5"
        :class="{
          'power-up-drawer-active': showDrawer,
        }"
      >
        <div
          class="text-center relative"
          v-for="item in power_ups.filter((el) => el.isActive)"
          :key="item.name"
        >
          <div
            v-if="item.isBeta"
            class="w-[34px] text-[10px] absolute -top-4 left-1/2 -translate-x-1/2"
            style="
              background-size: 100% 100%;
              background-image: url('/imgs/bg_beta.png');
            "
          >
            {{ t('BETA') }}
          </div>
          <Button
            shape="square"
            @click="
              item.action();
              showDrawer = false;
            "
            class="scale-[1.2]"
          >
            <Icon :name="item.icon" type="url" />
          </Button>
          <p
            class="text-[10px] mt-1 font-bold whitespace-nowrap"
            v-html="item.label"
          ></p>
        </div>
      </div>
      <q-spinner-tail
        v-if="geoState === 'WAITING_ACTIVE'"
        color="white"
        size="2em"
        class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
      />
      <div
        v-else
        class="fit leading-normal gap-0.5 flex-center column relative z-50"
        :class="{
          'gps-pulse': dynamic_btn === 'gps' && !isEnabledGPS,
        }"
      >
        <div
          v-if="MAP_DYNAMIC_BUTTON[dynamic_btn].tick"
          class="absolute right-[5px] top-[7px] bg-[#FF4242] w-[10px] h-[10px] rounded-[50%]"
        ></div>
        <Icon
          :name="MAP_DYNAMIC_BUTTON[dynamic_btn].icon"
          :size="MAP_DYNAMIC_BUTTON[dynamic_btn].size || 20"
          type="url"
          class="mr-[-6px]"
        />
        <p
          class="font-bold text-[10px] text-center mr-[-6px]"
          v-html="MAP_DYNAMIC_BUTTON[dynamic_btn].content"
        ></p>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.dynamic_btn {
  width: 68px;
  height: 68px;
  background: url(/imgs/button/dynamic.png);
  background-size: 100% 100%;
  .power-up-drawer {
    position: absolute;
    left: 5px;
    height: 68px;
    opacity: 0;
    transform: translateX(-100%);
    pointer-events: none;
    &-active {
      opacity: 1;
      pointer-events: all !important;
      border-radius: 5px;
      border: 1px solid #11d1f9;
      background: linear-gradient(
        180deg,
        #091a3b 0%,
        rgba(9, 26, 59, 0.7) 100%
      );
      border-right: none;
      padding: 5px 20px;
    }
  }
}

.gps-pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 35px;
  height: 35px;
  z-index: 2;
  transform: scale(1);
  background: rgba(52, 172, 224, 0.3);
  animation: pulse-blue 2s infinite;
  box-shadow: 0 0 0 0 rgba(52, 172, 224, 1);
  border-radius: 8px;
}

@keyframes pulse-blue {
  0% {
    transform: scale(1.05);
    box-shadow: 0 0 0 0 rgba(52, 172, 224, 0.7);
  }

  60% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(52, 172, 224, 0);
  }

  100% {
    transform: scale(1.05);
    box-shadow: 0 0 0 0 rgba(52, 172, 224, 0);
  }
}
</style>
