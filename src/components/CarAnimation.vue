<script lang="ts" setup>
import gsap, { Linear, Bounce } from 'gsap';

interface Props {
  assets?: string;
}

const props = defineProps<Props>();

const tl = gsap.timeline({ repeat: -1 });

function startAnimation() {
  tl.to('.driven-wheel', {
    rotation: '-=360_cw',
    ease: Linear.easeNone,
    duration: 1.5,
    repeat: -1,
  }).fromTo(
    '.car',
    {
      y: 8,
    },
    {
      y: 0,
      yoyo: true,
      repeat: -1,
      duration: 1.5,
      ease: Bounce.easeIn,
    },
    '-=1.5'
  );
}

onMounted(async () => {
  await nextTick();
  if (!props.assets) startAnimation();
});

onBeforeUnmount(() => {
  tl && tl.kill();
});
</script>

<template>
  <div class="kv absolute top-0 left-0 w-screen h-[109vw] pointer-events-none">
    <Icon v-if="assets" :name="assets" class="!w-full" />
    <template v-else>
      <div class="car absolute top-0 left-0 z-10 w-full h-full"></div>
      <div class="driven-wheel left">
        <div class="blur"></div>
      </div>
      <Icon
        class="absolute z-30 bottom-[19vw] left-[18vw] w-[25px] h-[25px] opacity-50"
        name="kv/driven-wheel-light"
      />
      <div class="driven-wheel right">
        <div class="blur"></div>
      </div>
      <Icon
        class="absolute z-30 bottom-[19vw] right-[23.5vw] w-[25px] h-[25px] opacity-50"
        name="kv/driven-wheel-light"
      />
    </template>
  </div>
</template>
<style lang="scss" scoped>
.kv {
  .car {
    background-image: url('/imgs/kv/car.png');
    background-size: contain;
    background-repeat: no-repeat;
    transform: scaleX(1.05);
  }

  .driven-wheel {
    position: absolute;
    width: 53px;
    height: 53px;
    background-image: url('/imgs/kv/driven-wheel.png');
    background-size: contain;
    background-repeat: no-repeat;
    bottom: 18vw;
    z-index: 20;
    .blur {
      background-image: radial-gradient(
        46.65% 46.43% at 50.05% 54%,
        #864dce 0%,
        #7c47bf 5%,
        #502e7c 31%,
        #2e1a46 54%
      );
      opacity: 0.4;
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 4;
      border-radius: 50%;
    }
    &.left {
      left: 11.5vw;
    }
    &.right {
      right: 22.5vw;
    }
  }
}
</style>
