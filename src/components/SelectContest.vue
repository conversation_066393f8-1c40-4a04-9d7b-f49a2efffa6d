<script lang="ts" setup>
interface Props {
  schools: string[];
}

interface Emits {
  (e: 'selected', value: string): void;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();

const school = ref('');
const expand = ref(false);

function handleSelected(s: string) {
  school.value = s;
  expand.value = false;
  emits('selected', s);
}
</script>

<template>
  <Expansion v-model="expand" group="schools" class="schools">
    <template v-slot:header>
      <div class="text-left flex flex-col justify-center">
        <div class="text-xs" v-html="t('VOTING_SELECT_UNIVERSITY')"></div>
        <div
          v-if="school"
          class="text-sm font-bold text-[#00e0ff] mt-2 whitespace-nowrap text-ellipsis overflow-hidden w-[240px]"
          v-html="t(school)"
        ></div>
      </div>
    </template>

    <q-card
      style="background: transparent"
      class="text-left h-[250px] overflow-y-auto overflow-x-hidden flex flex-nowrap flex-col px-3"
    >
      <div
        v-for="s in schools"
        :key="s"
        class="s text-sm py-2"
        :class="{
          'text-[#00e0ff]': s === school,
        }"
        v-html="t(s)"
        @click="handleSelected(s)"
      ></div>
    </q-card>
  </Expansion>
</template>
<style lang="scss" scoped>
.schools {
  width: 100%;
  background: #04081d;
  border-radius: 10px;
  box-shadow: 2px 2px 10px rgba(#04081d, 0.1);
  .s {
    &:not(:last-child) {
      border-bottom: 1px solid #00e0ff;
    }
  }
}
</style>
