<script setup lang="ts">
import { random } from 'lodash';
import { useMapStore, useUserStore } from '@stores';
import { useTrackData } from '@composables';
import gsap, { Power4, Linear } from 'gsap';
import { BRAND_SOV } from '@constants';
import { CapitalandKV } from '@components';

interface Props {
  fetched: boolean;
}

const props = defineProps<Props>();

const deploymentNumber = computed(() => process.env.DEPLOYMENT_NUMBER);

const tl = gsap.timeline();
const storeUser = useUserStore();
// const storeDialog = useDialogStore();

const { seasonCode } = storeToRefs(storeUser);
const { t } = useI18n();
const { track, trackTime } = useTrackData();
const { fetched } = toRefs(props);

const progress = ref(0);
const label = computed(() => (progress.value * 100).toFixed(0) + '%');

const fixedBrandSov = computed(() => {
  return [BRAND_SOV.CAPITALAND];
});
const randomBrandSov = ref(
  fixedBrandSov.value[random(0, fixedBrandSov.value.length - 1)]
);

track('brand_sov', {
  component_id: 'loading_screen_kv',
  result: randomBrandSov.value,
  success: true,
});

const TIPS = [
  'SAFETY_HINT1_TITLE',
  'SAFETY_HINT2_TITLE',
  'SAFETY_HINT3_TITLE',
  'SAFETY_HINT4_TITLE',
  'SAFETY_HINT5_TITLE',
  'SAFETY_HINT6_TITLE',
  'SAFETY_HINT7_TITLE',
];

const FLAVOUR = [
  'FLAVOUR_1',
  'FLAVOUR_2',
  'FLAVOUR_3',
  'FLAVOUR_4',
  'FLAVOUR_5',
];
const storeMap = useMapStore();
const { loading } = storeToRefs(storeMap);
const tip = computed(() => t(TIPS[random(0, TIPS.length - 1)]));
const flavour = computed(() => t(FLAVOUR[random(0, FLAVOUR.length - 1)]));

watch([loading, fetched], async ([loadingVal, fetchedVal]) => {
  if (!loadingVal && fetchedVal) {
    await nextTick();
    tl && tl.kill();
    gsap.to(progress, {
      value: 1,
      ease: Linear.easeNone,
      duration: (100 - progress.value) / 100 + 0.5,
      onComplete: () => {
        trackTime('loading_completed');
        storeMap._loading = loadingVal;
      },
    });
  }
});

onMounted(async () => {
  await nextTick();
  track('loading_start', {
    timestamp: Date.now(),
  });
  tl.to(progress, {
    duration: 50,
    value: 0.6,
    ease: Power4.easeOut,
  }).to(progress, {
    duration: 30,
    value: 0.99,
    ease: Power4.easeOut,
  });
});

onBeforeUnmount(() => {
  tl && tl.kill();
});
</script>

<template>
  <div class="text-center fixed inset-0 bg-[#0f132a]">
    <div
      class="fade-in loading fullscreen"
      :class="`season-${seasonCode.toLowerCase()}`"
    >
      <TestingComponent>
        <div
          style="
            position: fixed;
            bottom: 10px;
            width: 100%;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 8px;
            color: #777;
            z-index: 9999;
          "
        >
          Build ({{ deploymentNumber }}):
          {{ new Date(Number(deploymentNumber) * 1000).toLocaleString() }}
        </div>
      </TestingComponent>

      <CapitalandKV />
      <div class="absolute loading-card">
        <div
          class="tip-box text-sm mb-[30px]"
          :class="[randomBrandSov]"
          v-html="
            t('LOADING_TIP', {
              TIP: tip,
            })
          "
        ></div>
        <div class="loading-progress mb-[30px]">
          <div class="progress-bar" :style="{ width: `${progress * 100}%` }">
            <div class="text-sm font-bold progress-label">
              {{ label }}
            </div>
          </div>
        </div>
        <div class="text-base font-bold text-[#481700]" v-html="flavour"></div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.loading {
  background: linear-gradient(180deg, #193852 62.09%, #031e35 93.1%);
  overflow-x: hidden;

  .loading-card {
    width: 100%;
    padding: 18vw 30px 0 30px;
    bottom: 10vw;
    left: 0;
    right: 0;
    z-index: 9999;
  }
  .loading-progress {
    position: relative;
    background: rgba($color: #481700, $alpha: 0.9);
    width: 100%;
    height: 18px;
    border-radius: 100px;
    border: 2px solid #481700;
    .progress-bar {
      height: 100%;
      max-width: 100%;
      border-radius: 100px;
      background: linear-gradient(90deg, #f39100 0%, #ff7c24 100%);
      .progress-label {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .tip-box {
    padding: 10px;
    border-radius: 5px;
    background: linear-gradient(90deg, #e78c04 0%, #fb6e0f 100%), #fe842a;
  }
}

.fade-in {
  animation: fadeIn 0.1s ease-in-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
