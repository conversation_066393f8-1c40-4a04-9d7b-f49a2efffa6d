<script lang="ts" setup>
import { useTimedMission, useTrackData } from '@composables';
import { timeCountDown } from '@helpers';
import { useDialogStore } from '@stores';
import gsap, { Expo } from 'gsap';
import { clamp } from 'lodash';
import { useQuasar } from 'quasar';

const tl = gsap.timeline();

const storeDialog = useDialogStore();

const { showCoinSonarGUI, showBeaconGUI, showMetalDetectorGUI } =
  storeToRefs(storeDialog);
const { timeMissionData, expiredAt, NO_GO_BTN, loading, handleSubmitMission } =
  useTimedMission();
const { t } = useI18n();
const { track } = useTrackData();

const toggle = ref(false);
const { platform } = useQuasar();
const brandName = computed(() => {
  const data = timeMissionData.value?.data || {};
  const svClientId =
    data.location_based?.sv_client || data.use_sqkii_voucher?.sv_client;
  return svClientId ? t(`SV_CLIENT_${svClientId.toUpperCase()}`) : '';
});
watch(toggle, (value) => {
  track('home_missions_toggle', {
    toggle: !value ? 'close' : 'open',
  });
  track('home_screen', {
    button: 'home_mission',
  });
  if (value) tl.play(0);
  else tl.reverse(1);
});

watch(
  [showCoinSonarGUI, showBeaconGUI, showMetalDetectorGUI],
  ([sonarGUI, beaconGUI, metalDetectorGUI]) => {
    if ([sonarGUI, beaconGUI, metalDetectorGUI].some(Boolean))
      toggle.value = false;
  }
);
function toggleAnimate(): GSAPTimeline {
  tl.to('.missions', {
    opacity: 0,
    x: 5,
    position: 'absolute',
    duration: 0.2,
  })
    .fromTo(
      '.box',
      {
        width: 0,
        opacity: 0,
        overflow: 'hidden',
      },
      {
        opacity: 1,
        width: '75vw',
        duration: 1,
        ease: Expo.easeInOut,
      },
      '-=0.4'
    )
    .set('.box', {
      height: 'auto',
      overflow: 'visible',
      pointerEvents: 'all',
    })
    .fromTo(
      '.box > div',
      {
        x: -5,
        opacity: 0,
      },
      {
        opacity: 1,
        x: 0,
        duration: 1,
        stagger: 0.1,
        ease: Expo.easeInOut,
      }
    )
    .paused();
  return tl;
}
onMounted(() => {
  toggleAnimate();
  toggle.value = true;
});

watch(
  () => timeMissionData.value?.is_active,
  (isActive, prevIsActive) => {
    if (isActive && !prevIsActive) toggle.value = true;
  }
);
</script>
<template>
  <div
    v-if="
      timeMissionData &&
      (timeMissionData?.is_active ||
        Number(timeMissionData?.unclaimed_timed_missions?.length) > 0)
    "
  >
    <div class="relative missions w-[60px]" @click="toggle = !toggle">
      <Icon name="time-mission" :size="57" />
      <Icon
        name="toggle"
        :size="14"
        class="absolute right-0.5 top-1/2 -translate-y-1/2 rotate-90"
      />
    </div>
    <div
      class="w-[50vw] box flex flex-col items-start gap-2 relative opacity-0 pointer-events-none"
    >
      <div class="_bubble-name min-w-[180px]" v-if="!!expiredAt">
        <div
          class="bubble-name-text"
          v-html="
            t('TIMED_MISSION_EXPIRED_AT', {
              TIME: timeCountDown(expiredAt),
            })
          "
        ></div>
      </div>
      <Icon
        class="absolute -translate-y-1/2 -right-4 top-1/2"
        name="time-mission-toggle"
        :size="16"
        @click="toggle = !toggle"
      />
      <div class="flex w-full gap-2 flex-nowrap">
        <div
          class="relative flex items-center justify-center rewards size-12 shrink-0"
        >
          <Icon name="crystal" :size="40" />
          <div class="absolute bottom-0 text-sm right-1 text-border">
            {{ timeMissionData.mission.reward }}
          </div>
        </div>
        <div
          class="flex w-full gap-2 flex-nowrap"
          :class="{
            'flex-col': timeMissionData.status === 'verified',
          }"
        >
          <div class="flex flex-col w-full gap-2">
            <div
              class="w-full text-sm text-left text-d"
              v-html="
                t(timeMissionData.mission.description, {
                  brand_name: brandName,
                })
              "
            ></div>
            <div class="process-bar" v-if="!!timeMissionData.progress">
              <div class="process-label text-border">
                <span v-if="timeMissionData.progress.show_unit_on_current">
                  {{ timeMissionData.progress.prefix_unit }}
                </span>
                {{
                  Math.floor(
                    clamp(
                      timeMissionData.progress.current,
                      0,
                      timeMissionData.progress.required
                    )
                  )
                }}
                <span v-if="timeMissionData.progress.show_unit_on_current">
                  {{ timeMissionData.progress.suffix_unit }}
                </span>
                /
                <span>{{ timeMissionData.progress.prefix_unit }}</span>
                {{ timeMissionData.progress.required }}
                <span>{{ timeMissionData.progress.suffix_unit }}</span>
              </div>
              <div
                class="process"
                :style="{
                  width: `${
                    (timeMissionData.progress.current /
                      timeMissionData.progress.required) *
                    100
                  }%`,
                }"
              ></div>
            </div>
          </div>
          <div
            v-if="
              !NO_GO_BTN.includes(timeMissionData.type) &&
              timeMissionData.status === 'new'
            "
          >
            <template
              v-if="
                ['open_external_link', 'visit_web'].includes(
                  timeMissionData.type
                ) && timeMissionData.status === 'new'
              "
            >
              <a
                :href="
                  (platform.is.ios
                    ? timeMissionData.brandAction?.metadata.app_store_link
                    : timeMissionData.brandAction?.metadata.android_link) ||
                  timeMissionData.brandAction?.link
                "
                target="_blank"
                rel="noopener noreferrer"
              >
                <Button
                  size="small"
                  :label="t('OFFERWALL_BASE_BRANDACTIONACTIONBUTTON')"
                  @click="handleSubmitMission(timeMissionData)"
                />
              </a>
            </template>
            <template v-else>
              <Button
                size="small"
                :variant="
                  timeMissionData?.status === 'new' ? 'primary' : 'secondary'
                "
                :label="
                  timeMissionData?.status === 'new'
                    ? t('OFFERWALL_BASE_BRANDACTIONACTIONBUTTON')
                    : t('OFFERWALL_CLAIM_ACTIONBUTTON')
                "
                @click="handleSubmitMission(timeMissionData)"
              />
            </template>
          </div>
          <template v-if="timeMissionData.status === 'verified'">
            <Button
              class="!w-full"
              size="small"
              :label="t('DAILY_MISSIONS_BTN_CLAIM')"
              variant="secondary"
              :loading="loading"
              @click="handleSubmitMission(timeMissionData)"
            />
          </template>
        </div>
      </div>
      <div
        class="w-full rounded-[5px] bg-[#091A3B] p-1 text-pretty"
        v-if="
          timeMissionData.type === 'spf_quiz' &&
          timeMissionData.data?.spf_quiz?.crime_advisory_message
        "
      >
        {{ t(timeMissionData.data.spf_quiz.crime_advisory_message) }}
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.box {
  padding: 16px 10px;
  width: 70vw;
  min-height: 60px;
  border-radius: 5px;
  border-right: 1px solid #d64dda;
  background: linear-gradient(270deg, rgba(50, 38, 111, 0.7) 0%, #32266f 78.5%);
  backdrop-filter: blur(1.75px);
  height: auto;
  overflow: visible;
  pointer-events: all;
  .rewards {
    background-image: url(/imgs/daily-rewards-frame.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  ._bubble-name {
    position: absolute;
    left: 0;
    top: -10px;
    height: 18px;
    padding: 0 20px 0 8px;
    background: linear-gradient(270deg, #d535cf 2.37%, #7247cd 100%);
    border-radius: 2px;
    transform: skewX(-10deg);
    &-text {
      position: relative;
      transform: skewX(10deg) translateY(-50%);
      font-size: 12px;
      line-height: 14px;
      margin-top: 11px;
      width: 100%;
      height: 100%;
      white-space: nowrap;
    }
  }
}

.process-bar {
  position: relative;
  background: rgba($color: #4f0649, $alpha: 0.9);
  box-shadow: 0px -3px 5px 0px #00000080;
  width: 100%;
  height: 20px;
  border-radius: 6px;
  .process {
    position: absolute;
    height: 100%;
    background: linear-gradient(90deg, #7147cd 0%, #d834cf 100%);
    border-radius: inherit;
    max-width: 100%;
  }
  .process-label {
    position: absolute;
    z-index: 2;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
.text-border {
  text-shadow: rgb(65, 6, 60) 2px 0px 0px,
    rgb(65, 6, 60) 1.75517px 0.958851px 0px,
    rgb(65, 6, 60) 1.0806px 1.68294px 0px,
    rgb(65, 6, 60) 0.141474px 1.99499px 0px,
    rgb(65, 6, 60) -0.832294px 1.81859px 0px,
    rgb(65, 6, 60) -1.60229px 1.19694px 0px,
    rgb(65, 6, 60) -1.97998px 0.28224px 0px,
    rgb(65, 6, 60) -1.87291px -0.701566px 0px,
    rgb(65, 6, 60) -1.30729px -1.5136px 0px,
    rgb(65, 6, 60) -0.421592px -1.95506px 0px,
    rgb(65, 6, 60) 0.567324px -1.91785px 0px,
    rgb(65, 6, 60) 1.41734px -1.41108px 0px,
    rgb(65, 6, 60) 1.92034px -0.558831px 0px;
}
</style>
