<script lang="ts" setup>
import { playSFX } from '@composables';

interface Emits {
  (event: 'update:modelValue', value: number): void;
}

const emits = defineEmits<Emits>();

const quantity = ref(1);

watch(quantity, (value) => {
  emits('update:modelValue', value);
});
</script>
<template>
  <div
    class="w-[225px] h-[60px] mx-auto bg-[#091A3B] rounded flex justify-between items-center px-5"
  >
    <div
      class="flex justify-center items-center size-[30px] rounded-full bg-[#6bcfdd]"
      :class="{ 'opacity-50 pointer-events-none': quantity === 1 }"
      @click="
        quantity--;
        playSFX('button');
      "
    >
      <Icon name="minus" />
    </div>
    <div class="text-2xl font-bold">{{ quantity }}</div>
    <div
      class="flex justify-center items-center size-[30px] rounded-full bg-[#6bcfdd]"
      :class="{ 'opacity-50 pointer-events-none': quantity === 20 }"
      @click="
        quantity++;
        playSFX('button');
      "
    >
      <Icon name="plus" />
    </div>
  </div>
</template>
