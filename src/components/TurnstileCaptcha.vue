<script lang="ts" setup>
import VueTurnstile from 'vue-turnstile';

interface Props {
  modelValue?: string;
  theme?: 'light' | 'dark' | 'auto';
  size?: 'normal' | 'compact';
  language?: string;
  appearance?: 'always' | 'execute' | 'interaction-only';
  resetTrigger?: boolean;
}

interface Emits {
  (e: 'update:modelValue', token: string): void;
  (e: 'success', token: string): void;
  (e: 'error'): void;
  (e: 'expired'): void;
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'auto',
  size: 'normal',
  language: 'en',
  appearance: 'always',
  resetTrigger: false,
});

const emits = defineEmits<Emits>();

const turnstileRef = ref<InstanceType<typeof VueTurnstile>>();
const siteKey = process.env.TURNSTILE_SITE_KEY as string;
const token = ref('');

watch(
  () => props.resetTrigger,
  (newValue) => {
    if (newValue && turnstileRef.value) {
      turnstileRef.value.reset();
    }
  }
);

watch(
  token,
  (_token) => {
    if (_token) emits('success', token.value);
  },
  {
    immediate: true,
  }
);

function onError() {
  token.value = '';
  emits('update:modelValue', '');
  emits('error');
}

function onExpired() {
  token.value = '';
  emits('update:modelValue', '');
  emits('expired');
}

function reset() {
  token.value = '';
  if (turnstileRef.value) {
    turnstileRef.value.reset();
  }
}

onBeforeUnmount(() => {
  if (turnstileRef.value) {
    turnstileRef.value.reset();
  }
});

// Expose reset method
defineExpose({
  reset,
});
</script>

<template>
  <div class="turnstile-captcha">
    <VueTurnstile
      ref="turnstileRef"
      v-model="token"
      :site-key="siteKey"
      :theme="theme"
      :size="size"
      :language="language"
      :appearance="appearance"
      @error="onError"
      @expired="onExpired"
    />
  </div>
</template>

<style scoped>
.turnstile-captcha {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}
</style>
