<script lang="ts" setup>
import { useDialogStore } from '@stores';
import type { ISurveyAnswer, ISurveyData } from '@types';

interface ISurvey {
  data: ISurveyData;
  stage: number;
}

interface Emits {
  (e: 'survey', values: ISurvey): void;
  (e: 'submit'): void;
}

interface Props {
  stage: number;
  data: ISurveyData[];
  isBA?: boolean;
  loading: boolean;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeDialog = useDialogStore();

const { isBack } = storeToRefs(storeDialog);
const { t } = useI18n();
const { loading } = toRefs(props);
const routerStage = ref<number[]>([]);
const stage = ref(props.stage);
// const previous = usePrevious(stage);
const shadowData = ref<ISurveyData[]>(props.data);
const currentData = computed(() => shadowData.value[stage.value]);
// const star = computed(() => {
//   return {
//     active: 'imgs/star/active.png',
//     inactive: 'imgs/star/inactive.png',
//   };
// });

function surveyPush(id: number | string) {
  const idx = shadowData.value.findIndex((item) => item.id === id);
  if (idx >= 0) stage.value = idx;
}

const disabled = computed(() => {
  if (currentData.value.multiple)
    return (
      currentData.value.a.every((a) => !a.selected) ||
      currentData.value.a.some(
        (a) => !a.value && a.selected && a.type === 'area'
      )
    );
  if (currentData.value.type === 'select')
    return currentData.value.a.every((a) => !a.selected);
  return currentData.value.a.every((a) => !a.value);
});

function toggleSelection(answer: ISurveyAnswer) {
  shadowData.value[stage.value].a = shadowData.value[stage.value].a.map(
    (item) => ({
      ...item,
      selected: item === answer ? !item.selected : false,
    })
  );
}

async function handleNextQuestion() {
  if (props.isBA) {
    switch (currentData.value.type) {
      case 'rate':
        const a = Number(currentData.value.a[0].value);
        let id = 0;

        if (a <= 2) id = 2; // 1star/2star next question 2
        else if (a >= 3 && a < 5) id = 3; // 3star/4star next question 3
        else id = 4; // 5star next question 4

        if (!id) return;

        const data = shadowData.value.find((d) => d.id === id);

        if (!data) return;
        const currentStage = shadowData.value[stage.value];
        shadowData.value = [currentStage, data];
        emits('survey', {
          data: currentStage,
          stage: stage.value + 1,
        });

        break;
      case 'area':
        emits('survey', {
          data: currentData.value,
          stage: stage.value,
        });
    }

    if (stage.value === 1) emits('submit');
    else stage.value++;

    return;
  }

  if (stage.value === shadowData.value.length - 1) {
    emits('survey', {
      data: currentData.value,
      stage: stage.value,
    });
    emits('submit');
  } else {
    routerStage.value.push(stage.value);
    emits('survey', {
      data: currentData.value,
      stage: stage.value,
    });
    if (!!currentData.value.condition?.next) {
      const surveyId = currentData.value.condition?.next(
        currentData.value
      ) as unknown as number;
      if (surveyId) surveyPush(surveyId);
      else stage.value++;
    } else {
      stage.value++;
    }
  }

  // if (stage.value === shadowData.value.length - 1) {
  //   emits('survey', {
  //     data: currentData.value,
  //     stage: stage.value,
  //   });
  //   emits('submit');
  // } else {
  //   routerStage.value.push(stage.value);
  //   switch (stage.value) {
  //     case 3:
  //       const index_3 = shadowData.value.findIndex(
  //         (item) =>
  //           item.id === 4 &&
  //           item.a.some(
  //             (a) =>
  //               a.selected &&
  //               t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_4') === a.value
  //           )
  //       );
  //       if (index_3 > -1) stage.value = 4;
  //       else stage.value = 5;
  //       break;
  //     case 5:
  //       const index_5 = shadowData.value.findIndex(
  //         (item) =>
  //           item.id === 4 &&
  //           item.a.some(
  //             (a) =>
  //               a.selected &&
  //               [
  //                 t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_2'),
  //                 t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_3'),
  //               ].includes(a.value.toString())
  //           )
  //       );

  //       if (index_5 > -1) stage.value = 6;
  //       else stage.value = 11;
  //       break;
  //     case 8:
  //       if (+currentData.value.a[0].value > 6) stage.value = 9; // >= 7 stars
  //       else stage.value = 10;
  //       break;
  //     case 9:
  //       stage.value = 11;
  //       break;
  //     case 11:
  //       const index_10 = shadowData.value.findIndex(
  //         (item) =>
  //           item.id === 4 &&
  //           item.a.some(
  //             (a) =>
  //               a.selected &&
  //               [
  //                 t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_2'),
  //                 t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_3'),
  //               ].includes(a.value.toString())
  //           )
  //       );
  //       if (index_10 > -1) stage.value = 12;
  //       else stage.value = 17;
  //       break;
  //     case 12:
  //       if (+currentData.value.a[0].value <= 6) stage.value = 13;
  //       else stage.value = 14;
  //       break;
  //     case 14:
  //       const index_11 = currentData.value.a.findIndex(
  //         (item) =>
  //           item.selected &&
  //           (item.value === t('END_SURVEY_OPTIONS_HUNT_WITH_2') ||
  //             item.value === t('END_SURVEY_OPTIONS_HUNT_WITH_3'))
  //       );
  //       if (index_11 > -1) stage.value = 15;
  //       else stage.value = 16;
  //       break;
  //     case 15:
  //       const index_13 = currentData.value.a.findIndex(
  //         (item) =>
  //           item.selected && item.value === t('END_SURVEY_OPTIONS_HUNT_WITH_1')
  //       );
  //       if (index_13 > -1) stage.value = 16;
  //       else stage.value = 17;
  //       break;

  //     case 17:
  //       const index_15 = currentData.value.a.findIndex(
  //         (a) =>
  //           a.selected && a.value === t('END_SURVEY_OPTIONS_CRYSTAL_ACTIONS_1') // Choose "Yes"
  //       );
  //       if (index_15 > -1) stage.value = 18;
  //       else stage.value = 19;
  //       break;
  //     case 18:
  //       stage.value = 20;
  //       break;
  //     case 21:
  //       const index_19 = shadowData.value.findIndex(
  //         (item) =>
  //           item.id === 16 &&
  //           item.a.some(
  //             (a) =>
  //               a.selected &&
  //               a.value === t('END_SURVEY_OPTIONS_CRYSTAL_ACTIONS_1')
  //           )
  //       );
  //       if (index_19 > -1) stage.value = 22;
  //       else stage.value = 23;
  //       break;

  //     case 23:
  //       if (+currentData.value.a[0].value <= 6) stage.value = 24; // >= 7 stars
  //       else stage.value = 25;
  //       break;
  //     case 25:
  //       const answer = shadowData.value
  //         .find((item) => item.id === 24)
  //         ?.a.find((item) => item.selected)?.value;
  //       if (
  //         [
  //           t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_4'),
  //           t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_5'),
  //         ].includes(answer?.toString() || '')
  //       )
  //         stage.value = 26;
  //       else if (
  //         (t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_1'),
  //         t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_2'),
  //         t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_3'),
  //         t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_4').includes(
  //           answer?.toString() || ''
  //         ))
  //       )
  //         stage.value = 27;
  //       else stage.value = 29;
  //       break;
  //     case 27:
  //       if (+currentData.value.a[0].value <= 6) stage.value = 28; // >= 7 stars
  //       else stage.value = 29;
  //       break;
  //     case 28:
  //       const answer_26 = shadowData.value
  //         .find((item) => item.id === 24)
  //         ?.a.find((item) => item.selected)?.value;
  //       if (
  //         (t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_1'),
  //         t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_2'),
  //         t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_3'),
  //         t('END_SURVEY_OPTIONS_USE_SQKIIVOUCHER_4').includes(
  //           answer_26?.toString() || ''
  //         ))
  //       )
  //         stage.value = 29;
  //       else stage.value = 30;
  //       break;
  //     default:
  //       stage.value++;
  //       break;
  //   }

  //   emits('survey', {
  //     data: currentData.value,
  //     stage: stage.value,
  //   });
  // }
}

function handleBackQuestion() {
  const popValue = routerStage.value.pop();
  stage.value = popValue || 0;
  // switch (stage.value) {
  //   case 4:
  //   case 5:
  //     stage.value = 3;
  //     break;
  //   case 7:
  //   case 9:
  //     stage.value = 5;
  //     break;
  //   case 10:
  //   case 11:
  //   case 12:
  //   case 13:
  //   stage.value = 9;
  //     break
  //   case 15:
  //     stage.value = 4;
  //     break;
  //   case 16:
  //   case 17:
  //     stage.value = 15;
  //     break;
  //   default:
  //     stage.value = Number(previous.value);
  //     break;
  // }
}

watch(isBack, (value) => {
  if (value) {
    handleBackQuestion();
    shadowData.value = props.data;
    storeDialog.isBack = false;
  }
});
</script>
<template>
  <Transition>
    <div class="survey-question" v-if="shadowData.length">
      <div
        class="mb-1 text-sm opacity-50"
        v-html="
          t('SURVEY_POPUP_QUESTION_NUMBER', {
            CURRENT: stage + 1,
            TOTAL: isBA ? 2 : shadowData.length,
          })
        "
      ></div>
      <div class="mb-2 text-lg" v-html="currentData.q"></div>
      <div
        v-if="currentData.sub_q"
        class="mb-5 text-sm"
        v-html="currentData.sub_q"
      ></div>
      <template v-if="currentData.multiple">
        <div class="mb-10">
          <div class="flex flex-col gap-2">
            <div
              class="bg-[#12caf366] text-sm rounded-lg opacity-50 p-2"
              :class="{
                '!opacity-100': a.selected,
              }"
              v-for="(a, index) in currentData.a"
              :key="index"
            >
              <div v-if="a.type === 'area'" class="flex flex-col gap-2">
                <div
                  class="text-sm"
                  v-html="a.title"
                  @click="a.selected = !a.selected"
                ></div>
                <div @click="a.selected = true">
                  <q-input
                    v-model="a.value"
                    type="textarea"
                    standout
                    dense
                    bg-color="dark"
                    color="white"
                    :placeholder="t('SURVEY_POPUP_AREA_PLACEHOLDER')"
                  />
                </div>
              </div>
              <div
                v-else-if="a.type === 'image'"
                class="flex flex-col gap-2"
                @click="a.selected = !a.selected"
              >
                <div class="text-sm" v-html="a.title"></div>
                <Icon
                  :name="String(a.image)"
                  class="object-cover w-full h-max"
                />
              </div>
              <div
                v-else
                v-html="a.value"
                @click="a.selected = !a.selected"
              ></div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="mb-10">
          <section v-show="currentData.type === 'rate'">
            <div v-for="(a, index) in currentData.a" :key="index">
              <div class="mb-2 text-center">
                <q-slider
                  class="mt-8"
                  v-for="(a, index) in currentData.a"
                  :key="index"
                  :markers="true"
                  :label-always="true"
                  :model-value="Number(a.value)"
                  :min="0"
                  :max="a.total || 10"
                  @update:model-value="(value) => (a.value = Number(value))"
                />
                <!-- <q-rating
                  :model-value="Number(a.value)"
                  :max="a.total || 10"
                  :size="a.size || '25px'"
                  :icon="`img:${star.inactive}`"
                  :icon-selected="`img:${star.active}`"
                  @update:model-value="(value) => (a.value = value)"
                /> -->
              </div>
              <div class="flex justify-between">
                <div v-html="currentData.min_rate_text"></div>
                <div v-html="currentData.max_rate_text"></div>
              </div>
            </div>
          </section>
          <section v-show="currentData.type === 'area'">
            <q-input
              v-for="(a, index) in currentData.a"
              :key="index"
              v-model="a.value"
              type="textarea"
              standout
              dense
              bg-color="dark"
              color="white"
              :placeholder="t('SURVEY_POPUP_AREA_PLACEHOLDER')"
            />
          </section>
          <section v-show="currentData.type === 'select'">
            <div class="flex flex-col gap-2">
              <div
                class="bg-[#12caf366] text-sm rounded-lg opacity-50 p-2"
                :class="{
                  '!opacity-100': a.selected,
                }"
                v-for="(a, index) in currentData.a"
                :key="index"
                v-html="a.value"
                @click="toggleSelection(a)"
              ></div>
            </div>
          </section>
          <section v-show="currentData.type === 'slide'">
            <q-slider
              class="mt-8 hide-pin"
              v-for="(a, index) in currentData.a"
              :key="index"
              :markers="true"
              :label-value="a.value + (currentData.slide?.unit || '')"
              :label-always="true"
              :marker-labels="
                (val) => `${val + (currentData.slide?.unit || '')}`
              "
              :model-value="Number(a.value)"
              :min="0"
              :max="100"
              :step="currentData.slide?.step || 1"
              @update:model-value="(value) => (a.value = Number(value))"
            />
          </section>
        </div>
      </template>

      <div class="text-center">
        <Button
          :label="t('SURVEY_BUTTON_NEXT')"
          :disable="disabled"
          :loading="loading"
          @click="handleNextQuestion"
        />
      </div>
    </div>
  </Transition>
</template>
<style lang="scss">
.survey-question {
  .v-enter-active,
  .v-leave-active {
    transition: opacity 1s ease;
  }

  .v-enter-from,
  .v-leave-to {
    opacity: 0;
  }
}
</style>
