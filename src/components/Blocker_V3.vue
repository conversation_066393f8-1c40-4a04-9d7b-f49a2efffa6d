<script setup lang="ts">
import { useTrackData } from '@composables';
import { useWindowSize } from '@vueuse/core';
import QrcodeVue from 'qrcode.vue';

const { width } = useWindowSize();

const { t } = useI18n();
const { track } = useTrackData();

const URL = computed(() => process.env.APP_END_POINT);

track('page_blocker');
</script>
<template>
  <div class="relative w-screen h-screen blocker">
    <section v-show="width > 1024">
      <div class="desktop-background"></div>
      <div class="desktop-middle-ground"></div>
      <div class="desktop-far-ground"></div>
      <Icon
        name="sentosa-blocker/desktop-beach-objects"
        class="!w-[65vw] absolute left-[15vw] top-[50vh] z-10"
      />
      <Icon
        name="sentosa-blocker/desktop-characters"
        class="!w-[60vw] absolute top-[30vh] left-1/2 -translate-x-1/2 z-20"
      />
      <Icon
        name="sentosa-blocker/desktop-foreground"
        class="!w-full absolute bottom-0 z-[9999]"
      />
      <div
        class="left-1/2 -translate-x-1/2 w-full absolute bottom-0 z-50 flex justify-between items-center px-20"
      >
        <Icon name="sentosa-blocker/logo" class="!w-[23vw]" />
        <div class="flex items-center gap-5">
          <div>
            <div
              class="text-2xl font-bold text-[#481700]"
              v-html="t('BLOCKER_CTA')"
            ></div>
            <div
              class="text-2xl font-400 text-[#481700]"
              v-html="t('BLOCKER_CTA_DESC')"
            ></div>
          </div>
          <div class="qr flex justify-center items-center mb-5">
            <QrcodeVue class="rounded-lg" :value="URL" :size="140" level="H" />
          </div>
        </div>
      </div>
    </section>
    <section v-show="width <= 1024">
      <Icon
        name="sentosa-blocker/logo"
        class="!w-[40vw] absolute left-1/2 -translate-x-1/2 top-10"
      />
      <div class="tablet-background"></div>
      <div class="tablet-middle-ground"></div>
      <div class="tablet-far-ground"></div>
      <Icon
        name="sentosa-blocker/tablet-beach-objects"
        class="!w-[75vw] absolute left-[15vw] top-[50vh] z-10"
      />
      <Icon
        name="sentosa-blocker/tablet-characters"
        class="!w-[85vw] absolute top-[35vh] left-1/2 -translate-x-1/2 z-20"
      />
      <Icon
        name="sentosa-blocker/tablet-foreground"
        class="!w-full absolute bottom-0 z-[9999]"
      />
      <div
        class="left-1/2 -translate-x-1/2 absolute bottom-10 z-50 flex flex-col justify-center items-center"
      >
        <div class="qr flex justify-center items-center mb-5">
          <QrcodeVue class="rounded-lg" :value="URL" :size="140" level="H" />
        </div>
        <div
          class="text-2xl font-bold text-[#481700]"
          v-html="t('BLOCKER_CTA')"
        ></div>
        <div
          class="text-2xl font-400 text-[#481700]"
          v-html="t('BLOCKER_CTA_DESC')"
        ></div>
      </div>
    </section>
  </div>
</template>
<style lang="scss" scoped>
.blocker {
  background: linear-gradient(
    180deg,
    #146eff -10.23%,
    #b2eaee 24.11%,
    #b1d9bd 45.33%
  );
}

.desktop-background {
  width: 100%;
  height: 60vh;
  background: url('/imgs/sentosa-blocker/desktop-background.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.desktop-middle-ground {
  width: 100%;
  height: 100%;
  background: url('/imgs/sentosa-blocker/desktop-middle-ground.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 5;
}

.desktop-far-ground {
  width: 100%;
  height: 51vh;
  background: url('/imgs/sentosa-blocker/desktop-far-ground.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
}

.tablet-background {
  width: 100%;
  height: 60vh;
  background: url('/imgs/sentosa-blocker/tablet-background.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 5vh;
  left: 0;
  z-index: 1;
}

.tablet-middle-ground {
  width: 100%;
  height: 80%;
  background: url('/imgs/sentosa-blocker/tablet-middle-ground.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: bottom;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 5;
}

.tablet-far-ground {
  width: 100%;
  height: 40vh;
  background: url('/imgs/sentosa-blocker/tablet-far-ground.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 7vh;
  left: 0;
  z-index: 2;
}

.qr {
  width: 175px;
  height: 175px;
  background: linear-gradient(90deg, #f39100 0%, #ff7c24 100%);
  clip-path: polygon(
    10% 0%,
    90% 0%,
    100% 10%,
    100% 90%,
    90% 100%,
    10% 100%,
    0% 90%,
    0% 10%
  );
}
</style>
