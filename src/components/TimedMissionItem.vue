<script lang="ts" setup>
import { useTimedMission } from '@composables';
import type { IActivatingTimeMission } from '@types';
import { useQuasar } from 'quasar';

interface Props {
  activatingMission: IActivatingTimeMission;
}

const props = defineProps<Props>();

const { t } = useI18n();
const { handleSubmitMission, loading, NO_GO_BTN } = useTimedMission();
const { openDialog } = useMicroRoute();
const { platform } = useQuasar();
const brandName = computed(() => {
  const data = props.activatingMission?.data || {};
  const svClientId =
    data.location_based?.sv_client || data.use_sqkii_voucher?.sv_client;
  return svClientId ? t(`SV_CLIENT_${svClientId.toUpperCase()}`) : '';
});
</script>
<template>
  <div class="flex flex-col items-center justify-center gap-2 card">
    <div class="flex items-start w-full gap-2 flex-nowrap">
      <div
        class="relative flex items-center justify-center rewards size-12 shrink-0"
      >
        <Icon name="crystal" :size="40" />
        <div class="absolute bottom-0 text-sm right-1 text-border">
          {{ activatingMission.mission.reward }}
        </div>
      </div>
      <div class="flex flex-col w-full gap-2">
        <div
          class="text-sm text-left line-clamp-2"
          v-html="
            t(activatingMission.mission.description, { brand_name: brandName })
          "
        ></div>
        <div class="process-bar" v-if="!!activatingMission.progress">
          <div class="process-label text-border">
            {{ activatingMission.progress.current }}
            <span>{{ activatingMission.progress.prefix_unit }}</span>
            /
            {{ activatingMission.progress.required }}
            <span>{{ activatingMission.progress.suffix_unit }}</span>
          </div>
          <div
            class="process"
            :style="{
              width: `${
                (activatingMission.progress.current /
                  activatingMission.progress.required) *
                100
              }%`,
            }"
          ></div>
        </div>
        <div class="flex gap-4 flex-nowrap">
          <template v-if="activatingMission.status === 'new'">
            <Button
              class="flex-shrink-0"
              size="small"
              :label="t('DAILY_MISSIONS_BTN_GIVE_UP')"
              variant="purple"
              @click="openDialog('give_up_time_mission')"
            />
            <template v-if="!NO_GO_BTN.includes(activatingMission.type)">
              <template
                v-if="
                  ['open_external_link', 'visit_web'].includes(
                    activatingMission.type
                  ) && activatingMission.status === 'new'
                "
              >
                <a
                  :href="
                    (platform.is.ios
                      ? activatingMission.brandAction?.metadata.app_store_link
                      : activatingMission.brandAction?.metadata.android_link) ||
                    activatingMission.brandAction?.link
                  "
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button
                    class="flex-1"
                    size="small"
                    :label="t('DAILY_MISSIONS_BTN_GO')"
                    @click="handleSubmitMission(activatingMission)"
                  />
                </a>
              </template>
              <template v-else>
                <Button
                  class="flex-1"
                  size="small"
                  :label="t('DAILY_MISSIONS_BTN_GO')"
                  @click="handleSubmitMission(activatingMission)"
                />
              </template>
            </template>
          </template>
          <template v-else>
            <Button
              class="!w-full"
              size="small"
              :label="t('DAILY_MISSIONS_BTN_CLAIM')"
              variant="secondary"
              :loading="loading"
              @click="handleSubmitMission(activatingMission)"
            />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.card {
  width: 100%;
  padding: 12px;
  border-radius: 10px;
  border: 2px solid #38cfe5;
  background: #320b5b;
  box-shadow: 0px 0px 7px 0px #38cfe5;
  .rewards {
    background-image: url(/imgs/daily-rewards-frame.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .process-bar {
    position: relative;
    background: rgba($color: #4f0649, $alpha: 0.9);
    box-shadow: 0px -3px 5px 0px #00000080;
    width: 100%;
    height: 20px;
    border-radius: 6px;
    .process {
      position: absolute;
      height: 100%;
      background: linear-gradient(90deg, #7147cd 0%, #d834cf 100%);
      border-radius: inherit;
      max-width: 100%;
    }
    .process-label {
      position: absolute;
      z-index: 2;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .text-border {
    text-shadow: rgb(65, 6, 60) 2px 0px 0px,
      rgb(65, 6, 60) 1.75517px 0.958851px 0px,
      rgb(65, 6, 60) 1.0806px 1.68294px 0px,
      rgb(65, 6, 60) 0.141474px 1.99499px 0px,
      rgb(65, 6, 60) -0.832294px 1.81859px 0px,
      rgb(65, 6, 60) -1.60229px 1.19694px 0px,
      rgb(65, 6, 60) -1.97998px 0.28224px 0px,
      rgb(65, 6, 60) -1.87291px -0.701566px 0px,
      rgb(65, 6, 60) -1.30729px -1.5136px 0px,
      rgb(65, 6, 60) -0.421592px -1.95506px 0px,
      rgb(65, 6, 60) 0.567324px -1.91785px 0px,
      rgb(65, 6, 60) 1.41734px -1.41108px 0px,
      rgb(65, 6, 60) 1.92034px -0.558831px 0px;
  }
}
</style>
