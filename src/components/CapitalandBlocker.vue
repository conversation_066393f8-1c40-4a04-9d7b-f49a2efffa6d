<script setup lang="ts">
import { useTrackData } from '@composables';
import { useWindowSize } from '@vueuse/core';
import QrcodeVue from 'qrcode.vue';

const { width } = useWindowSize();

const { t } = useI18n();
const { track } = useTrackData();

const URL = computed(() => process.env.APP_END_POINT);

track('page_blocker');
</script>
<template>
  <section class="blocker" v-show="width > 1024">
    <div class="desktop-background"></div>
    <Icon
      name="capitaland-kv/geneo-logo"
      class="absolute left-[52%] top-[50%] w-[147px] h-auto"
    />
    <Icon
      name="capitaland-kv/shinobii"
      class="absolute right-[32.5%] top-[42%] w-16 h-auto"
    />
    <Icon
      name="capitaland-kv/nancii-rooster-table"
      class="absolute left-[28%] bottom-[20%] w-[213px] h-auto"
    />
    <Icon
      name="capitaland-kv/lynden-woods"
      class="absolute top-0 left-[20%] w-[250px] h-auto"
    />
    <Icon
      name="capitaland-kv/sqkii-handbag-desktop"
      class="absolute right-[7%] bottom-[15%] w-[360px] h-auto"
    />
    <div class="absolute left-5 right-5 bottom-2">
      <div class="w-full flex justify-between">
        <Icon name="capitaland-kv/logos" class="w-[400px] h-auto" />
        <div class="flex justify-center items-center gap-4 -mb-5">
          <div class="flex flex-col bg-[#2B2B2B60] p-2.5 rounded-xl">
            <div
              class="text-2xl font-bold text-white"
              v-html="t('BLOCKER_CTA')"
            ></div>
            <div
              class="text-2xl font-bold text-white"
              v-html="t('BLOCKER_CTA_DESC')"
            ></div>
          </div>
          <div class="qr flex justify-center items-center mb-5">
            <QrcodeVue class="rounded-md" :value="URL" :size="100" level="H" />
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="blocker" v-show="width <= 1024">
    <div class="tablet-background"></div>
    <Icon
      name="capitaland-kv/geneo-logo"
      class="absolute left-[53%] top-[42%] w-[137px] h-auto"
    />
    <Icon
      name="capitaland-kv/shinobii"
      class="absolute right-[21%] top-[37%] w-14 h-auto"
    />
    <Icon
      name="capitaland-kv/nancii-rooster-table"
      class="absolute left-[17%] bottom-[35.5%] w-[200px] h-auto"
    />
    <Icon
      name="capitaland-kv/lynden-woods"
      class="absolute top-0 left-[3%] w-[250px] h-auto"
    />
    <Icon
      name="capitaland-kv/sqkii-handbag-tablet"
      class="absolute right-0 bottom-[8%] w-[350px] h-auto"
    />
    <Icon
      name="capitaland-kv/logos"
      class="absolute top-6 right-6 w-[300px] h-auto"
    />
    <div
      class="absolute left-1/2 -translate-x-1/2 bottom-5 flex flex-nowrap justify-center items-center gap-4"
    >
      <div class="qr flex justify-center items-center">
        <QrcodeVue class="rounded-md" :value="URL" :size="100" level="H" />
      </div>
      <div
        class="flex flex-col flex-nowrap bg-[#2B2B2B60] p-2.5 rounded-xl w-max"
      >
        <div
          class="text-2xl font-bold text-white"
          v-html="t('BLOCKER_CTA')"
        ></div>
        <div
          class="text-2xl font-bold text-white"
          v-html="t('BLOCKER_CTA_DESC')"
        ></div>
      </div>
    </div>
  </section>
</template>
<style lang="scss" scoped>
.blocker {
  position: relative;
  width: 100vw;
  height: 100vh;
}

.desktop-background {
  position: relative;
  background-image: url(/imgs/capitaland-kv/desktop-blocker-bg.png);
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  background-position: center;
}

.tablet-background {
  position: relative;
  background-image: url(/imgs/capitaland-kv/tablet-blocker-bg.png);
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  background-position: center;
}

.qr {
  background-image: url(/imgs/capitaland-kv/qr-frame.png);
  width: 130px;
  height: 130px;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
