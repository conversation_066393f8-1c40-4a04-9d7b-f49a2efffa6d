<script lang="ts" setup>
import { Application } from 'pixi.js';
import { usePixiAnimations } from '@composables';

interface Props {
  name: string;
  json: string;
  animationSpeed?: number;
  size?: 'cover' | 'contain';
  width?: number;
  height?: number;
}

const props = defineProps<Props>();
const canvasPixi = ref<{
  app: Application;
  clearCache: () => void;
} | null>(null);

const pixiCanvas = ref(null);
const hide = ref(false);

onMounted(async () => {
  await nextTick();
  canvasPixi.value = await usePixiAnimations({
    ...props,
    id: pixiCanvas.value as unknown as HTMLCanvasElement,
  });
});

onBeforeUnmount(() => {
  const timeout = setTimeout(() => {
    canvasPixi.value?.app.destroy(false);
    canvasPixi.value?.clearCache();
    clearTimeout(timeout);
  }, 300);
});
</script>

<template>
  <canvas v-if="!hide" ref="pixiCanvas"></canvas>
</template>
