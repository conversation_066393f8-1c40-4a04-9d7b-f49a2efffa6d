<script lang="ts" setup>
import { useBeacon } from '@composables';
import { errorNotify } from '@helpers';
import { useUserStore } from '@stores';

const storeUser = useUserStore();

const { isEnabledGPS } = storeToRefs(storeUser);

const { t } = useI18n();
const {
  loading,
  lastLocations,
  handleBackToMain,
  beaconFitBounds,
  handleUseBeacon,
} = useBeacon();

watch(lastLocations, beaconFitBounds);

onMounted(async () => {
  await nextTick();
  beaconFitBounds();
  if (!isEnabledGPS.value)
    errorNotify({
      message: t('GPS_ERROR_NOT_DETECTED'),
    });
});
</script>
<template>
  <div class="fixed fullscreen">
    <Button
      class="fixed top-3 left-3 z-[9999]"
      shape="square"
      variant="secondary"
      @click="handleBackToMain"
    >
      <Icon name="arrow-left" />
    </Button>

    <div
      class="card p-4 text-sm text-center fixed top-4 left-1/2 -translate-x-1/2"
      v-html="t('BEACON_GUI_DESC_1')"
    ></div>
    <Button
      v-if="isEnabledGPS"
      class="fixed bottom-10 left-1/2 -translate-x-1/2"
      :label="t('BEACON_GUI_BTN_CONFIRM')"
      :loading="loading"
      @click="handleUseBeacon"
    />
  </div>
</template>
<style lang="scss" scoped>
.card {
  width: 70vw;
  max-width: 250px;
  border-radius: 10px;
  border: 1px solid #4f46c1;
  background: linear-gradient(261deg, #341c7c 1.58%, #1a0950 60.32%);
}
</style>
