<script setup lang="ts">
import gsap, { Linear } from 'gsap';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { storeToRefs } from 'pinia';
import { useUserStore } from '@stores';
import { convertTime, dateTimeFormat, getSocials } from '@helpers';
import { useTick } from '@composables';

const tl = gsap.timeline();
const { t } = useI18n();
const { now } = useTick();
const store = useUserStore();
const { settings } = storeToRefs(store);
const refVideo = ref();

const state = ref(0);
const isPlay = ref(false);
const targetTime = ref(
  +new Date(
    settings.value?.dates?.season_start_at || '2023-03-09T00:00:00+08:00'
  )
);
const timeConverted = computed(() =>
  convertTime(Math.max(targetTime.value - now.value, 0))
);

onMounted(async () => {
  isPlay.value = true;
  await tl
    .fromTo(
      '.mysterious-countdown__logo',
      {
        opacity: 0,
      },
      {
        opacity: 1,
        duration: 1,
        delay: 1,
      }
    )
    .fromTo(
      '.mysterious-countdown__text',
      {
        opacity: 0,
      },
      {
        opacity: 1,
        duration: 1,
      }
    )
    .fromTo(
      '.mysterious-countdown__text_1',
      {
        opacity: 0,
      },
      {
        opacity: 1,
        duration: 1,
      }
    );
  isPlay.value = false;
  gsap.set('.mysterious-countdown__coin', { rotation: 0 });
  gsap.to('.mysterious-countdown__coin', {
    rotation: '+=360_cw',
    ease: Linear.easeNone,
    duration: 0.4,
    repeat: -1,
  });
});

const action = async () => {
  if (isPlay.value || state.value > 1) return;
  isPlay.value = true;
  switch (state.value) {
    case 0:
      await tl
        .fromTo(
          '.mysterious-countdown__coin',
          {
            opacity: 1,
            left: -30,
          },
          {
            left: 'calc(100% + 30px)',
            duration: 1.5,
            ease: Linear.easeNone,
          }
        )
        .fromTo(
          '.mysterious-countdown__text_2',
          {
            opacity: 0,
          },
          {
            opacity: 1,
            duration: 1,
          }
        )
        .to('.mysterious-countdown__text_2', {
          opacity: 0,
          duration: 1,
          delay: 2,
        });
      state.value = 1;
      break;
    case 1:
      await tl
        .fromTo(
          '.mysterious-countdown__coin',
          {
            opacity: 1,
            left: -30,
          },
          {
            left: 'calc(100% + 30px)',
            duration: 1.5,
            ease: Linear.easeNone,
          }
        )
        .fromTo(
          '.mysterious-countdown__text_2',
          {
            opacity: 0,
          },
          {
            opacity: 1,
            duration: 1,
          }
        )
        .to('.mysterious-countdown__text_2', {
          opacity: 0,
          duration: 1,
          delay: 3,
        })
        .to('.mysterious-countdown__bottom', { opacity: 0, duration: 1 });
      state.value = 2;
      await tl
        .to('.mysterious-countdown__bottom', { opacity: 1, duration: 1 })
        .to('.mysterious-countdown__text_1', { opacity: 0, duration: 1 });
      state.value = 3;
      await tl
        .to('.mysterious-countdown__text_1', { opacity: 1, duration: 1 })
        .to('.mysterious-countdown__icon', {
          opacity: 1,
          duration: 1,
          onComplete: () => {
            refVideo.value?.play();
          },
        })
        .to(['.mysterious-countdown__bottom'], {
          paddingTop: 0,
          duration: 0.3,
        })
        .to(
          ['.mysterious-countdown__icon'],
          {
            paddingTop: window.innerWidth >= 640 ? 40 : 15,
            duration: 0.3,
          },
          '-=0.3'
        )
        .to(
          ['.text-bottom'],
          {
            marginBottom: 0,
            duration: 0.3,
          },
          '-=0.3'
        )
        .to(
          '.mysterious-countdown',
          {
            background: 'transparent',
            duration: 0.3,
          },
          '-=0.3'
        );
      gsap.set('.mysterious-countdown__icon', {
        pointerEvents: 'all',
      });
      tl.kill();
      break;
  }
  isPlay.value = false;
};
onBeforeUnmount(() => {
  tl?.kill();
});
</script>

<template>
  <div class="z-[-1] fixed top-0 left-0 bottom-0 right-0">
    <video ref="refVideo" class="fit object-cover" muted loop preload="auto">
      <source
        src="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4"
        type="video/mp4"
        class="fit"
      />
    </video>
  </div>
  <div
    class="fullscreen pb-[30%] sm:pb-0 overflow-auto mysterious-countdown column flex-nowrap justify-end sm:justify-center items-center"
  >
    <div class="mysterious-countdown__logo flex justify-center items-center">
      <img src="/imgs/cape.png" width="35" height="35" />
    </div>
    <div class="text-lg font-bold pt-[15px] mysterious-countdown__text">
      {{ dateTimeFormat(targetTime, 'DD.MM.YYYY') }}
    </div>
    <div class="pt-[15px] text-sm italic mysterious-countdown__text_1">
      <template v-if="state < 3">
        {{ t('MYSTERIOUS_COUNTDOWN_TEXT_1') }}
      </template>
      <template v-else-if="timeConverted">
        {{
          `${timeConverted.days} ${t('COUNTDOWN_TIMER_DAYS').toLowerCase()}, ${
            timeConverted.hours
          } ${t('COUNTDOWN_TIMER_HOURS').toLowerCase()}, ${
            timeConverted.minutes
          } ${t('COUNTDOWN_TIMER_MINUTES').toLowerCase()}, ${
            timeConverted.seconds
          } ${t('COUNTDOWN_TIMER_SECONDS').toLowerCase()}`
        }}
      </template>
    </div>

    <div
      class="flex justify-center items-center pt-20 mysterious-countdown__icon gap-[25px]"
    >
      <a
        :href="link"
        target="_blank"
        rel="noopener noreferrer"
        v-for="{ link, icon } in getSocials()"
        :key="icon"
      >
        <Icon :name="icon" :size="25" />
      </a>
    </div>

    <div
      class="left-0 right-0 text-center text-sm italic text-bottom sm:mb-[-10%] w-full"
    >
      <div
        class="mysterious-countdown__text_2"
        v-html="t(`MYSTERIOUS_COUNTDOWN_TEXT_2_${state < 1 ? 0 : 1}`)"
      ></div>
      <div
        class="pt-[35px] sm:pt-[85px] mysterious-countdown__bottom relative w-full"
      >
        <img
          class="absolute top-0 mysterious-countdown__coin"
          :src="`/imgs/coin/${!state ? `gold-coin` : `silver-coin`}.png`"
          width="30"
          height="30"
        />
        <template v-if="state < 2">
          Love, <b class="underline cursor-pointer" @click="action">Sqkii</b>.
        </template>
        <template v-else>
          <a class="underline" href="https://www.sqkii.com/" target="_blank"
            >sqkii.com</a
          >
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.mysterious-countdown {
  background: #090422;
  &__logo {
    width: 35px;
    height: 35px;
    background: #ffffff;
    border-radius: 50%;
    img {
      filter: invert(17%) sepia(85%) saturate(7338%) hue-rotate(254deg)
        brightness(14%) contrast(102%);
    }
  }
  &__logo {
    opacity: 0;
  }
  &__text {
    opacity: 0;
  }
  &__text_1 {
    opacity: 0;
  }
  &__text_2 {
    opacity: 0;
  }
  &__icon {
    opacity: 0;
    pointer-events: none;
  }
  &__coin {
    opacity: 0;
  }
}

@media only screen and (min-width: 640px) {
  .mysterious-countdown {
    &__logo {
      width: 70px;
      height: 70px;
      img {
        width: 70px;
        height: 70px;
      }
    }
    &__text {
      font-size: 36px;
      line-height: 56px;
    }
    &__text_1 {
      font-size: 28px;
      line-height: 36px;
    }
    &__text_2 {
      font-size: 28px;
      line-height: 36px;
    }
    &__icon {
      img {
        width: 50px;
        height: 50px;
        margin-right: 50px;
        &:last-child {
          margin-right: 0px;
        }
      }
    }
    &__bottom {
      font-size: 28px;
      line-height: 36px;
    }
    &__coin {
      width: 80px;
      height: 80px;
    }
  }
}
</style>
