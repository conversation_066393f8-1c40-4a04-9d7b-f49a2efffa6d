<script lang="ts" setup>
import axios from 'axios';
import Plyr from 'plyr';
import 'plyr/dist/plyr.css';

interface Emits {
  (event: 'play'): void;
  (event: 'pause'): void;
  (event: 'ended'): void;
  (event: 'ready'): void;
}

interface Props {
  source: string;
  autoPlay?: boolean;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

let player: Plyr | null = null;
const apiKey = process.env.GOOGLE_API_KEY;
const playerRef = ref<HTMLDivElement | null>(null);
const isPause = ref(false);
const isEnd = ref(false);
const loaded = ref(false);

const sourceId = computed(() => {
  const regex =
    /(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=|shorts\/)([^#\&\?]*).*/;
  const match = props.source.match(regex);
  if (match && match[2].length == 11) return match[2];
  return '6gb5bmb545E';
});

async function getVideo() {
  if (!playerRef.value || !sourceId.value) return;

  let thumbnailData;

  try {
    const { data } = await axios.get(
      `https://www.googleapis.com/youtube/v3/videos?id=${sourceId.value}&key=${apiKey}&part=snippet`
    );
    thumbnailData = data.items[0]?.snippet?.thumbnails;
  } catch (error) {}

  const thumbnail =
    thumbnailData?.maxres?.url ||
    thumbnailData?.high?.url ||
    thumbnailData?.standard?.url ||
    '';

  player = new Plyr(playerRef.value, {
    controls: ['play'],
    clickToPlay: true,
    autoplay: props.autoPlay ?? false,
    youtube: {
      noCookie: false,
      showinfo: 0,
      iv_load_policy: 3,
      modestbranding: 1,
      fs: 0,
      rel: 0,
    },
    previewThumbnails: {
      enabled: true,
      src: thumbnail,
    },
  });

  player.on('ready', () => {
    emits('ready');
    loaded.value = true;
  });

  player.on('play', () => {
    emits('play');
  });

  player.on('pause', () => {
    emits('pause');
  });

  player.on('ended', () => {
    isEnd.value = true;
    emits('ended');
  });
}

function play() {
  player?.play();
}

watch(isPause, (val) => {
  if (val) player?.pause();
  else player?.play();
});

onMounted(async () => {
  await nextTick();
  getVideo();
});

onBeforeUnmount(() => {
  if (player) {
    player.destroy();
    player = null;
  }
});

defineExpose({
  play,
});
</script>
<template>
  <div class="relative flex items-center justify-center w-full h-full">
    <slot v-if="loaded" />
    <q-circular-progress
      v-if="!loaded"
      class="absolute z-30 -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"
      indeterminate
      rounded
      size="50px"
      color="white"
    />
    <div
      :class="{
        'pointer-events-none': !loaded,
      }"
      ref="playerRef"
      data-plyr-provider="youtube"
      :data-plyr-embed-id="sourceId"
    ></div>
  </div>
</template>
<style lang="scss">
.plyr--video {
  width: 100% !important;
}
iframe {
  .ytp-chrome-top {
    display: none;
  }
}
.plyr__poster {
  background-size: cover !important;
}
</style>
