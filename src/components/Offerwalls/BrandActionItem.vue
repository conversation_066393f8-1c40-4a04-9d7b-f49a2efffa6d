<script setup lang="ts">
import { useTick, useTrackData } from '@composables';
import { useMapStore, useUserStore } from '@stores';
import { timeCountDown, baBtnName, baBtnBg, stars, newBtnBa } from '@helpers';
import type { IBrandAction, IBrandHook } from '@types';
import dayjs from 'dayjs';
import { useQuasar } from 'quasar';

interface Emits {
  (event: 'action'): void;
}

interface Props {
  data: IBrandAction;
  showType?: 'noFrame' | 'twinkle' | 'normal';
  brandHooks: IBrandHook;
  fromSponsor?: boolean;
  customAction?: (item: IBrandAction) => void;
}

const emits = defineEmits<Emits>();
const { platform } = useQuasar();
const props = withDefaults(defineProps<Props>(), {
  showType: 'normal',
});

const { t } = useI18n();
const { now } = useTick();
const { brandActionPhycial } = storeToRefs(useMapStore());
const { openDialog } = useMicroRoute();
const {
  hasMultiplier,
  checkWithFeatured,
  calculateReward,
  locked_item,
  handleAction,
  checkWithBonus,
  showStatus,
} = props.brandHooks;
const { track } = useTrackData();
const { settings } = storeToRefs(useUserStore());

const itemlocked = computed(() => locked_item(props.data));
const haveBonus = computed(() => checkWithBonus(props.data));
const haveFeatured = computed(() => checkWithFeatured(props.data));
const finalReward = computed(() => calculateReward(props.data));
const haveMultiplier = computed(() => hasMultiplier(props.data));
const isReleased = computed(() => {
  return (
    !props.data.release_date || +new Date(props.data.release_date) < now.value
  );
});
const isClosed = computed(() => {
  return (
    !!props.data.close_date && +new Date(props.data.close_date) < now.value
  );
});

const multiplierNumber = computed(() => {
  return {
    featured: settings.value?.brand_action?.multiplier?.featured || 3,
    firstTime: settings.value?.brand_action?.multiplier?.first_time || 2,
  };
});

function getReward(item: IBrandAction) {
  return item.user_reward?.crystal
    ? item.user_reward.crystal
    : item.display_reward?.length
    ? item.display_reward
    : item.reward?.crystal;
}

function doAction(item: IBrandAction) {
  if (props.fromSponsor)
    track('sponsor_brand_action', {
      brand_unique_id: item.brand_unique_id,
      type: item.type,
      unique_id: item.unique_id,
    });
  if (props.customAction) props.customAction(item);
  else handleAction(item);
  emits('action');
}
</script>
<template>
  <template v-if="showType === 'noFrame'">
    <div class="flex flex-col items-center justify-center">
      <p v-html="t(data.title)" class="mb-4"></p>
      <div class="bg-[#091A3B] rounded-[5px] p-[9px] flex items-center w-max">
        +
        <Icon name="dbs_crystal" :size="16" class="mx-1" />
        <p class="mr-3">{{ finalReward }}</p>
        <Button
          @click="
            openDialog('insufficient_crystals', {
              dataBA: data,
            });
            emits('action');
          "
          size="small"
          :label="t('GET')"
        />
      </div>
    </div>
  </template>
  <template v-else-if="showType === 'twinkle'">
    <div class="relative flex flex-col flex-center">
      <div
        v-if="haveFeatured && data.status === 'new'"
        class="twinkle-header z-[30] twinkle-header-blue"
        v-html="
          t('OFFERWALL_BASE_FEATURED', {
            TIME: timeCountDown(Date.parse(data.featured?.end_at || '') - now),
            MULTIPLIER: multiplierNumber.featured,
          })
        "
      ></div>
      <div
        v-if="data?.is_first_time && data.status === 'new'"
        class="twinkle-header z-[30] mb-[-9px]"
        v-html="
          t('OFFERWALL_BASE_BONUS', { MULTIPLIER: multiplierNumber.firstTime })
        "
      ></div>
      <div
        class="twinkle-header mb-[-12px] !px-[30px] relative z-[30]"
        v-if="haveBonus && data.status === 'new'"
        v-html="
          t('OFFERWALL_BONUS', {
            BONUS: data.bonus?.bonus,
            TIME: timeCountDown(Date.parse(data.bonus?.end_at || '') - now),
          })
        "
      ></div>

      <div
        class="relative twinkle"
        style="z-index: 2"
        @click="
          ['pending', 'claimed', 'rejected'].includes(data.status) &&
            showStatus(data)
        "
      >
        <Icon
          v-for="(item, index) in stars"
          name="star"
          class="star-twinkle"
          :key="`ba-icon-star-${index}`"
          :size="item.size"
          :style="`position:absolute;left:${item.left};bottom:${item.bottom};right:${item.right};top:${item.top};z-index:9999;`"
        />
        <div
          class="font-medium text-base mb-[5px] text-center"
          v-html="t(data.title)"
        ></div>

        <div class="flex items-center gap-1 mb-2.5">
          <div class="text-sm">{{ t('BRANDICONS_MAINTEXT_2') }}</div>
          <p
            class="flex items-center bg-[#6f1190] rounded-[5px] p-[5px] border-[1px] border-[#ba69d7]"
          >
            <Icon name="dbs_crystal" :size="22.79" />
            &nbsp;

            <span
              :style="
                (haveMultiplier &&
                  'text-decoration:line-through;opacity:0.5') ||
                ''
              "
            >
              {{ getReward(data) }}
            </span>
            &nbsp;

            <span v-if="haveMultiplier">
              {{ data.user_reward?.crystal || finalReward }}
            </span>
          </p>
        </div>
        <a
          :href="
            (platform.is.ios
              ? data.metadata.app_store_link
              : data.metadata.android_link) || data.link
          "
          v-if="
            data.status === 'new' && !!data.link && data.unique_id !== 'spf_4'
          "
          target="_blank"
          style="color: white"
        >
          <Button
            size="small"
            :variant="baBtnBg[data.status]"
            @click="doAction(data)"
          >
            {{
              data.status === 'new' && itemlocked && itemlocked?.lock_until
                ? timeCountDown(+new Date(itemlocked?.lock_until || 0) - now)
                : data.status === 'new'
                ? t((newBtnBa as any)[data.type] || baBtnName['new'])
                : t(baBtnName[data.status])
            }}
          </Button>
        </a>
        <Button
          @click="doAction(data)"
          size="small"
          :disable="
            data.status === 'claimed' ||
            data.status === 'rejected' ||
            !!itemlocked ||
            !isReleased ||
            isClosed
          "
          :variant="baBtnBg[data.status]"
          :pointerEvent="data.status === 'pending' ? 'none' : undefined"
          v-else
        >
          {{
            data.status === 'new' && itemlocked && itemlocked?.lock_until
              ? timeCountDown(+new Date(itemlocked?.lock_until || 0) - now)
              : data.status === 'new'
              ? t((newBtnBa as any)[data.type] || baBtnName['new'])
              : t(baBtnName[data.status])
          }}
        </Button>
        <p
          v-if="data.unique_id === 'bk_1'"
          class="px-5 my-2 italic text-center"
          v-html="t('burger_special_content')"
        ></p>
      </div>
    </div>
  </template>
  <template v-else>
    <div
      :id="data.type"
      class="mb-4 text-left brand_action_item"
      @click="
        ['pending', 'claimed', 'rejected'].includes(data.status) &&
          showStatus(data)
      "
    >
      <div
        class="mb-12 brand-header brand-header-red min-w-[150px]"
        v-if="data.status === 'new' && (!isReleased || isClosed)"
        v-html="t('UNAVAILABLE')"
      ></div>
      <div
        class="mb-12 brand-header brand-header-red"
        v-if="itemlocked && data.type === 'verify_mobile_number'"
        v-html="
          t('daily_limit_coundown', {
            TIME: timeCountDown(+dayjs().endOf('day') - now),
          })
        "
      ></div>
      <div
        class="mb-12 brand-header brand-header-blue"
        v-if="haveBonus && data.status === 'new'"
        v-html="
          t('OFFERWALL_BONUS', {
            BONUS: data.bonus?.bonus,
            TIME: timeCountDown(Date.parse(data.bonus?.end_at || '') - now),
          })
        "
      ></div>
      <div
        class="brand-header brand-header-sqkii-voucher mb-12 !pr-[100px] text-[#091A3B] font-bold"
        v-if="data.type === 'sqkii_voucher'"
        v-html="t('OFFERWALL_SQKII_VOUCHER')"
      ></div>
      <div
        class="mb-4 brand-header brand-header-blue"
        v-if="haveFeatured && data.status === 'new'"
        v-html="
          t('OFFERWALL_NEW_BASE_FEATURED', {
            NUMBER: multiplierNumber.featured,
            TIME: timeCountDown(Date.parse(data.featured?.end_at || '') - now),
          })
        "
      ></div>
      <div
        class="mb-4 brand-header brand-header-pink"
        v-if="data?.is_first_time && data.status === 'new'"
        v-html="
          t('OFFERWALL_NEW_BASE_BONUS', {
            NUMBER: multiplierNumber.firstTime,
          })
        "
      ></div>
      <div class="flex items-center">
        <div style="flex: 1" class="justify-center gap-2 column">
          <p style="opacity: 0.7">
            ~{{
              ((brandActionPhycial?.[data._id]?.distance || 0) / 1000).toFixed(
                2
              )
            }}km
          </p>
          <p v-html="t(data.title)" class="pr-4 text-base font-medium"></p>

          <p
            v-if="data.description?.length"
            v-html="t(data.description)"
            class="pr-4 text-sm italic font-normal"
          ></p>
          <template v-if="data.unique_id === 'sentosa_2'">
            <div class="flex items-center">
              <Icon name="light-bulb" :size="22.79" /> &nbsp;
              <p>1</p>
            </div>
          </template>
          <template v-else>
            <p class="flex items-center">
              <Icon name="dbs_crystal" :size="22.79" />
              &nbsp;
              <span
                :style="
                  (haveMultiplier &&
                    'text-decoration:line-through;opacity:0.5') ||
                  ''
                "
              >
                {{ getReward(data) }}
              </span>
              &nbsp;

              <span v-if="haveMultiplier">
                {{ data.user_reward?.crystal || finalReward }}
              </span>
            </p>
          </template>
        </div>

        <a
          :href="
            (platform.is.ios
              ? data.metadata.app_store_link
              : data.metadata.android_link) || data.link
          "
          v-if="
            data.status === 'new' && !!data.link && data.unique_id !== 'spf_4'
          "
          target="_blank"
          style="color: white"
          :class="{
            'pointer-events-none': !!itemlocked || !isReleased || isClosed,
          }"
        >
          <Button
            size="small"
            :variant="baBtnBg[data.status]"
            @click="doAction(data)"
            :disable="!!itemlocked || !isReleased || isClosed"
          >
            {{
              data.status === 'new' && itemlocked && itemlocked?.lock_until
                ? timeCountDown(+new Date(itemlocked?.lock_until || 0) - now)
                : t(baBtnName[data.status])
            }}
          </Button>
        </a>
        <Button
          @click="doAction(data)"
          size="small"
          :disable="
            data.status === 'claimed' ||
            data.status === 'rejected' ||
            !!itemlocked ||
            !isReleased ||
            isClosed
          "
          :variant="baBtnBg[data.status]"
          :pointerEvent="data.status === 'pending' ? 'none' : undefined"
          v-else
        >
          {{
            data.status === 'new' && itemlocked && itemlocked?.lock_until
              ? timeCountDown(+new Date(itemlocked?.lock_until || 0) - now)
              : t(
                  baBtnName[
                    data.ba_unique_id === 'sentosa_2' &&
                    data.status === 'claimed'
                      ? 'completed'
                      : data.status
                  ]
                )
          }}
        </Button>
      </div>
    </div>
  </template>
</template>
<style scoped lang="scss">
.brand_action_item {
  background: linear-gradient(0deg, #320b5b, #320b5b), #51178c;
  border: 2px solid #51178c;
  background-size: 100% 100% !important;
  width: 100%;
  padding: 10px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    width: 80%;
    height: 12px;
    left: -5px;
    transform: skew(25deg);
    bottom: -14px;
    background: linear-gradient(180deg, #804ebe 0%, #5a2998 100%);
    border-radius: 2px;
  }
  &::after {
    content: '';
    position: absolute;
    width: 80%;
    height: 12px;
    right: -5px;
    transform: skew(-25deg);
    bottom: -14px;
    background: linear-gradient(180deg, #804ebe 0%, #5a2998 100%);
    border-radius: 2px;
  }
  .brand-header {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    width: max-content;
    height: 35px;
    padding: 0 30px 3px 14px;
    margin-top: -10px;
    margin-left: -17px;
    margin-bottom: 10px;
    background-size: 100% 100%;
    &-pink {
      background: url(/imgs/crystal-shop.png);
      background-size: 100% 100%;
    }
    &-orange {
      background: url(/imgs/crystal-shop-orange.png);
      background-size: 100% 100%;
    }
    &-blue {
      background: url(/imgs/crystal-shop-blue.png);
      background-size: 100% 100%;
    }
    &-sqkii-voucher {
      background: url(/imgs/crystal-shop-sqkii-voucher.png);
      background-size: 100% 100%;
    }
    &-red {
      max-width: 340px;
      width: max-content !important;
      margin-top: 0 !important;
      margin-bottom: 5px;
      margin-left: -10px !important;
      background: url(/imgs/red-header.png);
      background-size: 100% 100%;
      height: 28px !important;
    }
  }
}
.twinkle {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px 10% 40px 10%;
  background-image: url(/imgs/action-base.png);
  width: calc(100% + 40px);
  margin-left: -20px;
  margin-right: -20px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  &-header {
    width: max-content;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    border-radius: 30px 30px 0px 0px;
    height: 35px;
    padding: 0 14px 5px 14px;
    font-weight: 700;
    font-size: 12px;
    background: url(/imgs/top-blue.png);
    background-size: 100% 100%;
    &-pink {
      background: url(/imgs/top-pink.png);
      background-size: 100% 100%;
    }
  }
}
</style>
