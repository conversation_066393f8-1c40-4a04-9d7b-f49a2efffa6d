<script setup lang="ts">
import { useBrandActions, useTick } from '@composables';
import { baBtnName, baBtnBg, timeCountDown, stars } from '@helpers';
import { useUserStore } from '@stores';
import type { IBrandAction } from '@types';

interface Props {
  listFeatured: IBrandAction[];
}
defineProps<Props>();

const storeUser = useUserStore();
const { settings } = storeToRefs(storeUser);
const { t } = useI18n();
const { now } = useTick();
const { calculateReward, handleAction } = useBrandActions();

const slide = ref(0);
const slideRef = ref();

const multiplierNumber = computed(() => {
  return {
    featured: settings.value?.brand_action?.multiplier?.featured || 3,
    firstTime: settings.value?.brand_action?.multiplier?.first_time || 2,
  };
});
</script>
<template>
  <div class="featured">
    <div class="relative items-center justify-start fit column">
      <Icon
        v-for="(item, index) in stars"
        name="star"
        class="star-twinkle"
        :key="`star-offerwall-${index}`"
        :size="item.size"
        :style="`position:absolute;left:${item.left};bottom:${item.bottom};right:${item.right};top:${item.top};z-index:9999;`"
      />
      <div
        class="flex items-center justify-between px-4 absolute-full"
        v-if="listFeatured?.length > 1"
      >
        <Icon
          name="arrow-left"
          :size="16"
          class="relative z-[99999]"
          @click="slideRef?.previous()"
        />
        <Icon
          name="arrow-left"
          :size="16"
          @click="slideRef?.next()"
          class="rotate-[180deg] relative z-[99999]"
        />
      </div>
      <div class="featured-content">
        <div
          class="pb-4 fit"
          style="background: rgba(0, 0, 0, 0.1); border-radius: 4px"
        >
          <q-carousel
            ref="slideRef"
            v-model="slide"
            transition-prev="slide-right"
            transition-next="slide-left"
            swipeable
            :autoplay="5000"
            animated
            infinite
            control-color="primary"
            class="rounded-borders"
          >
            <q-carousel-slide
              :name="index"
              v-for="(item, index) in listFeatured"
              :key="`featured-${index}`"
            >
              <div class="items-center justify-start column">
                <Icon
                  v-if="item.featured?.logo"
                  class="!w-16"
                  :name="item.featured?.logo"
                  type="url"
                />

                <div
                  class="items-center justify-start px-6 text-center full-width column"
                >
                  <p v-html="t(`${item.title}`)" class="my-2 text-lg"></p>

                  <div class="flex items-center">
                    <div class="flex items-center mt-1.5 featured-text w-max">
                      <div class="text-base font-bold featured-amount">
                        x{{ multiplierNumber.featured }}
                      </div>
                      <Icon name="crystal-s" :size="20" />
                      <span
                        style="text-decoration: line-through; color: #919191"
                        >{{ item.display_reward || item.reward?.crystal }}</span
                      >
                      <span class="ml-1">{{ calculateReward(item) }}</span>
                    </div>

                    <Button
                      @click="handleAction(item)"
                      size="small"
                      class="ml-[30px]"
                      :disable="
                        item.status === 'claimed' || item.status === 'rejected'
                      "
                      :variant="baBtnBg[item.status]"
                      >{{ t(baBtnName[item.status]) }}
                    </Button>
                  </div>

                  <p class="text-xs italic mt-1.5">
                    {{
                      timeCountDown(
                        +new Date(item.featured?.end_at || '') - now
                      )
                    }}
                    left
                  </p>
                </div>
              </div>
            </q-carousel-slide>
          </q-carousel>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.featured {
  width: calc(100% - 32px);
  margin: 0 auto 20px auto;

  .logo_container {
    background: url(/imgs/logo_frame.png);
    background-size: 100% 100%;
    width: 62px;
    height: 68px;
    margin-top: -20px;
    padding: 11px 8px;
  }
  &-amount {
    width: 44px;
    height: 44px;
    padding-bottom: 3px;
    background: url(/imgs/bonus_bg.png);
    background-size: 100% 100%;
    position: absolute;
    top: -6px;
    right: -24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &-text {
    background: #091a3b;
    border-radius: 4px;
    margin-top: -2px;
    padding: 4px 30px;
    width: max-content;
    position: relative;
  }
  &-logo {
    width: 120px;
  }
  &-content {
    background: url(/imgs/featured_bg.png);
    background-size: 100% 100%;
    width: 100%;
    padding: 10px 15px 10px 15px;
    position: relative;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 10;
    align-items: center;
    justify-content: flex-start;
  }
}
</style>
<style lang="scss">
.featured-content {
  .q-panel-parent {
    overflow: unset;
  }
  .q-panel {
    overflow: unset;
  }
  .q-carousel {
    padding: 0 !important;
    background-color: transparent !important;
    height: auto;
    width: 100% !important;
    &__slide {
      padding: 0 !important;
    }
    & > div {
      width: 100% !important;
    }
  }
}
</style>
