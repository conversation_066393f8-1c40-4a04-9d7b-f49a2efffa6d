<script setup lang="ts">
import { BRAND_ACTION } from '@repositories';
import { useBAStore, useUserStore } from '@stores';
import type { IBrandAction } from '@types';

interface Props {
  data: IBrandAction;
}

const props = defineProps<Props>();

const storeUser = useUserStore();
const storeBA = useBAStore();

const { push, openDialog } = useMicroRoute();
const { user } = storeToRefs(storeUser);
const { t } = useI18n();

const showDialog = ref(false);
const error = ref(false);
const loading = ref(false);

const mobile_number = ref(
  props.data.verify_type === 'mobile_number'
    ? user.value?.metadata?.tada_mobile_number?.substring(2, 100) || ''
    : ''
);
const validateEmail = computed(() =>
  /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mobile_number.value)
);
const countryRegion = ref({
  name: 'Singapore',
  code: '+65',
  iso: 'SG',
  mask: '#### ####',
  flag: 'https://cdn.kcak11.com/CountryFlags/countries/sg.svg',
});

const selectCountry = (data: any) => {
  // mobile_number.value = '';
  countryRegion.value = data;
};
const submit = async () => {
  if (loading.value) return;
  loading.value = true;
  try {
    const res =
      props.data.verify_type === 'email'
        ? await BRAND_ACTION.submitClientInfo({
            brand_action_id: props.data._id,
            email: mobile_number.value,
          })
        : await BRAND_ACTION.submitClientInfo({
            brand_action_id: props.data._id,
            mobile_number: countryRegion.value.code + mobile_number.value,
          });
    await storeBA.fetchBrandAction();

    loading.value = false;
    mobile_number.value = '';
    showDialog.value = false;
    push('submited', {
      header:
        props.data.verify_type === 'email'
          ? t('API_NANCIIPOPUP_HEADING_EMAIL')
          : t('API_NANCIIPOPUP_HEADING_MOBILE_NUMBER'),
      title: t('BARCODE_NANCIIPOPUP_TEXT_1'),
      // title: `${
      //   props.data.verify_type === 'email' ? 'Email' : 'Mobile number'
      // } submitted!`,
    });
  } catch (er) {
    error.value = (er as any).error_message;
    loading.value = false;
  }
};
</script>
<template>
  <Dialog>
    <template #header
      >Enter
      {{ data.verify_type === 'email' ? 'email' : 'mobile number' }}</template
    >
    <div class="items-center justify-start text-center full-width column">
      <p>
        Enter the
        {{ data.verify_type === 'email' ? 'email' : 'mobile number' }} you used
        to sign up for a {{ data.brand.name }} account to receive Crystals!
      </p>
      <Input
        class="mt-10 full-width"
        v-model="mobile_number"
        placeholder="Enter email"
        :error="error"
        v-if="data.verify_type === 'email'"
      />
      <div
        class="relative mt-20 full-width"
        :style="`flex:1;opacity:${
          user?.metadata?.tada_mobile_number ? 0.5 : 1
        };pointer-events:${
          user?.metadata?.tada_mobile_number ? 'none' : 'auto'
        }`"
        v-else
      >
        <Input
          v-model="mobile_number"
          :custom_prefix="65"
          class="full-width"
          placeholder="Mobile number"
          :mask="countryRegion.mask"
          :readonly="!!user?.metadata?.tada_mobile_number"
          :error="error"
        />
        <div class="flex link_prefix flex-center">
          <SelectCountry
            :error="error"
            :selectedCountry="countryRegion"
            @selectCountry="selectCountry"
          ></SelectCountry>
        </div>
      </div>
      <div class="error-link" v-if="error">
        {{ error }}
        <!-- The mobile number you have entered is invalid. Please try again. You
            have 4 more attempts. After which, you will be timed out. -->
      </div>
      <Button
        class="mt-20"
        @click="
          openDialog('confirm_submit', {
            header: `Submit this email?`,
            onSubmit: submit,
          })
        "
        :disable="!validateEmail"
        v-if="data.verify_type === 'email'"
        >Submit</Button
      >
      <Button
        class="mt-20"
        @click="
          openDialog('confirm_submit', {
            header: `Submit this mobile number?`,
            onSubmit: submit,
          })
        "
        :loading="loading"
        :disable="
          mobile_number.replace(/ /g, '').length <
          countryRegion.mask.replace(/ /g, '').length
        "
        v-else
        >Submit</Button
      >
    </div>
  </Dialog>
</template>
