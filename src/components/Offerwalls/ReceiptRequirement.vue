<script setup lang="ts">
defineProps(['data']);

const { t } = useI18n();
</script>
<template>
  <Dialog :hide-close="true">
    <template #header>
      {{ t('VERIFICATION_1A_REQUIREMENTSPOPUP_HEADER') }}
    </template>

    <ol
      class="gap-2.5 column justify-start pl-5 pb-2.5"
      style="list-style-type: decimal"
      v-html="
        t(
          data?.metadata?.photo_requirement ||
            'VERIFICATION_1A_REQUIREMENTSPOPUP_DESCRIPTION'
        )
      "
    ></ol>

    <div class="flex flex-center">
      <Button @click="$emit('close')" :label="t('BUTTON_GOT_IT')" />
    </div>
  </Dialog>
</template>
