<script lang="ts" setup>
interface Props {
  steps: string[];
  vertical?: boolean;
  text?: boolean;
  step: number;
}

defineProps<Props>();
</script>

<template>
  <div
    class="steppers text-left flex"
    :class="
      vertical
        ? 'vertical flex-col gap-8'
        : 'horizontal flex-row justify-between'
    "
  >
    <div
      class="flex flex-nowrap items-center gap-5 relative"
      v-for="(t, index) in steps"
      :key="index"
    >
      <div
        class="dots relative w-6 h-6 p-[2px] rounded-full"
        :class="{
          done: index < step,
          'not-done': step < index,
        }"
      >
        <div
          class="w-full h-full border border-[#000000] rounded-full flex justify-center items-center"
        >
          <svg
            v-if="index < step"
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="11"
            viewBox="0 0 14 11"
            fill="none"
          >
            <path
              d="M1 6L4 9.5L12.5 1"
              stroke="black"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
      <div
        v-if="text"
        class="text-base"
        :class="{
          '-ml-[2px]': index < step,
          'opacity-50': step < index,
        }"
        v-html="t"
      ></div>
      <div
        class="line"
        :class="[
          `line-${index}`,
          { active: index < step, 'bg-blur': step < index },
        ]"
        v-if="index < steps.length - 1"
      ></div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.steppers {
  &.vertical {
    .line {
      position: absolute;
      background: #7336f5;
      width: 4px;
      height: 64px;
      left: 10px;
      bottom: -50px;
      &.line-5 {
        bottom: -32px;
      }
      &.active {
        background: #167f80;
      }
      &.bg-blur {
        background: #4c289c;
      }
    }
  }
  &.horizontal {
    .line {
      position: absolute;
      background: #7336f5;
      width: 64px;
      height: 4px;
      &.bg-blur {
        background: #4c289c;
      }

      &.active {
        background: #167f80;
      }
    }
  }

  .dots {
    position: relative;
    z-index: 2;
    background: linear-gradient(0deg, #7336f5 28.33%, #7492ff 109.38%);
    flex: 0 0 24px;

    &.done {
      transform: scale(1.3);
      background: linear-gradient(180deg, #38e7d2 0%, #1da1d1 100%);
    }
    &.not-done {
      background: linear-gradient(0deg, #34215f 28.33%, #7492ff 109.38%);
    }
  }
}
</style>
