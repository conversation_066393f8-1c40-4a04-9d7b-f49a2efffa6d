<script setup lang="ts">
import { computed, ref } from 'vue';
import { useTick } from 'src/composables/useTicker';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@stores';
import { convertTime, dateTimeFormat, getSocials } from '@helpers';
import { CarAnimation } from '@components';
import { useBrandSov } from '@composables';

const store = useUserStore();
const { seasonCode, perpetualHunt } = storeToRefs(store);
const { now } = useTick();
const { settings } = storeToRefs(store);
const { t } = useI18n();

const targetTime = ref(
  +new Date(
    settings.value?.dates.season_start_at || '2023-03-09T00:00:00+08:00'
  )
);

const timeConverted = computed(() =>
  convertTime(Math.max(targetTime.value - now.value, 0))
);

const { ready: logoReady, randomResult: randomResultLogo } = useBrandSov(
  'pre_launch_landing_htm_logo'
);

const { ready: readyVan, randomResult: randomResultVan } = useBrandSov(
  'pre_launch_landing_htm_van'
);
</script>

<template>
  <div
    class="fullscreen wrapper-countdown"
    :class="`season-${seasonCode.toLowerCase()}`"
  >
    <div class="countdown">
      <div class="countdown-top">
        <template v-if="perpetualHunt">
          <CarAnimation assets="/blocker/new_car_silver" />
        </template>
        <template v-else>
          <CarAnimation
            v-if="readyVan"
            :assets="randomResultVan.pre_launch_landing_htm_van.getAsset()"
          />
        </template>
      </div>
      <div class="countdown-bottom">
        <div class="flex flex-nowrap justify-center items-center gap-2.5">
          <template v-if="perpetualHunt">
            <Icon class="htm-logo !w-[200px] !h-[45px]" name="sov/logo/sqkii" />
          </template>
          <template v-else>
            <Icon
              v-if="logoReady"
              class="htm-logo !w-[200px] !h-[45px]"
              :name="randomResultLogo.pre_launch_landing_htm_logo.getAsset()"
            />
          </template>
        </div>
        <div class="text-base font-bold mt-5">
          {{
            t('COUNTDOWN_START_TIME', {
              TIME: dateTimeFormat(targetTime, 'D MMMM YYYY'),
            })
          }}
        </div>
        <div
          class="countdown-bottom__time py-2.5 my-2.5 justify-center items-center grid grid-cols-4"
        >
          <div
            class="countdown-bottom__time-box column justify-center items-center"
          >
            <span class="text-4xl text-[40px] font-extrabold">
              {{ timeConverted.days }}
            </span>
            <span class="text-xs font-bold">
              {{ t('COUNTDOWN_TIMER_DAYS') }}</span
            >
          </div>
          <div
            class="countdown-bottom__time-box column justify-center items-center"
          >
            <span class="text-4xl text-[40px] font-extrabold">
              {{ timeConverted.hours }}
            </span>
            <span class="text-xs font-bold">
              {{ t('COUNTDOWN_TIMER_HOURS') }}</span
            >
          </div>
          <div
            class="countdown-bottom__time-box column justify-center items-center"
          >
            <span class="text-4xl text-[40px] font-extrabold">
              {{ timeConverted.minutes }}
            </span>
            <span class="text-xs font-bold">
              {{ t('COUNTDOWN_TIMER_MINUTES') }}</span
            >
          </div>
          <div
            class="countdown-bottom__time-box column justify-center items-center"
          >
            <span class="text-4xl text-[40px] font-extrabold">
              {{ timeConverted.seconds }}
            </span>
            <span class="text-xs font-bold">
              {{ t('COUNTDOWN_TIMER_SECONDS') }}</span
            >
          </div>
        </div>
        <div class="mb-4 text-sm" v-html="t('COUNTDOWN_CONTENT')"></div>

        <div class="flex items-center justify-center gap-4 mb-7">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.wrapper-countdown {
  overflow-y: auto;
  overflow-x: hidden;
  &.season-sg {
    background-size: 100% 112vw !important;
    background: url('/imgs/kv/sg.png') center -5vw no-repeat;
  }
  &.season-vn {
    background-size: 100% 112vw !important;
    background: url('/imgs/kv/vn.png') center -5vw no-repeat;
  }
  .htm-logo {
    margin-bottom: -4px;
    height: 32px;
  }
  .untame-logo {
    height: 56px;
  }
  .countdown {
    width: 100%;
    height: 100%;
    &-top {
      width: 100vw;
      height: 109vw;
      // .kv {
      //   position: relative;
      //   width: 100%;
      //   height: 100%;
      //   background: url('/imgs/kv/city.png'),
      //     linear-gradient(180deg, rgba(15, 19, 42, 0) 0%, #0f132a 24.07%);
      //   background-size: 100% 100%;
      //   background-repeat: no-repeat;
      //   .driven-wheel-light {
      //     width: 25px;
      //     height: 25px;
      //     z-index: 4;
      //     opacity: 0.3;
      //     bottom: 19vw;

      //     &.light-left {
      //       position: absolute;
      //       left: 17vw;
      //     }
      //     &.light-right {
      //       position: absolute;
      //       right: 24.5vw;
      //     }
      //   }
      //   .car {
      //     position: absolute;
      //     top: 0;
      //     left: 0;
      //     z-index: 3;
      //     width: 100%;
      //     height: 100%;
      //     background-image: url(/imgs/kv/car-1.png);
      //     background-size: contain;
      //     background-repeat: no-repeat;
      //     transform: scaleX(1.05);
      //   }

      //   .driven-wheel {
      //     position: absolute;
      //     width: 53px;
      //     height: 53px;
      //     background-image: url(/imgs/kv/driven-wheel.png);
      //     background-size: contain;
      //     background-repeat: no-repeat;
      //     bottom: 18vw;
      //     z-index: 3;
      //     &.left {
      //       left: 11vw;
      //     }
      //     &.right {
      //       right: 23.2vw;
      //     }
      //   }
      // }
    }
    &-bottom {
      text-align: center;
      padding: 20px;
      margin-top: -60px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      &__time {
        border-radius: 10px;
        background: linear-gradient(
          180deg,
          rgba(200, 107, 233, 0.8) 0%,
          #bb20c9 100%
        );
        min-width: 313px;
        gap: 5vw;
        padding-left: 5vw;
        padding-right: 5vw;
        @media screen and (min-width: 600px) {
          gap: 40px;
          padding-left: 20px;
          padding-right: 20px;
        }
        &-box {
          min-width: 60px;
        }
      }
    }
  }
}
@media screen and (min-width: 600px) {
  .wrapper-countdown {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-y: hidden;
    .htm-logo {
      height: 48px;
    }
    .untame-logo {
      height: 84px;
    }
    .countdown {
      width: 600px;
      &-top {
        width: 600px;
        height: 700px;
        margin-top: -150px;

        .kv {
          background: url('/imgs/kv/big-city.png'),
            linear-gradient(180deg, rgba(15, 19, 42, 0) 0%, #0f132a 24.07%);
          background-size: 100% 100%;
          background-repeat: no-repeat;

          .car {
            width: 80%;
            height: 80%;
            transform: scale(1);
            left: 50%;
            transform: translateX(-50%);
            top: 140px;
          }
          .driven-wheel {
            width: 60px;
            height: 60px;
            bottom: 120px;

            &.left {
              left: 124px;
            }
            &.right {
              right: 166px;
            }
          }
          .driven-wheel-light {
            width: 30px;
            height: 30px;
            bottom: 120px;

            &.light-left {
              left: 153px;
            }
            &.light-right {
              right: 166px;
            }
          }
        }
      }
      &-bottom {
        position: absolute;
        margin-top: -15vh;
        left: 50%;
        transform: translateX(-50%);
        &__time {
          margin: 0 auto;
          width: 400px;
        }
      }
    }
  }
}
</style>
