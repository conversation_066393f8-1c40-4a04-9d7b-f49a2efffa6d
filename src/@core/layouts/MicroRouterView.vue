<template>
  <div>
    <TransitionGroup name="page-slide" :css="cssTransition" @enter="onEnter">
      <RoutePage
        v-for="(route, i) in resolveRoutes"
        :style="{
          transition: cssTransition
            ? 'transform .5s cubic-bezier(0.65, 0, 0.35, 1)'
            : 'none',
          'z-index': i,
        }"
        :class="{
          deactive: resolveRoutes.length > 1 && i !== resolveRoutes.length - 1,
        }"
        :key="route.path"
      >
        <component :is="route.component" v-bind="route.attrs" />
      </RoutePage>
    </TransitionGroup>
    <q-dialog
      v-for="dialog in resolveDialogs"
      :key="`dialog-${dialog.path}`"
      v-model="dialog.actived"
      :persistent="dialog.persistent || true"
      :position="dialog.position"
      :transitionShow="dialog.transitionShow"
      :transitionHide="dialog.transitionHide"
      :transition-duration="dialog.transitionDuration || 300"
      :seamless="dialog.seamless || false"
      maximized
    >
      <component :is="dialog.component" v-bind="dialog.attrs" />
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { delay, intersection } from 'lodash';
import { useMicroRouter } from 'vue-micro-route';
import { useBackgroundMusic } from '@composables';
import gsap, { Power2 } from 'gsap';
import type { MicroDialog, MicroRoute } from 'vue-micro-route';
import RoutePage from './RoutePage.vue';

gsap.config({
  force3D: true,
  nullTargetWarn: false,
});

interface Props {
  defaultPath: string;
  routesGetter: MicroRoute[];
  dialogs: MicroDialog[];
  defaultBgm?: string;
}

const props = defineProps<Props>();

const { resolveRoutes, resolveDialogs, to, from } = useMicroRouter({
  defaultPath: props.defaultPath,
  routesGetter: props.routesGetter,
  dialogGetter: props.dialogs,
});

useBackgroundMusic({
  routesGetter: props.routesGetter,
  currentRoute: to,
  defaultBgm: props.defaultBgm,
});

const cssTransition = computed(() => {
  let toPaths = to.value.split('/').filter(Boolean);
  let fromPaths = from.value.split('/').filter(Boolean);
  return Boolean(intersection(toPaths, fromPaths).length);
});

function onEnter(el: Element, done: () => void) {
  if (!el || !el.parentNode) {
    done();
    return;
  }

  if (!cssTransition.value) {
    gsap.fromTo(
      el,
      {
        xPercent: -100,
      },
      {
        xPercent: 0,
        duration: 0.5,
        ease: Power2.easeInOut,
        onComplete: done,
      }
    );
  } else {
    delay(done, 500);
  }
}
</script>
