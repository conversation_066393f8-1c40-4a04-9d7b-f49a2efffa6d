<script lang="ts" setup>
interface Emits {
  (e: 'update:modelValue', value: any): void;
}

interface Props {
  type?: 'primary' | 'secondary' | 'neon-outline';
  modelValue: any;
  label?: string;
  options: any[];
  error?: boolean;
  clearable?: boolean;
}

withDefaults(defineProps<Props>(), {
  type: 'primary',
  clearable: true,
});
const emits = defineEmits<Emits>();
</script>

<template>
  <div class="qselect-box" :class="[type, error && 'error-select']">
    <q-select
      :model-value="modelValue"
      @update:model-value="emits('update:modelValue', $event)"
      color="cyan-7"
      options-selected-class="text-deep"
      borderless
      behavior="menu"
      v-bind="$attrs"
      :options="options"
      :label="label"
      dense
      popup-content-class="popup"
      :lazy-rules="false"
      :clearable="clearable"
      emit-value
      map-options
      :menu-offset="[0, -8]"
    >
      <template v-slot:option="scope">
        <q-item v-bind="scope.itemProps" :class="`bg-opt-${type}`">
          <q-item-section>
            <q-item-label class="mt-2 text-base" style="max-width: 100%">
              {{ options[scope.index].label || options[scope.index] }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>
  </div>
</template>
