<script setup lang="ts">
interface Props {
  name: string;
  size?: number;
  type?: 'png' | 'svg' | 'gif' | 'url';
  lazy?: boolean;
}

interface Emits {
  (e: 'click'): void;
}

const props = withDefaults(defineProps<Props>(), {
  size: 18,
  type: 'png',
});

const emits = defineEmits<Emits>();
const assetUrl = computed(() => {
  const url =
    props.type === 'url' ? props.name : `/imgs/${props.name}.${props.type}`;
  return url.replace(/([^:])\/+/g, '$1/');
});
</script>

<template>
  <img
    v-if="!lazy"
    v-bind="$attrs"
    :alt="name"
    :src="assetUrl"
    :class="`!w-[${size}px]`"
    :width="`${size}px`"
    @click="emits('click')"
    style="max-width: unset"
  />
  <q-img
    v-else
    v-bind="$attrs"
    :alt="name"
    :src="assetUrl"
    :class="`!w-[${size}px]`"
    :width="`${size}px`"
    @click="emits('click')"
    style="max-width: unset"
  />
</template>
