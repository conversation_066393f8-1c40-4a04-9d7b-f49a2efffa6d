<script lang="ts" setup>
import { useInventory } from '@composables';
import { InventoryItemType } from '@constants';
import { SilverCoinPowerUpSelection } from '@types';
import { onClickOutside } from '@vueuse/core';

interface Emits {
  (e: 'update:modelValue', value: SilverCoinPowerUpSelection): void;
}

const emits = defineEmits<Emits>();

const { totalItems, itemsQuickView } = useInventory();
const { t } = useI18n();

const expandRef = ref<HTMLDivElement | null>(null);
const expand = ref(false);

onClickOutside(expandRef, () => {
  expand.value = false;
});

const options = computed<SilverCoinPowerUpSelection[]>(() => {
  const totalShrink = totalItems.value.totalShrink;
  const totalShrinkLite = totalItems.value.totalShrinkLite;
  const totalCoinSonar = totalItems.value.totalCoinSonar;

  return [
    {
      type: InventoryItemType.SHRINK,
      total: totalShrink,
      title: t('SILVER_COIN_POWER_UP_SELECTION_SHRINK', { TOTAL: totalShrink }),
      canUseWithPowerUp: totalShrink > 0,
      item_id: totalShrink > 0 ? itemsQuickView.value.shrink[0]._id : undefined,
    },
    {
      type: InventoryItemType.SHRINK_LITE,
      total: totalShrinkLite,
      title: t('SILVER_COIN_POWER_UP_SELECTION_SHRINK_LITE', {
        TOTAL: totalShrinkLite,
      }),
      canUseWithPowerUp: totalShrinkLite > 0,
      item_id:
        totalShrinkLite > 0
          ? itemsQuickView.value.shrinkLite[0]._id
          : undefined,
    },
    {
      type: InventoryItemType.COIN_SONAR,
      total: totalCoinSonar,
      title: t('SILVER_COIN_POWER_UP_SELECTION_COIN_SONAR', {
        TOTAL: totalCoinSonar,
      }),
      canUseWithPowerUp: totalCoinSonar > 0,
      item_id:
        totalCoinSonar > 0 ? itemsQuickView.value.coinSonar[0]._id : undefined,
    },
  ];
});

const selected = ref<SilverCoinPowerUpSelection>(options.value[0]);

const handleSelect = (option: SilverCoinPowerUpSelection): void => {
  selected.value = option;
  expand.value = false;
  emits('update:modelValue', option);
};

onMounted(async () => {
  await nextTick();
  emits('update:modelValue', selected.value);
});
</script>
<template>
  <div class="relative">
    <div
      class="text-sm mb-2"
      v-html="t('SILVER_COIN_POWER_UP_SELECTION_1')"
    ></div>
    <Expansion
      ref="expandRef"
      v-model="expand"
      group="power-ups"
      class="power-ups relative !w-full rounded-xl"
      :class="{
        'rounded-b-none power-ups__border': expand,
      }"
    >
      <template v-slot:header>
        <div
          class="text-left flex flex-col transition-all"
          :class="{
            'justify-center': expand,
          }"
        >
          <div
            v-if="!expand"
            class="text-[8px]"
            v-html="t('SILVER_COIN_POWER_UP_SELECTION_2')"
          ></div>
          <div class="text-sm">
            <span v-if="!expand">{{ selected.title }}</span>
            <span
              v-else
              class="opacity-50"
              v-html="t('SILVER_COIN_POWER_UP_SELECTION_3')"
            ></span>
          </div>
        </div>
      </template>
    </Expansion>
    <Transition name="powerup-expand">
      <div
        v-if="expand"
        class="flex flex-col gap-2 bg-[#04081d] absolute z-[99999] w-full px-4 pb-4 text-left border border-[#00e0ff] border-t-0 rounded-b-xl -mt-1"
      >
        <div v-for="option in options" :key="option.type">
          <div
            class="text-sm"
            :class="{
              'font-bold text-[#00E0FF]': option.type === selected.type,
            }"
            @click="handleSelect(option)"
          >
            {{ option.title }}
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>
<style lang="scss" scoped>
.power-ups {
  background: #04081d;
  box-shadow: 2px 2px 10px rgba(#04081d, 0.1);
  &__border {
    border: 1px solid #00e0ff;
    border-bottom: none;
  }
}

// Transition for expand/collapse
.powerup-expand-enter-active,
.powerup-expand-leave-active {
  transition: opacity 0.2s, transform 0.2s;
}
.powerup-expand-enter-from,
.powerup-expand-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
.powerup-expand-enter-to,
.powerup-expand-leave-from {
  opacity: 1;
  transform: translateY(0);
}
</style>
