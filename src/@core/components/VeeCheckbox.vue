<script setup lang="ts">
import { useField } from 'vee-validate';

interface IVeeCheckBoxProps {
  label: string;
  name: string;
  id?: string;
  error?: boolean;
}

const props = defineProps<IVeeCheckBoxProps>();

const { errorMessage, value, meta } = useField(toRef(props, 'name'));

const id = props.id ?? `vee-checkbox-${props.name}`;
</script>

<template>
  <div>
    <q-checkbox
      v-bind="$attrs"
      v-model="value"
      :label="undefined"
      :id="id"
      :error="(!!errorMessage && !!meta.touched) || error"
    >
      <div class="text-sm" v-html="label"></div>
    </q-checkbox>
    <div
      v-if="!!errorMessage && !!meta.touched"
      class="error_msg text-center"
      v-html="errorMessage"
    ></div>
  </div>
</template>
<style lang="scss" scoped>
.error-msg {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}
</style>
