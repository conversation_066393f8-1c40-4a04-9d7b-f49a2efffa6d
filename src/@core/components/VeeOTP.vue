<script setup lang="ts">
import { useField } from 'vee-validate';

interface IVeeInputProps {
  name: string;
  id?: string;
  autofocus?: boolean;
  numInputs?: number;
  error?: boolean;
  inputType?: 'number' | 'tel' | 'password' | 'text';
}

const props = withDefaults(defineProps<IVeeInputProps>(), {
  numInputs: 6,
});

const { errorMessage, value, meta } = useField(toRef(props, 'name'));

const id = props.id ?? `vee-otp-${props.name}`;
</script>

<template>
  <div>
    <OTP
      v-bind="$attrs"
      v-model="value"
      :id="id"
      :input-type="inputType || 'tel'"
      :should-auto-focus="!!autofocus"
      :num-inputs="numInputs"
      class="vee-otp"
      :error="
        (!!errorMessage && !!meta.touched) ||
        (String(value).length < numInputs && !!meta.touched) ||
        error
      "
      @on-completed="(v: unknown) => (value = v)"
      @on-change="(v: unknown) => (value = v)"
    />
    <div
      class="error_msg text-center"
      v-if="!!errorMessage && !!meta.touched"
      :class="{ 'mb-4 -mt-3': !!errorMessage && !!meta.touched }"
      v-html="errorMessage"
    ></div>
  </div>
</template>
<style lang="scss">
.vee-input {
  .q-field__bottom {
    display: none;
  }
}
.error_msg {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}
</style>
