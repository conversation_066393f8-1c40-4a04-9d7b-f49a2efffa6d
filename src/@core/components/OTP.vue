<script lang="ts" setup>
import { BACKSPACE, DELETE, LEFT_ARROW, RIGHT_ARROW } from '@constants';

interface Props {
  numInputs: number;
  separator?: string;
  inputClasses?: string;
  inputType: 'number' | 'tel' | 'password' | 'text';
  shouldAutoFocus: boolean;
  error?: boolean;
  modelValue?: unknown;
}

interface Emits {
  (event: 'onCompleted', values: string): void;
  (event: 'onChange', values: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  numInputs: 4,
  separator: ' ',
  inputType: 'tel',
});

const emits = defineEmits<Emits>();

const widthClass: { [key: number]: string } = {
  6: '100%',
  5: '288px',
  4: '228px',
  3: '168px',
  2: '108px',
};

let activeInputEl: HTMLInputElement | null = null;
const activeInput = ref(0);
const otp = ref<string[]>([]);
const oldOtp = ref<string[]>([]);
const type = ref(props.inputType);

watch(
  () => props.modelValue,
  (value) => {
    if (!value) {
      otp.value = [];
      oldOtp.value = [];
    }
  },
  { immediate: true }
);

function handleOnFocus(index: number) {
  activeInput.value = index;
}

function checkFilledAllInputs() {
  if (otp.value.join('').length === props.numInputs) {
    activeInputEl?.blur();
    emits('onCompleted', otp.value.join(''));
  } else return 'Wait until the user enters the required number of characters';
}

function focusInput(input: number) {
  activeInput.value = Math.max(Math.min(props.numInputs - 1, input), 0);
}

function focusNextInput() {
  if (activeInput.value < props.numInputs - 1)
    focusInput(activeInput.value + 1);
}

function focusPrevInput() {
  if (activeInput.value > 0) focusInput(activeInput.value - 1);
}

function changeCodeAtFocus(value: string) {
  oldOtp.value = [...otp.value];
  otp.value[activeInput.value] = value;
  if (oldOtp.value.join('') !== otp.value.join('')) {
    emits('onChange', otp.value.join(''));
    checkFilledAllInputs();
  }
}

function handleOnPaste(event: ClipboardEvent) {
  event.preventDefault();
  const pastedData = event.clipboardData
    ?.getData('text/plain')
    .slice(0, props.numInputs - activeInput.value)
    .split('');
  if (type.value === 'number' && !pastedData?.join('').match(/^\d+$/))
    return 'Invalid pasted data';

  const currentCharsInOtp = otp.value.slice(0, activeInput.value);
  const combinedWithPastedData = currentCharsInOtp.concat(pastedData || []);
  otp.value = combinedWithPastedData.slice(0, props.numInputs);
  focusInput(combinedWithPastedData.slice(0, props.numInputs).length);
  return checkFilledAllInputs();
}

function handleOnChange(values: any) {
  const { value, ref } = values;
  activeInputEl = ref.value;
  changeCodeAtFocus(value);
  focusNextInput();
}

function handleOnKeyDown(event: KeyboardEvent) {
  switch (event.keyCode) {
    case BACKSPACE:
      event.preventDefault();
      changeCodeAtFocus('');
      focusPrevInput();
      break;
    case DELETE:
      event.preventDefault();
      changeCodeAtFocus('');
      break;
    case LEFT_ARROW:
      event.preventDefault();
      focusPrevInput();
      break;
    case RIGHT_ARROW:
      event.preventDefault();
      focusNextInput();
      break;
    default:
      break;
  }
}
</script>
<template>
  <div
    class="v-otp-input relative"
    :style="{
      gridTemplateColumns: `repeat(${numInputs}, 1fr)`,
      width:
        inputType === 'password'
          ? `calc(${widthClass[numInputs]} - 25px)`
          : widthClass[numInputs],
    }"
  >
    <SingleOTP
      v-for="(_, i) in numInputs"
      :key="i"
      :focus="activeInput === i"
      :modelValue="otp[i]"
      :separator="separator"
      :input-type="type"
      :input-classes="inputClasses"
      :class="{
        'border-green': otp[i] !== '' && otp[i] !== undefined,
        'border-red': !!error,
      }"
      :is-last-child="i === numInputs - 1"
      :should-auto-focus="shouldAutoFocus"
      @on-change="handleOnChange"
      @on-keydown="handleOnKeyDown"
      @on-paste="handleOnPaste"
      @on-focus="handleOnFocus(i)"
    />
    <div
      class="absolute -right-7 top-1/2 -translate-y-1/2"
      v-if="inputType === 'password'"
      @pointerdown="type = 'tel'"
      @pointerup="type = 'password'"
    >
      <Icon v-show="type === 'password'" name="icons/show" type="svg" />
      <Icon v-show="type === 'tel'" name="icons/hide" type="svg" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.border-green {
  border: 1px solid #00f7ff !important;
}
.border-red {
  border: 1px solid #ff4545 !important;
}
</style>
