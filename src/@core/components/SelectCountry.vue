<script lang="ts" setup>
const props = defineProps([
  'selectedCountry',
  'error',
  'countries',
  'hideArrow',
]);
const emits = defineEmits(['selectCountry']);
const countryRegion = ref(props.selectedCountry);

watch(
  () => countryRegion.value,
  (val) => {
    if (val) emits('selectCountry', val);
  }
);

watch(
  () => props.selectedCountry,
  (val) => {
    if (val) {
      countryRegion.value = val;
    }
  }
);
</script>

<template>
  <div class="qselect-country" :class="[hideArrow && 'hide-arrow']">
    <q-select
      v-model="countryRegion"
      :options="props.countries || []"
      color="cyan-7"
      options-selected-class="text-deep"
      borderless
      behavior="menu"
      v-bind="$attrs"
      popup-content-class="popup"
      class="relative"
      :lazy-rules="false"
    >
      <template v-slot:selected>
        <div
          v-if="countryRegion"
          class="flex flex-nowrap items-center selected-country"
          :class="{ 'color-red': error }"
        >
          <div class="mr-2 pl-1">{{ countryRegion.code }}</div>
          <Icon
            :name="`icons/${props.error ? 'red' : 'white'}-arr`"
            :size="10"
          ></Icon>
        </div>
      </template>

      <template v-slot:option="scope">
        <div class="fit" style="background: rgba(29, 65, 137, 0.9)">
          <q-item v-bind="scope.itemProps">
            <q-item-section avatar>
              <img :src="scope.opt.flag" width="26" height="18" />
            </q-item-section>
            <q-item-section>
              <q-item-label class="mt-2" style="max-width: 100%">
                {{ scope.opt.name }}
              </q-item-label>
            </q-item-section>
            <q-item-section>
              <q-item-label class="mt-2" style="max-width: 100%">
                ({{ scope.opt.code }})
              </q-item-label>
            </q-item-section>
          </q-item>
        </div>
      </template>
    </q-select>
  </div>
</template>
