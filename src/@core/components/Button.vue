<script lang="ts" setup>
import { playSFX } from '@composables';
import { numeralFormat } from '@helpers';

interface Emits {
  (e: 'click'): void;
}

interface Props {
  variant?: 'primary' | 'secondary' | 'purple' | 'neon';
  shape?: 'rounded' | 'square' | 'medium-square';
  size?: 'medium' | 'max-content' | 'small' | 'medium-fit';
  disable?: boolean;
  hideSFX?: boolean;
  icon?: string;
  label?: string;
  loading?: boolean;
  block?: boolean;
  isPlus?: boolean;
  title?: string;
  amount?: number;
  oldAmount?: number;
  resourceType?: string;
}

withDefaults(defineProps<Props>(), {
  variant: 'primary',
  disable: false,
  size: 'medium',
  shape: 'rounded',
  resourceType: 'crystal',
});
const emits = defineEmits<Emits>();

const { t } = useI18n();
</script>
<template>
  <q-btn
    style="height: 48px; width: max-content"
    class="text-sm o-custom-btn"
    :class="[
      'o-' + variant + '-' + shape,
      'o-' + variant,
      'o-' + shape,
      'o-' + size,
      block && '!w-full',
      loading && 'loading',
    ]"
    :label="undefined"
    :loading="false"
    :disable="disable"
    @click.stop="
      playSFX('button');
      emits('click');
    "
    :push="!disable"
    :rounded="shape === 'rounded'"
    v-bind="$attrs"
  >
    <div
      class="flex custom-btn flex-center"
      :class="[
        variant + '-' + shape,
        variant,
        shape,
        size,
        disable && 'no-pointer-events',
      ]"
    >
      <template v-if="loading">
        <q-circular-progress indeterminate size="30" :thickness="0.3" />
      </template>
      <template v-else>
        <div v-if="label" v-html="label"></div>
        <slot></slot>
        <div
          class="flex items-center justify-center flex-nowrap full-width full-height"
          v-if="title && (amount || amount === 0)"
        >
          <div class="title" v-html="title"></div>
          <div class="amount ml-[10px] whitespace-nowrap" v-if="amount > 0">
            {{ isPlus ? '+' : '-' }}
            <Icon :name="resourceType" :size="20" class="mx-1" />
            <template v-if="['crystal', 'dbs_crystal'].includes(resourceType)">
              <span v-if="oldAmount" class="line-through opacity-50 mx-0.5">
                {{ numeralFormat(oldAmount) }}
              </span>
              {{ numeralFormat(amount) }}
            </template>
          </div>
          <div class="amount ml-[10px]" v-else>
            {{ t('BUTTON_FREE') }}
          </div>
        </div>
      </template>
    </div>
  </q-btn>
</template>
