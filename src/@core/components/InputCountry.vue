<script lang="ts" setup>
import { countries } from '@helpers';
import { useUserStore } from '@stores';
import type { ICountryRegion } from '@types';

interface Emits {
  (event: 'update:country', country: ICountryRegion): void;
}

interface Props {
  label?: string;
  defaultCountry?: ICountryRegion;
  customCountries?: ICountryRegion[];
  readonly?: boolean;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const { seasonCode } = storeToRefs(storeUser);

const country = ref<ICountryRegion>(countries[0]);

watch(
  () => props.defaultCountry,
  (value) => {
    country.value = value || countries[0];
    emits('update:country', country.value);
  }
);

onMounted(() => {
  country.value =
    countries.find((c) => c.iso === seasonCode.value) || countries[0];
  emits('update:country', country.value);
});
</script>
<template>
  <Input v-bind="$attrs" :label="label" has-select :readonly="!!readonly">
    <template #prepend>
      <div class="relative flex items-center ml-[10px]">
        <div
          class="absolute w-[1px] h-4 bg-white opacity-50 -right-2 top-4"
        ></div>
        <div
          class="text-sm text-white mt-[6px] pointer-events-none"
          v-html="country.code"
        ></div>
        <div class="qselect-country">
          <q-select
            class="relative"
            v-model="country"
            :options="customCountries || countries"
            options-selected-class="selected"
            borderless
            behavior="menu"
            dense
            popup-content-class="popup"
            @update:model-value="emits('update:country', country)"
            :readonly="!!readonly"
          >
            <template v-slot:option="scope">
              <div class="fit">
                <q-item v-bind="scope.itemProps">
                  <q-item-section avatar>
                    <q-img :src="scope.opt.flag" class="w-8 h-5" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="mt-2" style="max-width: 100%">
                      {{ scope.opt.name }}
                    </q-item-label>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="mt-2" style="max-width: 100%">
                      ({{ scope.opt.code }})
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </div>
            </template>
          </q-select>
        </div>
      </div>
    </template>
  </Input>
</template>
<style lang="scss">
.qselect-country {
  margin-top: 10px;

  .q-field__control {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    width: 20px;
    height: 30px !important;
    min-height: 30px !important;
  }

  .q-field__native {
    display: none !important;
  }
  .selected-country .normal-flag {
    margin: 0 !important;
  }
  .q-field__append {
    margin-top: -4px;
  }
  .q-field__append .q-icon {
    position: absolute;
    width: 100px;
    top: 0;
    right: -40px;
    color: #00f7ff;
  }
  .flag {
    top: 7px;
    left: 7px;
    position: absolute;
  }
}
</style>
