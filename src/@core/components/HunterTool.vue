<script setup lang="ts">
import { DailyMissionItem } from '@components';
import {
  delay,
  playSFX,
  useAsync,
  useMotionTracking,
  useState,
  useTrackData,
} from '@composables';
import { numeralFormat } from '@helpers';
import {
  GPS,
  PedometerConfirmOnboarding,
  PedometerOnboarding,
  TrackStep,
} from '@routerDialogs';
import { useDialogStore, useUserStore } from '@stores';
import { DAILYMISSION } from '@repositories';
import { Component as Com } from 'vue';
import type { IGlobeState, IDailyMission } from '@types';
import gsap, { Elastic } from 'gsap';

type DialogKey =
  | 'gps'
  | 'track_step'
  | 'pedometer_onboarding'
  | 'pedometer_onboarding_confirm';

const tl = gsap.timeline();

const storeDialog = useDialogStore();
const storeUser = useUserStore();

const { toggleHunterTool } = storeToRefs(storeDialog);
const { state } = useState<IGlobeState>();
const { push, openDialog } = useMicroRoute();
const { t } = useI18n();
const { crystals, dailyMissions, features, user, currentStep, perpetualHunt } =
  storeToRefs(storeUser);
const { track } = useTrackData();
const { requestDeviceMotion } = useMotionTracking();

let timeout: NodeJS.Timeout;
const dailyExpand = ref(true);
const hunterToolRef = ref<HTMLElement | null>(null);

const mission = computed(() => {
  return dailyMissions.value.find(
    (m) => m.progress >= m.required || !m.claimed_at
  );
});

const dialogManagers = ref<
  Record<DialogKey, { active: boolean; component: Com; attrs: any }>
>({
  gps: {
    active: false,
    component: markRaw(GPS),
    attrs: {
      onClose: function () {
        dialogManagers.value['gps'].active = false;
      },
      onAllow: function () {
        transitionClose();
      },
    },
  },
  track_step: {
    active: false,
    component: markRaw(TrackStep),
    attrs: {
      onClose: function () {
        dialogManagers.value['track_step'].active = false;
      },
    },
  },
  pedometer_onboarding: {
    active: false,
    component: markRaw(PedometerOnboarding),
    attrs: {
      onClose: function () {
        dialogManagers.value['pedometer_onboarding'].active = false;
      },
      onStarted: function () {
        dialogManagers.value['pedometer_onboarding_confirm'].active = true;
      },
    },
  },
  pedometer_onboarding_confirm: {
    active: false,
    component: markRaw(PedometerConfirmOnboarding),
    attrs: {
      onClose: function () {
        dialogManagers.value['pedometer_onboarding_confirm'].active = false;
      },
      onAllow: async function () {
        try {
          dialogManagers.value['pedometer_onboarding'].active = false;
          dialogManagers.value['pedometer_onboarding_confirm'].active = false;
          transitionClose();
          await requestDeviceMotion();
          state.value.pedometer_onboarding_success = true;
        } catch (error) {}
      },
    },
  },
});

const transitionOpen = () => {
  storeUser.fetchDailyMission();
  hunterToolRef.value?.classList.add('fullscreen');
  hunterToolRef.value?.classList.add('z-[10999]');
  gsap.set('.hunter_tool, .btn_close', {
    pointerEvents: 'none',
  });
  timeout = setTimeout(() => {
    gsap.set('.hunter_tool, .btn_close', {
      pointerEvents: 'all',
    });
    if (timeout) clearTimeout(timeout);
  }, 1000);
  tl.to('.tool_backdrop', {
    translateX: 0,
    translateY: 0,
    duration: 0.8,
  })
    .to(
      '.bottom_control',
      {
        translateX: 0,
        translateY: 0,
        duration: 0.3,
      },
      '-=0.6'
    )
    .to(
      '.btn-gps',
      {
        scale: 1,
        duration: 0.2,
      },
      '-=0.2'
    )
    .to(
      '.btn_close',
      {
        rotate: 0,
        x: 0,
        duration: 0.2,
        opacity: 1,
      },
      '-=0.1'
    )
    .to(
      '.control_table',
      {
        scale: 1,
        stagger: 1 / 8,
        duration: 0.2,
      },
      '-=0.6'
    )
    .to(
      '.tool-header',
      {
        opacity: 1,
        translateX: 0,
        duration: 0.4,
      },
      '-=0.4'
    )
    .to(
      '.hunter_daily_mission',
      {
        ease: Elastic.easeInOut.config(1, 0.35),
        translateY: 0,
        duration: 1.6,
      },
      '-=0.8'
    );
};

const transitionClose = (fn?: () => void) => {
  gsap.set('.hunter_tool, .btn_close', {
    pointerEvents: 'none',
  });
  setTimeout(() => {
    fn?.();
  }, 400);
  tl.to('.tool-header', {
    opacity: 0,
    duration: 0.4,
  })
    .to(
      '.hunter_daily_mission',
      {
        translateY: '-500px',
        duration: 0.4,
      },
      '-=0.4'
    )
    .to(
      '.control_table',
      {
        scale: 0,
        stagger: -1 / 8,
        duration: 0.2,
      },
      '-=0.2'
    )
    .to(
      '.btn-gps',
      {
        scale: 0,
        duration: 0.2,
      },
      '-=0.2'
    )
    .to(
      '.btn_close',
      {
        rotate: '90deg',
        x: '20px',
        duration: 0.2,
        opacity: 0,
      },
      '-=0.2'
    )
    .to('.bottom_control', {
      translateX: '100%',
      translateY: '100%',
      duration: 0.6,
    })
    .to(
      '.tool_backdrop',
      {
        translateX: '100%',
        translateY: '100%',
        duration: 0.8,
        onComplete: () => {
          storeDialog.toggleHunterTool = false;
          hunterToolRef.value?.classList.remove('fullscreen');
          hunterToolRef.value?.classList.remove('z-[10999]');
        },
      },
      '-=0.6'
    );
};

// function triggerGPS() {
//   if (isEnabledGPS.value) storeMap.triggerGPS();
//   else {
//     dialogManagers.value['gps'].active = true;
//   }
// }

function btnClick(type: 'get_hint' | 'get_crystal' | 'sqkii_vouchers') {
  track('toolkit', {
    button: type,
  });
  track('hunters_toolkit', {
    action: type,
  });
  playSFX('button');
  switch (type) {
    case 'get_hint':
      transitionClose(() => push('shop'));
      break;
    case 'get_crystal':
      transitionClose(() => push('offer_wall'));
      break;
    case 'sqkii_vouchers':
      if (perpetualHunt.value && !features.value?.sqkii_voucher) return;
      transitionClose(() => push('sqkii_vouchers'));
      break;
    default:
      break;
  }
}

const { loading, execute: claimMission } = useAsync({
  async fn(mission: IDailyMission) {
    const { data } = await DAILYMISSION.claim(mission.unique_id);
    return data;
  },
  async onSuccess({ crystal }) {
    await storeUser.fetchDailyMission();
    storeUser.updateCrystals(crystal);
    transitionClose();
    await delay(700);
    openDialog('promo_success', {
      crystal,
      buttonType: 'fromToolKitMission',
    });
  },
});

watch(toggleHunterTool, (value) => {
  if (value) transitionOpen();
  else transitionClose();
});

watch(
  () => state.value.triggerPedometerFromGUI,
  (value) => {
    if (value) {
      dialogManagers.value[
        !!user.value?.setting.pedometer ? 'track_step' : 'pedometer_onboarding'
      ].active = true;
      state.value.triggerPedometerFromGUI = false;
    }
  }
);

onBeforeUnmount(() => {
  tl?.kill();
  if (timeout) clearTimeout(timeout);
});
</script>
<template>
  <Teleport to="body">
    <div
      ref="hunterToolRef"
      :class="{
        'pointer-events-none': !storeDialog.toggleHunterTool,
      }"
    >
      <div class="fit hunter_tool">
        <div class="tool_backdrop">
          <q-dialog
            v-for="key in Object.keys(dialogManagers)"
            :key="key"
            :maximized="true"
            :model-value="dialogManagers[key as DialogKey].active"
            :persistent="true"
            class="z-[222222]"
          >
            <component
              :is="dialogManagers[key as DialogKey].component"
              v-bind="dialogManagers[key as DialogKey].attrs"
            />
          </q-dialog>
        </div>
        <!-- Pedometer -->
        <div
          class="liner-bg top-[25px] left-5 flex items-center gap-1 opacity-0 translate-x-[-30px] tool-header"
          v-if="features?.pedometer"
        >
          <div class="p-2 flex items-center gap-2">
            <img
              :src="`/icons/${
                user?.setting.pedometer ? 'step_running' : 'bi_person-standing'
              }.png`"
              :width="user?.setting.pedometer ? 12 : 20"
            />
            <p
              :class="
                !!currentStep &&
                (user?.setting.pedometer_goal || 0) <= currentStep &&
                'text-[#54D6E2]'
              "
            >
              {{ currentStep }}
            </p>
          </div>
          <Button
            shape="square"
            class="!p-0"
            @click="
              dialogManagers[
                !!user?.setting.pedometer
                  ? 'track_step'
                  : 'pedometer_onboarding'
              ].active = true
            "
          >
            <Icon name="arrow-up" :size="18" class="rotate-[90deg]" />
          </Button>
        </div>
        <div
          class="liner-bg top-[25px] right-5 flex items-center gap-1 opacity-0 translate-x-[30px] tool-header"
        >
          <div class="p-2 flex items-center gap-2">
            <Icon name="dbs_crystal" :size="20" />
            <div
              class="text-sm"
              v-html="
                crystals > 99999
                  ? `99,999<sup>+</sup>`
                  : numeralFormat(crystals)
              "
            ></div>
          </div>
          <Button
            shape="square"
            class="!p-0"
            @click="transitionClose(() => push('offer_wall'))"
          >
            <Icon name="white_plus" :size="18" />
          </Button>
        </div>
        <div
          class="rounded-[4px] bg-[#091A3B] left-5 right-5 fixed top-[79px] z-[12304] hunter_daily_mission translate-y-[-500px]"
          :class="!dailyExpand && '!rounded-b-none'"
          style="border: 1px solid #11d1f9"
          v-if="features?.daily_mission"
        >
          <div class="relative w-full">
            <div
              class="h-[3px] absolute left-0 right-0 bottom-[-1px] bg-[#46004D] transition-opacity"
              :class="{
                ['opacity-0']: dailyExpand,
                ['opacity-1']: !dailyExpand,
              }"
            >
              <p
                class="h-[3px] bg-[#E66BF6] shadow-[0px 0px 7px 0px rgba(219, 0, 255, 0.25)]"
                :style="`width: ${
                  ((mission?.progress || 0) / (mission?.required || 1)) * 100
                }%`"
              ></p>
            </div>
            <q-expansion-item v-model="dailyExpand">
              <template #header>
                <div
                  class="text-left flex flex-col justify-center"
                  v-if="dailyExpand || !mission"
                >
                  {{ t('DAILY_MISSION') }}
                </div>
                <div class="text-left flex items-center gap-5" v-else>
                  <p>
                    {{
                      (
                        ((mission?.progress || 0) / (mission?.required || 1)) *
                        100
                      ).toFixed(0)
                    }}%
                  </p>
                  <p class="font-bold">
                    {{ t(mission?.name || '') }}
                  </p>
                </div>
              </template>
              <div
                class="w-full p-5 column items-center"
                style="border-top: 1px solid #27879e"
              >
                <DailyMissionItem hideBtn :mission="mission" v-if="!!mission" />
                <div v-else class="text-center">
                  <p class="text-base font-bold mb-[15px]">
                    {{ t('DAILY_MISSION') }}
                  </p>
                  <p v-html="t('MISSION_EMPTY')"></p>
                </div>
                <Button
                  :variant="
                    mission && mission?.progress >= mission?.required
                      ? 'secondary'
                      : 'purple'
                  "
                  class="mx-auto mt-5"
                  :loading="loading"
                  @click="
                    mission && mission?.progress >= mission?.required
                      ? claimMission(mission)
                      : transitionClose(() => {
                          track('go_to_missions_page');
                          track('hunters_toolkit', {
                            action: 'directto_missions',
                          });
                          push('missions');
                        })
                  "
                  :label="
                    mission && mission?.progress >= mission?.required
                      ? t('DAILY_MISSIONS_BTN_CLAIM')
                      : t('BTN_GO_MISSION')
                  "
                />
              </div>
            </q-expansion-item>
          </div>
        </div>

        <!-- <Button
          shape="square"
          variant="neon"
          @click="triggerGPS"
          :disable="geoState === 'UNAVAILABLE' || geoState === 'WAITING_ACTIVE'"
          :loading="geoState === 'WAITING_ACTIVE'"
          class="fixed left-[28px] bottom-[24px] z-[99999999] btn-gps scale-0"
        >
          <template v-if="geoState === 'OFF'">
            <div class="gps-pulse"></div>
            <div
              class="absolute top-0 right-0 w-[10px] h-[10px] rounded-full bg-[#ff4242]"
            ></div>
          </template>
          <Icon
            v-if="geoState === 'OFF' || geoState === 'BACKGROUND'"
            name="gps-off"
          />

          <Icon v-else name="gps-on" />
          <div
            v-if="geoState === 'UNAVAILABLE'"
            class="w-6 h-[3px] absolute bg-white -rotate-45"
          ></div>
        </Button> -->

        <div class="fixed bottom_control">
          <div class="relative fit">
            <div class="fit relative px-5 py-5">
              <div
                class="absolute control_table pointer-events-none"
                :class="{
                  'right-[1.7%] top-0 w-[46.5%]': !perpetualHunt,
                  'w-[65%] -top-6 right-2': perpetualHunt,
                  '!opacity-50 ': perpetualHunt && !features?.sqkii_voucher,
                }"
              >
                <img
                  class="w-full relative z-10"
                  :src="`imgs/toolkit/${
                    !perpetualHunt ? 'btn_sqkiivouchers' : 'btn_sqkiivouchers_1'
                  }.png`"
                />
                <div class="absolute-full column flex-center z-20">
                  <img
                    src="/imgs/icons/voucher.png"
                    width="36px"
                    class="ml-[20%]"
                  />
                  <p class="text-[10px] font-bold mt-1 ml-[20%]">
                    {{ t('BTN_SQKII_VOUCHEAR') }}
                  </p>
                </div>
                <div
                  class="absolute w-[60%] h-[70%] z-30 rotate-[-14deg] top-[15%] left-[28%] trapezium"
                  @click="btnClick('sqkii_vouchers')"
                >
                  <p class="p-left"></p>
                  <p class="p-right"></p>
                </div>
              </div>
              <div
                class="absolute control_table scale-0 pointer-events-none"
                :class="{
                  'left-[15%] top-[15%] w-[51.5%]': !perpetualHunt,
                  'w-[65%] -left-8 bottom-0': perpetualHunt,
                }"
              >
                <img
                  class="w-full relative z-10"
                  :src="`imgs/toolkit/${
                    !perpetualHunt ? 'btn_crystal' : 'btn_crystal_1'
                  }.png`"
                />
                <div class="absolute-full column flex-center z-20">
                  <img
                    src="/icons/big_crystal.png"
                    width="36px"
                    class="mt-[6%]"
                  />
                  <p class="text-[10px] font-bold mt-1">
                    {{ t('MENU_CARD_GET_CRYSTALS') }}
                  </p>
                </div>
                <div
                  class="absolute w-[50%] h-[65%] z-30 rotate-[-45deg] top-[25%] left-[30%] trapezium"
                  @click="btnClick('get_crystal')"
                >
                  <p class="p-left"></p>
                  <p class="p-right"></p>
                </div>
              </div>

              <div
                class="absolute left-0 bottom-[1.7%] w-[44%] control_table scale-0 pointer-events-none"
                v-if="!perpetualHunt"
              >
                <img
                  class="w-full relative z-10"
                  src="imgs/toolkit/btn_gethint.png"
                />
                <div class="absolute-full column flex-center z-20">
                  <img
                    src="/imgs/icons/hint.png"
                    width="36px"
                    class="mt-[20%] ml-[-2%]"
                  />
                  <p class="text-[10px] font-bold mt-1 ml-[-2%]">
                    {{ t('BUTTON_EMPTY_GET_HINTS') }}
                  </p>
                </div>
                <div
                  class="absolute w-[60%] h-[70%] z-30 rotate-[-76deg] top-[25%] left-[20%] trapezium"
                  @click="btnClick('get_hint')"
                >
                  <p class="p-left"></p>
                  <p class="p-right"></p>
                </div>
              </div>
            </div>
            <div
              class="absolute right-0 bottom-0 translate-x-[50%] translate-y-[50%] w-[120%] aspect-square pointer-events-none"
            >
              <div class="fit relative">
                <div class="btn_close_container">
                  <img
                    @click="
                      playSFX('button');
                      transitionClose();
                    "
                    src="/imgs/toolkit/btn_close.png"
                    class="absolute rotate-[45deg] btn_close z-[12304] translate-x-[20px] opacity-0 pointer-events-none"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>
<style lang="scss" scoped>
.tool_backdrop {
  position: fixed;
  top: -10vh;
  left: -40vh;
  width: 150vh;
  height: 150vh;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 12302;
  border-radius: 50%;
  transform: translate(100%, 100%);
}
.bottom_control {
  width: 40vh;
  height: 40vh;
  max-height: 292px;
  max-width: 292px;
  bottom: 0;
  right: 0;
  border-radius: 713px;
  // border: 1px rgba($color: #11d1f9, $alpha: 0.7) solid;
  // background: rgba($color: #091a3c, $alpha: 0.7);
  z-index: 12303;
  transform: translate(100%, 100%);
}
.trapezium {
  pointer-events: all;
  .p-left {
    position: absolute;
    width: 100%;
    height: 100%;
    transform: skew(14deg);
    left: -12%;
    top: 0;
  }
  .p-right {
    position: absolute;
    width: 100%;
    height: 100%;
    transform: skew(-14deg);
    right: -15%;
    top: 0;
  }
}

.btn_close_container {
  width: 100%;
  height: 100%;
  background: linear-gradient(#11d1f900, #11d1f9);
  border-radius: 50%;
  position: relative;
  &::before {
    content: '';
    right: 1px;
    left: 1px;
    top: 1px;
    bottom: 1px;
    position: absolute;
    border-radius: 50%;
    background: #04081d;
  }
  .btn_close {
    left: 17%;
    top: 17%;
    width: 26%;
  }
}
.liner-bg {
  position: fixed;
  border-radius: 5px;
  background: linear-gradient(180deg, #2a084f 0%, #5a1b99 100%);
  z-index: 12303;
  width: max-content;
}
.grow-card {
  border-radius: 10px;
  border: 1px #38cfe5 solid;
  box-shadow: 0px 0px 7px 0px #38cfe5;
}
</style>
