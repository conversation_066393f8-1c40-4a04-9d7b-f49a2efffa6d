<script lang="ts" setup>
import { HTMLAttributes } from 'vue';

interface Props {
  modelValue?: string;
  separator?: string;
  focus?: boolean;
  inputClasses?: string;
  shouldAutoFocus: boolean;
  inputType?: 'number' | 'tel' | 'password' | 'text';
  isLastChild: boolean;
  inputmode?: HTMLAttributes['inputmode'];
}

interface Emits {
  (event: 'onChange', values: any): void;
  (event: 'onFocus'): void;
  (event: 'onBlur'): void;
  (event: 'onKeydown', value: KeyboardEvent): void;
  (event: 'onPaste', value: ClipboardEvent): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const inputEl = ref<HTMLInputElement | null>(null);
const model = ref(props.modelValue);

onMounted(async () => {
  await nextTick();
  if (inputEl.value && props.focus && props.shouldAutoFocus)
    inputEl.value.focus();
});

watch(
  () => props.focus,
  (newFocusValue, oldFocusValue) => {
    if (oldFocusValue !== newFocusValue && inputEl.value && props.focus) {
      inputEl.value.focus();
      inputEl.value.select();
    }
  }
);

watch(
  () => props.modelValue,
  (newVal) => {
    model.value = newVal;
  }
);

function handleOnChange(e: Event) {
  let val = (e.target as HTMLInputElement).value.slice(0, 1);
  if (!!val) emits('onChange', { value: val, ref: inputEl });
}

function handleOnKeyDown(event: KeyboardEvent) {
  const keyEvent = event || window.event;
  const charCode = keyEvent.which || keyEvent.keyCode;
  if (isCodeNumeric(charCode) || charCode === 8 || charCode === 46)
    emits('onKeydown', event);
  else keyEvent.preventDefault();
}

function isCodeNumeric(charCode: number) {
  // numeric keys and numpad keys
  return props.inputType !== 'text'
    ? (charCode >= 48 && charCode <= 57) || (charCode >= 96 && charCode <= 105)
    : charCode >= 48 && charCode <= 105;
}

function handleOnPaste(event: ClipboardEvent) {
  emits('onPaste', event);
}

function handleOnFocus() {
  if (inputEl.value) inputEl.value.select();
  emits('onFocus');
}

function handleOnBlur() {
  emits('onBlur');
}
</script>

<template>
  <div class="single-otp">
    <input
      ref="inputEl"
      :type="inputType"
      min="0"
      max="9"
      maxlength="1"
      pattern="[0-9]"
      :inputmode="inputmode"
      :value="model"
      :class="[
        inputClasses,
        { 'border-white': model?.length === 0 || model === undefined },
      ]"
      @input="handleOnChange"
      @keydown="handleOnKeyDown"
      @paste="handleOnPaste"
      @focus="handleOnFocus"
      @blur="handleOnBlur"
    />
    <span v-if="!isLastChild && separator">
      <span v-html="separator"></span>
    </span>
  </div>
</template>
<style lang="scss" scoped>
.border-white {
  border-bottom: 1px solid #ffffff !important;
}
</style>
