<script setup lang="ts">
import { useField } from 'vee-validate';

interface IVeeInputProps {
  name: string;
  label?: string;
  id?: string;
  autofocus?: boolean;
  mask?: string;
  maxlength?: number;
  error?: boolean;
  type?:
    | 'number'
    | 'text'
    | 'search'
    | 'textarea'
    | 'time'
    | 'password'
    | 'email'
    | 'tel'
    | 'file'
    | 'url'
    | 'date'
    | undefined;
}

const props = defineProps<IVeeInputProps>();
const { errorMessage, value, meta } = useField(toRef(props, 'name'));

const id = props.id ?? `vee-input-${props.name}`;
</script>

<template>
  <div>
    <Input
      v-bind="$attrs"
      v-model="value"
      :label="label"
      :id="id"
      :autofocus="autofocus"
      :mask="mask"
      :maxlength="maxlength"
      :type="type"
      class="vee-input"
      :error="(!!errorMessage && !!meta.touched) || error"
      no-error-icon
    >
      <template #prepend>
        <slot name="prepend"></slot>
      </template>
    </Input>
    <div
      class="error_msg text-center"
      v-if="!!errorMessage && !!meta.touched"
      :class="{ 'mb-4 -mt-4': !!errorMessage && !!meta.touched }"
      v-html="errorMessage"
    ></div>
  </div>
</template>
<style lang="scss">
.vee-input {
  .q-field__bottom {
    display: none;
  }
}
.error_msg {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}
</style>
