<script lang="ts" setup>
interface Props {
  label?: string;
  error?: boolean;
  custom_prefix?: number;
  type?:
    | 'number'
    | 'text'
    | 'search'
    | 'textarea'
    | 'time'
    | 'password'
    | 'email'
    | 'tel'
    | 'file'
    | 'url'
    | 'date'
    | undefined;
  modelValue?: unknown;
  readonly?: boolean;
  prefix?: string;
  placeholder?: string;
  hasMobileLegend?: boolean;
  hasSelect?: boolean;
  center?: boolean;
  autofocus?: boolean;
  mask?: string;
  maxlength?: number;
}

interface Emits {
  (event: 'update:modelValue', value: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  label: '',
  error: false,
  type: 'text',
  readonly: false,
});
const emits = defineEmits<Emits>();

const inputType = ref(props.type);
const focused = ref(false);

onMounted(async () => {
  await nextTick();
  if (props.autofocus) {
    const inputEl = document.querySelector('[data-focus]') as HTMLInputElement;
    if (inputEl)
      inputEl.focus({
        preventScroll: true,
      });
  }
});
</script>
<template>
  <div
    class="legend-input"
    :class="[
      (focused || modelValue || prefix || hasMobileLegend || hasSelect) &&
        'input-focused',
      hasMobileLegend && 'has-mobile-number',
      hasSelect && 'has-select',
    ]"
  >
    <div
      class="label"
      :class="[error ? 'text-error' : 'text-normal', center && 'center']"
      :style="
        custom_prefix && !modelValue ? `padding-left:${custom_prefix}px` : ''
      "
    >
      {{ label }}
    </div>
    <q-input
      :placeholder="placeholder"
      :class="[error && 'error-input']"
      v-bind="$attrs"
      :model-value="(modelValue as any)"
      @update:model-value="emits('update:modelValue', $event)"
      outlined
      dense
      :type="inputType"
      :input-style="[
        error ? 'color: #ff0000' : 'color: #202020',
        custom_prefix && !modelValue ? `padding-left:${custom_prefix}px` : '',
      ]"
      :readonly="readonly"
      :prefix="prefix"
      @focusin="focused = true"
      @focusout="focused = false"
      :autofocus="autofocus"
      :mask="mask"
      :maxlength="maxlength"
      data-focus
    >
      <slot></slot>
      <template v-slot:prepend>
        <slot name="prepend"></slot>
      </template>
      <div
        class="icon-password"
        @click="inputType = inputType === 'password' ? 'text' : 'password'"
        v-if="type === 'password'"
      >
        <Icon v-show="inputType === 'password'" name="icons/show" type="svg" />
        <Icon v-show="inputType === 'text'" name="icons/hide" type="svg" />
      </div>

      <div class="icon-password" v-if="type === 'search'">
        <Icon name="search" type="svg" />
      </div>
    </q-input>
  </div>
</template>
