<script lang="ts" setup>
import axios from 'axios';

interface Emits {
  (event: 'click'): void;
}

interface Props {
  src: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  fallbackType?: 'image' | 'video';
  parentHeight?: string;
  controls?: boolean;
  playsinline?: boolean;
  autoplay?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  objectFit: 'cover',
  parentHeight: '150px',
  controls: false,
  playsinline: false,
  autoplay: false,
});
const emits = defineEmits<Emits>();

const mediaType = ref<'image' | 'video' | 'unknown'>('unknown');

async function detectMediaType(
  url: string
): Promise<'image' | 'video' | 'unknown'> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await axios.head(url, {
      signal: controller.signal,
      timeout: 10000,
      headers: {
        Accept: 'image/*,video/*',
      },
    });

    clearTimeout(timeoutId);

    const contentType = response.headers['content-type']?.toLowerCase() || '';

    if (contentType.startsWith('image/')) return 'image';
    else if (contentType.startsWith('video/')) return 'video';

    return detectFromUrl(url);
  } catch (error) {
    console.warn('Failed to detect media type from headers:', error);
    return detectFromUrl(url);
  }
}

function detectFromUrl(url: string): 'image' | 'video' | 'unknown' {
  const extension = url.split('.').pop()?.toLowerCase() || '';

  const imageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'svg',
    'bmp',
    'ico',
    'avif',
  ];
  const videoExtensions = [
    'mp4',
    'webm',
    'ogg',
    'avi',
    'mov',
    'wmv',
    'flv',
    'm4v',
    '3gp',
  ];

  if (imageExtensions.includes(extension)) return 'image';
  else if (videoExtensions.includes(extension)) return 'video';
  return props.fallbackType || 'unknown';
}

async function initializeMedia() {
  if (!props.src) return;

  try {
    const detectedType = await detectMediaType(props.src);
    mediaType.value = detectedType;

    if (detectedType === 'unknown')
      throw new Error('Unable to determine media type');
  } catch (error) {
    console.error('Error detecting media type:', error);
  }
}

watch(
  () => props.src,
  () => {
    if (props.src) initializeMedia();
  },
  { immediate: true }
);
</script>

<template>
  <div
    class="media-renderer relative w-full flex justify-center items-center rounded-lg"
    :style="{ height: `${parentHeight}` }"
    @click="emits('click')"
  >
    <span class="absolute inset-0">
      <video
        v-if="mediaType === 'video'"
        :src="src"
        :style="{ objectFit }"
        class="shadow-md object-cover blur-sm brightness-75"
      />
    </span>
    <!-- Image renderer -->
    <img
      v-if="mediaType === 'image'"
      :src="src"
      :style="{ objectFit }"
      class="size-full object-cover object-center relative z-10"
    />

    <!-- Video renderer -->
    <video
      v-else-if="mediaType === 'video'"
      :src="src"
      :style="{
        objectFit,
        boxShadow: '0px 0px 100px #606060',
      }"
      class="relative max-w-full h-full z-10"
      :controls="controls"
      :autoplay
      :playsinline
    ></video>
    <svg
      v-if="mediaType === 'video' && !autoplay"
      class="absolute z-50 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 48 48"
    >
      <path
        fill="currentColor"
        fill-rule="evenodd"
        d="M24 46.5C11.574 46.5 1.5 36.426 1.5 24S11.574 1.5 24 1.5S46.5 11.574 46.5 24S36.426 46.5 24 46.5m-3.49-32.279c-2.436-.97-4.724.496-5.076 3.094A50 50 0 0 0 15 24c0 2.67.195 4.925.434 6.685c.352 2.598 2.64 4.064 5.076 3.094c1.534-.611 3.547-1.528 6.137-2.904c2.446-1.3 4.378-2.462 5.878-3.446c2.653-1.74 2.653-5.118 0-6.858c-1.5-.984-3.432-2.147-5.878-3.446c-2.59-1.376-4.603-2.293-6.137-2.904"
        clip-rule="evenodd"
      />
    </svg>
  </div>
</template>

<style lang="scss" scoped>
.media-renderer {
  overflow: hidden;

  img,
  video {
    display: block;
  }
}
</style>
