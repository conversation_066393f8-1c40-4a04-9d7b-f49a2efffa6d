<script setup lang="ts">
import { useField } from 'vee-validate';
import type { ICountryRegion } from '@types';

interface Emits {
  (event: 'update:country', country: ICountryRegion): void;
}

interface IVeeInputProps {
  name: string;
  label: string;
  id?: string;
  autofocus?: boolean;
  mask?: string;
  error?: boolean;
  defaultCountry?: ICountryRegion;
  customCountries?: ICountryRegion[];
  readonly?: boolean;
}

const props = defineProps<IVeeInputProps>();
const emits = defineEmits<Emits>();

const { errorMessage, value, meta } = useField(toRef(props, 'name'));

const id = props.id ?? `vee-input-country-${props.name}`;
</script>

<template>
  <div>
    <InputCountry
      v-bind="$attrs"
      v-model="value"
      :label="label"
      :id="id"
      :customCountries="customCountries"
      :autofocus="autofocus"
      :mask="mask || '################'"
      :default-country="defaultCountry"
      class="vee-input-country"
      :error="(!!errorMessage && !!meta.touched) || error"
      no-error-icon
      :readonly="readonly"
      @update:country="(country: ICountryRegion) => emits('update:country', country)"
    />
    <div
      class="text-center error_msg"
      v-if="!!errorMessage && !!meta.touched"
      :class="{ 'mb-4 -mt-4': !!errorMessage && !!meta.touched }"
      v-html="errorMessage"
    ></div>
  </div>
</template>
<style lang="scss">
.vee-input-country {
  .q-field__bottom {
    display: none;
  }
}
.error_msg {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}
</style>
