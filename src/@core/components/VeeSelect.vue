<script setup lang="ts">
import { useField } from 'vee-validate';

interface IVeeInputProps {
  name: string;
  label: string;
  options: any[];
  id?: string;
  error?: boolean;
}

const props = defineProps<IVeeInputProps>();

const { errorMessage, value, meta } = useField(toRef(props, 'name'));

const id = props.id ?? `vee-select-${props.name}`;
</script>

<template>
  <div>
    <Select
      v-bind="$attrs"
      v-model="value"
      :label="label"
      :options="options"
      :id="id"
      class="vee-select"
      :error="(!!errorMessage && !!meta.touched) || error"
      no-error-icon
    />
    <div
      class="error_msg text-center"
      v-if="!!errorMessage && !!meta.touched"
      :class="{ 'mb-4 -mt-4': !!errorMessage && !!meta.touched }"
      v-html="errorMessage"
    ></div>
  </div>
</template>
<style lang="scss">
.vee-select {
  .q-field__bottom {
    display: none;
  }
}
.error_msg {
  padding: 2px 8px;
  border-radius: 5px;
  background: #981515;
  color: #ffffff;
  font-size: 12px;
}
</style>
