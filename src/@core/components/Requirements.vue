<script setup lang="ts">
import {
  REGEX_DIGIT,
  REGEX_UPPERCASE,
  REGEX_LOWERCASE,
  REGEX_SPECIAL,
} from '@constants';

interface Emits {
  (event: 'valid', value: boolean): void;
}

interface Props {
  password: string;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const { password } = toRefs(props);
const { t } = useI18n();

const valid = computed(() => {
  return {
    text_length: password.value.length >= 8,
    lowercase: REGEX_LOWERCASE.test(password.value),
    uppercase: REGEX_UPPERCASE.test(password.value),
    digit: REGEX_DIGIT.test(password.value),
    special: REGEX_SPECIAL.test(password.value),
  };
});

watch(password, () => {
  emits('valid', Object.values(valid.value).every(Boolean));
});
</script>

<template>
  <div class="requirements text-sm">
    <ul>
      <li
        :class="
          !password ? 'error' : valid['text_length'] ? 'success' : 'error'
        "
      >
        <span class="circle"></span>
        <span
          v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_1')"
        ></span>
      </li>
      <li :class="!password ? 'error' : valid['digit'] ? 'success' : 'error'">
        <span class="circle"></span>
        <span
          v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_2')"
        ></span>
      </li>
      <li
        :class="!password ? 'error' : valid['lowercase'] ? 'success' : 'error'"
      >
        <span class="circle"></span>
        <span
          v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_3')"
        ></span>
      </li>
      <li
        :class="!password ? 'error' : valid['uppercase'] ? 'success' : 'error'"
      >
        <span class="circle"></span>
        <span
          v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_4')"
        ></span>
      </li>
      <li :class="!password ? 'error' : valid['special'] ? 'success' : 'error'">
        <span class="circle"></span>
        <span
          v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_PASSWORDREQUIREMENT_5')"
        ></span>
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.requirements {
  text-align: left;
  ul {
    padding-top: 12px;
    list-style: none;
    li {
      color: rgba(213, 213, 213, 0.9);
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      .circle {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 8px;

        display: inline-flex;
        justify-content: center;
        align-items: center;
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
    .success {
      .circle {
        background-image: url('/imgs/correct.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
      }
    }
    .error {
      .circle {
        background-image: url('/imgs/uncorrect.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
      }
    }
  }
}
</style>
