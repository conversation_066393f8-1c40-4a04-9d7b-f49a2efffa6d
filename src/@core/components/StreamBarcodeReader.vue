<script setup lang="ts">
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { BrowserMultiFormatReader, Exception } from '@zxing/library';

const emits = defineEmits(['decode', 'loaded']);
const props = defineProps(['stream']);
const isLoading = ref(true);
const codeReader = new BrowserMultiFormatReader();
let scanner: Ref<HTMLVideoElement | null> = ref(null);
const isMediaStreamAPISupported =
  navigator?.mediaDevices && 'enumerateDevices' in navigator.mediaDevices;

function start() {
  if (scanner.value)
    void codeReader.decodeFromStream(
      props.stream,
      scanner.value,
      (result: { getText: () => void }) => {
        if (result) {
          emits('decode', result.getText());
        }
      }
    );
}

onMounted(async () => {
  await nextTick();
  if (!isMediaStreamAPISupported) {
    throw new Exception('Media Stream API is not supported');
  }
  start();
  if (scanner.value)
    scanner.value.oncanplay = () => {
      isLoading.value = false;
      emits('loaded');
    };
});
onBeforeUnmount(() => {
  codeReader.reset();
});
</script>

<template>
  <div class="scanner-container flex flex-center">
    <div v-show="!isLoading" class="fit" style="border-radius: inherit">
      <video poster="data:image/gif,AAAA" ref="scanner"></video>
    </div>
    <div class="loading_qr flex flex-center" v-show="isLoading">
      <q-spinner color="primary" size="5em" :thickness="3" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading_qr {
  width: 297px;
  height: 297px;
  border-radius: 10px;
  background-color: #555555;
}
video {
  width: 297px;
  height: 297px;
  object-fit: cover;
  z-index: 999;
  display: inline-block;
  transform: unset;
  position: unset;
  top: unset;
  left: unset;
  border-radius: inherit;
}
.scanner-container {
  position: relative;
  text-align: center;
}
</style>
