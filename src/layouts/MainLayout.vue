<script lang="ts" setup>
import { CapitalandBlocker, PreLaunch } from '@components';
import {
  // usePushNotificationStore,
  useUserStore,
  useBrandSovStore,
} from '@stores';
import { useNow } from '@vueuse/core';

const storeUser = useUserStore();
const brandSovStore = useBrandSovStore();

// const { initPushNotification } = usePushNotificationStore();
const { settings } = storeToRefs(storeUser);
const {} = useI18n();
const now = useNow();

const isMobile = computed(() => (window as any).mobileCheck());

const isMysteriousCountdown = computed(() => {
  if (!settings.value) return false;
  const time = +new Date(settings.value?.dates.blocker_countdown_start_at);
  return time > +new Date(now.value);
});

const isBlockerCountdown = computed(() => {
  if (!settings.value) return false;
  const time = +new Date(settings.value?.dates.blocker_countdown_start_at);
  const seasonStart = +new Date(settings.value?.dates.season_start_at);
  return time < +new Date(now.value) && seasonStart > +new Date(now.value);
});

const isPreLaunch = computed(() => {
  return (
    (isBlockerCountdown.value && isMobile.value) || isMysteriousCountdown.value
  );
});

onMounted(() => {
  // initPushNotification();
});

onBeforeMount(() => {
  brandSovStore.fetchBrandSovSettings().catch();
});
</script>
<template>
  <template v-if="isPreLaunch">
    <PreLaunch
      :is-blocker-countdown="isBlockerCountdown"
      :is-mysterious-countdown="isMysteriousCountdown"
    />
  </template>
  <template v-else>
    <template v-if="isMobile">
      <RouterView />
    </template>
    <template v-else>
      <CapitalandBlocker />
    </template>
  </template>
</template>
