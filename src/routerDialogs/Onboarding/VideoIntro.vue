<script lang="ts" setup>
import { useSound, useTrackData } from '@composables';
import { cn } from '@helpers';
import { useUserStore } from '@stores';
import { clamp } from 'lodash';

const VIDEO_URL =
  'https://htm-global-production.s3.ap-southeast-1.amazonaws.com/assets/htm1m_intro.mp4';
const VOLUME_EASE_DURATION = 2000;
const VOLUME_EASE_STEP = 20;

const storeUser = useUserStore();

const { closeDialog } = useMicroRoute();
const { play, stop } = useSound();
const { track } = useTrackData();

const videoRef = ref<HTMLVideoElement | null>(null);
const isLoading = ref(true);
const isMuted = ref(false);
const volumeAnimationId = ref<number | null>(null);

const showMuteIndicator = computed(() => isMuted.value);
const videoElement = computed(() => videoRef.value);

const handleVideoEnded = (): void => {
  closeDialog('video_intro');
  storeUser.updateOnboarding('dbs_video_intro');
  play('default');
};

const handleSkipVideo = (): void => {
  track('skip_video');
  handleVideoEnded();
};

const unmutateVideo = (): void => {
  const video = videoElement.value;
  if (!video || !isMuted.value) return;

  isMuted.value = false;
  video.muted = false;
  video.volume = 0;
  animateVolumeIn();
};

const animateVolumeIn = (): void => {
  const video = videoElement.value;
  if (!video) return;

  if (volumeAnimationId.value) {
    clearInterval(volumeAnimationId.value);
  }

  let elapsed = 0;

  volumeAnimationId.value = window.setInterval(() => {
    if (elapsed >= VOLUME_EASE_DURATION || !video) {
      if (volumeAnimationId.value) {
        clearInterval(volumeAnimationId.value);
        volumeAnimationId.value = null;
      }
      return;
    }

    // Quadratic ease-in-out curve
    const progress = elapsed / VOLUME_EASE_DURATION;
    const easedProgress =
      progress < 0.5 ? 2 * progress ** 2 : 1 - 2 * (1 - progress) ** 2;

    video.volume = clamp(easedProgress, 0, 1);
    elapsed += VOLUME_EASE_STEP;
  }, VOLUME_EASE_STEP);
};

const attemptAutoplay = async (): Promise<void> => {
  const video = videoElement.value;
  if (!video) return;

  try {
    video.muted = false;
    await video.play();

    if (video.paused) {
      throw new DOMException('Autoplay blocked', 'NotAllowedError');
    }

    animateVolumeIn();
  } catch (error) {
    console.warn('Autoplay failed, falling back to muted playback:', error);

    if (error instanceof DOMException && error.name === 'NotAllowedError') {
      isMuted.value = true;
      video.muted = true;

      try {
        await video.play();
      } catch (mutedError) {
        console.error('Failed to play muted video:', mutedError);
      }
    }
  }
};

onMounted(() => {
  track('onboarding_video');
  stop();
});

onUnmounted(() => {
  if (volumeAnimationId.value) {
    clearInterval(volumeAnimationId.value);
  }
});

watch(isLoading, async (loading) => {
  if (!loading && videoElement.value) {
    await attemptAutoplay();
  }
});
</script>
<template>
  <div class="bg-black fullscreen" @click="unmutateVideo">
    <video
      ref="videoRef"
      class="relative object-cover w-full h-full"
      autoplay
      muted
      playsinline
      :controls="false"
      :src="VIDEO_URL"
      preload="metadata"
      @canplay="isLoading = false"
      @ended="handleVideoEnded"
    />
    <div class="z-10 flex items-center justify-center fullscreen">
      <q-spinner color="ffffff" size="5em" v-if="isLoading" />
    </div>
    <Button
      class="absolute top-2 right-2 z-[99]"
      shape="square"
      @click="handleSkipVideo"
    >
      <Icon name="skip" :size="12" />
    </Button>
    <div
      :class="
        cn(
          'fixed flex items-center justify-center p-2 rounded-full bottom-2 right-2 bg-slate-500/20 backdrop-blur-[2px] pointer-events-none transition-opacity opacity-0',
          showMuteIndicator && 'opacity-100'
        )
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1.5em"
        height="1.5em"
        viewBox="0 0 16 16"
      >
        <path
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
          d="M1.75 5.75v4.5h2.5l4 3V2.75l-4 3zm12.5 0l-3.5 4.5m0-4.5l3.5 4.5"
        />
      </svg>
    </div>
  </div>
</template>
