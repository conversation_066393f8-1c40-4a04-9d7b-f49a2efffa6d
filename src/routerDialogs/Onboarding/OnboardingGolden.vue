<script setup lang="ts">
import { useBrandSov, useTrackData } from '@composables';
import { useUserStore } from '@stores';
import gsap, { Power1 } from 'gsap';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const tl = gsap.timeline();
const storeUser = useUserStore();

const { track } = useTrackData();
const { t } = useI18n();
// const { openDialog } = useMicroRoute();
const { onboarding } = storeToRefs(storeUser);

const { randomResult, ready } = useBrandSov('welcome_screen_htm_logo');

async function handleStartHunt() {
  await track('start_hunting');
  emits('close');
  if (!onboarding.value?.dbs_onboarding_journey)
    storeUser.updateOnboarding('dbs_onboarding_journey');
}

function startAnimation() {
  track('popup_hunt_intro');
  tl.fromTo(
    '.title',
    {
      opacity: 0,
      y: 10,
    },
    {
      y: 0,
      opacity: 1,
      duration: 0.5,
      delay: 0.5,
      ease: Power1.easeInOut,
    },
    '-=.2'
  )
    .fromTo(
      '.coin',
      {
        opacity: 0,
        scale: 0.5,
      },
      {
        duration: 0.5,
        opacity: 1,
        scale: 1,
      },
      '-=.5'
    )
    .fromTo(
      '.glow',
      {
        opacity: 0,
      },
      {
        opacity: 1,
        duration: 1,
        ease: Power1.easeInOut,
      },
      '-=.5'
    )
    .fromTo(
      ['.text-coin', '.btn-start', '.lang', '.text-hint'],
      {
        opacity: 0,
        y: 10,
      },
      {
        y: 0,
        opacity: 1,
        duration: 0.5,
        stagger: 0.5,
        ease: Power1.easeInOut,
      },
      '-=.7'
    );
}

onMounted(async () => {
  await nextTick();
  track('onboarding_intro');
  startAnimation();
});

onBeforeUnmount(async () => {
  tl.kill();
});
</script>
<template>
  <Dialog hideClose bgHeader="bg-[#CC0000]">
    <template #header>
      <Icon
        v-if="ready"
        :name="randomResult.welcome_screen_htm_logo.getAsset()"
        class="w-auto h-[60px]"
      />
    </template>
    <div class="w-full text-center">
      <div
        class="mb-6 text-sm title px-4"
        v-html="t('ONBOARDING_GOLDEN_DESC_1')"
      ></div>
      <div class="grid grid-cols-1 mb-4">
        <div>
          <div class="relative flex justify-center mb-4">
            <Icon
              class="absolute -translate-x-1/2 glow -top-1/4 left-1/2"
              name="glow-silver-coin"
              :size="150"
            />
            <Icon class="z-10 coin" name="silver-coin" :size="100" />
          </div>
          <div class="text-coin text-center">
            <div class="text-sm" v-html="t('ONBOARDING_GOLDEN_DESC_2')"></div>
            <div
              class="text-xl font-bold flex items-center justify-center gap-2"
              v-html="t('ONBOARDING_GOLDEN_DESC_3')"
            ></div>
            <!-- <div class="text-sm" v-html="t('ONBOARDING_GOLDEN_DESC_7')"></div> -->
          </div>
        </div>

        <!-- <div>
          <div class="relative flex justify-center mb-4">
            <Icon
              class="absolute -translate-x-1/2 glow -top-1/4 left-1/2"
              name="glow-silver-coin"
              :size="150"
            />
            <Icon class="z-10 coin" name="silver-coin" :size="100" />
          </div>
          <div class="text-coin text-center">
            <div class="text-sm" v-html="t('ONBOARDING_GOLDEN_DESC_4')"></div>
            <div
              class="text-xl font-bold flex items-center justify-center gap-2"
              v-html="t('ONBOARDING_GOLDEN_DESC_5')"
            ></div>
            <div class="text-sm" v-html="t('ONBOARDING_GOLDEN_DESC_8')"></div>
          </div>
        </div> -->
      </div>

      <div class="btn-start mb-5">
        <Button
          class="mx-auto"
          :label="t('ONBOARDING_GOLDEN_BUTTON')"
          @click="handleStartHunt"
        />
      </div>
      <!-- <div
        style="background: rgba(9, 9, 9, 0.5)"
        class="text-hint text-sm rounded-md italic py-2 px-4"
        v-html="t('ONBOARDING_GOLDEN_DESC_6')"
      ></div> -->
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
  margin-top: -100px;
}
</style>
