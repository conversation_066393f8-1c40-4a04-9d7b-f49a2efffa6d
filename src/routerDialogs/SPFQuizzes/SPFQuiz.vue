<script lang="ts" setup>
import { delay, useTimedMission } from '@composables';
import { useDialogStore } from '@stores';

const storeDialog = useDialogStore();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const {
  indexQuiz,
  currentQuiz,
  loading,
  correct,
  completedAnswer,
  quizzes,
  handleSubmitQuiz,
  handleNextQuiz,
} = useTimedMission();

const answer = ref('');

watchEffect(async () => {
  if (completedAnswer.value) {
    const el = document.getElementById(`spf_quiz_${indexQuiz.value}`);
    if (el) {
      el.scrollTop = el.scrollHeight;
      await delay(10);
      el.scrollTop = el.scrollHeight;
    }
  }
});

onMounted(() => {
  if (!storeDialog.spfStartTimeAt) storeDialog.spfStartTimeAt = Date.now();
});
</script>
<template>
  <div
    class="flex flex-col fullscreen bg-linear-gradient-1 flex-nowrap"
    v-if="!!currentQuiz"
  >
    <div
      class="relative flex items-center justify-center h-16 px-20 mb-5 shrink-0"
    >
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="openDialog('spf_quit')"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="w-full text-lg font-extrabold text-center"
        v-html="
          t('SPF_QUIZ_QUESTION_NUMBER', {
            NUMBER: indexQuiz + 1,
            TOTAL: quizzes.length,
          })
        "
      ></div>
    </div>
    <div
      :id="`spf_quiz_${indexQuiz}`"
      class="flex flex-col items-center w-full h-full px-5 pb-5 overflow-y-auto flex-nowrap"
      style="scroll-behavior: smooth"
    >
      <div
        class="mb-10 text-lg font-bold"
        v-html="t(currentQuiz.question)"
      ></div>
      <div
        class="flex flex-col w-full gap-4 mb-10"
        :class="{
          'pointer-events-none': completedAnswer,
        }"
      >
        <div
          class="relative text-sm bg-[#12caf340] opacity-100 p-3 rounded-md transition-opacity duration-500 flex flex-nowrap items-center justify-between gap-2"
          :class="{
            '!opacity-50': answer && answer !== option,
            'border border-[#00F7FF]':
              completedAnswer && answer === option && correct,
            'border border-[#CA1717] bg-[#482433]':
              completedAnswer && answer === option && !correct,
          }"
          v-for="option in currentQuiz.options"
          :key="option"
          @click="answer = option"
        >
          <div v-html="t(option)"></div>
          <Icon
            v-if="completedAnswer && answer === option"
            :name="correct ? 'correct' : 'incorrect'"
          />
        </div>
      </div>

      <template v-if="completedAnswer">
        <div
          class="relative p-4 bg-[#091A3C] border border-[#B663E9] rounded-[10px] flex flex-col items-center mb-10"
        >
          <template v-if="correct">
            <Icon
              class="absolute -top-2 -left-3"
              name="flare-quiz-top"
              :size="30"
            />

            <Icon
              class="absolute -bottom-2 -right-3"
              name="flare-quiz-bottom"
              :size="30"
            />
          </template>
          <Icon
            class="mb-2"
            :name="correct ? 'top-up-success' : 'top-up-failed'"
            :size="73"
          />
          <div
            class="mb-2 text-lg font-bold"
            v-html="
              t(
                correct
                  ? currentQuiz.explanation_title.correct_answer
                  : currentQuiz.explanation_title.wrong_answer
              )
            "
          ></div>
          <div
            class="text-sm"
            v-html="
              t(
                correct
                  ? currentQuiz.explanation_content.correct_answer
                  : currentQuiz.explanation_content.wrong_answer
              )
            "
          ></div>
        </div>
        <Button
          :label="t('SPF_WELCOME_BTN_NEXT')"
          class="!w-[210px] shrink-0"
          :loading="loading"
          @click="handleNextQuiz"
        />
      </template>
      <template v-else>
        <Button
          v-if="currentQuiz.options.includes(answer)"
          :label="t('SPF_QUIZ_BTN_SUBMIT')"
          :loading="loading"
          class="!w-[210px] shrink-0"
          @click="handleSubmitQuiz(answer)"
        />
      </template>
    </div>
  </div>
</template>
