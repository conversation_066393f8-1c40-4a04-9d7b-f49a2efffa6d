<script lang="ts" setup>
import { useTimedMission } from '@composables';
import { useBAStore } from '@stores';

const storeBA = useBAStore();

const { t } = useI18n();
const { closeAllDialog, closeDialog } = useMicroRoute();
const { clearSpfQuizContext } = useTimedMission();

const closeSpfQuiz = () => {
  closeAllDialog();
  clearSpfQuizContext();
  storeBA.fetchBrandAction();
};
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('SPF_QUIT_HEADER')"></div>
    </template>
    <div class="text-center">
      <div class="my-5 text-sm" v-html="t('SPF_QUIT_DESC')"></div>
      <div class="flex gap-4 flex-nowrap">
        <Button
          class="flex-1"
          size="max-content"
          :label="t('SPF_QUIT_BTN_YES')"
          variant="purple"
          @click="closeSpfQuiz"
        />
        <Button
          size="max-content"
          class="flex-1"
          :label="t('SPF_QUIT_BTN_NO')"
          block
          @click="closeDialog('spf_quit')"
        />
      </div>
    </div>
  </Dialog>
</template>
