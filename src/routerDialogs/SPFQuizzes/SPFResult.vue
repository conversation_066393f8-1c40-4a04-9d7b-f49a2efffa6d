<script lang="ts" setup>
import { useTimedMission } from '@composables';
import type { ISPFQuiz } from '@types';

interface Props {
  correct: boolean;
  currentQuiz: ISPFQuiz;
}

const props = defineProps<Props>();

const { t } = useI18n();
const { indexQuiz, handleNextQuiz, loading } = useTimedMission();

// keep data to ignore the reactivity of props, if props.currentQuiz get null value, dataToShow will not be affected
const dataToShow = ref(props.currentQuiz);
const indexToShow = ref(indexQuiz.value);

onMounted(() => {
  dataToShow.value = unref(props.currentQuiz);
  indexToShow.value = unref(indexQuiz);
});
</script>
<template>
  <div
    class="flex flex-col fullscreen bg-linear-gradient-1 flex-nowrap"
    v-if="dataToShow"
  >
    <div
      class="relative flex items-center justify-center h-16 px-20 mb-5 shrink-0"
    >
      <div
        class="w-full text-lg font-extrabold text-center"
        v-html="
          t('SPF_QUIZ_QUESTION_NUMBER', {
            NUMBER: indexQuiz + 1,
          })
        "
      ></div>
    </div>
    <div
      class="flex flex-col items-center w-full h-full px-5 pb-5 overflow-y-auto flex-nowrap"
    >
      <Icon
        class="mb-2"
        :name="correct ? 'top-up-success' : 'top-up-failed'"
        :size="140"
      />
      <div
        class="mb-2 text-lg font-bold"
        v-html="
          t(
            correct
              ? dataToShow.explanation_title.correct_answer
              : dataToShow.explanation_title.wrong_answer
          )
        "
      ></div>
      <div
        class="mb-10 text-sm"
        v-html="
          t(
            correct
              ? dataToShow.explanation_content.correct_answer
              : dataToShow.explanation_content.wrong_answer
          )
        "
      ></div>
      <Button
        :label="t('SPF_WELCOME_BTN_NEXT')"
        class="!w-[210px]"
        :loading="loading"
        @click="handleNextQuiz"
      />
    </div>
  </div>
</template>
