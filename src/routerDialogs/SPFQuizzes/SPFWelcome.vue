<script lang="ts" setup>
import { useTimedMission } from '@composables';
import { useBAStore } from '@stores';

interface Props {
  skipQuiz?: boolean;
  idFromOfferWall?: string;
}

const props = defineProps<Props>();

const storeBA = useBAStore();

const { t } = useI18n();
const { closeAllDialog, openDialog } = useMicroRoute();
const { handleGetQuizzes, loading, canSkipQuiz } = useTimedMission();

function handleStartQuiz() {
  openDialog('spf_quiz');
}

onMounted(async () => {
  await handleGetQuizzes(props.idFromOfferWall);
  if (canSkipQuiz.value && props.skipQuiz) {
    openDialog('spf_completed', {
      noReattemptReward: true,
    });
  }
});
</script>
<template>
  <div
    class="flex flex-col fullscreen bg-linear-gradient-1 flex-nowrap fade-in"
  >
    <div
      class="absolute top-0 left-0 !w-full aspect-square flex justify-center items-center"
    >
      <Icon name="big-glow" class="!w-full" />
      <Icon class="absolute" name="crystal" :size="115" />
    </div>
    <div
      class="relative flex items-center justify-center h-16 px-20 mb-5 shrink-0"
    >
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="
          storeBA.fetchBrandAction();
          closeAllDialog();
        "
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="w-full text-lg font-extrabold text-center"
        v-html="t('SPF_WELCOME_HEADER_2')"
      ></div>
    </div>
    <div class="flex flex-col items-center pt-[55vw] w-full h-full px-10">
      <div class="mb-10 text-sm" v-html="t('SPF_WELCOME_DESC_2')"></div>
      <Button
        class="!w-[210px]"
        :label="t('SPF_WELCOME_BTN_START')"
        :loading="loading"
        @click="handleStartQuiz"
      />
    </div>
  </div>
</template>

<style>
.fade-in {
  animation: fadeIn 0.5s;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  99% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
