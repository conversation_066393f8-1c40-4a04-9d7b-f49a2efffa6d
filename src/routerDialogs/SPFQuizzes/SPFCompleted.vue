<script lang="ts" setup>
import { useTimedMission, useTrackData } from '@composables';
import { useBAStore, useDialogStore, useUserStore } from '@stores';

interface Props {
  noReattemptReward?: boolean;
}

const storeUser = useUserStore();
const storeBA = useBAStore();
const storeDialog = useDialogStore();

defineProps<Props>();

const { settings } = storeToRefs(storeUser);
const { brand_actions } = storeToRefs(storeBA);
const {
  quizzes,
  countingWrongQuizzes,
  quizzData,
  loading,
  handleShareQuiz,
  handleRetryQuiz,
  handleExitQuiz,
} = useTimedMission();
const { t } = useI18n();
const { track } = useTrackData();

const multiplierNumber = computed(() => {
  return {
    featured: settings.value?.brand_action?.multiplier?.featured || 3,
    firstTime: settings.value?.brand_action?.multiplier?.first_time || 2,
  };
});

const shareReward = computed(() => {
  const shareSpfBa = brand_actions.value.find(
    (ba) =>
      ba.type === 'spf_sharing' &&
      quizzData.value?.sharing_ba_unique_id === ba.unique_id
  );
  if (!shareSpfBa || !shareSpfBa.can_perform || !shareSpfBa.reward) return 0;
  const baseReward = shareSpfBa.reward.crystal;
  const multiplier = shareSpfBa.is_first_time
    ? multiplierNumber.value.firstTime
    : 1;
  return baseReward * multiplier;
});

const reward = computed(() => {
  if (!quizzData.value) return;
  const numberOfCorrect =
    quizzes.value.length - quizzData.value.corrected_questions.length;

  const ba = brand_actions.value.find(
    (ba) =>
      ba.unique_id === quizzData.value?.quiz_ba_unique_id &&
      ba.type === 'spf_quiz'
  );
  if (ba && ba.is_first_time)
    return (
      numberOfCorrect *
      quizzData.value.crystal_per_question *
      multiplierNumber.value.firstTime
    );
  return numberOfCorrect * quizzData.value.crystal_per_question;
});

onMounted(async () => {
  await nextTick();
  // Tracking total time spent on SPF
  if (storeDialog.spfStartTimeAt) {
    const time = Date.now() - storeDialog.spfStartTimeAt;
    track('spf_total_time', {
      time,
    });
    storeDialog.spfStartTimeAt = 0;
  }
});
</script>
<template>
  <div
    class="flex flex-col items-center pb-5 fullscreen bg-linear-gradient-1 flex-nowrap"
    v-if="quizzData"
  >
    <Button
      v-if="countingWrongQuizzes && !noReattemptReward"
      class="absolute right-4 top-4"
      shape="square"
      size="small"
      variant="purple"
      @click="handleShareQuiz"
    >
      <Icon name="share" :size="20" />
    </Button>
    <Icon name="big-glow" class="!w-full aspect-square absolute top-0" />
    <div class="relative w-full mt-[35vw]">
      <Icon
        name="spf-quiz-result"
        :size="129"
        class="absolute left-[53%] -translate-x-1/2 -top-20 z-0"
      />
      <div
        class="relative z-10 p-5 bg-[#091A3C] border border-[#B663E9] rounded-[10px] w-[90%] mx-auto mb-5"
      >
        <div class="relative size-[120px] mx-auto mb-5">
          <q-circular-progress
            :value="quizzes.length - countingWrongQuizzes"
            :max="quizzes.length"
            size="120px"
            color="white"
            track-color="grey-5"
            rounded
          />
          <div
            class="absolute text-center -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"
          >
            <div
              class="text-sm whitespace-nowrap"
              v-html="t('SPF_COMPLETED_DESC_1')"
            ></div>
            <div class="text-2xl font-bold">
              {{ quizzes.length - countingWrongQuizzes }}/{{ quizzes.length }}
            </div>
          </div>
        </div>
        <div
          class="mb-5 text-lg font-bold text-center"
          v-html="
            !countingWrongQuizzes
              ? t('SPF_COMPLETED_DESC_3')
              : t('SPF_COMPLETED_DESC_2')
          "
        ></div>
        <div
          class="text-sm"
          v-html="
            !countingWrongQuizzes
              ? t('SPF_COMPLETED_DESC_4')
              : t('SPF_COMPLETED_DESC_5')
          "
        ></div>
      </div>
    </div>
    <div class="flex flex-nowrap items-center justify-center w-full gap-5 px-5">
      <Button
        :label="t('SPF_COMPLETED_BTN_1')"
        variant="purple"
        size="max-content"
        @click="handleExitQuiz"
      />
      <template v-if="!countingWrongQuizzes || noReattemptReward">
        <Button
          v-if="shareReward"
          :title="t('SPF_COMPLETED_BTN_2')"
          :amount="shareReward"
          is-plus
          @click="handleShareQuiz"
        />
        <Button
          v-else
          :label="t('SPF_COMPLETED_BTN_2')"
          @click="handleShareQuiz"
        />
      </template>
      <template v-else-if="!noReattemptReward">
        <Button
          v-if="quizzes.length === quizzData.corrected_questions.length"
          :label="t('SPF_COMPLETED_BTN_3')"
          class="flex-1"
          :loading="loading"
          @click="handleRetryQuiz"
        />

        <Button
          v-else
          :title="t('SPF_COMPLETED_BTN_3')"
          :amount="reward"
          is-plus
          class="flex-1"
          @click="handleRetryQuiz"
        />
      </template>
    </div>
  </div>
</template>
