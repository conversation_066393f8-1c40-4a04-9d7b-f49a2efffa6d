<script lang="ts" setup>
import { useBrandSov } from '@composables';
import { dateTimeFormat, FULL_DATE_TIME_24H_FORMAT } from '@helpers';
import type { IHint } from '@types';

interface Props {
  hint: IHint;
}

defineProps<Props>();

const { t } = useI18n();
const { randomResult, ready } = useBrandSov(
  'gold_coin_hint_cards_front_outfit',
  'gold_coin_text_hint_reveals_back_logo'
);
</script>
<template>
  <Dialog no-header>
    <div
      v-if="ready"
      class="flex flex-col items-center justify-center gap-5 py-20"
    >
      <div
        class="w-[270px] h-[360px] p-10 flex flex-col justify-center items-center text-center gap-5 relative"
        :style="{
          backgroundImage: `url(/imgs/${randomResult.gold_coin_hint_cards_front_outfit.getAsset()}_back_${
            hint.rarity
          }.png)`,
          backgroundSize: '100% 100%',
        }"
      >
        <Icon
          class="absolute left-[8px] bottom-6"
          :name="randomResult.gold_coin_text_hint_reveals_back_logo.getAsset()"
          :size="28"
        />
        <div
          class="text-base"
          v-html="
            t('MY_HINT_NUMBER', {
              NUMBER: hint.hint_id,
            })
          "
        ></div>
        <div class="text-sm font-bold" v-html="hint.content"></div>
      </div>
      <div
        class="text-sm"
        v-html="
          t('MY_HINT_RECEIVED_AT', {
            TIME: dateTimeFormat(hint.received_at, FULL_DATE_TIME_24H_FORMAT),
          })
        "
      ></div>
    </div>
  </Dialog>
</template>
