<script lang="ts" setup>
import { useShop, useTrackData } from '@composables';

interface Props {
  quantity: number;
}

interface Emits {
  (event: 'close'): void;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { buyTextHint, loading } = useShop();
const { track } = useTrackData();
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('CONFIRM_TEXT_HINT_TITLE')"></div>
    </template>
    <div class="text-center flex flex-col gap-5">
      <Icon class="mx-auto" name="shop/text" :size="105" />
      <div
        class="text-sm"
        v-html="t('CONFIRM_TEXT_HINT_DESC', { QUANTITY: quantity })"
      ></div>
      <div class="flex flex-nowrap gap-4">
        <Button
          class="flex-1"
          size="max-content"
          :label="t('BUTTON_CANCEL')"
          :class="{
            'pointer-events-none': loading,
          }"
          variant="purple"
          @click="
            track('golden_coin_shop', {
              screen: 'golden_text_hint_confirmation',
              button: 'cancel',
            });
            emits('close');
          "
        />
        <Button
          class="flex-1"
          size="max-content"
          :label="t('BUTTON_YES')"
          :loading="loading"
          @click="
            track('golden_coin_shop', {
              screen: 'golden_text_hint_confirmation',
              button: 'confirm',
            });
            buyTextHint(quantity);
          "
        />
      </div>
    </div>
  </Dialog>
</template>
