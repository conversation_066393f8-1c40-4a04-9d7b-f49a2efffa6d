<script lang="ts" setup>
import { ShopInputQuntity } from '@components';
import { useShop, useTrackData } from '@composables';
import { HINT } from '@repositories';
import { useMapStore, useUserStore } from '@stores';
import { useQuery } from '@tanstack/vue-query';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeMap = useMapStore();

const { lastLocations } = storeToRefs(storeMap);
const { hintState, crystals, hints } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { timeNextDay } = useShop();
const { track, trackTime } = useTrackData();

const quantity = ref(1);
const locationWhenCheckPrice = ref([0, 0]);

const amount = computed(() => {
  return Number(hintState.value?.text_hint_price) * quantity.value;
});

function handleGetHint() {
  track('golden_coin_shop', {
    screen: 'golden_text_hint_available',
    button: 'use',
    quantity: quantity.value,
  });

  const price = discount.value?.beacon ? discount.value.price : amount.value;

  if (crystals.value < price) return openDialog('insufficient_crystals');

  openDialog('confirm_text_hint', {
    quantity: quantity.value,
  });
}

async function getHintPrice() {
  locationWhenCheckPrice.value = lastLocations.value;
  const [lng, lat] = locationWhenCheckPrice.value;

  const { data } = await HINT.getHintPrice({
    quantity: quantity.value,
    lng,
    lat,
  });

  return data;
}

const { data: discount, refetch } = useQuery({
  queryKey: ['getHintPrices'],
  queryFn: getHintPrice,
  refetchInterval: 1000 * 5,
});

onBeforeUnmount(() => {
  trackTime('hintsshop_mistaps', {
    type: 'text_hint',
  });
});
</script>
<template>
  <Dialog
    crystals
    @close="
      track('golden_coin_shop', {
        screen: !hintState?.is_opened_all_text_hints
          ? 'golden_text_hint_available'
          : 'golden_text_hint_unavailable',
        button: 'close',
      });
      emits('close');
    "
  >
    <template #header>
      <div v-html="t('TEXT_HINT_TITLE')"></div>
    </template>
    <div class="flex flex-nowrap flex-col gap-4">
      <div class="flex flex-nowrap items-center justify-center gap-3">
        <Icon name="shop/text" :size="105" />
        <div class="flex flex-col gap-1">
          <div class="text-base font-bold" v-html="t('TEXT_HINT_DESC_1')"></div>
          <div
            class="text-sm text-wrap break-words"
            v-html="t('TEXT_HINT_DESC_2')"
          ></div>
        </div>
      </div>
      <template v-if="!hintState?.is_opened_all_text_hints">
        <div class="text-sm text-center" v-html="t('TEXT_HINT_DESC_3')"></div>
        <ShopInputQuntity
          @update:model-value="
            (qty) => {
              quantity = qty;
              refetch();
              track('golden_coin_shop', {
                screen: 'golden_text_hint_available',
                button: 'change_quantity',
                quantity: qty,
              });
            }
          "
        />
        <div
          v-if="hints.length > 0"
          class="text-sm text-center"
          v-html="t('TEXT_HINT_DESC_7')"
        ></div>
      </template>
      <template v-else>
        <div
          class="text-sm text-center px-10"
          v-html="t('TEXT_HINT_DESC_5')"
        ></div>
        <div
          class="w-[225px] mx-auto bg-[#091A3B] rounded flex justify-center items-center px-5 py-3"
          v-html="
            t('TEXT_HINT_DESC_6', {
              TIME: timeNextDay,
            })
          "
        ></div>
      </template>
      <div class="flex justify-center items-center gap-2">
        <div class="text-sm" v-html="t('TEXT_HINT_DESC_4')"></div>
        <Icon
          name="question-mark"
          @click="
            openDialog('about_text_hint');
            track('golden_coin_shop', {
              screen: 'golden_text_hint_available',
              button: 'how_its_works',
            });
          "
        />
      </div>
      <div
        v-if="discount?.beacon"
        class="frame-discount-powerup text-sm mb-5 text-center"
        v-html="t('BEACON_DISCOUNT_DESC_2')"
      ></div>
      <div class="text-center" v-if="!hintState?.is_opened_all_text_hints">
        <Button
          class="!w-[220px]"
          :title="t('BUTTON_TEXT_HINT_GET', { QUANTITY: quantity })"
          :old-amount="discount?.beacon ? amount : undefined"
          :amount="
            discount?.is_free ? 0 : discount?.beacon ? discount?.price : amount
          "
          @click="handleGetHint"
        />
      </div>
    </div>
  </Dialog>
</template>
