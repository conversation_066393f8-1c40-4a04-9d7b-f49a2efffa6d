<script lang="ts" setup>
import { useBrandSov, useShop, useTrackData } from '@composables';
import { HINT } from '@repositories';
import { useMapStore, useUserStore } from '@stores';
import { useQuery } from '@tanstack/vue-query';
import type { IHint } from '@types';
import gsap, { Linear, Elastic } from 'gsap';

interface Props {
  data: IHint;
  quantity: number;
  fromMultiple?: boolean;
  sovCard?: string;
}

const props = defineProps<Props>();

const tl = gsap.timeline();
const storeUser = useUserStore();
const storeMap = useMapStore();

const { closeAllDialog, closeDialog } = useMicroRoute();
const { hintState } = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { t } = useI18n();
const { getMoreTextHint, loading } = useShop();
const { track } = useTrackData();

const flipped = ref(false);
const locationWhenCheckPrice = ref([0, 0]);

const { randomResult: randomResultMascot } = useBrandSov(
  'gold_coin_text_hint_mascot'
);

const { randomResult: randomResultCard } = useBrandSov(
  'gold_coin_hint_cards_front_outfit'
);

const { randomResult: randomResultLogo } = useBrandSov(
  'gold_coin_text_hint_reveals_back_logo'
);

const amount = computed(() => {
  return Number(hintState.value?.text_hint_price) * props.quantity;
});

const TITLE = {
  C: 'SINGLE_CARD_HINT_TITLE_C',
  U: 'SINGLE_CARD_HINT_TITLE_U',
  R: 'SINGLE_CARD_HINT_TITLE_R',
  SR: 'SINGLE_CARD_HINT_TITLE_SR',
  SSR: 'SINGLE_CARD_HINT_TITLE_SSR',
};

async function animated() {
  await tl
    .set('.wrapper', {
      display: 'none',
    })
    .fromTo(
      '.upload_flare',
      {
        scale: 0,
      },
      {
        scale: 1,
        duration: 1,
      }
    )
    .fromTo(
      '.upload_flare',
      {
        rotate: 0,
      },
      {
        rotate: 360,
        duration: 10,
        repeat: -1,
        ease: Linear.easeNone,
      }
    )
    .fromTo(
      '.upload_star',
      {
        opacity: 0,
      },
      {
        opacity: 1,
        yoyo: true,
        repeat: -1,
        delay: 1,
        duration: 1,
      },
      '-=13'
    )
    .fromTo(
      '.card',
      {
        opacity: 0,
        scale: 0,
      },
      {
        opacity: 1,
        scale: 1,
        duration: 1.5,
        ease: Elastic.easeOut,
      }
    )
    .fromTo(
      '.text',
      {
        opacity: 0,
        y: -20,
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.5,
      },
      '-=2'
    )
    .fromTo(
      '.panda',
      {
        opacity: 0,
        y: 50,
      },
      {
        opacity: 1,
        duration: 0.5,
        y: 0,
        ease: Linear.easeIn,
      }
    )
    .set('.wrapper', {
      delay: 1,
      display: 'block',
    });
}

function handleFlip() {
  if (flipped.value) return;
  track('reveal_text_hint', {
    screen: 'reveal_text_hint',
    button: 'flip_card',
    hint_id: props.data.hint_id,
  });

  flipped.value = !flipped.value;
  tl.to('.panda', {
    opacity: 0,
    y: 100,
    duration: 0.5,
  })
    .fromTo(
      '.get-more',
      {
        opacity: 0,
        y: -20,
      },
      {
        delay: 1,
        opacity: 1,
        duration: 0.25,
        stagger: 0.1,
        y: 0,
        ease: Linear.easeOut,
      }
    )
    .set('.wrapper', {
      display: 'none',
    });
}

function handleBack() {
  track('reveal_text_hint', {
    screen: 'reveal_text_hint',
    button: 'back',
  });
  props.fromMultiple ? closeDialog('single_card_hint') : closeAllDialog();
}

async function getHintPrice() {
  locationWhenCheckPrice.value = lastLocations.value;
  const [lng, lat] = locationWhenCheckPrice.value;

  const { data } = await HINT.getHintPrice({
    quantity: props.quantity,
    lng,
    lat,
  });

  return data;
}

const { data: discount } = useQuery({
  queryKey: ['getHintPrices'],
  queryFn: getHintPrice,
  refetchInterval: 1000 * 5,
});

onMounted(async () => {
  await nextTick();
  await animated();
});

onBeforeUnmount(() => {
  tl.kill();
});
</script>
<template>
  <div
    class="fullscreen flex flex-col justify-center items-center bg-[#090422] overflow-hidden"
  >
    <div
      class="fixed wrapper top-0 left-0 bottom-0 w-full h-full z-[888]"
      @click="handleFlip"
    ></div>
    <Button
      v-if="flipped"
      class="absolute top-2 left-2 z-[999]"
      shape="square"
      variant="secondary"
      @click="handleBack"
    >
      <Icon name="arrow-left" />
    </Button>
    <Icon
      class="panda fixed bottom-4 left-0"
      :name="randomResultMascot.gold_coin_text_hint_mascot.getAsset()"
      :size="125"
    />
    <Icon
      class="upload_flare fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[120vw] aspect-square pointer-events-none"
      name="upload_flare"
    />
    <Icon
      class="upload_star fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[120vw] aspect-square pointer-events-none"
      name="star_frame"
    />
    <div
      class="text text-lg font-bold absolute left-1/2 -translate-x-1/2 top-[20vw]"
      v-html="t(flipped ? TITLE[data.rarity] : 'SINGLE_CARD_HINT_TITLE')"
    ></div>
    <div
      class="card"
      :class="{
        flipped: flipped,
      }"
    >
      <div
        class="back flex flex-col items-center gap-2 px-5 py-10"
        :style="{
          backgroundImage: `url(/imgs/${randomResultCard.gold_coin_hint_cards_front_outfit.getAsset(
            `_back_${data.rarity}`
          )}.png)`,
        }"
      >
        <Icon
          class="fixed left-[7px] bottom-4"
          :name="
            randomResultLogo.gold_coin_text_hint_reveals_back_logo.getAsset()
          "
          :size="28"
        />
        <Icon
          v-if="data.is_new"
          class="fixed -right-5 -top-5 z-10"
          name="hint/new"
          :size="60"
        />

        <div
          class="text-base"
          v-html="
            t('MY_HINT_NUMBER', {
              NUMBER: data.hint_id,
            })
          "
        ></div>
        <div
          class="text-sm font-bold text-center"
          v-html="t(data.content)"
        ></div>
      </div>
      <div
        class="front"
        :style="{
          backgroundImage: `url(/imgs/${
            sovCard ||
            randomResultCard.gold_coin_hint_cards_front_outfit.getAsset(
              `_front_${data.rarity}`
            )
          }.png)`,
        }"
      ></div>
    </div>
    <div
      class="fixed left-1/2 -translate-x-1/2 bottom-20 text-center"
      v-show="flipped && !fromMultiple"
    >
      <div
        class="get-more text-sm mb-5"
        v-html="t('SINGLE_CARD_HINT_DESC')"
      ></div>
      <Button
        class="get-more"
        :title="t('BUTTON_QUANTITY_GET_MORE', { QUANTITY: quantity })"
        :old-amount="discount?.beacon ? amount : undefined"
        :amount="
          discount?.is_free ? 0 : discount?.beacon ? discount?.price : amount
        "
        :loading="loading"
        @click="
          track('reveal_text_hint', {
            screen: 'reveal_text_hint',
            button: 'use_more',
          });
          getMoreTextHint(quantity);
        "
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.card {
  position: relative;
  transform-style: preserve-3d;
  width: 64vw;
  height: 88vw;
  &.flipped {
    transform-origin: center right;
    transform: translateX(-100%) rotateY(-180deg) !important;
    transition: all 0.5s;
  }
  .front {
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-position: center;
    backface-visibility: hidden;
  }
  .back {
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-position: center;
    backface-visibility: hidden;
    transform: rotateY(180deg);
  }
}
</style>
