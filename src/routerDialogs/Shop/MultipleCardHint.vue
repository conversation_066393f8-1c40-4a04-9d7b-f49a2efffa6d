<script lang="ts" setup>
import { useBrandSov, useShop, useTrackData } from '@composables';
import { HINT } from '@repositories';
import { useMapStore, useUserStore } from '@stores';
import { useQuery } from '@tanstack/vue-query';
import type { IHint, THintRarity } from '@types';
import gsap, { Elastic, Linear } from 'gsap';

interface Props {
  quantity: number;
  data: IHint[];
}

const props = defineProps<Props>();

const tl = gsap.timeline();
const storeUser = useUserStore();
const storeMap = useMapStore();

const { hintState } = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { getMoreTextHint, loading } = useShop();
const { t } = useI18n();
const { data } = toRefs(props);
const { openDialog, closeAllDialog } = useMicroRoute();
const { track } = useTrackData();

const loaded = ref(false);
const flippedAll = ref(false);
const locationWhenCheckPrice = ref([0, 0]);

const { randomResult: randomResultMascot } = useBrandSov(
  'gold_coin_text_hint_mascot'
);

const { randomResult: randomResultCard } = useBrandSov(
  'gold_coin_hint_cards_front_outfit'
);

const { randomResult: randomResultLogo } = useBrandSov(
  'gold_coin_text_hint_reveals_back_logo'
);

const amount = computed(() => {
  return Number(hintState.value?.text_hint_price) * props.quantity;
});

const hints = ref<IHint[]>(
  data.value.map((hint) => ({ ...hint, is_opened: false }))
);

const openedAllHints = computed(() => {
  return hints.value.every((hint) => hint.is_opened) || flippedAll.value;
});

async function animated() {
  await tl
    .fromTo(
      '.card',
      {
        scale: 0,
      },
      {
        scale: 1,
        duration: 1.5,
        stagger: 0.5 / props.quantity,
        ease: Elastic.easeOut,
      }
    )
    .fromTo(
      '.text',
      {
        opacity: 0,
        y: -20,
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.5,
      },
      '-=1'
    )
    .fromTo(
      '.panda',
      {
        opacity: 0,
        y: '100%',
      },
      {
        opacity: 1,
        duration: 0.5,
        y: 0,
        ease: Linear.easeIn,
      },
      '-=1'
    )
    .fromTo(
      '.btn-reveal',
      {
        opacity: 0,
        x: 20,
      },
      {
        opacity: 1,
        x: 0,
        ease: Linear.easeOut,
        onComplete: () => {
          loaded.value = true;
        },
      },
      '-=1'
    );
}

function handleRevealHints() {
  if (!loaded.value || flippedAll.value) return;

  track('reveal_text_hint', {
    screen: 'reveal_text_hint',
    button: 'flip_card',
  });

  flippedAll.value = !flippedAll.value;
  tl.to(['.btn-reveal', '.panda'], {
    opacity: 0,
    duration: 0.5,
  }).fromTo(
    '.get-more',
    {
      opacity: 0,
      y: -20,
    },
    {
      delay: 1,
      opacity: 1,
      duration: 0.25,
      stagger: 0.1,
      y: 0,
      ease: Linear.easeOut,
    }
  );
}

function handleSingleCard(index: number, rarity: THintRarity) {
  if (hints.value[index].is_opened) return;
  hints.value[index].is_opened = true;
  openDialog('single_card_hint', {
    data: hints.value[index],
    quantity: props.quantity,
    fromMultiple: true,
    sovCard: randomResultCard.value.gold_coin_hint_cards_front_outfit.getAsset(
      `_front_${rarity}`
    ),
  });
}

async function getHintPrice() {
  locationWhenCheckPrice.value = lastLocations.value;
  const [lng, lat] = locationWhenCheckPrice.value;

  const { data } = await HINT.getHintPrice({
    quantity: props.quantity,
    lng,
    lat,
  });

  return data;
}

const { data: discount } = useQuery({
  queryKey: ['getHintPrices'],
  queryFn: getHintPrice,
  refetchInterval: 1000 * 5,
});

onMounted(async () => {
  await nextTick();
  await animated();
});

onBeforeUnmount(() => {
  tl.kill();
});
</script>
<template>
  <div class="fullscreen multiple-hint">
    <div
      class="text text-lg font-bold absolute left-1/2 -translate-x-1/2 top-[20vw]"
      v-html="t('MULTIPLE_CARD_HINT_TITLE')"
    ></div>
    <Button
      v-if="!openedAllHints"
      class="btn-reveal absolute top-2 right-2"
      :label="t('BUTTON_REVEAL_HINTS')"
      variant="purple"
      size="max-content"
      @click="handleRevealHints"
    />
    <Button
      v-if="openedAllHints"
      class="absolute top-2 left-2"
      shape="square"
      variant="secondary"
      @click="
        track('reveal_text_hint', {
          screen: 'reveal_text_hint',
          button: 'back',
        });
        closeAllDialog();
      "
    >
      <Icon name="arrow-left" />
    </Button>
    <Icon
      class="panda fixed -bottom-6 left-0"
      :name="randomResultMascot.gold_coin_text_hint_mascot.getAsset()"
      :size="125"
    />
    <div class="w-full h-full overflow-y-auto p-5">
      <div class="grid grid-cols-3 gap-5">
        <div
          class="card"
          v-for="(
            { rarity, is_new, hint_id, content, is_opened }, index
          ) in hints"
          :key="index"
          :class="{
            flipped: flippedAll || is_opened,
            'pointer-events-none': is_opened || !loaded || flippedAll,
          }"
          @click="handleSingleCard(index, rarity)"
        >
          <div
            class="back flex flex-col flex-nowrap justify-center items-center gap-2 px-4 py-5"
            :style="{
              backgroundImage: `url(/imgs/${randomResultCard.gold_coin_hint_cards_front_outfit.getAsset(
                `_back_${rarity}`
              )}.png)`,
            }"
          >
            <Icon
              class="fixed left-1 bottom-4"
              :name="
                randomResultLogo.gold_coin_text_hint_reveals_back_logo.getAsset()
              "
              :size="10"
            />
            <Icon
              v-if="is_new"
              class="fixed -right-3 -top-3 z-10"
              name="hint/new"
              :size="30"
            />

            <div
              class="text-[10px]"
              v-html="
                t('MY_HINT_NUMBER', {
                  NUMBER: hint_id,
                })
              "
            ></div>
            <div
              class="text text-[8px] font-bold text-center overflow-hidden"
              v-html="t(content)"
            ></div>
          </div>
          <div
            class="front"
            :style="{
              backgroundImage: `url(/imgs/${randomResultCard.gold_coin_hint_cards_front_outfit.getAsset(
                `_front_${rarity}`
              )}.png)`,
            }"
          ></div>
        </div>
      </div>
    </div>
    <div
      class="fixed left-1/2 -translate-x-1/2 bottom-10 text-center"
      v-show="openedAllHints"
    >
      <div
        class="get-more text-sm mb-5"
        v-html="t('SINGLE_CARD_HINT_DESC')"
      ></div>
      <Button
        class="get-more"
        :title="t('BUTTON_QUANTITY_GET_MORE', { QUANTITY: quantity })"
        :old-amount="discount?.beacon ? amount : undefined"
        :amount="
          discount?.is_free ? 0 : discount?.beacon ? discount?.price : amount
        "
        :loading="loading"
        @click="
          track('reveal_text_hint', {
            screen: 'reveal_text_hint',
            button: 'use_more',
          });
          getMoreTextHint(quantity);
        "
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.multiple-hint {
  background: url(/imgs/hint/multiple_hint_flare.png), #090422;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 150px 0px;
  overflow: hidden;
  .card {
    position: relative;
    transform-style: preserve-3d;
    width: 100%;
    height: calc(100vw / 3);
    &.flipped {
      transform: translateX(-100%) rotateY(-180deg) !important;
      transform-origin: center right;
      transition: all 1s;
    }
    .front {
      position: absolute;
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
      background-position: center;
      backface-visibility: hidden;
    }
    .back {
      position: absolute;
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
      background-position: center;
      backface-visibility: hidden;
      transform: rotateY(180deg);
      .text > * {
        font-size: 8px !important;
        line-height: normal;
      }
    }
  }
}
</style>
