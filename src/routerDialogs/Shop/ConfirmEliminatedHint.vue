<script lang="ts" setup>
import { useShop, useTrackData } from '@composables';

interface Props {
  quantity: number;
}

interface Emits {
  (event: 'close'): void;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { buyEliminatedHint, loading } = useShop();
const { track } = useTrackData();
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('CONFIRM_ELIMINATED_HINT_TITLE')"></div>
    </template>
    <div class="text-center flex flex-col gap-5">
      <Icon class="mx-auto" name="shop/eliminated" :size="105" />
      <div
        class="text-sm"
        v-html="t('CONFIRM_ELIMINATED_HINT_DESC', { QUANTITY: quantity })"
      ></div>
      <div class="flex flex-nowrap gap-4">
        <Button
          class="flex-1"
          size="max-content"
          :label="t('BUTTON_CANCEL')"
          :class="{
            'pointer-events-none': loading,
          }"
          variant="purple"
          @click="
            track('golden_coin_shop', {
              screen: 'eliminate_pu_confirmation',
              button: 'cancel',
            });
            emits('close');
          "
        />
        <Button
          class="flex-1"
          size="max-content"
          :label="t('BUTTON_YES')"
          :loading="loading"
          @click="
            track('golden_coin_shop', {
              screen: 'eliminate_pu_confirmation',
              button: 'confirm',
            });
            buyEliminatedHint(quantity);
          "
        />
      </div>
    </div>
  </Dialog>
</template>
