<script lang="ts" setup>
import { delay, useSound } from '@composables';
import { INDEX_RARITY } from '@constants';
import { useDialogStore, useMapStore } from '@stores';
import type { IHint } from '@types';

interface Props {
  type: 'text_hint' | 'eliminated_hint';
  quantity: number;
  hints?: IHint[];
  grids?: string[];
}

const props = defineProps<Props>();

const storeMap = useMapStore();
const storeDialog = useDialogStore();

const { hints } = toRefs(props);
const { play, stop } = useSound();
const { openDialog, push, closeAllDialog } = useMicroRoute();
const { t } = useI18n();

const loading = ref(true);

const sortedHints = computed(() => {
  if (!hints.value) return [];
  return hints.value.sort(
    (a, b) => INDEX_RARITY[b.rarity] - INDEX_RARITY[a.rarity]
  );
});

async function handlePlaySound() {
  loading.value = false;
  if (props.type === 'eliminated_hint') play('video');
  else {
    await delay(1000);
    play('step_1');
    await delay(1500);
    play('step_2');
    await delay(500);
    play('step_3');
    await delay(1000);
    play('step_4');
  }
}

function handleEneded() {
  play('default');
  if (props.type === 'text_hint') {
    openDialog(props.quantity > 1 ? 'multiple_card_hint' : 'single_card_hint', {
      data: props.quantity > 1 ? hints.value : sortedHints.value[0],
      quantity: props.quantity,
    });
    return;
  }

  closeAllDialog();
  storeMap.fromGrids = false;
  push('map_eliminated_grids', {
    grids: props.grids,
    quantity: props.quantity,
    totals: props.grids?.length,
  });
}

onMounted(async () => {
  await nextTick();
  stop();
});
</script>
<template>
  <div class="relative w-full h-full bg-[#090422]">
    <video
      class="relative object-cover w-full h-full"
      autoplay
      muted
      playsinline
      :controls="false"
      :src="
        type === 'text_hint'
          ? `/anims/${sortedHints?.[0]?.rarity}.mp4`
          : `/anims/${props.quantity}-star.mp4`
      "
      @canplay="handlePlaySound"
      @ended="handleEneded"
    />
    <div class="z-10 flex items-center justify-center fullscreen">
      <q-spinner color="ffffff" size="5em" v-if="loading" />
    </div>
    <Button
      class="absolute top-2 right-2 z-[99]"
      :label="t('BUTTON_SKIP')"
      size="max-content"
      variant="purple"
      @click="handleEneded"
    />
  </div>
</template>
