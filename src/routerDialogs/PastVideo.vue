<script lang="ts" setup>
import { Plyr } from '@components';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { openDialog } = useMicroRoute();

function onBack() {
  emits('close');
  openDialog('welcome_hunter');
}
</script>

<template>
  <Dialog hideClose>
    <template #header>
      <div v-html="t('PAST_VIDEO_TITLE')"></div>
    </template>
    <template #btnTopLeft>
      <Button shape="square" variant="secondary" @click="onBack">
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>
    <div class="coin-found text-center">
      <div class="contain-video relative !w-[230px] !h-[350px] mb-5">
        <Plyr source="https://www.youtube.com/shorts/6gb5bmb545E" />
        <Icon class="icon" name="sqkii-mouse" width="60" />
      </div>
      <div class="text-sm" v-html="t('PAST_VIDEO_DESC')"></div>
    </div>
  </Dialog>
</template>
