[
  {
    id: '1',
    q: 'MID_SURVEYQUESTION_1_AGE',
    sub_q: 'MID_SURVEY_DESC_AGE',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_AGE_1',
      },
      {
        value: 'MID_SURVEY_OPTIONS_AGE_2',
      },
      {
        value: 'MID_SURVEY_OPTIONS_AGE_3',
      },
      {
        value: 'MID_SURVEY_OPTIONS_AGE_4',
      },
      {
        value: 'MID_SURVEY_OPTIONS_AGE_5',
      },
      {
        value: 'MID_SURVEY_OPTIONS_AGE_6',
      },
      {
        value: 'MID_SURVEY_OPTIONS_AGE_7',
      },
      {
        value: 'MID_SURVEY_OPTIONS_AGE_8',
      },
    ],
    selected: '',
    type: 'select',
  },
  //2
  {
    id: '2',
    q: 'MID_SURVEYQUESTION_GENDER',
    sub_q: 'MID_SURVEY_DESC_GENDER',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_GENDER_1',
      },
      {
        value: 'MID_SURVEY_OPTIONS_GENDER_2',
      },
    ],
    selected: '',
    type: 'select',
  },
  //3
  {
    id: '3',
    q: 'MID_SURVEYQUESTION_FINDOUTHTM',
    sub_q: 'MID_SURVEYDESC_FINDOUTHTM',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_FINDOUTHTM_1',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_FINDOUTHTM_2',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_FINDOUTHTM_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_FINDOUTHTM_4',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_FINDOUTHTM_5',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_FINDOUTHTM_6',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_FINDOUTHTM_7',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_FINDOUTHTM_8',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //4
  {
    id: '4',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '4');
        if (
          index >= 0 &&
          surveyAnswers.value[index].answer ===
            t('MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_4')
        )
          return '5';
        return '6';
      },
    },
    q: 'MID_SURVEYQUESTION_WHICH_COIN_HUNT',
    sub_q: 'MID_SURVEY_DESC_WHICH_COIN_HUNT',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_1',
      },
      {
        value: 'MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_2',
      },
      {
        value: 'MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_3',
      },
      {
        value: 'MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_4',
      },
    ],
    selected: '',
    type: 'select',
  },
  //5
  {
    id: '5',
    q: 'MID_SURVEYQUESTION_NOTHUNT',
    sub_q: 'MID_SURVEYDESC_NOTHUNT',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_NOTHUNT_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOTHUNT_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOTHUNT_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOTHUNT_4',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOTHUNT_5',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  {
    id: '6',
    q: 'MID_SURVEYQUESTION_TAKE_TRAIN',
    sub_q: 'MID_SURVEY_DESC_TAKE_TRAIN',

    a: [
      {
        value: 60,
        type: 'vote',
        vote: 0,
        step: 20,
        unit: '%',
        scale: 100, // Likert scale (0-10)
      },
    ],
    min_rate_text: '',
    max_rate_text: '',
    selected: 'vote',
  },

  {
    id: '7',
    q: 'MID_SURVEYQUESTION_HUNTING_EXPERIENCE',
    sub_q: 'MID_SURVEYDESC_HUNTING_EXPERIENCE',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '7');

        switch (true) {
          case Number(surveyAnswers.value[index].answer) >= 6:
            return '8';
          default:
            return '9';
        }
      },
      back: '4',
    },
    a: [
      {
        value: 0,
        type: 'vote',
        vote: 0,
        scale: 10, // Likert scale (0-10)
      },
    ],
    min_rate_text: 'MID_SURVEY_OPTIONS_HUNTING_EXPERIENCE_RATE_1',
    max_rate_text: 'MID_SURVEY_OPTIONS_HUNTING_EXPERIENCE_RATE_2',
    selected: 'vote',
  },
  //8
  {
    id: '8',
    q: 'MID_SURVEYQUESTION_ENJOY_HTM',
    sub_q: 'MID_SURVEYDESC_ENJOY_HTM',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_ENJOY_HTM_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENJOY_HTM_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENJOY_HTM_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENJOY_HTM_4',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //9
  {
    id: '9',
    condition: {
      next: '9',
      back: '6',
    },
    q: 'MID_SURVEYQUESTION_NOTENJOY_HTM',
    sub_q: 'MID_SURVEYDESC_NOTENJOY_HTM',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_NOTENJOY_HTM_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOTENJOY_HTM_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOTENJOY_HTM_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOTENJOY_HTM_4',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //10
  {
    id: '10',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '4');

        switch (true) {
          case index === -1 ||
            [
              t('MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_2'),
              t('MID_SURVEY_OPTIONS_WHICH_COIN_HUNT_3'),
            ].includes(surveyAnswers.value[index].answer):
            return '11';
          default:
            return '16';
        }
      },
    },
    q: 'MID_SURVEYQUESTION_RECC_HTM',
    sub_q: 'MID_SURVEYDESC_RECC_HTM',
    a: [
      {
        value: 0,
        type: 'vote',
        vote: 0,
        scale: 10, // Likert scale (0-10)
      },
    ],
    min_rate_text: 'MID_SURVEY_OPTIONS_RECC_HTM_1',
    max_rate_text: 'MID_SURVEY_OPTIONS_RECC_HTM_2',
    selected: 'vote',
  },
  //11
  {
    id: '11',
    q: 'MID_SURVEYQUESTION_MAP',
    sub_q: 'MID_SURVEYDESC_MAP',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '10');

        if (index >= 0 && +surveyAnswers.value[index].answer <= 6) return '12';
        return '13';
      },
    },
    a: [
      {
        value: 0,
        type: 'vote',
        vote: 0,
        scale: 10, // Likert scale (0-10)
      },
    ],
    min_rate_text: 'MID_SURVEY_OPTIONS_MAP_1',
    max_rate_text: 'MID_SURVEY_OPTIONS_MAP_2',
    selected: 'vote',
  },
  //12
  {
    id: '12',
    q: 'MID_SURVEYQUESTION_MAP_NOT_USEFUL',
    sub_q: 'MID_SURVEY_DESC_MAP_NOT_USEFUL',

    a: [
      {
        value: 'MID_SURVEY_OPTIONS_MAP_NOT_USEFUL_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_MAP_NOT_USEFUL_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_MAP_NOT_USEFUL_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_MAP_NOT_USEFUL_4',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_MAP_NOT_USEFUL_4',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_MAP_NOT_USEFUL_5',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //13
  {
    id: '13',
    q: 'MID_SURVEYQUESTION_HUNT_WITH',
    sub_q: 'MID_SURVEYDESC_HUNT_WITH',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '13');

        if (
          index >= 0 &&
          surveyAnswers.value[index].answer !==
            t('MID_SURVEY_OPTIONS_HUNT_WITH_1')
        )
          return '14';
        return '15';
      },
    },
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_WITH_1',

        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_WITH_2',

        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_WITH_3',

        active: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //14
  {
    id: '14',
    q: 'MID_SURVEYQUESTION_HUNT_TOGETHER',
    sub_q: 'MID_SURVEYDESC_HUNT_TOGETHER',

    a: [
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_TOGETHER_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_TOGETHER_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_TOGETHER_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_TOGETHER_4',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_TOGETHER_5',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //15
  {
    id: '15',

    q: 'MID_SURVEYQUESTION_HUNT_ALONE',
    sub_q: 'MID_SURVEYDESC_HUNT_ALONE',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_ALONE_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_ALONE_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_ALONE_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_ALONE_4',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_ALONE_5',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_ALONE_6',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_HUNT_ALONE_7',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //16
  {
    id: '16',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '16');

        if (
          index >= 0 &&
          surveyAnswers.value[index].answer ===
            t('MID_SURVEY_OPTIONS_CRYSTAL_ACTIONS_1')
        )
          return '17';
        return '18';
      },
    },
    q: 'MID_SURVEYQUESTION_CRYSTAL_ACTIONS',
    sub_q: 'MID_SURVEYDESC_CRYSTAL_ACTIONS',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_CRYSTAL_ACTIONS_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_CRYSTAL_ACTIONS_2',
        active: false,
      },
      {
        value: 'SURVEY_OPTIONS_HUNT_ALONE_3',
        active: false,
      },
      {
        value: 'SURVEY_OPTIONS_HUNT_ALONE_4',
        active: false,
      },
      {
        value: 'SURVEY_OPTIONS_HUNT_ALONE_5',
        active: false,
      },
    ],
    selected: '',
    type: 'select',
  },

  //17
  {
    id: '17',
    condition: {
      next: '19',
    },
    q: 'MID_SURVEYQUESTION_WHY_CRYSTAL_ACTIONS',
    sub_q: 'MID_SURVEYDESC_WHY_CRYSTAL_ACTIONS',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_4',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //18
  {
    id: '18',
    condition: {
      next: '22',
      back: '16',
    },
    q: 'MID_SURVEYQUESTION_WHY_NO_CRYSTAL_ACTIONS',
    sub_q: 'SURVEYDESC_WHY_NO_CRYSTAL_ACTIONS',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_4',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //19
  {
    id: '19',

    q: 'MID_SURVEYQUESTION_POWERUP_USEFUL',
    sub_q: 'MID_SURVEY_DESC_POWERUP_USEFUL',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_USEFUL_1',
      },
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_USEFUL_2',
      },
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_USEFUL_3',
      },
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_USEFUL_4',
      },
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_USEFUL_5',
      },
    ],
    selected: '',
    type: 'select',
  },
  //20
  {
    id: '20',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '16');

        if (
          index >= 0 &&
          surveyAnswers.value[index].answer !==
            t('MID_SURVEY_OPTIONS_CRYSTAL_ACTIONS_1')
        )
          return '21';
        return '22';
      },
    },
    q: 'MID_SURVEYQUESTION_POWERUP_APPEALING',
    sub_q: 'MID_SURVEY_DESC_POWERUP_APPEALING',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_APPEALING_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_APPEALING_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_APPEALING_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_APPEALING_4',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_POWERUP_APPEALING_5',

        active: false,
      },
    ],
    selected: '',
    type: 'select',
  },
  //21
  {
    id: '21',

    q: 'MID_SURVEYQUESTION_NOT_USE_POWERUP',
    sub_q: 'MID_SURVEY_DESC_NOT_USE_POWERUP',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_NOT_USE_POWERUP_1',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOT_USE_POWERUP_2',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOT_USE_POWERUP_3',
        active: false,
      },
      {
        value: 'MID_SURVEY_OPTIONS_NOT_USE_POWERUP_4',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  //22
  {
    id: '22',
    q: 'MID_SURVEYQUESTION_HOW_EASY_GAME',
    sub_q: 'MID_SURVEY_DESC_HOW_EASY_GAME',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '22');

        if (index >= 0 && +surveyAnswers.value[index].answer <= 6) return '23';
        return '24';
      },
    },
    a: [
      {
        value: 0,
        type: 'vote',
        vote: 0,
        scale: 10, // Likert scale (0-10)
      },
    ],
    min_rate_text: 'MID_SURVEY_OPTIONS_HOW_EASY_GAME_1',
    max_rate_text: 'MID_SURVEY_OPTIONS_HOW_EASY_GAME_2',
    selected: 'vote',
  },
  {
    id: '23',

    q: 'MID_SURVEYQUESTION_LEARN_GAME_DIFFICULT',
    sub_q: 'MID_SURVEY_DESC_LEARN_GAME_DIFFICULT',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_LEARN_GAME_DIFFICULT_1',
      },
      {
        value: 'MID_SURVEY_OPTIONS_LEARN_GAME_DIFFICULT_2',
      },
      {
        value: 'MID_SURVEY_OPTIONS_LEARN_GAME_DIFFICULT_3',
      },
      {
        value: 'MID_SURVEY_OPTIONS_LEARN_GAME_DIFFICULT_4',
      },
      {
        value: 'MID_SURVEY_OPTIONS_LEARN_GAME_DIFFICULT_5',
      },
      {
        value: 'MID_SURVEY_OPTIONS_LEARN_GAME_DIFFICULT_6',
      },
      {
        value: 'MID_SURVEY_OPTIONS_LEARN_GAME_DIFFICULT_7',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'select',
  },
  {
    id: '24',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '22');
        if (
          index > -1 &&
          [
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_4'),
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_5'),
          ].includes(surveyAnswers.value[index].answer)
        )
          return '25';
        else if (
          index > -1 &&
          [
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_1'),
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_2'),
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_3'),
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_4'),
          ].includes(surveyAnswers.value[index].answer)
        )
          return '26';
        return '29';
      },
    },
    q: 'MID_SURVEYQUESTION_USE_SQKIIVOUCHER',
    sub_q: 'MID_SURVEY_DESC_USE_SQKIIVOUCHER',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_1',
      },
      {
        value: 'MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_2',
      },
      {
        value: 'MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_3',
      },
      {
        value: 'MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_4',
      },
      {
        value: 'MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_5',
      },
    ],
    selected: '',
    type: 'select',
  },
  {
    id: '25',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '22');
        if (
          index > -1 &&
          [
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_1'),
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_2'),
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_3'),
            t('MID_SURVEY_OPTIONS_USE_SQKIIVOUCHER_4'),
          ].includes(surveyAnswers.value[index].answer)
        )
          return '26';
        return '29';
      },
    },
    q: 'MID_SURVEYQUESTION_ENCOURAGE_SQKIIVOUCHER',
    sub_q: 'MID_SURVEY_DESC_ENCOURAGE_SQKIIVOUCHER',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_ENCOURAGE_SQKIIVOUCHER_1',
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENCOURAGE_SQKIIVOUCHER_2',
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENCOURAGE_SQKIIVOUCHER_3',
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENCOURAGE_SQKIIVOUCHER_4',
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENCOURAGE_SQKIIVOUCHER_5',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'multiple',
  },
  {
    id: '26',
    q: 'MID_SURVEYQUESTION_SQKIIVOUCHER_SECURE',
    sub_q: 'MID_SURVEY_DESC_SQKIIVOUCHER_SECURE',
    condition: {
      next: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '26');

        if (index >= 0 && +surveyAnswers.value[index].answer <= 6) return '27';
        return '28';
      },
    },
    a: [
      {
        value: 0,
        type: 'vote',
        vote: 0,
        scale: 10, // Likert scale (0-10)
      },
    ],
    min_rate_text: 'MID_SURVEY_OPTIONS_SQKIIVOUCHER_SECURE_1',
    max_rate_text: 'MID_SURVEY_OPTIONS_SQKIIVOUCHER_SECURE_2',
    selected: 'vote',
  },
  {
    id: '27',
    q: 'MID_SURVEYQUESTION_ENCOUNTER_SQKIIVOUCHER_ISSUE',
    sub_q: 'SURVEYDESC_25_KNOW_UNTAME',
    a: [
      {
        value: 'MID_SURVEY_OPTIONS_ENCOUNTER_SQKIIVOUCHER_ISSUE_1',
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENCOUNTER_SQKIIVOUCHER_ISSUE_2',
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENCOUNTER_SQKIIVOUCHER_ISSUE_3',
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENCOUNTER_SQKIIVOUCHER_ISSUE_4',
      },
      {
        value: 'MID_SURVEY_OPTIONS_ENCOUNTER_SQKIIVOUCHER_ISSUE_5',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        active: false,
        error: false,
      },
    ],
    selected: '',
    type: 'select',
  },
  {
    id: '28',
    q: 'MID_SURVEYQUESTION_SQKIIVOUCHER_FEATURE',
    sub_q: 'MID_SURVEY_DESC_SQKIIVOUCHER_FEATURE',
    a: [
      {
        value: 'Other:',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        onlyInput: true,
        error: false,
      },
    ],
    selected: '',
    type: 'onlyInput',
  },
  {
    id: '29',
    condition: {
      back: () => {
        let index = surveyAnswers.value.findIndex((a: any) => a.id === '19');

        switch (true) {
          case surveyAnswers.value[index].answer ===
            t('SURVEY_OPTIONS_USE_CRYSTALS_1'):
            return '20';
          case surveyAnswers.value[index].answer ===
            t('SURVEY_OPTIONS_USE_CRYSTALS_2'):
            return '21';
          default:
            return '18';
        }
      },
    },
    q: 'MID_SURVEYQUESTION_OTHER_FEEDBACK',
    sub_q: 'MID_SURVEYDESC_OTHER_FEEDBACK',
    a: [
      {
        value: 'Other:',
        type: 'input',
        feedback: '',
        ref: 'refOther',
        onlyInput: true,
        error: false,
      },
    ],
    selected: '',
    type: 'onlyInput',
  },
];
