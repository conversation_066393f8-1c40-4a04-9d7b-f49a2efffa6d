<script lang="ts" setup>
import { useTrackData } from '@composables';
import { useDialogStore, useMapStore } from '@stores';

interface Emits {
  (e: 'close'): void;
  (e: 'allow'): void;
  (e: 'later'): void;
}

const emits = defineEmits<Emits>();

const storeMap = useMapStore();
const storeDialog = useDialogStore();

const { geoState } = storeToRefs(storeMap);
const { t } = useI18n();
const { track } = useTrackData();

const URL = computed(() => {
  return process.env.APP_END_POINT;
});

async function triggerGPS() {
  await storeMap.triggerGPS();
  emits('allow');
  onClose();
}

function onClose() {
  emits('close');
  emits('later');
  storeDialog.gpsTapped = true;
}
</script>

<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('GPS_ALLOWGPSPOPUP_HEADING')"></div>
    </template>
    <div class="text-center">
      <div
        class="text-sm mb-5"
        v-html="t('GPS_ALLOWGPSPOPUP_DESCRIPTION_1')"
      ></div>
      <div class="box">
        <div
          class="text-black text-sm font-bold px-1 py-4 break-words"
          v-html="
            t('GPS_ALLOWGPSPOPUP_PERMISSION_1', {
              URL,
            })
          "
        ></div>
        <div class="keyline-horizontal"></div>
        <div class="relative flex px-5 py-2">
          <div
            class="text-xs text-[#007aff] w-1/2"
            v-html="t('GPS_ALLOWGPSPOPUP_PERMISSION_2')"
          ></div>
          <div class="keyline-vertical"></div>
          <div
            class="text-xs text-[#007aff] w-1/2"
            v-html="t('GPS_ALLOWGPSPOPUP_PERMISSION_3')"
          ></div>
        </div>
      </div>
      <div
        class="text-sm mb-5 px-2"
        v-html="t('GPS_ALLOWGPSPOPUP_DESCRIPTION_2')"
      ></div>
      <div class="flex flex-nowrap justify-center gap-4">
        <!-- <Button
          class="flex-1"
          size="max-content"
          :label="t('GPS_ALLOWGPSPOPUP_BUTTON_LATER')"
          :class="{
            'no-pointer-events': geoState === 'WAITING_ACTIVE',
          }"
          variant="purple"
          @click="
            track('enable_GPS_popup', {
              action: 'popup_gps_later',
            });
            onClose();
          "
        /> -->
        <Button
          size="max-content"
          :label="t('GPS_ALLOWGPSPOPUP_BUTTON_ALLOWGPS')"
          :loading="geoState === 'WAITING_ACTIVE'"
          class="!w-[210px]"
          @click="
            track('enable_GPS_popup', {
              action: 'popup_gps_allow',
            });
            triggerGPS();
          "
        />
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.box {
  background: #f8f8f8d1;
  border-radius: 12px;
  width: 100%;
  height: auto;
  margin: 0 auto 20px;
  .url {
    max-width: 100%;
    word-wrap: break-word;
  }

  @media screen and (max-width: 360px) {
    width: calc(100% + 20px);
    margin-left: -10px;
  }
  .keyline-horizontal {
    background: linear-gradient(0deg, #3f3f3f, #3f3f3f), rgba(0, 0, 80, 0.05);
    background-blend-mode: color-burn, normal;
    transform: matrix(1, 0, 0, -1, 0, 0);
    width: 100%;
    height: 0.5px;
  }
  .keyline-vertical {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(0deg, #3f3f3f, #3f3f3f), rgba(0, 0, 80, 0.05);
    background-blend-mode: color-burn, normal;
    transform: matrix(1, 0, 0, -1, 0, 0);
    width: 0.5px;
    height: 100%;
  }
}
</style>
