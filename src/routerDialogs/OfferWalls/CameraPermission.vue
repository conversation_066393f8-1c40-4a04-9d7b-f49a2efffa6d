<script setup lang="ts">
import { useTrackData } from '@composables';

interface Props {
  description?: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'request'): void;
}

defineProps<Props>();
const emits = defineEmits<Emits>();
const { track } = useTrackData();
const URL = computed(() => {
  return process.env.APP_END_POINT || 'http://localhost:8080';
});

const { t } = useI18n();
</script>
<template>
  <Dialog
    @close="
      emits('close');
      track('receiptverification_enablecamera', {
        action: 'receiptverification_enablecamera_close',
      });
    "
  >
    <template #header>
      <div v-html="t('CAMERA_PERMISSION_HEADER')"></div>
    </template>
    <div class="full-width column items-center justify-start text-center px-3">
      <div
        class="mb-5"
        v-html="description ? t(description) : t('CAMERA_PERMISSION_DES')"
      ></div>
      <div class="box">
        <div
          class="text-black text-sm font-bold px-1 py-4 break-words"
          v-html="
            t('GPS_ALLOWGPSPOPUP_PERMISSION_1', {
              URL,
            })
          "
        ></div>
        <div class="keyline-horizontal"></div>
        <div class="relative flex px-5 py-2">
          <div
            class="text-xs text-[#007aff] w-1/2"
            v-html="t('GPS_ALLOWGPSPOPUP_PERMISSION_2')"
          ></div>
          <div class="keyline-vertical"></div>
          <div
            class="text-xs text-[#007aff] w-1/2"
            v-html="t('GPS_ALLOWGPSPOPUP_PERMISSION_3')"
          ></div>
        </div>
      </div>
      <div class="flex flex-center gap-4 w-full flex-nowrap">
        <Button
          size="max-content"
          variant="purple"
          @click="
            emits('close');
            track('receiptverification_enablecamera', {
              action: 'receiptverification_enablecamera_later',
            });
          "
          :label="t('CAMERA_PERMISSION_BTN_1')"
        />
        <Button
          @click="
            emits('request');
            track('receiptverification_enablecamera', {
              action: 'receiptverification_enablecamera_allow',
            });
          "
          class="flex-1"
          :label="t('CAMERA_PERMISSION_BTN_2')"
        />
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.box {
  background: #f8f8f8d1;
  border-radius: 12px;
  width: 100%;
  height: auto;
  margin: 0 auto 20px;
  .url {
    max-width: 100%;
    word-wrap: break-word;
  }

  @media screen and (max-width: 360px) {
    width: calc(100% + 20px);
    margin-left: -10px;
  }
  .keyline-horizontal {
    background: linear-gradient(0deg, #3f3f3f, #3f3f3f), rgba(0, 0, 80, 0.05);
    background-blend-mode: color-burn, normal;
    transform: matrix(1, 0, 0, -1, 0, 0);
    width: 100%;
    height: 0.5px;
  }
  .keyline-vertical {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(0deg, #3f3f3f, #3f3f3f), rgba(0, 0, 80, 0.05);
    background-blend-mode: color-burn, normal;
    transform: matrix(1, 0, 0, -1, 0, 0);
    width: 0.5px;
    height: 100%;
  }
}
</style>
