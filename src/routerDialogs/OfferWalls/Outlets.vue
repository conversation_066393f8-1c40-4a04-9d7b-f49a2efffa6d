<script setup lang="ts">
import { useMapStore } from '@stores';

interface Props {
  header: string;
  brand_unique_id: string;
}

interface Emits {
  (event: 'close'): void;
}

const { mapIcons } = storeToRefs(useMapStore());
const props = defineProps<Props>();
const emits = defineEmits<Emits>();
const { t } = useI18n();

const data = computed(() =>
  mapIcons.value
    .filter((icon) => icon.brand_unique_id === props.brand_unique_id)
    .map((icon) => {
      return {
        name: icon.display.name,
        address: icon.display.address,
        opening_hours: Object.keys(icon.display.opening_hours).map((day) => {
          return {
            day,
            hours: icon.display.opening_hours[day],
          };
        }),
      };
    })
);
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header>{{ t(header) }}</template>

    <ul
      class="gap-2.5 column justify-start pl-5 pb-2.5"
      style="list-style-type: disc"
    >
      <li v-for="item in data" :key="item.name" class="mb-2">
        <span class="font-bold mb-1" v-html="t(item.name)"></span>
        <ul class="pl-4" style="list-style-type: disc">
          <li v-html="t(item.address)"></li>
          <li class="mt-2">
            {{ t('OUTLET_DETAILS_OPENING') }}
            <ul style="list-style-type: circle" class="pl-4 italic">
              <li
                v-for="hours in item.opening_hours"
                :key="item.name + hours.day + hours.hours"
              >
                {{ t(hours.day) }}&nbsp; : &nbsp; {{ hours.hours || '-' }}
              </li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
    <div class="flex flex-center">
      <Button @click="emits('close')" :label="t('BUTTON_GOT_IT')" />
    </div>
  </Dialog>
</template>
