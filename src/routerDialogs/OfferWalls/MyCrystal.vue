<script setup lang="ts">
import { useTrackData } from '@composables';
import { dateTimeFormat, numeralFormat } from '@helpers';
import { useUserStore } from '@stores';

interface Emits {
  (e: 'close'): void;
}
const emits = defineEmits<Emits>();
const { openDialog, push } = useMicroRoute();
const { crystals, listCrystalExpiring } = storeToRefs(useUserStore());
const { t } = useI18n();
const { track } = useTrackData();
</script>
<template>
  <Dialog
    @close="
      track('crystalhistory_balance', {
        action: 'crystalhistory_balance_close',
      });
      emits('close');
    "
  >
    <template #header><span v-html="t('MY_CRYSTAL_HEADER')"></span></template>
    <div class="full-width column items-center justify-start text-center">
      <div class="w-full px-2 text-center">
        <div class="mb-2">
          <p>{{ t('YOUHAVE') }}</p>
          <p class="flex flex-center">
            <span
              class="font-bold text-4xl text-[40px]"
              v-html="
                crystals > 99999
                  ? `99,999<sup>+</sup>`
                  : numeralFormat(crystals)
              "
            >
            </span>
            <Icon name="dbs_crystal" :size="36" class="ml-0.5" />
          </p>
        </div>
        <div
          class="flex items-center gap-2 bg-[#091A3B] rounded-[4px] py-2.5 px-4 w-full"
        >
          <p class="flex-1 text-left" v-if="!listCrystalExpiring.length">
            {{ t('CRYSTAL_HISTORY_NO_EXPRY', { MONTH: 3 }) }}
          </p>
          <p class="flex-1 flex flex-center gap-0.5" v-else>
            <b
              v-html="
                listCrystalExpiring[0].expiring_amount > 99999
                  ? `99,999<sup>+</sup>`
                  : numeralFormat(listCrystalExpiring[0].expiring_amount)
              "
            >
            </b>
            <Icon name="icons/crystal-s" :size="14" class="ml-0.5" />
            <span
              v-html="
                t('CRYSTAL_HISTORY_EXPRY', {
                  DATE: dateTimeFormat(
                    listCrystalExpiring[0].date,
                    'DD MMM YYYY'
                  ),
                })
              "
            ></span>
          </p>
          <Icon
            name="question-mark"
            :size="20"
            class="ml-0.5"
            @click="
              openDialog(
                !!listCrystalExpiring.length ? 'crystal_expry' : 'learn_more'
              );
              track('crystalexpiration_info');
              track('crystalhistory_balance', {
                action: 'crystalhistory_balance_info',
              });
            "
          />
        </div>

        <Button
          class="mx-auto my-5"
          @click="
            emits('close');
            push('offer_wall');
            track('crystalhistory_balance', {
              action: 'crystalhistory_balance_getcrystals',
            });
          "
          >{{ t('BUTTON_GETMORECRYSTALS') }}</Button
        >
        <p
          class="underline text-[#00E0FF]"
          @click="
            emits('close');
            push('crystal_history');
            track('crystalhistory_balance', {
              action: 'crystalhistory_balance_viewhistory',
            });
          "
        >
          {{ t('MY_CRYSTAL_LINK') }}
        </p>
      </div>
    </div>
  </Dialog>
</template>
