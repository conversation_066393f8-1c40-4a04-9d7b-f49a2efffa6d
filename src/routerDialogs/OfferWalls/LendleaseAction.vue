<script setup lang="ts">
import { useAsync } from '@composables';
import { formatMobileNumber, isMatchSeasonNumber } from '@helpers';
import { BRAND_ACTION } from '@repositories';
import { useBAStore } from '@stores';
import type { IBrandAction, ICountryRegion } from '@types';
import { useForm } from 'vee-validate';
import * as yup from 'yup';
import ConfirmSubmit from './ConfirmSubmit.vue';
import { CountryCode } from 'libphonenumber-js';

interface Props {
  data: IBrandAction;
}
const showConfirm = ref(false);
const props = defineProps<Props>();
const baStore = useBAStore();
const { push, closeAllDialog } = useMicroRoute();
const { t } = useI18n();
const country = ref<ICountryRegion>();
const error = ref(false);
const validationSchema = yup.object({
  mobile_number: yup
    .string()
    .required(t('MOBILE_NUMBER_REQUIRED'))
    .test('mobile-number', t('SIGNUP_FORM_INVALIDMOBILENUMBER'), (value) => {
      const number = country.value?.code + value;
      return isMatchSeasonNumber(number);
    }),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    mobile_number: '',
  },
  validationSchema,
});

const callback = handleSubmit(() => {
  showConfirm.value = true;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const mobile_number =
      `${country.value?.code}${values.mobile_number}`.replace(/\+/g, '');
    return await BRAND_ACTION.submitClientInfo({
      brand_action_id: props.data._id,
      mobile_number,
    });
  },
  onSuccess() {
    baStore.fetchBrandAction();
    closeAllDialog();
    push('submited', {
      header: t('LENDLEASE_SUBMITED_HEADER'),
    });
  },
});
</script>
<template>
  <Dialog>
    <template #header>{{ t('LENDLEASE_ACTION_HEADER') }}</template>
    <q-dialog maximized :model-value="showConfirm" persistent>
      <ConfirmSubmit
        :loading="loading"
        :header="t('SUBMIT_MOBILE_NUMBER_CONFIRM_HEADER')"
        @close="showConfirm = false"
        @submit="onSubmit"
        :another-content="
         formatMobileNumber(values.mobile_number,country?.iso as CountryCode)
        "
      ></ConfirmSubmit>
    </q-dialog>
    <div class="full-width column items-center justify-start text-center">
      <p class="px-2" v-html="t('LENDLEASE_ACTION_CONTENT')"></p>

      <q-form
        @submit="callback"
        class="flex flex-col flex-nowrap mt-5 w-full px-4"
      >
        <VeeInputCountry
          class="mb-5"
          :label="t('MOBILE_NUMBER')"
          name="mobile_number"
          @update:country="country = $event"
          :error="!!error"
          autofocus
        />

        <div class="text-center mt-[-8px]">
          <Button :label="t('BTN_SUBMIT')" type="submit" :loading="loading" />
        </div>
      </q-form>
    </div>
  </Dialog>
</template>
