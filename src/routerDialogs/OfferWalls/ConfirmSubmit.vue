<script setup lang="ts">
import { useUserStore } from '@stores';
import { useClick, useTrackData } from '@composables';
import type { IBrandAction } from '@types';

interface Props {
  header: string;
  btn_label?: string;
  anotherContent?: string;
  loading?: boolean;
  data: IBrandAction;
}

interface Emits {
  (e: 'close'): void;
  (e: 'submit', include_milo: boolean): void;
}

const props = defineProps<Props>();

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { user } = storeToRefs(storeUser);

const include_milo = ref(false);
const { track } = useTrackData();

const trackReceipt = (type: string, data?: Record<string, unknown>) => {
  if (props.data?.type !== 'receipt_verification') return;
  track(type, data);
};

useClick('SIGNUP', () => {
  emits('close');
  openDialog('signup');
  trackReceipt('receiptverification_confirmation', {
    action: 'receiptverification_confirmation_signup',
  });
});

useClick('LOGIN', () => {
  emits('close');
  openDialog('login');
  trackReceipt('receiptverification_confirmation', {
    action: 'receiptverification_confirmation_login',
  });
});
</script>

<template>
  <Dialog :hideClose="true">
    <template #header>
      <div class="text-base font-bold" v-html="header"></div>
    </template>
    <template #btnTopLeft>
      <Button
        shape="square"
        variant="secondary"
        @click="
          emits('close');
          trackReceipt('receiptverification_confirmation', {
            action: 'receiptverification_confirmation_back',
          });
        "
      >
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>
    <div class="items-center justify-start text-center full-width column">
      <p
        v-if="anotherContent"
        v-html="anotherContent"
        class="mb-4 text-lg font-bold mobile_box"
      />
      <p
        v-if="!user?.mobile_number"
        v-html="t('REDEEMPOPUP_CONFIRMATION_ACCOUNT_GUEST')"
      ></p>
      <p v-else v-html="t('REDEEMPOPUP_CONFIRMATION_ACCOUNT')"></p>
      <div class="mt-5 text-lg font-bold">
        {{
          user?.mobile_number
            ? t('SIGNUP_CREATED_HUNTER')
            : t('SIGNUP_CREATED_GUEST')
        }}
        {{ user?.hunter_id }}
      </div>
      <template
        v-if="
          data?.type === 'receipt_verification' && data?.unique_id !== 'np_1'
        "
      >
        <q-checkbox
          class="my-5 text-left !items-start"
          v-model="include_milo"
          :label="t('CONFIRM_MILO_CHECKBOX')"
        />
      </template>
      <Button
        class="mt-5"
        :loading="loading"
        @click="
          emits('submit', include_milo);
          trackReceipt('receiptverification_confirmation', {
            action: 'receiptverification_confirmation_confirm',
          });
        "
        >{{ btn_label || t('CONFIRM_DEFAULT_BTN') }}
      </Button>
      <div
        class="mt-5"
        v-if="!user?.mobile_number"
        v-html="t('REDEEMPOPUP_CONFIRMATION_SIGNUP_LOGIN')"
      ></div>
    </div>
  </Dialog>
</template>
<style scoped lang="scss">
.mobile_box {
  border-radius: 10px;
  border: 1px solid #b663e9;
  background: #091a3c;
  display: flex;
  width: 239px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
</style>
