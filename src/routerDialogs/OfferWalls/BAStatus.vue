<script setup lang="ts">
import { useTrackData } from '@composables';
import {
  FULL_DATE_TIME_24H_FORMAT,
  dateTimeFormat,
  numeralFormat,
} from '@helpers';
import type { IBrandAction } from '@types';

interface Props {
  brandAction: IBrandAction;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { track } = useTrackData();

const multiplierNumber = computed(() => {
  return {
    featured: props.brandAction.metadata?.featured_multiplier || 3,
    firstTime: props.brandAction.metadata?.first_time_multiplier || 2,
  };
});

const rewardBreakdownContext = computed(() => {
  if (props.brandAction.status !== 'claimed') return null;
  const isFeatured = props.brandAction.metadata?.is_featured;
  const isFirstTime = props.brandAction.metadata?.is_first_time;
  const bonus = props.brandAction.metadata?.bonus || 0;
  const originalReward =
    props.brandAction.metadata?.original_reward ||
    props.brandAction.reward?.crystal ||
    0;
  const baseReward =
    originalReward * (isFeatured ? multiplierNumber.value.featured : 1);
  const total = props.brandAction.metadata?.total || 0;
  const quantity = props.brandAction.metadata?.quantity || 1;
  const regularQuantity = quantity - (isFirstTime ? 1 : 0);

  return {
    isFeatured,
    isFirstTime,
    bonus,
    originalReward,
    baseReward,
    total,
    quantity,
    regularQuantity,
  };
});

const getNoteText = () => {
  switch (props.brandAction.type) {
    case 'promo_code':
    case 'enter_promo_code':
      return 'STATUS_PROMO';
    case 'enter_barcode':
      return 'STATUS_BARCODE';
    case 'receipt_verification':
      if (props.brandAction.status === 'rejected')
        return 'STATUS_RECEIPT_REJECT';
      else return 'STATUS_RECEIPT';
    default:
      if (props.brandAction.status === 'pending') return 'STATUS_PENDING';
      else return 'STATUS_COMPLETED';
  }
};
const getShowTime = computed<string>(() => {
  switch (props.brandAction.type) {
    case 'receipt_verification':
      return (
        props.brandAction.rejected_at || props.brandAction.created_at || ''
      );
    default:
      return props.brandAction.claimed_at || props.brandAction.created_at || '';
  }
});
</script>
<template>
  <Dialog
    @close="
      brandAction.type === 'receipt_verification' &&
        track('receiptverification_status', {
          action: 'receiptverification_status_close',
        });
      emits('close');
    "
  >
    <template #header><span v-html="t('OFFERWALL_STATUS')"></span></template>
    <div
      class="items-center justify-start text-center ba-status full-width column"
    >
      <p>
        <span class="text-base font-bold" v-html="t(brandAction.title)"></span>
        <br /><br />
        <span v-html="t(getNoteText())" class="mr-1"></span>
        <span>
          {{
            dateTimeFormat(+new Date(getShowTime), FULL_DATE_TIME_24H_FORMAT)
          }}</span
        ><br /><br />
        <span v-if="brandAction.status === 'rejected'"
          >{{ t('STATUS_RECEIPT_REASON') }}
          {{ t(brandAction.reject_reason || '') || 'empty' }}<br /><br
        /></span>
        <span
          >{{ t('OFFERWALL_STATUS') }}:
          <span class="italic">{{
            t(`${brandAction.status.toUpperCase()}`)
          }}</span></span
        >
      </p>
      <div
        class="mt-5"
        v-if="
          brandAction.type === 'etiqa_insurance' && brandAction.data?.policies
        "
      >
        <p>{{ t('BA_STATUS_POLICY_TITLE') }}</p>
        <p
          class="font-bold"
          v-for="item in brandAction.data.policies || []"
          :key="item.id"
        >
          {{ item.id }}
        </p>
      </div>
      <div
        class="text-left mt-[35px] full-width"
        v-if="brandAction.status === 'claimed' && !!rewardBreakdownContext"
      >
        <p class="text-base font-bold text-center">
          {{ t('BA_STATUS_BREAKDOWN') }}
        </p>
        <p
          class="flex items-center justify-between mt-3 full-width"
          v-if="rewardBreakdownContext.isFirstTime"
        >
          <i>{{ t('BA_STATUS_BONUS') }}</i>
          <span class="flex items-center gap-[5px]">
            1x <Icon name="crystal-s" :size="20" />
            <span style="text-decoration: line-through; opacity: 0.5">
              {{ numeralFormat(rewardBreakdownContext.baseReward) }}
            </span>
            <span>
              {{
                numeralFormat(
                  rewardBreakdownContext.baseReward * multiplierNumber.firstTime
                )
              }}
            </span>
          </span>
        </p>
        <!-- <p
          class="flex items-center justify-between mt-3 full-width"
          v-else-if="brandAction.metadata.is_featured"
        >
          <i>{{ t('BA_STATUS_FEATURED') }}</i>
          <span class="flex items-center gap-[5px]">
            1x <Icon name="crystal-s" :size="20" />
            <span style="text-decoration: line-through; opacity: 0.5">
              {{ rewardBreakdownContext.originalReward }}
            </span>
            <span>
              {{ rewardBreakdownContext.originalReward * 3 }}
            </span>
          </span>
        </p> -->

        <p
          class="flex items-center justify-between mt-3 full-width"
          v-if="rewardBreakdownContext.regularQuantity"
        >
          <i>{{
            rewardBreakdownContext.isFeatured
              ? t('BA_STATUS_FEATURED')
              : t('BA_STATUS_REGULAR')
          }}</i>

          <span class="flex items-center gap-[5px]">
            {{ rewardBreakdownContext.regularQuantity }}
            x <Icon name="crystal-s" :size="20" />
            <span
              v-if="rewardBreakdownContext.isFeatured"
              style="text-decoration: line-through; opacity: 0.5"
            >
              {{ numeralFormat(rewardBreakdownContext.originalReward) }}
            </span>
            {{ numeralFormat(rewardBreakdownContext.baseReward) }}
          </span>
        </p>

        <p
          class="flex items-center justify-between mt-3 full-width"
          v-if="rewardBreakdownContext.bonus"
        >
          <i>{{ t('BA_STATUS_LIMITED') }}</i>
          <span class="flex items-center gap-[5px]">
            {{ rewardBreakdownContext.quantity }}
            x
            <Icon name="crystal-s" :size="20" />
            {{ rewardBreakdownContext.bonus }}
          </span>
        </p>

        <div
          style="height: 1px; background: #ffffff; opacity: 0.5"
          class="my-3 full-width"
        ></div>
        <p class="flex items-center justify-between mt-5 full-width">
          <b>{{ t('TOTAL_REWARD') }}</b>
          <span class="flex items-center gap-[5px]">
            <Icon name="crystal-s" :size="20" />
            {{
              numeralFormat(
                rewardBreakdownContext.total ||
                  brandAction.metadata?.total ||
                  brandAction.user_reward?.crystal ||
                  brandAction.reward?.crystal
              )
            }}
          </span>
        </p>
      </div>
    </div>
  </Dialog>
</template>
<style>
.ba-status i {
  opacity: 0.5;
}
</style>
