<script lang="ts" setup>
interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div>{{ t('ba_survey_title') }}</div>
    </template>
    <div class="text-center">
      <div class="text-base mb-5">
        {{ t('ba_survey_content_1') }}
      </div>

      <div class="text-sm">
        {{ t('ba_survey_content_2') }}
      </div>
    </div>
  </Dialog>
</template>
