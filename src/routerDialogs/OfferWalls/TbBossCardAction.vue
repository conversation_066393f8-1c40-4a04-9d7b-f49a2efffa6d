<script setup lang="ts">
import { useAsync, useTick } from '@composables';
import {
  formatMobileNumber,
  isMatchSeasonNumber,
  timeCountDown,
} from '@helpers';
import { BRAND_ACTION } from '@repositories';
import { useBAStore } from '@stores';
import type { IAPIResponseError, IBrandAction, ICountryRegion } from '@types';
import { useForm } from 'vee-validate';
import * as yup from 'yup';
import ConfirmSubmit from './ConfirmSubmit.vue';
import { CountryCode } from 'libphonenumber-js';

interface Props {
  data: IBrandAction;
}
const showConfirm = ref(false);
const props = defineProps<Props>();
const baStore = useBAStore();
const { push, closeAllDialog, closeDialog } = useMicroRoute();
const { t } = useI18n();
const country = ref<ICountryRegion>();
const error = ref(false);
const { now } = useTick();
const errorInfo = ref<
  | {
      remaining_attempts: number;
      lock_until: string;
    }
  | undefined
>();
const validationSchema = yup.object({
  mobile_number: yup
    .string()
    .required(t('MOBILE_NUMBER_REQUIRED'))
    .test('mobile-number', t('SIGNUP_FORM_INVALIDMOBILENUMBER'), (value) => {
      const number = country.value?.code + value;
      return isMatchSeasonNumber(number);
    }),
  name_nric: yup.string().required(t('NRIC_REQUIRED')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    mobile_number: '',
    name_nric: '',
  },
  validationSchema,
});

const callback = handleSubmit(() => {
  showConfirm.value = true;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const mobile_number =
      `${country.value?.code}${values.mobile_number}`.replace(/\+/g, '');
    return await BRAND_ACTION.submitClientInfo({
      brand_action_id: props.data._id,
      mobile_number,
      name_nric: values.name_nric,
    });
  },
  onSuccess() {
    baStore.fetchBrandAction();
    closeAllDialog();
    push('submited', {
      header: t('TB_BOSS_SUBMITED_HEADER'),
    });
  },
  onError: (error: IAPIResponseError) => {
    if (error.error_message === 'temp_locked') {
      closeDialog('confirm_submit');
      errorInfo.value = error.data.info as any;
    }
  },
});
</script>
<template>
  <Dialog>
    <template #header>{{ t('TB_BOSS_ACTION_HEADER') }}</template>
    <q-dialog maximized :model-value="showConfirm" persistent>
      <ConfirmSubmit
        :loading="loading"
        :header="t('SUBMIT_MOBILE_NUMBER_CONFIRM_HEADER')"
        @close="showConfirm = false"
        @submit="onSubmit"
        :another-content="
         formatMobileNumber(values.mobile_number,country?.iso as CountryCode)+'</br>'+values.name_nric
        "
      ></ConfirmSubmit>
    </q-dialog>
    <div class="full-width column items-center justify-start text-center">
      <p class="px-2" v-html="t('TB_BOSS_ACTION_CONTENT')"></p>

      <q-form
        @submit="callback"
        class="flex flex-col flex-nowrap mt-5 w-full px-4"
      >
        <VeeInputCountry
          class="mb-5"
          :label="t('MOBILE_NUMBER')"
          name="mobile_number"
          @update:country="country = $event"
          :error="!!error"
          autofocus
        />
        <VeeInput class="mb-5 -mt-2" :label="t('NAME_NRIC')" name="name_nric" />
        <div
          class="bg-[#981515] rounded-[5px] w-full p-2.5 mb-5 text-center"
          v-if="!!errorInfo && +new Date(errorInfo.lock_until) > now"
          v-html="
            !errorInfo.remaining_attempts
              ? t('POLICY_LOCKED')
              : t('POLICY_LOCK_ATTEMPTS', {
                  ATTEMPT: errorInfo.remaining_attempts,
                })
          "
        ></div>
        <div class="text-center mt-[-8px]">
          <Button
            v-if="
              !!errorInfo &&
              !errorInfo.remaining_attempts &&
              +new Date(errorInfo.lock_until) > now
            "
            type="submit"
            :disable="true"
            >{{
              t('SERIALNUMBER_BUTTON_SUBMIT_LOCKED', {
                TIME: timeCountDown(+new Date(errorInfo.lock_until) - now),
              })
            }}</Button
          >
          <Button v-else type="submit">{{ t('BTN_SUBMIT') }}</Button>
        </div>
      </q-form>
    </div>
  </Dialog>
</template>
