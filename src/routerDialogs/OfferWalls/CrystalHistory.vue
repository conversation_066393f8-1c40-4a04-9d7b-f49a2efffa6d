<script setup lang="ts">
import { useTrackData } from '@composables';
import { BRAND_SOV } from '@constants';
import { dateTimeFormat, numeralFormat } from '@helpers';
import { useBAStore, useMapStore, useUserStore } from '@stores';
import { ITransaction } from '@types';

interface ITransactionMapped extends ITransaction {
  name: string;
}
const { openDialog, push, closeDialog } = useMicroRoute();
const userStore = useUserStore();
const baStore = useBAStore();
const mapStore = useMapStore();
const { brand_actions_byUniqueId } = storeToRefs(baStore);
const { user, crystals, baseCrystals, transactions, listCrystalExpiring } =
  storeToRefs(userStore);
const { silverCoinById } = storeToRefs(mapStore);
const { t } = useI18n();
const { track } = useTrackData();

const RESOURCE_TYPE = ['dbs_crystal', 'crystal'];

const filter = ref(t('ALL_TRANSACTIONS'));

const containsUndefined = (obj: Record<string, unknown>) => {
  const inValid = Object.values(obj).some((v) => v === undefined);
  if (inValid) throw new Error('invalid');
  return obj;
};

const TRANSACTION_MAPPING = (item: ITransaction) => {
  let name = '';

  try {
    switch (item.reason) {
      case 'brand_action':
        containsUndefined({ brand_action_id: item.metadata?.brand_action_id });
        name = t(
          brand_actions_byUniqueId.value[
            item.metadata.brand_action_id as string
          ].title
        );
        break;
      case 'use_shrink_power_up':
        name = t(
          'HISTORY_USE_SHRINK_POWERUP',
          containsUndefined({
            COIN_NAME:
              silverCoinById.value[item.metadata.coin_id as string]?.coin_id,
          })
        );

        break;
      case 'admin':
        name = t('ADMIN');
        break;
      case 'eliminate_pu':
        name = t(
          'HISTORY_ELIMINATION',
          containsUndefined({
            COUNT: Object.keys(item.metadata).length || 0,
          })
        );
        break;
      case 'buy_text_hint':
        name = t(
          'HISTORY_BUY_HINT',
          containsUndefined({ COUNT: item.metadata?.quantity })
        );
        break;
      case 'referral':
        name = t(
          'HISTORY_REFERRAL',
          containsUndefined({
            REFEREE_HUNTER_ID: item.metadata?.referee_hunter_id,
          })
        );
        break;
      case 'change_hunter_id':
        name = t('HISTORY_CHANGE_HUNTER_ID');
        break;
      case 'survey':
        name = t('HISTORY_SURVEY');
        break;
      case 'welcome_reward':
        name = t('HISTORY_WELCOME_REWARD');
        break;
      case 'online_time_bonus':
        name = t('HISTORY_ONLINE_TIME_BONUS');
        break;
      case 'daily_reward':
        name = t('HISTORY_DAILY_REWARD');
        break;
      case 'daily_mission':
        name = t('HISTORY_DAILY_MISSION');
        break;
      case 'untame_spent':
        name = t('HISTORY_UNTAME_SPENT');
        break;
      case 'brand_action_milestone':
        name = t('HISTORY_BA_MILESTONE');
        break;
      case 'vote_contest':
        name = t('HISTORY_VOTE_CONTEST');
        break;
      case 'redeem_code':
        name = t('HISTORY_REDEEM_CODE');
        break;
      case 'use_metal_detector':
        name = t('HISTORY_USE_METAL_DETECTOR');
        break;
      case 'use_metal_sonar':
        name = t(
          'HISTORY_USE_METAL_SONAR',
          containsUndefined({
            RANGE: item.metadata?.radius,
          })
        );
        break;
      case 'sqkii_voucher':
        name = t(
          item.metadata.type === 'top_up'
            ? 'HISTORY_SQKII_VOUCHER_TOP_UP'
            : 'HISTORY_SQKII_VOUCHER_SPENDING'
        );
        break;
      case 'skip_mission':
        name = t('HISTORY_SKIP_MISSION');
        break;
      case 'timed_mission':
        name = t('HISTORY_TIMED_MISSION');
        break;
      case 'sentosa_daily_reward':
        name = t('HISTORY_SENTOSA_DAILY_REWARD');
        break;
      case 'sentosa_island_bounty':
        name = t('HISTORY_SENTOSA_ISLAND_BOUNTY');
        break;
      case 'capitaland_amenities_check_in':
        name = t('HISTORY_CAPITALAND_AMENITIES_CHECK_IN');
        break;
      case 'capitaland_daily_reward':
        name = t('HISTORY_CAPITALAND_DAILY_REWARD');
        break;
      default:
        break;
    }
    return {
      ...item,

      name,
    };
  } catch (error) {
    return {
      ...item,
      name: t(`FALLBACK_${item.reason.toUpperCase()}`),
    };
  }
};

const history = computed<ITransactionMapped[]>(() => {
  return transactions.value
    .filter((item) => {
      switch (filter.value) {
        case t('CRYSTALS_SPENT'):
          return RESOURCE_TYPE.includes(item.resource_type) && item.amount <= 0;
        case t('CRYSTALS_RECEIVED'):
          return RESOURCE_TYPE.includes(item.resource_type) && item.amount > 0;
        default:
          return RESOURCE_TYPE.includes(item.resource_type);
      }
    })
    .map((item) => TRANSACTION_MAPPING(item));
});

onMounted(() => {
  userStore.fetchTranSaction();
  if (!user.value?.onboarding.crystal_expiring) {
    userStore.updateOnboarding('crystal_expiring');
  }
});
</script>
<template>
  <div class="fullscreen column items-center flex-nowrap bg-[#090422]">
    <div class="min-h-[64px] w-full flex flex-center text-center relative">
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="
          track(
            !transactions.some((v) => RESOURCE_TYPE.includes(v.resource_type))
              ? 'crystalhistory_nohistory'
              : 'crystalhistory',
            {
              action: !transactions.some((v) =>
                RESOURCE_TYPE.includes(v.resource_type)
              )
                ? 'crystalhistory_nohistory_back'
                : 'crystalhistory_back',
            }
          );
          push(-1);
        "
      >
        <Icon name="arrow-left"></Icon>
      </Button>
      <p class="text-lg font-extrabold text-center w-full px-[90px]">
        {{ t('CRYSTAL_HISTORY_HEADER') }}
      </p>
      <HeaderCrystal class="absolute right-4" />
    </div>
    <div
      v-if="!transactions.some((v) => RESOURCE_TYPE.includes(v.resource_type))"
      class="items-center justify-start flex-1 px-5 py-20 overflow-auto text-center flex-nowrap full-width column"
    >
      <Icon :name="`sov/crystal_history/${BRAND_SOV.DBS}`" :size="100" />
      <p class="mt-5 text-lg font-bold">
        {{ t('CRYSTAL_HISTORY_NO_TRANSACTION_TITLE') }}
      </p>
      <p class="mt-2.5">{{ t('CRYSTAL_HISTORY_NO_TRANSACTION_CONTENT') }}</p>
      <Button
        class="my-5"
        @click="
          track('crystalhistory_nohistory', {
            action: 'crystalhistory_nohistory_getmorecrystals',
          });
          push('offer_wall');
        "
        >{{ t('BUTTON_GETMORECRYSTALS') }}</Button
      >
      <p
        class="underline text-[#00E0FF]"
        @click="
          track('crystalhistory_nohistory', {
            action: 'crystalhistory_nohistory_returntomap',
          });
          push('home');
        "
      >
        {{ t('RETURN_TO_MAP') }}
      </p>
    </div>
    <div
      v-else
      class="items-center justify-start flex-1 px-5 py-5 overflow-auto text-center flex-nowrap full-width column"
    >
      <div class="w-full px-2 text-center">
        <div class="flex flex-wrap items-center mb-2 flex-center gap-2">
          <div>{{ t('YOUHAVE') }}</div>
          <div class="flex items-center" v-if="baseCrystals > 0">
            <span
              class="font-bold text-2xl text-[28px]"
              v-html="
                baseCrystals > 99999
                  ? `99,999<sup>+</sup>`
                  : numeralFormat(baseCrystals)
              "
            >
            </span>
            <Icon name="crystal" :size="22" class="ml-0.5" />
            <span class="ml-2">and</span>
          </div>
          <div class="flex items-center">
            <div
              class="font-bold text-2xl text-[28px]"
              v-html="
                crystals > 99999
                  ? `99,999<sup>+</sup>`
                  : numeralFormat(crystals)
              "
            ></div>
            <Icon name="dbs_crystal" :size="22" class="ml-0.5" />
          </div>
        </div>
        <div
          class="flex items-center gap-2 bg-[#091A3B] rounded-[4px] py-2.5 px-4 w-full"
        >
          <p class="flex-1 text-left" v-if="!listCrystalExpiring.length">
            {{ t('CRYSTAL_HISTORY_NO_EXPRY', { MONTH: 3 }) }}
          </p>
          <p class="flex-1 flex flex-center gap-0.5" v-else>
            <b
              v-html="
                listCrystalExpiring[0].expiring_amount > 99999
                  ? `99,999<sup>+</sup>`
                  : numeralFormat(listCrystalExpiring[0].expiring_amount)
              "
            ></b>
            <Icon name="dbs_crystal" :size="14" class="ml-0.5" />
            <span
              v-html="
                t('CRYSTAL_HISTORY_EXPRY', {
                  DATE: dateTimeFormat(
                    listCrystalExpiring[0].date,
                    'DD MMM YYYY'
                  ),
                })
              "
            ></span>
          </p>
          <Icon
            name="question-mark"
            :size="20"
            class="ml-0.5"
            @click="
              openDialog(
                !!listCrystalExpiring.length ? 'crystal_expry' : 'learn_more'
              );
              track('crystalexpiration_info');
              track('crystalhistory', {
                action: 'crystalhistory_info',
              });
            "
          />
        </div>
        <hr class="w-full bg-white opacity-50 my-[15px]" />
        <p>{{ t('CRYSTAL_HISTORY_CONTENT') }}</p>
        <Select
          class="w-full my-[15px]"
          v-model="filter"
          :label="t('FILTER')"
          :options="[
            t('ALL_TRANSACTIONS'),
            t('CRYSTALS_SPENT'),
            t('CRYSTALS_RECEIVED'),
          ]"
          :clearable="false"
          type="neon-outline"
          @update:model-value="
            track('crystalhistory', {
              action: 'crystalhistory_filter',
              filter: $event,
            })
          "
        />
      </div>

      <div class="w-full">
        <div
          class="bg-[#091A3C] mb-[15px] rounded-[4px] px-2.5 py-[15px] w-full text-left flex items-center"
          :class="
            item.reason === 'brand_action' &&
            t(item.name) !== t('FALLBACK_BRAND_ACTION')
              ? 'pointer-events-auto'
              : 'pointer-events-none'
          "
          v-for="(item, index) in history"
          :key="`history-item-${index}`"
          @click="
            openDialog('ba_status', {
              brandAction: {
                title: item.name,
                type: brand_actions_byUniqueId[
                  (item.metadata?.brand_action_id || '') as string
                ]?.type,
                status: 'claimed',
                claimed_at: item.created_at,
                created_at: item.created_at,
                reward: {
                  crystal: item.amount,
                },
                metadata: item.metadata,
              },
              onClose: () => {
                closeDialog('ba_status');
                track('crystalhistory_transaction', {
                  action: 'crystalhistory_transaction_close',
                });
              },
            });
            track('crystalhistory', {
              action: 'crystalhistory_transactiondetails',
            });
          "
        >
          <div class="flex-1">
            <p class="opacity-70">
              {{ dateTimeFormat(item.created_at, 'DD MMM YYYY, hh:mmA') }}
            </p>
            <p v-html="t(item.name)"></p>
            <p
              class="flex items-center mt-1 text-base font-bold"
              :class="item.amount > 0 && 'text-[#00E0FF]'"
            >
              {{ item.amount > 0 ? '+' : '-' }}
              <Icon name="dbs_crystal" :size="20" />
              {{ numeralFormat(Math.abs(item.amount)) }}
            </p>
          </div>

          <Icon
            v-if="
              item.reason === 'brand_action' &&
              t(item.name) !== t('FALLBACK_BRAND_ACTION')
            "
            name="arrow-left"
            class="rotate-180"
            :size="14"
          ></Icon>
        </div>
      </div>
    </div>
  </div>
</template>
