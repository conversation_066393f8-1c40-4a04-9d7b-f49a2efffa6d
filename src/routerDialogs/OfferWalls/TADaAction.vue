<script setup lang="ts">
import { useAsync, useTick } from '@composables';
import { errorNotify, formatMobileNumber, timeCountDown } from '@helpers';
import { BRAND_ACTION } from '@repositories';
import { useBAStore, useUserStore } from '@stores';
import type { IAPIResponseError, IBrandAction, ICountryRegion } from '@types';
import { useForm } from 'vee-validate';
import * as yup from 'yup';
import ConfirmSubmit from './ConfirmSubmit.vue';

interface Props {
  data: IBrandAction;
}
const showConfirm = ref(false);
const props = defineProps<Props>();
const baStore = useBAStore();
const userStore = useUserStore();
const { push, closeAllDialog, closeDialog } = useMicroRoute();
const { t } = useI18n();

const { user } = storeToRefs(userStore);
const country = ref<ICountryRegion>();
const error = ref(false);
const { now } = useTick();
const errorInfo = ref<
  | {
      remaining_attempts: number;
      lock_until: string;
    }
  | undefined
>();
const validationSchema = yup.object({
  mobile_number: yup.string().required(t('MOBILE_NUMBER_REQUIRED')),
  // .test('mobile-number', t('SIGNUP_FORM_INVALIDMOBILENUMBER'), (value) => {
  //   const number = country.value?.code + value;
  //   return isMatchSeasonNumber(number);
  // }),
});
const formattedMobileNumber = computed(() =>
  formatMobileNumber(user.value?.mobile_number || '')
);
const { handleSubmit, values } = useForm({
  initialValues: {
    mobile_number: formatMobileNumber(user.value?.mobile_number || ''),
  },
  validationSchema,
});

const callback = handleSubmit(() => {
  showConfirm.value = true;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const res = await BRAND_ACTION.submitClientInfo({
      brand_action_id: props.data._id,
      mobile_number: user.value?.mobile_number,
    });
    return res;
  },
  onSuccess() {
    baStore.fetchBrandAction();
    closeAllDialog();
    push('submited', {
      header: t('TADA_SUBMITED_HEADER'),
    });
  },
  onError: (error: IAPIResponseError) => {
    switch (error.error_message) {
      case 'temp_locked':
        errorInfo.value = error.data.info as any;
        closeDialog('confirm_submit');
        break;
      case 'tada_already_used_by_other':
        errorNotify({
          message: t('tada_already_used_by_other'),
        });
        break;
    }
  },
});
</script>
<template>
  <Dialog>
    <template #header>{{ t('TADA_ACTION_HEADER') }}</template>
    <q-dialog maximized :model-value="showConfirm" persistent>
      <ConfirmSubmit
        :loading="loading"
        :header="t('SUBMIT_MOBILE_NUMBER_CONFIRM_HEADER')"
        :data="data"
        @close="showConfirm = false"
        @submit="onSubmit"
        :another-content="formattedMobileNumber"
      ></ConfirmSubmit>
    </q-dialog>
    <div class="items-center justify-start text-center full-width column">
      <p class="px-2" v-html="t('TADA_ACTION_CONTENT')"></p>

      <q-form
        @submit="callback"
        class="flex flex-col w-full px-4 mt-5 flex-nowrap"
      >
        <VeeInput
          class="mb-5"
          :label="t('MOBILE_NUMBER')"
          name="mobile_number"
          @update:country="country = $event"
          :error="!!error"
          autofocus
          readonly
        />
        <div
          class="bg-[#981515] rounded-[5px] w-full p-2.5 mb-5 text-center"
          v-if="!!errorInfo && +new Date(errorInfo.lock_until) > now"
          v-html="
            !errorInfo.remaining_attempts
              ? t('POLICY_LOCKED')
              : t('POLICY_LOCK_ATTEMPTS', {
                  ATTEMPT: errorInfo.remaining_attempts,
                })
          "
        ></div>
        <div class="text-center mt-[-8px]">
          <Button
            v-if="
              !!errorInfo &&
              !errorInfo.remaining_attempts &&
              +new Date(errorInfo.lock_until) > now
            "
            type="submit"
            :disable="true"
            >{{
              t('SERIALNUMBER_BUTTON_SUBMIT_LOCKED', {
                TIME: timeCountDown(+new Date(errorInfo.lock_until) - now),
              })
            }}</Button
          >
          <Button v-else type="submit">{{ t('BTN_SUBMIT') }}</Button>
        </div>
      </q-form>
    </div>
  </Dialog>
</template>
