<script setup lang="ts">
import { dateTimeFormat, numeralFormat } from '@helpers';
import { useUserStore } from '@stores';

interface Emits {
  (e: 'close'): void;
}
const emits = defineEmits<Emits>();
const { openDialog } = useMicroRoute();
const {  listCrystalExpiring } = storeToRefs(useUserStore());

const { t } = useI18n();
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header
      ><span v-html="t('CRYSTAL_EXPRY_HEADER')"></span
    ></template>
    <div class="full-width column items-center justify-start text-center">
      <p>{{ t('CRYSTAL_EXPRY_CONTENT') }}</p>
      <div class="bg-[#091A3C] mt-[15px] rounded-[4px] px-6 py-5 w-full">
        <div class="flex items-center justify-between font-bold">
          <p>{{ t('EXPIRATION_DATE') }}</p>
          <p>{{ t('CRYSTALS') }}</p>
        </div>
        <hr class="w-full bg-white opacity-50 mt-3 mb-3.5" />
        <div class="w-full px-2 column items-center gap-[18px]">
          <div
            class="w-full flex items-center justify-between"
            v-for="(item, index) in listCrystalExpiring"
            :key="`expring_${index}`"
          >
            <p>{{ dateTimeFormat(item.date, 'DD MMM YYYY') }}</p>
            <p>{{ numeralFormat(item.expiring_amount) }}</p>
          </div>
        </div>
        <hr class="w-full bg-white opacity-50 mt-3 mb-3.5" />
        <div class="flex items-center justify-between font-bold">
          <p>{{ t('TOTAL') }}</p>
          <p class="flex items-center">
            {{
              numeralFormat(listCrystalExpiring.reduce<number>((r, a) => {
                return r+(a.expiring_amount || 0)
              }, 0))
            }}
            <Icon name="crystal" :size="20" />
          </p>
        </div>
      </div>
      <Button class="mx-auto mt-5" @click="openDialog('learn_more')">{{
        t('CRYSTAL_EXPRY_BTN')
      }}</Button>
    </div>
  </Dialog>
</template>
