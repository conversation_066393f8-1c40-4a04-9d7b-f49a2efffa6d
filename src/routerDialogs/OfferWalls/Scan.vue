<script setup lang="ts">
import type { IBrandAction } from '@types';

interface Props {
  data?: IBrandAction;
  streamVideo: any;
  type: string;
}
defineProps<Props>();
const { t } = useI18n();

const emits = defineEmits(['scan', 'close']);

const scan = (code: string) => {
  emits('scan', code?.split('code=').pop());
};
</script>
<template>
  <div class="fullscreen">
    <div
      class="scan_promo text-center px-[22px] py-5 fit overflow-auto relative"
    >
      <div
        class="flex flex-center full-width"
        style="position: fixed; top: 0; left: 0"
      >
        <p class="text-lg font-extrabold mt-[22px]">
          {{ t(`SCAN_${type.toUpperCase()}_TITLE`) }}
        </p>
        <Button
          class="btn-back-left fixed"
          shape="square"
          variant="secondary"
          @click="emits('close')"
        >
          <Icon name="arrow-left" :size="14" />
        </Button>
      </div>
      <div class="fullscreen px-5 pb-5 overflow-auto" style="top: 60px">
        <StreamBarcodeReader
          :stream="streamVideo"
          class="mt-10"
          style="border-radius: 10px !important"
          @decode="scan"
        />
        <p
          class="my-[30px] px-[30px]"
          v-html="t(`SCAN_${type.toUpperCase()}_CONTENT`)"
        ></p>
        <div class="full-width flex flex-center">
          <Button variant="purple" @click="emits('close')">{{
            t(`SCAN_${type.toUpperCase()}_BTN`)
          }}</Button>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.scan_promo {
  padding-bottom: 100px;
  background-image: url(/imgs/gradient_color.png),
    linear-gradient(#090422, #090422);
  background-size: 100% 87vw, 100% 100%;
  background-position: center top, center center;
  background-repeat: no-repeat;

  .guild {
    background: #d9d9d9;
    border-radius: 10px;
    width: 100%;
    height: 63vw;
  }
}
</style>
