<script lang="ts" setup>
import { useAsync, useTick } from '@composables';
import { errorNotify, timeCountDown } from '@helpers';
import { BRAND_ACTION } from '@repositories';
import { useBAStore } from '@stores';
import { IAPIResponseError } from '@types';
import { useFieldArray, useForm } from 'vee-validate';

import * as yup from 'yup';

interface Emits {
  (event: 'close'): void;
}
const MAX_POLICY_PER_SUBMISSION = 5;
const emits = defineEmits<Emits>();
const storeBA = useBAStore();
const { t } = useI18n();
const {
  openDialog,
  closeAllDialog,
  push: pushRouter,
  closeDialog,
} = useMicroRoute();
const initialValues = {
  policyIds: [''],
};
const errorInfo = ref<
  | {
      remaining_attempts: number;
      lock_until: string;
    }
  | undefined
>();
const { now } = useTick();

const { handleSubmit, values } = useForm({
  initialValues,
  validationSchema: {
    policyIds: yup
      .array()
      .of(yup.string().trim().required(t('ENTER_POLICY_EMPTY_ERROR')))
      .test('unique', t('ENTER_POLICY_DUPLICATE_ERROR'), (value) => {
        if (!Array.isArray(value)) return false;
        const uniqueValues = new Set(value);
        return uniqueValues.size === value.length;
      }),
  },
});

const { fields, push, remove } = useFieldArray('policyIds');
let loading = false;
const { execute: onSubmit } = useAsync({
  async fn() {
    if (loading) return;
    loading = true;
    return await BRAND_ACTION.submitEtiqaPolicies(values.policyIds);
  },
  async onSuccess() {
    await storeBA.fetchBrandAction();
    closeAllDialog();
    pushRouter('submited', {
      header: t('POLICY_SUBMITED_HEADER'),
    });
  },
  onError: (error: IAPIResponseError) => {
    if (error.error_message === 'temp_locked') {
      closeDialog('confirm_submit');
      errorInfo.value = error.data.info as any;
    }
    loading = false;
  },
});

const onConfirm = handleSubmit(
  async () => {
    openDialog('confirm_submit', {
      header: t('SUBMIT_CONFIRM_POLICY_ID_HEADER'),
      onSubmit: onSubmit,
      anotherContent: `<span class="text-lg font-bold">${values.policyIds.reduce(
        (r, a) => {
          return r + a + '</br>';
        },
        ''
      )}</span>`,
    });
  },
  ({ errors }) => {
    errorNotify({ message: errors.policyIds });
  }
);
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('ENTER_POLICY_HEADER')"></div>
    </template>

    <q-form @submit="onConfirm" class="flex flex-col flex-nowrap">
      <p class="mb-5 text-base text-center">{{ t('ENTER_POLICY_CONTENT') }}</p>

      <div
        class="flex items-center gap-2 mb-5"
        v-for="(field, idx) in fields"
        :key="field.key"
      >
        <VeeInput
          class="flex-1"
          :name="`policyIds[${idx}]`"
          :label="t('ENTER_POLICY_FIELD_LABEL')"
        />
        <div v-if="fields.length > 1" class="p-3 pr-0" @click="remove(idx)">
          <Icon name="icons/delete" />
        </div>
      </div>
      <div
        class="bg-[#981515] rounded-[5px] w-full p-2.5 mb-5 text-center"
        v-if="!!errorInfo && +new Date(errorInfo.lock_until) > now"
        v-html="
          !errorInfo.remaining_attempts
            ? t('POLICY_LOCKED')
            : t('POLICY_LOCK_ATTEMPTS', {
                ATTEMPT: errorInfo.remaining_attempts,
              })
        "
      ></div>
      <div
        class="flex items-center w-full gap-2 mb-5 flex-center"
        @click="
          fields.length === MAX_POLICY_PER_SUBMISSION
            ? errorNotify({
                message: t('REACHED_MAX_POLICIES_PER_SUBMISSION', {
                  MAX: MAX_POLICY_PER_SUBMISSION,
                }),
              })
            : push('')
        "
      >
        <p>{{ t('ENTER_POLICY_ADD_CONTENT') }}</p>
        <div
          class="w-[30px] h-[30px] flex flex-center"
          style="
            border-radius: 100px;
            border-radius: 50%;
            background: linear-gradient(180deg, #99e3ed 0%, #6bcfdd 100%);
          "
        >
          <Icon name="plus" />
        </div>
      </div>
      <div class="mb-5 text-center">
        <Button
          v-if="
            !!errorInfo &&
            !errorInfo.remaining_attempts &&
            +new Date(errorInfo.lock_until) > now
          "
          type="submit"
          :disable="true"
          >{{
            t('SERIALNUMBER_BUTTON_SUBMIT_LOCKED', {
              TIME: timeCountDown(+new Date(errorInfo.lock_until) - now),
            })
          }}</Button
        >
        <Button v-else type="submit">{{ t('BTN_SUBMIT') }}</Button>
      </div>
    </q-form>
  </Dialog>
</template>
