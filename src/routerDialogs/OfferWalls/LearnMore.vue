<script setup lang="ts">
import { useTrackData } from '@composables';

const { t } = useI18n();
const emits = defineEmits<{
  (e: 'close'): void;
}>();

const { track } = useTrackData();
</script>
<template>
  <div class="full-screen bg-[#090B20]">
    <div class="h-[64px] w-full flex flex-center text-center relative">
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="
          track('crystalhistory_moreinfo', {
            action: 'crystalhistory_moreinfo_back',
          });
          emits('close');
        "
      >
        <Icon name="arrow-left"></Icon>
      </Button>
      <p class="text-lg font-extrabold text-center w-full px-[60px]">
        {{ t('CRYSTAL_LEARNMORE_HEADER') }}
      </p>
    </div>
    <div class="w-full px-7 pt-4">
      <p class="font-medium text-base mb-3">What are #HuntTheMouse Crystals?</p>
      <p class="mb-6">
        #HuntTheMouse Crystals are used to exchange for power ups and hints in
        #HuntTheMouse.<br /><br />
        Crystals have an expiry date of x months after they are earned, on the
        last day of the month.
      </p>
      <img src="/imgs/learn_more.png" class="w-full" />
    </div>
  </div>
</template>
