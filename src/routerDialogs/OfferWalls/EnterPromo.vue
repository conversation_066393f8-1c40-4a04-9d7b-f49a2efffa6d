<script setup lang="ts">
import {
  playSFX,
  useGlobal,
  useMediaDevice,
  useTick,
  useTrackData,
} from '@composables';
import { metaPixelTacking, showNotify, timeCountDown } from '@helpers';
import { last } from 'lodash';
import { useUserStore, useBAStore } from '@stores';
import { BRAND_ACTION } from '@repositories';
import type { IBrandAction } from '@types';

interface Props {
  type?: 'promocode' | 'barcode';
  data?: IBrandAction;
  code_query?: string;
}

interface Emits {
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'promocode',
  code_query: '',
});

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeBA = useBAStore();

const { t } = useI18n();
const { now } = useTick();
const { openDialog, push, closeDialog } = useMicroRoute();
const { trackTime, track } = useTrackData();
const { trackUserLocation } = useGlobal();

let streamVideo: null | MediaStream = null;
const qr_code = ref(props.code_query);
const result = ref<string>('');
const last_error = ref();
const status = ref('start');
const valid_until = ref<null | number>(null);
const loading = ref(false);

//----------------------Functions--------------------------

const requestCameraSuccess = (stream: any) => {
  streamVideo = stream;
  closeDialog('camera_permission');
  openDialog('scan', {
    onScan: scan,
    streamVideo,
    type: props.type,
  });
  track('camera_pop_up', {
    result: true,
  });
};

const requestCameraFailed = () => {
  showNotify({
    message: t('CAMERA_ERROR_NOT_DETECTED'),
    classes: 'errors-notify',
    position: 'top',
    timeout: 8000,
  });
  closeDialog('camera_permission');
  track('camera_pop_up', {
    result: false,
  });
};

const { request } = useMediaDevice(
  {
    video: {
      facingMode: 'environment',
    },
    audio: false,
  },
  requestCameraSuccess,
  requestCameraFailed
);

const goScan = () => {
  if (!!LocalStorage.getItem('camera_permission')) request();
  else {
    openDialog('camera_permission', {
      onRequest: request,
    });
  }
};

const scan = (code: string) => {
  qr_code.value = last(code.split('?=code')) || '';
  qr_code.value = last(qr_code.value.split('?=c')) || '';
  closeDialog('scan');
};

const submit = async () => {
  if (loading.value) return;
  loading.value = true;
  result.value = '';
  valid_until.value = null;
  last_error.value = undefined;

  try {
    const res = await BRAND_ACTION.scanQrCode({
      code: qr_code.value.trim(),
    });
    playSFX('approve');
    await storeUser.fetchUser();
    storeBA.fetchBrandAction();
    trackUserLocation('brand_action', {
      type: props.type,
      brand_action_id: props.data?._id,
    });
    trackTime('promo');
    openDialog('promo_success', {
      crystal: res.data.crystal,
      onClose: () => {
        emits('close');
        closeDialog('promo_success');
        if (['jp_1', 'jp_2', 'jp_3'].includes(res.data.brand_action.unique_id))
          metaPixelTacking('PaidBAJulieShop');
        openDialog('status', {
          data: {
            ...res.data.brand_action,
            created_at: new Date().toISOString(),
          },
        });
        push('offer_wall');
      },
    });
    loading.value = false;
  } catch (error) {
    const _err = error as any;
    if (_err?.data?.lock_until) last_error.value = _err.data;
    else last_error.value = _err;
    playSFX('reject');
  } finally {
    loading.value = false;
    closeDialog('confirm_submit');
  }
};

//-----------------Hooks----------------------
watch(now, (val) => {
  if (val > Date.parse(last_error.value?.lock_until)) last_error.value = null;
});
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div
        class="text-base font-bold"
        v-html="
          t(data?.metadata?.popup_title || `${type.toUpperCase()}_POPUP_HEADER`)
        "
      ></div>
    </template>
    <div
      class="full-width enter-code text-center column items-center justify-start px-2.5"
    >
      <p
        v-html="
          t(
            data?.metadata?.popup_description ||
              `${type.toUpperCase()}_POPUP_INSTRUCTIONS`
          )
        "
      ></p>

      <div class="mt-4 gap-2.5 column justify-start items-center full-width">
        <div
          class="full-width flex flex-center gap-2.5"
          style="flex-wrap: nowrap"
        >
          <div class="relative" style="flex: 1">
            <Input
              v-model="qr_code"
              class="full-width"
              :label="t(`${type.toUpperCase()}_INPUT_LABEL`)"
              :readonly="status !== 'start'"
              :error="result === 'error'"
              :custom_prefix="40"
            />
            <div
              class="prefix flex flex-center"
              @click="goScan"
              v-if="qr_code.length === 0"
            >
              <Icon name="icons/ic_scan" :size="15" style="opacity: 0.7" />
            </div>
          </div>
        </div>
      </div>
      <div v-if="last_error" class="full-width">
        <div
          class="error-link mt-2.5 full-width"
          v-if="last_error?.lock_until"
          v-html="t('PROMOCODE_POPUP_5INVALIDATTEMPTS')"
        ></div>
        <div
          class="error-link mt-2.5 full-width"
          v-else
          v-html="t(`${last_error.error_message || last_error}`)"
        ></div>
      </div>

      <Button
        v-if="
          last_error?.lock_until && Date.parse(last_error.lock_until) >= now
        "
        class="mt-5"
        :disable="true"
        ><span v-html="t('SUBMIT')"></span> ({{
          timeCountDown(Date.parse(last_error.lock_until) - now)
        }})</Button
      >
      <Button
        v-else-if="status === 'start'"
        class="mt-5"
        @click="
          openDialog('confirm_submit', {
            header: t(`${type.toUpperCase()}_CONFIRM_HEADER`),
            btn_label: type === 'promocode' && t('REDEEM'),
            onSubmit: submit,
          })
        "
        :disable="!qr_code.length"
        ><span v-html="t('SUBMIT')"></span
      ></Button></div
  ></Dialog>
</template>
<style scoped lang="scss">
.enter-code {
  .prefix {
    position: absolute;
    left: 0;
    top: 9px;
    width: 50px;
    border-right: 1px solid rgba($color: #ffffff, $alpha: 0.3);
    height: calc(100% - 18px);
  }
}
</style>
