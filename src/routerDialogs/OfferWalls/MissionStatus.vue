<script setup lang="ts">
import { FULL_DATE_TIME_24H_FORMAT, dateTimeFormat } from '@helpers';
import type { IActivatingTimeMission } from '@types';

interface Props {
  pendingMission: IActivatingTimeMission;
}

interface Emits {
  (e: 'close'): void;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header><span v-html="t('OFFERWALL_STATUS')"></span></template>
    <div
      class="ba-status full-width column items-center justify-start text-center"
    >
      <p>
        <span
          class="font-bold text-base"
          v-html="t(pendingMission.mission?.description)"
        ></span>
        <br /><br />
        <span v-html="t('STATUS_PENDING')" class="mr-1"></span>
        <span>
          {{
            dateTimeFormat(
              +new Date(pendingMission.updated_at),
              FULL_DATE_TIME_24H_FORMAT
            )
          }}</span
        ><br /><br />

        <span
          >{{ t('OFFERWALL_STATUS') }}:
          <span class="italic">{{ t(`PENDING`) }}</span></span
        >
      </p>
    </div>
  </Dialog>
</template>
<style>
.ba-status i {
  opacity: 0.5;
}
</style>
