<script setup lang="ts">
import { generateRandomString } from '@helpers';
import { useUserStore } from '@stores';

const { t } = useI18n();
const { user } = storeToRefs(useUserStore());
</script>
<template>
  <Dialog>
    <template #header>{{ t('REQUEST_TB_PERMISSION_HEADER') }} </template>
    <div class="full-width column items-center justify-start text-center">
      <p class="text-left mb-4" v-html="t('REQUEST_TB_PERMISSION_CONTENT')"></p>
      <a
        :href="`https://openapi.tigerfintech.com/oauth2/v1/authorize?client_id=63e0a0e5c5974c51aff2a039f3d7b7e9&response_type=code&scope=uuid%20offline%20api.basic%3Aread%20api.fundings%3Awrite&audience=openapi&redirect_uri=https://huntthemouse.sqkii.com/&state=${generateRandomString(
          8
        )}&external_id=${user?.id}`"
      >
        <Button size="max-content" variant="primary">{{
          t('REQUEST_TB_PERMISSION_BTN')
        }}</Button></a
      >
    </div>
  </Dialog>
</template>
