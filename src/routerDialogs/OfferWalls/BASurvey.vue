<script setup lang="ts">
import { SurveyQuestion } from '@components';
import { useBAStore, useDialogStore, useUserStore } from '@stores';
import { useAsync, useGlobal } from '@composables';
import { DAILYMISSION } from '@repositories';
import { uniqBy } from 'lodash';
import type { IBrandAction, ISurveyData } from '@types';

interface Props {
  dataBrand: IBrandAction;
}

interface ISurvey {
  data: ISurveyData;
  stage: number;
}

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();
const props = defineProps<Props>();

const storeDialog = useDialogStore();
const storeUser = useUserStore();
const storeBA = useBAStore();

const { trackUserLocation } = useGlobal();
const { currentSeason } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();

const stage = ref(0);
const dataSurvey = ref<ISurveyData[]>([]);

const DATA = computed<ISurveyData[]>(() => [
  {
    id: 1,
    q: t('CIRCLE_SHRINK_SURVEY_QUESTION_1'),
    sub_q: t('CIRCLE_SHRINK_SURVEY_DESCRIPTION_1'),
    a: [
      {
        value: 0,
        total: 5,
        size: '50px',
      },
    ],
    min_rate_text: t('QUICK_SURVEY_RATE_POOR'),
    max_rate_text: t('QUICK_SURVEY_RATE_AMAZING'),
    type: 'rate',
  },
  {
    id: 2,
    q: t('CIRCLE_SHRINK_SURVEY_QUESTION_2'),
    sub_q: t('CIRCLE_SHRINK_SURVEY_DESCRIPTION_2'),
    a: [
      {
        value: '',
      },
    ],
    type: 'area',
  },
  {
    id: 3,
    q: t('CIRCLE_SHRINK_SURVEY_QUESTION_3'),
    sub_q: t('CIRCLE_SHRINK_SURVEY_DESCRIPTION_3'),
    a: [
      {
        value: '',
      },
    ],
    type: 'area',
  },
  {
    id: 4,
    q: t('CIRCLE_SHRINK_SURVEY_QUESTION_4'),
    sub_q: t('CIRCLE_SHRINK_SURVEY_DESCRIPTION_4'),
    a: [
      {
        value: '',
      },
    ],
    type: 'area',
  },
]);

function handleSurvey(survey: ISurvey) {
  dataSurvey.value = [...dataSurvey.value, survey.data];
  stage.value = survey.stage;
}

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = dataSurvey.value.map((d) => {
      if (!currentSeason.value) return;
      let answer;
      if (!d.multiple)
        answer = d.a
          .filter((a) => a.selected || (d.type !== 'select' && a.value))
          .map((a) => a.value)[0];
      else answer = d.a.filter((a) => a.selected).map((a) => a.value);
      return {
        id: d.id,
        question: d.q,
        answer,
        season_id: currentSeason.value.id,
      };
    });
    await DAILYMISSION.survey({
      data: uniqBy(data, 'id'),
    });
    await storeBA.fetchBrandAction();

    trackUserLocation('brand_action', {
      type: props.dataBrand.type,
      brand_action_id: props.dataBrand._id,
    });
    emits('close');
    openDialog('ba_survey_completed');
  },
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #btnTopLeft v-if="stage > 0">
      <Button
        shape="square"
        variant="secondary"
        @click="
          stage--;
          storeDialog.isBack = true;
        "
      >
        <Icon name="arrow-left" />
      </Button>
    </template>

    <template #header>
      <div>{{ t('ba_survey_header') }}</div>
    </template>
    <SurveyQuestion
      :data="DATA"
      :stage="stage"
      :loading="loading"
      @survey="handleSurvey"
      @submit="onSubmit"
      isBA
    />
  </Dialog>
</template>
