<script setup lang="ts">
import { IUserLang } from '@types';
import { last } from 'lodash';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();
const props = defineProps<{
  ba_unique_id: string;
}>();
const { t, getLocaleMessage } = useI18n();

const tacs = computed(() => {
  const lang = LocalStorage.getItem('lang') as IUserLang;
  const localeMsg = getLocaleMessage(lang || process.env.APP_LANGUAGE_CODE);

  const pattern = new RegExp(`^ba_${props.ba_unique_id}_tac_(\\d+)$`);
  return Object.keys(localeMsg || {})
    .filter((key) => key?.toString().match(pattern))
    .sort((b, a) => {
      return +(last(b.split('_')) || '0') - +(last(a.split('_')) || '0');
    })
    .map((key) => localeMsg[key]);
});
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header>{{ t('SETTINGS_TACS') }}</template>

    <ul
      style="list-style-type: decimal"
      class="pl-4 column items-center flex-nowrap gap-2"
    >
      <li v-for="(tac, idx) in tacs" :key="`ba_tac_${idx}`" v-html="tac"></li>
    </ul>
    <div class="flex flex-center mt-2">
      <Button @click="emits('close')" :label="t('BUTTON_GOT_IT')" />
    </div>
  </Dialog>
</template>
