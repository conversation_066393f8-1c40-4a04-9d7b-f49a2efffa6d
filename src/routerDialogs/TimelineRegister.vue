<script setup lang="ts">
import { BUSINESS_EMAIL_REGEX, EMAIL_REGEX, getSocials } from '@helpers';
import { useAsync, useTrackData } from '@composables';
import { useUserStore } from '@stores';
import { TIMELINE } from '@repositories';
import { useForm } from 'vee-validate';
import type { IReminderPayload, ITimeline } from '@types';
import * as yup from 'yup';

interface Emits {
  (event: 'close'): void;
}

interface Props {
  timeline: ITimeline;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { t } = useI18n();
const { track } = useTrackData();

const isSubmitted = ref(false);

const options = computed(() => [
  { value: 'brand', label: t('TIMELINE_INTTOJOINGAMEAS_1') },
  { value: 'player', label: t('TIMELINE_INTTOJOINGAMEAS_2') },
]);

const validationSchema = yup.object({
  type: yup.string().required(t('TIMELINE_TYPE_REQUIRED')),
  email: yup
    .string()
    .required(t('TIMELINE_EMAILADDRESS_REQUIRED'))
    .test('email-test', t('TIMELINE_EMAILADDRESS_INVALID'), (value) => {
      if (!value) return false;
      return EMAIL_REGEX.test(value);
    })
    .test('email-test', t('TIMELINE_REGISTER_BRAND_NOTE'), (value) => {
      const { type } = values;
      if (!value) return false;
      if (type === 'brand') return BUSINESS_EMAIL_REGEX.test(value);
      return EMAIL_REGEX.test(value);
    }),
});

const { handleSubmit, values, setFieldError } = useForm<IReminderPayload>({
  initialValues: {
    email: '',
    type: '',
    season_id: props.timeline._id,
  },
  validationSchema,
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const callback = handleSubmit(async (values) => {
      if (values.type === 'brand' && !BUSINESS_EMAIL_REGEX.test(values.email)) {
        setFieldError('email', t('TIMELINE_REGISTER_BRAND_NOTE'));
        return;
      }
      await TIMELINE.reminder(values);
      await storeUser.fetchTimeline();
      isSubmitted.value = true;
    });
    await callback();
  },
  onSettled() {
    track('register_interest_popup', {
      action: 'submit_interest',
    });
  },
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div
        v-html="
          !isSubmitted
            ? timeline.hunt_name
            : t('TIMELINE_EMAILADDRESSSUBMITTED_TITLE')
        "
      ></div>
    </template>
    <q-form @submit="onSubmit" class="text-center season-consent-form">
      <section v-show="!isSubmitted">
        <div
          class="mb-4 text-sm text-center"
          v-html="t('TIMELINE_REGISTERINTERESTFORM_DESC')"
        ></div>
        <VeeInput
          name="email"
          :label="t('TIMELINE_EMAILADDRESS_LABEL')"
          class="mb-5 email-input"
          autofocus
        />
        <!-- <Transition>
          <div
            class="mb-3 text-center bg-[#981515] rounded-lg -mx-2 py-1 px-2"
            v-html="t('TIMELINE_REGISTER_BRAND_NOTE')"
            v-if="
              values.type === 'brand' &&
              !BUSINESS_EMAIL_REGEX.test(values.email) &&
              values.email
            "
          ></div>
        </Transition> -->
        <VeeSelect
          name="type"
          :label="t('TIMELINE_TYPE_LABEL')"
          :options="options"
          class="mb-5"
        />

        <Button
          :label="t('TIMELINE_REGISTERINTERESTFORM_BUTTON')"
          :loading="loading"
          type="submit"
        />
      </section>
      <section v-show="isSubmitted">
        <div
          class="mb-4 text-sm text-center"
          v-html="
            t(
              values.type === 'brand'
                ? 'TIMELINE_EMAILADDRESSSUBMITTED_BUSINESS_DESC'
                : 'TIMELINE_EMAILADDRESSSUBMITTED_PLAYER_DESC',
              {
                HUNT_NAME: timeline.hunt_name,
              }
            )
          "
        ></div>
        <div
          class="mb-5 text-lg font-bold text-center"
          v-html="values.email"
        ></div>
        <div class="mt-5 text-center">
          <div
            class="px-10 mb-5 text-sm"
            v-html="t('TIMELINE_REGISTER_SOCIALMEDIA')"
          ></div>
          <div class="flex items-center justify-center gap-4">
            <a
              :href="link"
              target="_blank"
              rel="noopener noreferrer"
              v-for="{ link, icon } in getSocials()"
              :key="icon"
            >
              <Icon :name="icon" :size="25" />
            </a>
          </div>
        </div>
      </section>
    </q-form>
  </Dialog>
</template>

<style lang="scss">
.season-consent-form .email-input .error_msg {
  margin: 0.25rem -0.5rem;
  padding: 0.25rem 0.5rem;
  font-size: 14px !important;
  border-radius: 0.5rem;
}
</style>
