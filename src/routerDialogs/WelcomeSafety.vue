<script setup lang="ts">
import { useUserStore, useDialogStore } from '@stores';
import {
  useBrandDoodleSov,
  useBrandSov,
  useClick,
  useTrackData,
} from '@composables';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeDialog = useDialogStore();

const { currentSeason } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

const { randomResult, ready } = useBrandSov('safety_hints_pop_up_flag', {
  id: 'safety_hints_pop_up_character',
  track: false,
});
const doodle = useBrandDoodleSov({
  ready,
  randomResult,
  referenceId: 'safety_hints_pop_up_flag',
  doodleId: 'safety_hints_pop_up_character',
});

useClick('goTAC', () => {
  openDialog('tac');
  track('auto_window_other', {
    screen_type: 'welcome_safety',
    other_button: 'tac',
  });
});

const checkbox = ref(true);

const CONTENTS = [
  {
    title: t('SAFETY_HINT1_TITLE'),
    description: t('SAFETY_HINT1_DESC'),
  },
  {
    title: t('SAFETY_HINT2_TITLE'),
    description: t('SAFETY_HINT2_DESC'),
  },
  {
    title: t('SAFETY_HINT3_TITLE'),
    description: t('SAFETY_HINT3_DESC'),
  },
  {
    title: t('SAFETY_HINT4_TITLE'),
    description: t('SAFETY_HINT4_DESC'),
  },
  {
    title: t('SAFETY_HINT5_TITLE'),
    description: t('SAFETY_HINT5_DESC'),
  },
  {
    title: t('SAFETY_HINT6_TITLE'),
    description: t('SAFETY_HINT6_DESC'),
  },
  {
    title: t('SAFETY_HINT7_TITLE'),
    description: t('SAFETY_HINT7_DESC'),
  },
];

async function handleAgree() {
  const date = new Date().toISOString();
  if (checkbox.value) storeDialog.setWelcomeSafetyDate(date);
  else storeDialog.setWelcomeSafetyDate('');
  track('safetyhints_dontshowagain', {
    checked: checkbox.value,
  });
  track('safety_popup', {
    action: 'agree_tnc',
  });
  emits('close');

  // fly to capitaland capitaland
  // const _bbox = bbox(currentGeojson.value);
  // await delay(1000);
  // await storeMap.mapIns?.fitBounds(_bbox as LngLatBoundsLike, {
  //   padding: 20,
  //   maxZoom: 15,
  //   duration: 1000,
  // });
  // if (!onboarding.value?.capitaland_onboarding_journey) {
  //   await delay(500);
  //   firstInstuctor();
  // } else {
  //   await delay(1000);
  //   openDialog('capitaland_daily_reward');
  // }

  // if (!endSentosaGame.value) openDialog('sentosa_daily_reward');
  // if (goldenCoinScheduled.value) {
  //   closeMessage();
  //   triggerMessage('onboarding_golden_dialog');
  // }
}
</script>
<template>
  <Dialog
    hide-close
    @close="
      track('silvercoin_safetyhint', {
        action: 'silvercoin_safetyhint_close',
      });
      emits('close');
    "
  >
    <template #icon-center>
      <!-- <template v-if="perpetualHunt">
        <div
          class="flex flex-nowrap absolute left-[50%] -translate-x-1/2 -top-[62px] -z-10"
        >
          <Icon name="capitaland-timii" class="shrink-0" :size="72" />
          <Icon
            name="sov/brand_flag/capitaland"
            class="-ml-[12px] shrink-0"
            :size="96"
          />
        </div>
      </template> -->
      <!-- <template v-else> -->
      <template v-if="ready && doodle">
        <div
          class="flex flex-nowrap absolute left-[50%] -translate-x-1/2 -top-[62px] -z-10"
        >
          <Icon :name="doodle.getAsset('_timii')" class="shrink-0" :size="72" />
          <Icon
            :name="randomResult.safety_hints_pop_up_flag.getAsset()"
            class="-ml-[12px] shrink-0"
            :size="96"
          />
        </div>
      </template>
    </template>
    <!-- </template> -->
    <template #header>
      <div v-html="t('SAFETY_WELCOMETEXT_TITLE')"></div>
    </template>
    <div
      class="mb-5 text-sm text-center"
      v-html="t('SAFETY_WELCOMETEXT_1')"
    ></div>
    <div class="flex flex-col flex-nowrap">
      <Expansion
        class="shadow-1 overflow-hidden mb-3 bg-[#091A3C] rounded"
        group="safety-hint"
        v-for="(c, index) in CONTENTS"
        :key="index"
      >
        <template v-slot:header>
          <div class="text-sm font-bold" v-html="c.title"></div>
        </template>
        <q-card style="background: transparent">
          <q-card-section>
            <div class="text-sm">
              {{ c.description }}
            </div>
          </q-card-section>
        </q-card>
      </Expansion>
      <div
        class="text-sm italic text-center"
        v-html="t('SAFETY_WELCOMETEXT_2')"
      ></div>
      <div class="w-full h-[1px] bg-[#FFFFFF] my-3"></div>
      <div
        class="px-2 mb-5 text-sm text-center"
        v-html="
          t('SAFETY_TERMSANDCONDITIONS', {
            HUNT_NAME: currentSeason?.hunt_name,
          })
        "
      ></div>
      <Button
        class="mx-auto mb-3"
        :label="t('SAFETY_BUTTON')"
        @click="handleAgree"
      />
      <q-checkbox
        class="mx-auto"
        v-model="checkbox"
        :label="t('SAFETY_CHECKBOX')"
        @click="
          track('auto_window_other', {
            screen_type: 'welcome_safety',
            other_button: 'checkbox',
          })
        "
      />
    </div>
  </Dialog>
</template>
