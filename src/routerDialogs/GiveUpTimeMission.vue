<script lang="ts" setup>
import { useTimedMission } from '@composables';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { handleGiveUpMission, loading, skip_mission_price } = useTimedMission();
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('GIVE_UP_TIME_MISSION_HEADER')"></div>
    </template>
    <div class="text-center text-sm">
      <div class="mb-4 text-sm" v-html="t('GIVE_UP_TIME_MISSION_DESC')"></div>
      <div class="flex flex-nowrap gap-4">
        <Button
          v-if="!skip_mission_price"
          class="!w-[120px] !min-w-[auto]"
          :label="t('GIVE_UP_TIME_MISSION_BTN_YES')"
          variant="purple"
          :loading="loading"
          @click="handleGiveUpMission"
        />

        <Button
          v-if="skip_mission_price"
          class="!w-[120px] !min-w-[auto]"
          :title="t('GIVE_UP_TIME_MISSION_BTN_YES')"
          variant="purple"
          :loading="loading"
          :amount="skip_mission_price"
          @click="handleGiveUpMission"
        />

        <Button
          class="flex-1"
          :class="{ 'pointer-events-none': loading }"
          :label="t('GIVE_UP_TIME_MISSION_BTN_NO')"
          @click="emits('close')"
        />
      </div>
    </div>
  </Dialog>
</template>
