<script lang="ts" setup>
import { useAsync, useTick, useTrackData } from '@composables';
import { EMAIL_REGEX, successNotify, timeCountDown } from '@helpers';
import { USER, VOUCHERS } from '@repositories';
import { useUserStore, useVouchersStore } from '@stores';
import { useForm } from 'vee-validate';
import type { IAPIVouchersResponseError } from '@types';
import * as yup from 'yup';

interface Props {
  fromRecoverPW?: boolean;
}

defineProps<Props>();

const storeVouchers = useVouchersStore();
const storeUser = useUserStore();

const { user } = storeToRefs(storeUser);
const { t } = useI18n();
const { closeAllDialog, openDialog } = useMicroRoute();
const { now } = useTick();
const { track } = useTrackData();

const error = ref('');
const locked_until = ref('');

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  return +new Date(locked_until.value) - now.value;
});

const validationSchema = yup.object({
  email: yup
    .string()
    .required(t('EMAIL_REQUIRED'))
    .matches(EMAIL_REGEX, t('EMAIL_INVALID')),
  password: yup.string().required(t('PASSWORD_REQUIRED')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    email: '',
    password: '',
    sdk_linking: {
      hunter_id: String(user.value?.hunter_id),
      user_id: String(user.value?.id),
      mobile_number: String(user.value?.mobile_number),
    },
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  if (!user.value) return;
  const { data } = await VOUCHERS.login(values);
  return data;
});

const {
  loading,
  status,
  execute: onSubmit,
} = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  async onSuccess(data) {
    if (!data) return;
    storeVouchers.setToken(data.token);
    const { data: balance } = await VOUCHERS.getUserBalance();
    await USER.setSVToken(data.token);
    await storeUser.fetchUser();
    storeVouchers.setUser({
      ...data.user,
      balance: balance.balance,
      currency: balance.currency,
    });
    closeAllDialog();
    successNotify({
      message: 'Linked to sqkii vouchers successfully!',
    });
    if (!data.user.hasPin) openDialog('set_pin');
  },
  onError(err: IAPIVouchersResponseError) {
    const { data, message } = err;

    switch (true) {
      case data?.info === 'no_email':
        error.value = t('LINK_KEE_INVALID_CREDENTIALS', {
          URL: process.env.VOUCHERS_URL,
        });
        break;
      case data?.info === 'locked':
        error.value = t('LINK_KEE_LOCKED_CREDENTIALS');
        break;
      case data?.info === 'temp_locked':
        error.value = t('LINK_KEE_MAX_CREDENTIALS');
        locked_until.value = data.locked_until;
        break;
      case data?.info === 'user_inactive':
        openDialog('create_kee_account', {
          email: values.email,
          stage: 3,
        });
        break;
      case !!data?.failed:
        error.value = t('LINK_KEE_INVALID_CREDENTIALS_ATTEMPTS', {
          URL: process.env.VOUCHERS_URL,
          ATTEMPTS: 5 - data.failed,
        });
        break;
      default:
        error.value = t(message);
        break;
    }
  },
  onSettled() {
    track('sv_keeaccount_sync', {
      status: status.value,
    });
  },
});

watch(
  values,
  () => {
    error.value = '';
  },
  { deep: true }
);
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('LINK_KEE_HEADER')"></div>
    </template>
    <q-form @submit="onSubmit" class="text-center">
      <div
        v-if="fromRecoverPW"
        class="text-center text-sm mb-5 bg-[#03833E] rounded p-2"
        v-html="t('LINK_KEE_DESC_2')"
      ></div>
      <div class="text-sm mb-5" v-html="t('LINK_KEE_DESC')"></div>
      <VeeInput
        class="mb-5"
        name="email"
        :label="t('LINK_KEE_LABEL_EMAIL')"
        :error="!!error"
        autofocus
      />
      <VeeInput
        class="mb-5"
        name="password"
        :label="t('LINK_KEE_LABEL_PW')"
        :error="!!error"
        type="password"
      />

      <div
        class="card-error mt-2 mb-5 text-center"
        v-if="!!error"
        v-html="t(error)"
      ></div>
      <Button
        class="!w-[210px] mb-5"
        :label="
          countdown > 0 ? timeCountDown(countdown) : t('LINK_KEE_BTN_SUBMIT')
        "
        :loading="loading"
        :disable="countdown > 0"
        type="submit"
      />
      <div
        class="text-sm underline text-[#00E0FF]"
        v-html="t('LINK_KEE_DESC_1')"
        @click="
          closeAllDialog();
          openDialog('kee_forgot_pw');
        "
      ></div>
    </q-form>
  </Dialog>
</template>
