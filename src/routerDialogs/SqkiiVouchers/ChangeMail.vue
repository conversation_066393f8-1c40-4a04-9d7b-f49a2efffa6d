<script lang="ts" setup>
import { useAsync, useTick } from '@composables';
import {
  dateTimeFormat,
  EMAIL_REGEX,
  formatMobileNumber,
  FULL_DATE_TIME_12H_FORMAT_IN_SECOND,
  timeCountDown,
} from '@helpers';
import { VOUCHERS } from '@repositories';
import { useVouchersStore } from '@stores';
import { useForm } from 'vee-validate';
import { identity, pickBy } from 'lodash';
import type { IAPIVouchersResponseError, IPayloadChangeMail } from '@types';
import dayjs from 'dayjs';
import * as yup from 'yup';

const storeVouchers = useVouchersStore();

const { user } = storeToRefs(storeVouchers);
const { t } = useI18n();
const { now } = useTick();

const el = ref<HTMLElement | null>(null);
const stage = ref<1 | 2 | 3>(1);
const error = ref('');
const resendAfter = ref('');

const countdownResend = computed(() => {
  if (!resendAfter.value) return 0;
  return +new Date(resendAfter.value) - now.value;
});

const header = computed(() => {
  const text = {
    1: t('CHANGE_MAIL_HEADER_1'),
    2: t('CHANGE_MAIL_HEADER_2'),
    3: t('CHANGE_MAIL_HEADER_3'),
  };
  return text[stage.value];
});

const validationSchema = computed(() => {
  const schema = {
    1: {
      otp_code: yup
        .string()
        .required(t('SIGNUP_FORM_OTP_REQUIRED'))
        .length(6, t('SIGNUP_FORM_OTP_INVALID')),
    },
    2: {
      email: yup
        .string()
        .required(t('EMAIL_REQUIRED'))
        .matches(EMAIL_REGEX, t('EMAIL_INVALID'))
        .test('unique', t('EMAIL_SAME_CURRENT_MAIL'), async (value) => {
          return user.value?.email !== value;
        }),
    },
    3: {},
  };
  return yup.object().shape(schema[stage.value]);
});

const { handleSubmit, values, setTouched } = useForm({
  initialValues: {
    otp_code: '',
    email: '',
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  const payload = pickBy(values, identity) as unknown as IPayloadChangeMail;
  const { data } = await VOUCHERS.changeMail(payload);
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = callback();
    return data;
  },
  onSuccess() {
    stage.value = 3;
    storeVouchers.fetchUser();
  },
  onError(err: IAPIVouchersResponseError) {
    const { data, message } = err;
    switch (true) {
      case data?.info === 'exist_email':
        error.value = t('CHANGE_MAIL_EXIST_EMAIL');
        break;
      case data?.info === 'otp_expired':
        error.value = t('CHANGE_MAIL_INVALID_OTP');
        break;
      case !!data?.verify:
        setTouched(false);
        stage.value = 2;
        break;
      default:
        error.value = t(message);
        break;
    }
  },
});

async function requestOTP() {
  try {
    const { data } = await VOUCHERS.requestChangeMail(
      String(user.value?.mobile_number)
    );
    resendAfter.value = data?.resend_after;
  } catch (err) {
    const { data, message } = err as IAPIVouchersResponseError;
    switch (true) {
      case data?.info === 'resend_limited':
        error.value = t('CHANGE_MAIL_RESEND_LIMIT_CREDENTIALS');
        break;
      default:
        error.value = t(message);
        break;
    }
  }
}

watch(
  values,
  () => {
    error.value = '';
  },
  { deep: true }
);

onMounted(async () => {
  await nextTick();
  await requestOTP();

  el.value = document.getElementById('CHANGE_MAIL_RESEND_OTP');
  if (el.value)
    el.value.addEventListener('click', async () => {
      await requestOTP();
    });
});

onBeforeUnmount(() => {
  if (el.value)
    el.value.removeEventListener('click', async () => {
      await requestOTP();
    });
});
</script>
<template>
  <Dialog>
    <template #btnTopLeft>
      <Button
        v-if="stage === 2"
        shape="square"
        variant="secondary"
        @click="stage--"
      >
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>
    <template #header>
      <div v-html="header"></div>
    </template>
    <q-form @submit="onSubmit" class="text-center">
      <section v-show="stage === 1">
        <div class="text-sm" v-html="t('CHANGE_MAIL_DESC_1')"></div>
        <div class="text-lg font-bold">
          {{ formatMobileNumber('+' + user?.mobile_number) }}
        </div>
        <div class="text-sm mb-5" v-html="t('CHANGE_MAIL_DESC_2')"></div>
        <VeeOTP class="mb-5" name="otp_code" :num-inputs="6" :error="!!error" />
        <div
          class="card-error mt-2 mb-5 text-center"
          v-if="!!error"
          v-html="t(error)"
        ></div>
        <section v-show="!!resendAfter">
          <div
            class="text-sm mb-5"
            v-html="
              t('CHANGE_MAIL_DESC_3', {
                TIME: dateTimeFormat(
                  resendAfter,
                  FULL_DATE_TIME_12H_FORMAT_IN_SECOND
                ),
                TIMEZONE: dayjs().format('Z').replace(':00', ''),
              })
            "
          ></div>
          <div class="text-sm mb-5">
            <span
              v-show="countdownResend > 0"
              v-html="
                t('CHANGE_MAIL_DESC_4', {
                  TIME: timeCountDown(countdownResend),
                })
              "
            ></span>
            <span
              :class="{
                'opacity-0 absolute': countdownResend > 0,
              }"
              v-html="t('CHANGE_MAIL_DESC_5')"
            ></span>
          </div>
        </section>
      </section>
      <section v-show="stage === 2">
        <div class="text-sm mb-5" v-html="t('CHANGE_MAIL_DESC_6')"></div>
        <VeeInput
          name="email"
          :label="t('LINK_KEE_LABEL_EMAIL')"
          autofocus
          class="mb-5"
          :error="!!error"
        />
        <div
          class="card-error mt-2 mb-5 text-center"
          v-if="!!error"
          v-html="t(error)"
        ></div>
      </section>
      <section v-show="stage === 3">
        <div class="flex flex-col justify-center items-center">
          <Icon class="mb-5" name="top-up-success" :size="140" />
          <div class="text-sm" v-html="t('CHANGE_MAIL_DESC_7')"></div>
        </div>
      </section>
      <Button
        :label="t('CHANGE_MAIL_BTN_NEXT')"
        :loading="loading"
        type="submit"
        class="w-full"
        v-if="stage !== 3"
      />
    </q-form>
  </Dialog>
</template>
