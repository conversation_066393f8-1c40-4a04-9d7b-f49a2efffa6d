<script lang="ts" setup>
import { useAsync, useTick } from '@composables';
import { VOUCHERS } from '@repositories';
import { useForm } from 'vee-validate';
import { timeCountDown } from '@helpers';
import type { IAPIVouchersResponseError, IUsedPromoCode } from '@types';
import * as yup from 'yup';
import { last } from 'lodash';

interface Emits {
  (e: 'used', payload: IUsedPromoCode): void;
}

const emits = defineEmits<Emits>();

const { closeDialog, openDialog } = useMicroRoute();
const { t } = useI18n();
const { now } = useTick();

const error = ref('');
const locked_until = ref('');

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  return +new Date(locked_until.value) - now.value;
});

const validationSchema = yup.object().shape({
  code: yup.string().required(t('CODE_REQUIRED')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    code: '',
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  let qr_code = last(values.code.split('?=code')) || '';
  qr_code = last(qr_code.split('?=c')) || '';
  if (!qr_code) return;
  const { data } = await VOUCHERS.checkPromoCode(qr_code);
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    emits('used', {
      code: values.code,
      bonusCrystal: data.crystals,
    });
    closeDialog('enter_top_up_promo');
  },
  onError(err: IAPIVouchersResponseError) {
    const { data, message } = err;
    switch (true) {
      case data?.info === 'temp_locked':
        error.value = t('PROMO_MAX_CREDENTIALS');
        locked_until.value = data.locked_until;
        break;
      case !!data?.failed && data?.info === 'invalid_code':
        error.value = t('PROMO_INVALID_CREDENTIALS_ATTEMPTS', {
          ATTEMPTS: 5 - data.failed,
        });
        break;
      default:
        error.value = t(message);
        break;
    }
  },
});

watch(
  values,
  () => {
    error.value = '';
  },
  { deep: true }
);
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="closeDialog('enter_top_up_promo')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('ENTER_TOP_UP_PROMO_HEADER')"></div>
    </template>
    <q-form @submit="onSubmit" class="px-5 text-center">
      <VeeInput class="mb-5" name="code" :error="!!error">
        <template #prepend>
          <div class="border-r w-8 ml-[6px] h-[calc(100%-18px)]">
            <Icon
              name="icons/ic_scan"
              :size="20"
              style="opacity: 0.7"
              @click="
                openDialog('scan_top_up_promo', {
                  onScan: onSubmit,
                })
              "
            />
          </div>
        </template>
      </VeeInput>
      <div
        class="card-error mt-2 mb-5 text-center"
        v-if="!!error"
        v-html="t(error)"
      ></div>
      <Button
        :loading="loading"
        :disable="countdown > 0"
        type="submit"
        :label="
          countdown > 0
            ? timeCountDown(countdown)
            : t('ENTER_TOP_UP_PROMO_BTN_SUBMIT')
        "
      />
    </q-form>
  </Dialog>
</template>
