<script lang="ts" setup>
import { useAsync } from '@composables';
import { VOUCHERS } from '@repositories';
import { useForm } from 'vee-validate';
import type { IAPIVouchersResponseError } from '@types';
import * as yup from 'yup';

interface Props {
  recover_token: string;
}

const props = defineProps<Props>();

const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();

const matched = ref(false);
const error = ref('');

const validationSchema = yup.object({
  password: yup.string().required(t('PASSWORD_REQUIRED')),
  cf_password: yup
    .string()
    .required(t('RE_ENTER_PASSWORD_REQUIRED'))
    .oneOf([yup.ref('password')], t('PASSWORD_NOT_MATCH')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    password: '',
    cf_password: '',
    recover_token: props.recover_token,
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  const { data } = await VOUCHERS.setPassword({
    password: values.password,
    recover_token: values.recover_token,
  });
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    closeDialog('kee_recover_pw');
    openDialog('link_kee', {
      fromRecoverPW: true,
    });
  },
  onError(err: IAPIVouchersResponseError) {
    const { message } = err;
    error.value = message;
  },
});

function onClose() {
  if (loading.value) return;
  closeDialog('kee_recover_pw');
}

watch(
  values,
  () => {
    error.value = '';
  },
  { deep: true }
);
</script>
<template>
  <Dialog @close="onClose">
    <template #header>
      <div v-html="t('KEE_RECOVER_PW_HEADER')"></div>
    </template>
    <q-form @submit="onSubmit" class="text-center">
      <div class="text-sm px-7 mb-5" v-html="t('KEE_RECOVER_PW_DESC')"></div>
      <VeeInput
        class="mb-5"
        name="password"
        :label="t('PASSWORD')"
        :error="!!error"
        type="password"
      />
      <Requirements
        class="mb-3 -mt-4"
        v-if="values.password && !matched"
        :password="values.password"
        @valid="matched = $event"
      />

      <VeeInput
        class="mb-5"
        name="cf_password"
        :label="t('RE_ENTER_PASSWORD')"
        :error="!!error"
        type="password"
      />

      <div
        class="card-error mt-2 mb-5 text-center"
        v-if="!!error"
        v-html="t(error)"
      ></div>

      <Button
        class="!w-[210px] mb-5"
        :loading="loading"
        :label="t('KEE_RECOVER_PW_BTN_SUBMIT')"
        type="submit"
      />
    </q-form>
  </Dialog>
</template>
