<script lang="ts" setup>
import { useAsync } from '@composables';
import { VOUCHERS } from '@repositories';
import { useForm } from 'vee-validate';
import type { IAPIVouchersResponseError } from '@types';
import * as yup from 'yup';

const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();

const error = ref('');

const validationSchema = yup.object().shape({
  code: yup.string().required(t('CODE_REQUIRED')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    code: '',
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  const { data } = await VOUCHERS.paymentScanQR(values.code);
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onSuccess(data) {
    openDialog('pay_to_merchant', {
      outlet: data,
    });
  },
  onError(err: IAPIVouchersResponseError) {
    const { data, message } = err;
    switch (true) {
      case data?.info === 'outlet_invalid':
        error.value = t('OUTLET_INVALID_CODE');
        break;
      default:
        error.value = t(message);
        break;
    }
  },
});

watch(
  values,
  () => {
    error.value = '';
  },
  { deep: true }
);
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('ENTER_PAYMENT_CODE_HEADER')"></div>
    </template>
    <q-form @submit="onSubmit" class="px-5 text-center">
      <VeeInput class="mb-5" name="code" :error="!!error">
        <template #prepend>
          <div class="border-r w-8 ml-[6px] h-[calc(100%-18px)]">
            <Icon
              name="icons/ic_scan"
              :size="20"
              style="opacity: 0.7"
              @click="closeDialog('enter_payment_code')"
            />
          </div>
        </template>
      </VeeInput>
      <div
        class="card-error mt-2 mb-5 text-center"
        v-if="!!error"
        v-html="t(error)"
      ></div>
      <Button
        :loading="loading"
        type="submit"
        :label="t('ENTER_PAYMENT_CODE_BTN_SUBMIT')"
      />
    </q-form>
  </Dialog>
</template>
