<script lang="ts" setup>
const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('LINK_KEE_WELCOME_HEADER')"></div>
    </template>
    <div class="text-center flex flex-col items-center gap-5">
      <div class="text-sm" v-html="t('LINK_KEE_WELCOME_DESC')"></div>
      <Icon name="voucher_img" class="w-full rounded-lg mb-5" />
      <div class="text-sm" v-html="t('LINK_KEE_WELCOME_DESC_1')"></div>
      <Button
        :label="t('SQKII_VOUCHERS_BTN_GET_STARTED')"
        class="!w-[210px]"
        @click="
          closeDialog('link_kee_welcome');
          openDialog('link_kee');
        "
      />
    </div>
  </Dialog>
</template>
