<script lang="ts" setup>
import { useAsync, useTick } from '@composables';
import {
  dateTimeFormat,
  formatMobileNumber,
  FULL_DATE_TIME_12H_FORMAT_IN_SECOND,
  timeCountDown,
} from '@helpers';
import { useVouchersStore } from '@stores';
import { useForm } from 'vee-validate';
import { VOUCHERS } from '@repositories';
import type { IAPIVouchersResponseError } from '@types';
import dayjs from 'dayjs';
import * as yup from 'yup';

const storeVouchers = useVouchersStore();

const { user } = storeToRefs(storeVouchers);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { now } = useTick();

const el = ref<HTMLElement | null>(null);
const resendAfter = ref('');
const error = ref('');

const countdownResend = computed(() => {
  if (!resendAfter.value) return 0;
  return +new Date(resendAfter.value) - now.value;
});

const validationSchema = yup.object().shape({
  otp_code: yup
    .string()
    .required(t('SIGNUP_FORM_OTP_REQUIRED'))
    .length(6, t('SIGNUP_FORM_OTP_INVALID')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    otp_code: '',
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  const { data } = await VOUCHERS.setPinOTP({
    otp_code: values.otp_code,
  });
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onError(err: IAPIVouchersResponseError) {
    const { data, message } = err;
    switch (true) {
      case data?.info === 'otp_expired':
        error.value = t('FORGOT_PIN_OTP_EXPIRED_CREDENTIALS');
        break;
      case !!data?.verify:
        openDialog('set_pin', {
          otp: values.otp_code,
        });
        break;
      default:
        error.value = t(message);
        break;
    }
  },
});

async function getOTP() {
  try {
    const { data } = await VOUCHERS.forgotPIN(
      String(user.value?.mobile_number)
    );
    resendAfter.value = data?.resend_after;
  } catch (err) {
    const { data, message } = err as IAPIVouchersResponseError;
    switch (true) {
      case data?.info === 'resend_limited':
        error.value = t('FORGOT_PIN_RESEND_LIMIT_CREDENTIALS');
        break;
      default:
        error.value = t(message);
        break;
    }
  }
}

onMounted(async () => {
  await nextTick();
  await getOTP();

  el.value = document.getElementById('FORGOT_PIN_RESEND_OTP');
  if (el.value)
    el.value.addEventListener('click', async () => {
      await getOTP();
    });
});

onBeforeUnmount(() => {
  if (el.value)
    el.value.removeEventListener('click', async () => {
      await getOTP();
    });
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('FORGOT_PIN_HEADER')"></div>
    </template>
    <q-form class="text-center" @submit="onSubmit">
      <div class="text-sm mb-1" v-html="t('FORGOT_PIN_DESC_1')"></div>
      <div class="text-lg font-bold mb-1">
        {{ formatMobileNumber('+' + user?.mobile_number) }}
      </div>
      <div class="text-sm mb-5" v-html="t('FORGOT_PIN_DESC_2')"></div>
      <VeeOTP class="mb-5" name="otp_code" :num-inputs="6" :error="!!error" />
      <div
        class="card-error mt-2 mb-5 text-center"
        v-if="!!error"
        v-html="t(error)"
      ></div>
      <section v-show="!!resendAfter">
        <div
          class="text-sm mb-5"
          v-html="
            t('FORGOT_PIN_DESC_3', {
              TIME: dateTimeFormat(
                resendAfter,
                FULL_DATE_TIME_12H_FORMAT_IN_SECOND
              ),
              TIMEZONE: dayjs().format('Z').replace(':00', ''),
            })
          "
        ></div>
        <div class="text-sm mb-5">
          <span
            v-show="countdownResend > 0"
            v-html="
              t('FORGOT_PIN_DESC_4', {
                TIME: timeCountDown(countdownResend),
              })
            "
          >
          </span>
          <span
            :class="{
              'opacity-0 absolute': countdownResend > 0,
            }"
            v-html="t('FORGOT_PIN_DESC_5')"
          ></span>
        </div>
      </section>
      <Button
        :label="t('FORGOT_PIN_BTN_NEXT')"
        :loading="loading"
        class="!w-[210px]"
        type="submit"
      />
    </q-form>
  </Dialog>
</template>
