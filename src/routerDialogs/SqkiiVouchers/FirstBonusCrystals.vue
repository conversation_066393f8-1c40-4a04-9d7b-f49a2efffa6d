<script lang="ts" setup>
interface Props {
  amount?: number;
  crystals?: number;
  newCrystal?: number;
  hiddenButton?: boolean;
}

const props = defineProps<Props>();

import { useTrackData } from '@composables';
import { numeralFormat } from '@helpers';
import { VOUCHERS } from '@repositories';
import { useBAStore, useUserStore, useVouchersStore } from '@stores';

const storeVouchers = useVouchersStore();
const storeBA = useBAStore();
const storeUser = useUserStore();

const { t } = useI18n();
const { settings } = storeToRefs(storeUser);
const { user } = storeToRefs(storeVouchers);
const { brand_actions } = storeToRefs(storeBA);
const { closeDialog, openDialog } = useMicroRoute();
const { track } = useTrackData();

const crystals = ref(props.crystals || 0);

const hasFeatured = computed(() => {
  const sv_1BA = brand_actions.value.find((ba) => ba.unique_id === 'sv_1');
  return sv_1BA?.featured;
});

const multiplierNumber = computed(() => {
  return {
    featured: settings.value?.brand_action?.multiplier?.featured || 3,
    firstTime: settings.value?.brand_action?.multiplier?.first_time || 2,
  };
});

const rewardMultiplier = computed(() => {
  if (hasFeatured.value) return multiplierNumber.value.featured;
  return multiplierNumber.value.firstTime;
});

async function getBonusRate() {
  const { data } = await VOUCHERS.checkBonusRate({
    amount: props.amount || 10,
    item: 'crystals',
  });
  crystals.value = data.crystals;
}

onMounted(async () => {
  await getBonusRate();
  track('sv_2xcrystals_popup_view');
});
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="closeDialog('first_bonus_crystals')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div
        v-html="
          hasFeatured
            ? t('SV_FIRST_BONUS_HEADER_1')
            : t('SV_FIRST_BONUS_HEADER', { MULTIPLIER: rewardMultiplier })
        "
      ></div>
    </template>
    <div
      class="text-sm text-center"
      v-html="
        hasFeatured ? t('SV_FIRST_BONUS_DESC_1') : t('SV_FIRST_BONUS_DESC')
      "
    ></div>
    <div class="relative w-[calc(100%+48px)] -ml-6 -mt-20 aspect-square">
      <Icon name="big-glow" class="!w-full" />
      <Icon
        class="absolute -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"
        name="crystal"
        :size="140"
      />
      <div
        class="absolute top-[60%] left-[45%] -translate-x-1/2 -translate-y-1/2 font-bold text-[40px]"
      >
        {{ rewardMultiplier }}x
      </div>
    </div>
    <div
      class="bg-[#091A3B] w-max mx-auto px-5 max-w-full rounded py-2 flex flex-nowrap justify-center items-center text-xl font-bold gap-1 -mt-20 z-10 relative mb-5"
    >
      <div>
        {{ user?.currency || 'S$' }}
        {{ numeralFormat(amount || 10, '0,0.00') }}
      </div>
      <div>=</div>
      <div class="line-through opacity-50">
        {{
          numeralFormat(
            hasFeatured ? crystals * multiplierNumber.firstTime : crystals
          )
        }}
      </div>
      <div>
        {{
          numeralFormat(
            hasFeatured
              ? crystals * rewardMultiplier * multiplierNumber.firstTime
              : crystals * rewardMultiplier
          )
        }}
      </div>
      <Icon name="crystal" :size="20" />
    </div>
    <div class="text-center" v-if="!hiddenButton">
      <Button
        :label="t('SV_FIRST_BONUS_BTN')"
        class="!w-[200px]"
        @click="
          closeDialog('first_bonus_crystals');
          openDialog('get_sqkii_vouchers');
        "
      />
    </div>
  </Dialog>
</template>
