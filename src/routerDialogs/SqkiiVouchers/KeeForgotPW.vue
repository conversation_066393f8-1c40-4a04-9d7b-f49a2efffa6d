<script lang="ts" setup>
import { useForm } from 'vee-validate';
import { EMAIL_REGEX, timeCountDown } from '@helpers';
import { VOUCHERS } from '@repositories';
import { useAsync, useTick } from '@composables';
import type { IAPIVouchersResponseError } from '@types';
import * as yup from 'yup';

const { t } = useI18n();
const { closeDialog } = useMicroRoute();
const { now } = useTick();

const stage = ref<1 | 2>(1);
const error = ref('');
const locked_until = ref('');

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  return +new Date(locked_until.value) - now.value;
});

const validationSchema = yup.object({
  email: yup
    .string()
    .required(t('EMAIL_REQUIRED'))
    .matches(EMAIL_REGEX, t('EMAIL_INVALID')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    email: '',
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  const { data } = await VOUCHERS.forgotPW(values.email);
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    stage.value = 2;
  },
  onError(err: IAPIVouchersResponseError) {
    const { data, message } = err;
    switch (true) {
      case data?.info === 'no_email':
        error.value = t('LINK_KEE_INVALID_CREDENTIALS', {
          URL: process.env.VOUCHERS_URL,
        });
        break;
      case data?.info === 'temp_locked':
        error.value = t('LINK_KEE_MAX_CREDENTIALS');
        locked_until.value = data.locked_until;
        break;
      case data?.info === 'resend_limited':
        error.value = t('KEE_RESEND_LIMIT_CREDENTIALS', {
          URL: process.env.VOUCHERS_URL,
        });
        break;
      default:
        error.value = t(message);
        break;
    }
  },
});

function onClose() {
  if (loading.value) return;
  closeDialog('kee_forgot_pw');
}

watch(
  values,
  () => {
    error.value = '';
  },
  { deep: true }
);
</script>
<template>
  <Dialog @close="onClose">
    <template #header>
      <div
        v-html="
          stage === 1
            ? t('KEE_FORGOT_PW_HEADER')
            : t('KEE_FORGOT_PW_HEADER_CHECK_MAIL')
        "
      ></div>
    </template>
    <template v-if="stage === 1">
      <q-form @submit="onSubmit" class="text-center">
        <div class="text-sm px-7 mb-5" v-html="t('KEE_FORGOT_PW_DESC')"></div>
        <VeeInput
          class="mb-5"
          name="email"
          :label="t('LINK_KEE_LABEL_EMAIL')"
          :error="!!error"
        />
        <div
          class="card-error mt-2 mb-5 text-center"
          v-if="!!error"
          v-html="t(error)"
        ></div>
        <Button
          class="!w-[210px] mb-5"
          :loading="loading"
          :disable="countdown > 0"
          :label="
            countdown > 0
              ? timeCountDown(countdown)
              : t('KEE_FORGOT_PW_BTN_SUBMIT')
          "
          type="submit"
        />
      </q-form>
    </template>
    <template v-else>
      <div class="text-sm text-center" v-html="t('KEE_FORGOT_PW_DESC_1')"></div>
    </template>
  </Dialog>
</template>
