<script lang="ts" setup>
import { useAsync } from '@composables';
import { successNotify } from '@helpers';
import { VOUCHERS } from '@repositories';
import { useVouchersStore } from '@stores';
import { useForm } from 'vee-validate';
import * as yup from 'yup';

interface Props {
  otp?: string;
}

const props = defineProps<Props>();

const storeVouchers = useVouchersStore();

const { t } = useI18n();
const { closeAllDialog } = useMicroRoute();

const stage = ref<1 | 2>(1);
const pin_code = ref('');

const validationSchema = computed(() => {
  const schema = {
    1: {
      pin_code: yup
        .string()
        .required(t('PIN_REQUIRED'))
        .length(6, t('SIGNUP_FORM_OTP_INVALID')),
    },
    2: {
      cf_pin_code: yup
        .string()
        .required(t('CONFIRM_PIN_REQUIRED'))
        .length(6, t('SIGNUP_FORM_OTP_INVALID'))
        .test('match', t('PIN_NOT_MATCH'), (value) => value === pin_code.value),
    },
  };
  return yup.object().shape({ ...schema[stage.value] });
});

const { handleSubmit, values, setTouched } = useForm({
  initialValues: {
    pin_code: '',
    cf_pin_code: '',
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  if (stage.value === 1) return values.pin_code;
  if (props.otp) {
    const { data } = await VOUCHERS.setPinOTP({
      otp_code: props.otp,
      pin_code: values.pin_code,
    });
    return data;
  }
  const { data } = await VOUCHERS.setPIN(pin_code.value);
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onSuccess() {
    if (stage.value === 1) {
      pin_code.value = values.pin_code;
      setTouched(false);
      stage.value = 2;
      return;
    }
    storeVouchers.updateUser({
      hasPin: true,
    });
    successNotify({
      message: t('SET_PIN_SUCCESS'),
    });
    closeAllDialog();
  },
});
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        v-if="stage === 2"
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="stage = 1"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div
        v-html="stage === 1 ? t('SET_PIN_HEADER') : t('SET_PIN_HEADER_1')"
      ></div>
    </template>
    <q-form class="text-center">
      <div
        class="text-sm mb-5"
        v-html="stage === 1 ? t('SET_PIN_DESC') : t('SET_PIN_DESC_1')"
      ></div>
      <section v-show="stage === 1">
        <VeeOTP
          class="mb-5"
          name="pin_code"
          :num-inputs="6"
          input-type="password"
        />
      </section>
      <section v-show="stage === 2">
        <VeeOTP
          class="mb-5"
          name="cf_pin_code"
          :num-inputs="6"
          input-type="password"
        />
      </section>

      <Button
        :label="
          stage === 1 ? t('SET_PIN_BTN_SUBMIT') : t('SET_PIN_BTN_SUBMIT_1')
        "
        :loading="loading"
        @click="onSubmit"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
