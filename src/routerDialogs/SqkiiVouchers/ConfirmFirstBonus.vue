<script lang="ts" setup>
import { useTrackData } from '@composables';
import { numeralFormat } from '@helpers';
import { useBAStore, useUserStore, useVouchersStore } from '@stores';

interface Props {
  amount: number;
  promo_code: string;
  crystals: number;
  newCrystal: number;
}
defineProps<Props>();

const storeVouchers = useVouchersStore();
const storeUser = useUserStore();
const storeBA = useBAStore();

const { brand_actions } = storeToRefs(storeBA);
const { settings } = storeToRefs(storeUser);
const { user } = storeToRefs(storeVouchers);
const { openDialog, closeDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();

const hasFeatured = computed(() => {
  const sv_1BA = brand_actions.value.find((ba) => ba.unique_id === 'sv_1');
  return sv_1BA?.featured;
});

const multiplierNumber = computed(() => {
  return {
    featured: settings.value?.brand_action?.multiplier?.featured || 3,
    firstTime: settings.value?.brand_action?.multiplier?.first_time || 2,
  };
});

const rewardMultiplier = computed(() => {
  if (hasFeatured.value) return multiplierNumber.value.featured;
  return multiplierNumber.value.firstTime;
});
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="closeDialog('confirm_first_bonus')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('SV_CONFIRM_FIRST_BONUS_HEADER')"></div>
    </template>
    <div class="text-center">
      <div
        class="text-sm mb-5"
        v-html="
          t('SV_CONFIRM_FIRST_BONUS_DESC', {
            CURRENTCY: user?.currency || 'S$',
            AMOUNT: numeralFormat(amount, '0,0.00'),
            CRYSTALS: numeralFormat(
              newCrystal ? crystals * multiplierNumber.firstTime : crystals
            ),
          })
        "
      ></div>
      <div
        class="bg-[#091A3B] w-max mx-auto px-5 max-w-full rounded py-2 flex flex-nowrap justify-center items-center text-xl font-bold gap-1 mb-8"
      >
        <div>
          {{ user?.currency || 'S$' }}
          {{ numeralFormat(amount, '0,0.00') }}
        </div>
        <div>=</div>
        <div class="line-through opacity-50">
          {{
            numeralFormat(
              hasFeatured ? crystals * multiplierNumber.firstTime : crystals
            )
          }}
        </div>
        <div>
          {{ numeralFormat(newCrystal ?? crystals * rewardMultiplier) }}
        </div>
        <Icon name="crystal" :size="20" />
      </div>
      <Button
        :label="t('SV_CONFIRM_FIRST_BONUS_BTN_MORE')"
        variant="purple"
        class="!w-[210px] mb-8"
        @click="
          closeDialog('confirm_first_bonus');
          track('sv_morecrystals');
        "
      />
      <div
        class="text-sm text-link underline"
        v-html="t('SV_CONFIRM_FIRST_BONUS_BTN_YES')"
        @click="
          openDialog('enter_pin_top_up', {
            amount,
            promo_code,
          })
        "
      ></div>
    </div>
  </Dialog>
</template>
