<script lang="ts" setup>
import { SlideIllustration } from '@components';
import { useClick } from '@composables';
import { useUserStore } from '@stores';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { user } = storeToRefs(storeUser);
const { t } = useI18n();
const { closeAllDialog, openDialog } = useMicroRoute();

useClick('GET_STARTED', () => {
  closeAllDialog();
  openDialog('link_kee_welcome');
});
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('SQKII_VOUCHERS_WELCOME_HEADER')"></div>
    </template>
    <template #btnTopLeft>
      <Button shape="square" variant="secondary" @click="emits('close')">
        <Icon name="arrow-left" />
      </Button>
    </template>
    <div class="text-center">
      <div
        class="text-sm flex justify-center items-center mb-5"
        v-html="t('SQKII_VOUCHERS_WELCOME_DESC')"
      ></div>
      <!-- <Icon name="voucher_img" class="w-full rounded-lg mb-5" /> -->
      <SlideIllustration
        class="mb-8"
        :illustrations="[
          { name: 'sqkii_voucher_1', type: 'gif' },
          { name: 'sqkii_voucher_2', type: 'png' },
          { name: 'sqkii_voucher_3', type: 'png' },
          { name: 'sqkii_voucher_4', type: 'png' },
          { name: 'sqkii_voucher_5', type: 'png' },
        ]"
      />
      <template v-if="!user?.mobile_number">
        <div
          class="text-sm mb-5"
          v-html="t('SQKII_VOUCHERS_WELCOME_DESC_1')"
        ></div>
        <Button
          :label="t('SQKII_VOUCHERS_BTN_SIGN_UP')"
          class="!w-[210px] mb-5"
          @click="openDialog('signup')"
        />
        <div
          class="text-sm underline text-[#00E0FF]"
          @click="closeAllDialog"
          v-html="t('SQKII_VOUCHERS_MAYBE_LATER')"
        ></div>
      </template>
      <template v-else>
        <Button
          :label="t('SQKII_VOUCHERS_BTN_GET_STARTED')"
          class="!w-[210px] mb-5"
          @click="closeAllDialog()"
        />
        <div class="text-sm" v-html="t('SQKII_VOUCHERS_WELCOME_DESC_3')"></div>
      </template>
    </div>
  </Dialog>
</template>
