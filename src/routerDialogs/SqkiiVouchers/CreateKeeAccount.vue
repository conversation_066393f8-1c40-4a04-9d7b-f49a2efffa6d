<script lang="ts" setup>
import { useAsync, useClick, useTick } from '@composables';
import {
  REGEX_DIGIT,
  REGEX_LOWERCASE,
  REGEX_SPECIAL,
  REGEX_UPPERCASE,
} from '@constants';
import {
  dateTimeFormat,
  EMAIL_REGEX,
  FULL_DATE_TIME_12H_FORMAT_IN_SECOND,
  getCountryCode,
  successNotify,
  timeCountDown,
} from '@helpers';
import { VOUCHERS } from '@repositories';
import { useUserStore, useVouchersStore } from '@stores';
import { useForm } from 'vee-validate';
import type { IAPIVouchersResponseError } from '@types';
import dayjs from 'dayjs';
import * as yup from 'yup';

interface Props {
  stage?: 1 | 2 | 3;
  email?: string;
}

const props = defineProps<Props>();

const storeUser = useUserStore();
const storeVouchers = useVouchersStore();

const { user } = storeToRefs(storeUser);
const { closeAllDialog, openDialog } = useMicroRoute();
const { t } = useI18n();
const { now } = useTick();

const el = ref<HTMLElement | null>(null);
const stage = ref<1 | 2 | 3>(props.stage || 1);
const error = ref('');
const matched = ref(false);
const resendAfter = ref('');

const countdownResend = computed(() => {
  if (!resendAfter.value) return 0;
  return +new Date(resendAfter.value) - now.value;
});

const header = computed(() => {
  const text = {
    1: t('CREATE_KEE_HEADER_1'),
    2: t('CREATE_KEE_HEADER_2'),
    3: t('CREATE_KEE_HEADER_3'),
  };
  return text[stage.value];
});

useClick('LINK_KEE', () => {
  closeAllDialog();
  openDialog('link_kee');
});

useClick('TC', () => {
  openDialog('tac', {
    onClose: () => {
      setFieldValue('tac', true);
    },
  });
});

const validationSchema = computed(() => {
  const schema = {
    1: {
      email: yup
        .string()
        .required(t('EMAIL_REQUIRED'))
        .matches(EMAIL_REGEX, t('EMAIL_INVALID')),
    },
    2: {
      password: yup
        .string()
        .required(t('PASSWORD_REQUIRED'))
        .test('password', t('PASSWORD_INVALID'), (value) => {
          const valid = {
            text_length: value.length >= 8,
            lowercase: REGEX_LOWERCASE.test(value),
            uppercase: REGEX_UPPERCASE.test(value),
            digit: REGEX_DIGIT.test(value),
            special: REGEX_SPECIAL.test(value),
          };
          matched.value = Object.values(valid).every(Boolean);
          return Object.values(valid).every(Boolean);
        }),
      cf_password: yup
        .string()
        .required(t('RE_ENTER_PASSWORD_REQUIRED'))
        .oneOf([yup.ref('password')], t('PASSWORD_NOT_MATCH')),
      tac: yup.boolean().oneOf([true], t('SIGNUP_TNC_ERROR')),
    },
    3: {
      code: yup
        .string()
        .required(t('SIGNUP_FORM_OTP_REQUIRED'))
        .length(6, t('SIGNUP_FORM_OTP_INVALID')),
    },
  };

  return yup.object().shape({ ...schema[stage.value] });
});

const { handleSubmit, values, setTouched, setFieldError, setFieldValue } =
  useForm({
    initialValues: {
      email: props.email || '',
      password: '',
      cf_password: '',
      tac: false,
      code: '',
    },
    validationSchema,
  });

const callback = handleSubmit(async (values) => {
  if (!user.value) return;
  const payload = {
    email: values.email,
    mobile_number: user.value.mobile_number,
    country: getCountryCode(user.value.mobile_number),
    sdk_linking: {
      hunter_id: user.value.hunter_id,
      user_id: user.value.id,
    },
  };

  if (stage.value === 1) {
    const { data } = await VOUCHERS.checkCredentials(payload);
    return data;
  }

  if (stage.value === 2) {
    const { data } = await VOUCHERS.requestCreateAccount({
      ...payload,
      password: values.password,
    });
    if (data)
      return {
        requested: true,
      };
  }

  if (stage.value === 3) {
    const { data } = await VOUCHERS.activeOTP({
      email: values.email,
      code: values.code,
    });
    return data;
  }
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  async onSuccess(data: any) {
    if (!data) return;
    if (stage.value === 1) {
      if (data.email) setFieldError('email', t('CREATE_KEE_EMAIL_EXIST'));
      else if (data.mobile_number && typeof data.mobile_number === 'string')
        setFieldError('email', data.mobile_number);
      else if (data.username || data.mobile_number)
        setFieldError('email', t('CREATE_KEE_USER_NAME_EXIST'));
      else {
        setTouched(false);
        stage.value = 2;
      }
    }
    if (data.requested && stage.value === 2) {
      await resendCode();
      setTouched(false);
      stage.value = 3;
    }
    if (stage.value === 3) {
      storeVouchers.setToken(data.token);
      const { data: balance } = await VOUCHERS.getUserBalance();
      storeVouchers.setUser({
        ...data.user,
        balance: balance.balance,
        currency: balance.currency,
      });
      closeAllDialog();
      successNotify({
        message: t('CREATE_KEE_SUCCESS'),
      });
      openDialog('set_pin');
    }
  },
  async onError(err: IAPIVouchersResponseError) {
    const { data, message } = err;
    switch (true) {
      case data?.info === 'user_inactive':
        setTouched(false);
        stage.value = 3;
        setFieldValue('code', '');
        await resendCode();
        break;
      case data?.info === 'otp_expired':
        error.value = t('CREATE_KEE_OTP_EXPIRED_CREDENTIALS');
        break;
      default:
        error.value = t(message);
        break;
    }
  },
});

async function resendCode() {
  try {
    const { data } = await VOUCHERS.resendActiveCode(values.email);
    resendAfter.value = data?.resend_after;
  } catch (err) {
    const { data, message } = err as IAPIVouchersResponseError;
    switch (true) {
      case data?.info === 'resend_limited':
        error.value = t('CREATE_KEE_RESEND_LIMIT_CREDENTIALS');
        break;
      default:
        error.value = t(message);
        break;
    }
  }
}

watch(
  values,
  () => {
    error.value = '';
  },
  { deep: true }
);

onMounted(async () => {
  await nextTick();
  if (props.email) await resendCode();

  el.value = document.getElementById('CREATE_KEE_RESEND_OTP');

  if (el.value)
    el.value.addEventListener('click', async () => {
      await resendCode();
    });
});

onBeforeUnmount(() => {
  if (el.value)
    el.value.removeEventListener('click', async () => {
      await resendCode();
    });
});
</script>
<template>
  <Dialog>
    <template #btnTopLeft>
      <Button
        v-if="stage === 3"
        shape="square"
        variant="secondary"
        @click="stage--"
      >
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>
    <template #header>
      <div v-html="header"></div>
    </template>
    <q-form @submit="onSubmit" class="text-center">
      <section v-show="stage === 1">
        <div class="text-sm mb-5" v-html="t('CREATE_KEE_DESC_1')"></div>
        <VeeInput
          name="email"
          :label="t('LINK_KEE_LABEL_EMAIL')"
          autofocus
          class="mb-5"
          :error="!!error"
        />
        <div
          class="card-error mt-2 mb-5 text-center"
          v-if="!!error"
          v-html="t(error)"
        ></div>
      </section>
      <section v-show="stage === 2">
        <div class="text-sm mb-5" v-html="t('CREATE_KEE_DESC_2')"></div>
        <VeeInput
          class="mb-5"
          name="password"
          :label="t('PASSWORD')"
          type="password"
        />
        <Requirements
          class="mb-3 -mt-4"
          v-if="values.password && !matched"
          :password="values.password"
          @valid="matched = $event"
        />

        <VeeInput
          class="mb-5"
          name="cf_password"
          :label="t('RE_ENTER_PASSWORD')"
          type="password"
        />

        <VeeCheckbox
          class="mb-1 text-left"
          name="tac"
          :label="t('SIGNUP_TNC_CHECKBOX')"
        />
      </section>
      <section v-show="stage === 3">
        <div class="text-sm" v-html="t('CREATE_KEE_DESC_3')"></div>
        <div class="text-lg font-bold">{{ values.email }}</div>
        <div class="text-sm mb-5" v-html="t('CREATE_KEE_DESC_4')"></div>
        <VeeOTP class="mb-5" name="code" :num-inputs="6" :error="!!error" />
        <div
          class="card-error mt-2 mb-5 text-center"
          v-if="!!error"
          v-html="t(error)"
        ></div>
        <section v-show="!!resendAfter">
          <div
            class="text-sm mb-5"
            v-html="
              t('CREATE_KEE_DESC_5', {
                TIME: dateTimeFormat(
                  resendAfter,
                  FULL_DATE_TIME_12H_FORMAT_IN_SECOND
                ),
                TIMEZONE: dayjs(Date.now()).format('Z').replace(':00', ''),
              })
            "
          ></div>
          <div class="text-sm mb-5">
            <span
              v-show="countdownResend > 0"
              v-html="
                t('CREATE_KEE_DESC_6', {
                  TIME: timeCountDown(countdownResend),
                })
              "
            ></span>
            <span
              :class="{
                'opacity-0 absolute': countdownResend > 0,
              }"
              v-html="t('CREATE_KEE_DESC_7')"
            ></span>
          </div>
        </section>
      </section>
      <Button
        :label="t('CREATE_KEE_BTN_NEXT')"
        class="!w-[210px] mb-5"
        :loading="loading"
        type="submit"
      />
      <div
        class="text-sm"
        v-if="stage === 1"
        v-html="t('CREATE_KEE_DESC_8')"
      ></div>
    </q-form>
  </Dialog>
</template>
