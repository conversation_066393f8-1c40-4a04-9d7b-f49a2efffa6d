<script lang="ts" setup>
import { useTrackData } from '@composables';
import { numeralFormat } from '@helpers';
import { VOUCHERS } from '@repositories';
import { useBAStore, useUserStore, useVouchersStore } from '@stores';
import type { IVouchersTopUp, IVouchersTopUpDetails } from '@types';

interface Props {
  status: 'processing' | 'completed' | 'failed';
  data?: IVouchersTopUp;
  id?: string;
}

const props = defineProps<Props>();

const storeVouchers = useVouchersStore();
const storeUser = useUserStore();
const storeBA = useBAStore();

const { user } = storeToRefs(storeVouchers);
const { user_brand_actions } = storeToRefs(storeBA);
const { t } = useI18n();
const { closeAllDialog, openDialog, push } = useMicroRoute();
const { track } = useTrackData();

const timeRemaning = ref(5);
const topUpInfo = ref<IVouchersTopUpDetails>();

const header = computed(() => {
  const headerStatus = {
    processing: t('TOP_UP_RESULT_HEADER_1'),
    completed: t('TOP_UP_RESULT_HEADER_2'),
    failed: t('TOP_UP_RESULT_HEADER_3'),
  };
  return headerStatus[props.status];
});

async function getTopUpInfo() {
  if (!props.id || props.status !== 'completed') return;
  const { data } = await VOUCHERS.getTopUpById(props.id);
  storeUser.fetchUser();
  topUpInfo.value = data;
}

const boundUserBA = computed(() => {
  return user_brand_actions.value.find(
    (uba) => uba?.data?.tracking_id === topUpInfo.value?.id
  );
});

const finalCrystals = computed(() => {
  if (props.status === 'failed') return 0;
  if (!boundUserBA.value?.metadata?.total)
    return topUpInfo.value?.rewarded?.crystals ?? 0;
  return boundUserBA.value?.metadata?.total;
});

onMounted(async () => {
  await nextTick();
  if (props.status === 'processing') {
    const interval = setInterval(() => {
      timeRemaning.value -= 1;
      if (timeRemaning.value === 0) {
        clearInterval(interval);
        const a = document.createElement('a');
        a.href = props.data?.confirm_link || '';
        a.target = 'self';

        const event = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: true,
        });

        a.dispatchEvent(event);
      }
    }, 1000);
  }
  await getTopUpInfo();
  if (props.status === 'completed') {
    track('sv_getvouchers_firstsession', {
      session_number: storeUser.user?.session_number,
    });
  }
});
</script>
<template>
  <Dialog hide-close :crystals="!!id">
    <template #header>
      <div v-html="header"></div>
    </template>
    <div class="text-center">
      <template v-if="status === 'processing' && !!data">
        <Icon class="!w-full rounded-lg mb-5" name="sqkii_voucher_kv" />
        <div
          class="px-5 mb-5 text-sm"
          v-if="timeRemaning"
          v-html="
            t('TOP_UP_RESULT_TEXT_1', {
              TIME: timeRemaning,
            })
          "
        ></div>
        <div
          class="text-sm"
          v-html="
            t('TOP_UP_RESULT_TEXT_2', {
              URL: data.confirm_link,
            })
          "
        ></div>
      </template>
      <template v-if="status === 'completed' && !!topUpInfo">
        <div class="silver-coin">
          <Icon class="mt-10" name="top-up-success" :size="140" />
        </div>
        <div class="-mt-10 text-sm" v-html="t('TOP_UP_RESULT_TEXT_3')"></div>
        <div class="mb-5 text-base font-bold">
          {{ user?.currency }}
          {{ numeralFormat(Number(user?.balance), '0,0.00') }}
        </div>
        <div
          class="flex flex-col items-center justify-center mb-5 mx-auto bonus"
        >
          <div class="mt-2 text-sm" v-html="t('TOP_UP_RESULT_TEXT_4')"></div>
          <div class="flex items-center">
            <div class="text-base font-bold">
              {{ numeralFormat(Number(finalCrystals)) }}
            </div>
            <Icon name="crystal" :size="25" />
            <div>!</div>
          </div>
        </div>
        <div class="flex items-center gap-5 flex-nowrap">
          <Button
            :label="t('TOP_UP_RESULT_BTN_BACK_MAIN')"
            variant="purple"
            @click="closeAllDialog"
            size="max-content"
            class="flex-1"
          />
          <Button
            :label="t('TOP_UP_RESULT_BTN_USE_VOUCHERS')"
            @click="
              closeAllDialog();
              push('use_sqkii_vouchers');
            "
            size="max-content"
            class="flex-1"
          />
        </div>
      </template>
      <template v-if="status === 'failed'">
        <div class="silver-coin">
          <Icon class="mt-10" name="top-up-failed" :size="140" />
        </div>
        <div
          class="mb-5 -mt-10 text-sm"
          v-html="t('TOP_UP_RESULT_TEXT_5')"
        ></div>
        <div class="flex items-center gap-5 flex-nowrap">
          <Button
            :label="t('TOP_UP_RESULT_BTN_BACK')"
            variant="purple"
            @click="closeAllDialog"
            size="max-content"
            class="flex-1"
          />
          <Button
            :label="t('TOP_UP_RESULT_BTN_TRY_AGAIN')"
            @click="
              closeAllDialog();
              openDialog('get_sqkii_vouchers');
            "
            size="max-content"
            class="flex-1"
          />
        </div>
      </template>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/big-glow-2.png');
  background-size: cover;
  background-position: center;
  margin-left: -30px;
  margin-top: -50px;
}

.bonus {
  background-image: url(/imgs/top-up-bonus.png);
  max-width: 100%;
  width: 75vw;
  height: 20vw;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
