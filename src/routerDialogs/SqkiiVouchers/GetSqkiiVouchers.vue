<script lang="ts" setup>
import { numeralFormat } from '@helpers';
import { VOUCHERS } from '@repositories';
import { useBAStore, useUserStore, useVouchersStore } from '@stores';
import { debounce } from 'lodash';
import { useForm } from 'vee-validate';
import type { IUsedPromoCode } from '@types';
import * as yup from 'yup';
import { useTrackData } from '@composables';

const storeVouchers = useVouchersStore();
const storeBA = useBAStore();
const storeUser = useUserStore();

const { user } = storeToRefs(storeVouchers);
const { brand_actions } = storeToRefs(storeBA);
const { settings } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

const MAX_AMOUNT = 1000;
const crystals = ref(0);
const hasFeatured = computed(() => {
  const sv_1BA = brand_actions.value.find((ba) => ba.unique_id === 'sv_1');
  return sv_1BA?.featured;
});

const multiplierNumber = computed(() => {
  return {
    featured: settings.value?.brand_action?.multiplier?.featured || 3,
    firstTime: settings.value?.brand_action?.multiplier?.first_time || 2,
  };
});

const validationSchema = yup.object({
  amount: yup
    .string()
    .required(t('AMOUNT_REQUIRED'))
    .test('invalid', t('AMOUNT_INVALID_NUMBER'), (value) => {
      const data = value.split('.');
      if (data[1] && +data[1] === 0) return true;
      if (!data.map((d) => +d).every(Boolean)) return false;
      return !isNaN(Number(value));
    })
    .test('limit-two-decimals', t('AMOUNT_INVALID_DECIMALS'), (value) => {
      if (!value.includes('.')) return true;
      return value.includes('.') && value.split('.')[1]?.length <= 2;
    })
    .test('min', t('AMOUNT_INVALID'), (value) => {
      return Number(value) >= 10;
    }),
});

const { handleSubmit, errors, meta, setFieldValue, values, setFieldError } =
  useForm({
    initialValues: {
      amount: '10',
      promo_code: '',
      newCrystal: 0,
      bonusCrystal: 0,
    },
    validationSchema,
  });

const onSubmit = handleSubmit(async (values) => {
  const inputAmount = Number(values.amount);
  const currentBalance = Number(user.value?.balance);

  if (currentBalance > MAX_AMOUNT) {
    return setFieldError(
      'amount',
      t('SQKII_VOUCHER_MAX_AMOUNT_1', { MAX_AMOUNT })
    );
  }

  if (currentBalance + inputAmount > MAX_AMOUNT) {
    return setFieldError(
      'amount',
      t('SQKII_VOUCHER_MAX_AMOUNT_2', { MAX_AMOUNT })
    );
  }

  if (!user.value?.total_topup)
    return openDialog('confirm_first_bonus', {
      amount: Number(values.amount),
      promo_code: values.promo_code,
      crystals: crystals.value,
      newCrystal: rewardContext.value.totalReward,
    });

  return openDialog('enter_pin_top_up', {
    amount: Number(values.amount),
    promo_code: values.promo_code,
  });
});

const debounceGetBonusRate = debounce(getBonusRate, 500);

const rewardContext = computed(() => {
  const isFirstTime = !user.value?.total_topup;
  const isFeatured = hasFeatured.value;
  const firstTimeMultiplier = isFirstTime
    ? multiplierNumber.value.firstTime
    : 1;
  const featuredMultiplier = isFeatured ? multiplierNumber.value.featured : 1;
  const bonusReward = values.bonusCrystal;
  const baseReward = crystals.value;

  const totalReward =
    baseReward * firstTimeMultiplier * featuredMultiplier + bonusReward;
  const hasMultiplier = firstTimeMultiplier * featuredMultiplier > 1;
  const multiplierBonus = isFirstTime && isFeatured;

  return {
    isFirstTime,
    isFeatured,
    firstTimeMultiplier,
    featuredMultiplier,
    bonusReward,
    baseReward,
    totalReward,
    hasMultiplier,
    multiplierBonus,
  };
});

async function getBonusRate() {
  if (!values.amount) {
    crystals.value = 0;
    return;
  }
  const { data } = await VOUCHERS.checkBonusRate({
    amount: Number(values.amount),
    item: 'crystals',
    promo_code: values.promo_code || undefined,
  });
  crystals.value = data.crystals;
  setFieldValue('newCrystal', rewardContext.value.totalReward);
}

watch(() => values.amount, debounceGetBonusRate);

onMounted(async () => {
  await getBonusRate();
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('GET_SQKII_VOUCHERS_HEADER')"></div>
    </template>
    <q-form @submit="onSubmit">
      <div
        class="flex justify-between items-center bg-[#091a3c50] p-3 rounded mb-5"
      >
        <div
          class="text-xs opacity-50"
          v-html="t('GET_SQKII_VOUCHERS_TEXT_1')"
        ></div>
        <div class="text-base">
          {{ user?.currency || 'S$' }}
          {{ numeralFormat(Number(user?.balance || 0), '0,0.00') }}
        </div>
      </div>
      <div class="mb-2 text-sm" v-html="t('GET_SQKII_VOUCHERS_TEXT_2')"></div>
      <div class="relative">
        <div
          class="absolute z-50 w-10 h-10 text-2xl font-bold text-white pointer-events-none left-3 top-1.5"
          :class="{
            '!text-[#ff0000]': errors.amount && meta.touched,
          }"
        >
          {{ user?.currency || 'S$' }}
        </div>
        <VeeInput
          name="amount"
          class="relative w-full mb-5"
          input-class="!text-2xl font-bold text-right"
          autofocus
        />
      </div>
      <div
        class="mb-5 -mt-3 text-xs opacity-70"
        v-html="t('GET_SQKII_VOUCHERS_TEXT_3')"
      ></div>

      <div
        class="relative flex flex-col flex-nowrap items-center justify-center mb-5 bonus p-2 py-6 mr-6"
        :class="{
          '!max-w-[75vw]': rewardContext.hasMultiplier,
        }"
      >
        <div
          class="mt-1 text-sm"
          v-if="values.promo_code"
          v-html="
            t('GET_SQKII_VOUCHERS_TEXT_7', {
              CODE: values.promo_code,
            })
          "
        ></div>
        <div
          class="text-sm text-center w-full"
          :class="{
            'order-2': rewardContext.isFirstTime,
            'pr-10': rewardContext.isFeatured || rewardContext.isFirstTime,
          }"
          v-html="
            rewardContext.isFeatured && rewardContext.isFirstTime
              ? t('GET_SQKII_VOUCHERS_TEXT_10')
              : rewardContext.isFirstTime
              ? t('GET_SQKII_VOUCHERS_TEXT_8')
              : t('GET_SQKII_VOUCHERS_TEXT_4')
          "
        ></div>
        <div class="flex items-center">
          <div
            class="mr-2 text-sm"
            v-if="rewardContext.hasMultiplier"
            v-html="t('GET_SQKII_VOUCHERS_TEXT_9')"
          ></div>
          <div
            v-if="rewardContext.hasMultiplier"
            class="text-base font-bold line-through opacity-50"
          >
            {{
              numeralFormat(
                rewardContext.isFeatured
                  ? rewardContext.baseReward * rewardContext.firstTimeMultiplier
                  : rewardContext.baseReward
              )
            }}
          </div>
          <div class="ml-2 text-base font-bold">
            {{ numeralFormat(rewardContext.totalReward) }}
          </div>
          <Icon name="crystal" :size="25" />
          <div v-if="rewardContext.hasMultiplier">!</div>
        </div>

        <Icon
          v-if="rewardContext.isFirstTime"
          class="absolute mt-1 -translate-x-1/2 -translate-y-1/2 right-5 top-1/2"
          name="question-mark"
          @click="
            openDialog('first_bonus_crystals', {
              hiddenButton: true,
              amount: Number(values.amount),
              crystals,
              newCrystal: rewardContext.totalReward,
            })
          "
        />
        <Icon
          v-if="rewardContext.hasMultiplier"
          class="absolute -right-[3px] top-2/3 translate-x-1/2 pointer-events-none"
          :name="rewardContext.isFeatured ? 'featured_sv_tag' : 'limit-offer'"
          :size="68"
        />
        <!-- <Icon
          v-if="rewardContext.isFeatured"
          class="absolute -right-[0] top-0 -translate-y-[25%] translate-x-[calc(50%-17%)] pointer-events-none"
          name="featured_sv_x3_deco"
          :size="60"
        /> -->
      </div>
      <div class="mb-5 text-center">
        <Button
          :label="t('GET_SQKII_VOUCHERS_BTN_NEXT')"
          class="!w-[210px]"
          type="submit"
        />
      </div>
      <div
        class="underline text-[#00E0FF] text-sm text-center mb-5"
        v-html="t('GET_SQKII_VOUCHERS_TEXT_5')"
        @click="
          openDialog('enter_top_up_promo', {
            onUsed(data: IUsedPromoCode) {
              setFieldValue('promo_code', data.code);
              setFieldValue('bonusCrystal', data.bonusCrystal);
              setFieldValue('newCrystal', rewardContext.totalReward);
            },
          });
          track('sv_promocode_abandon_getvouchers');
        "
      ></div>
      <div
        class="text-xs text-center opacity-70"
        v-html="t('GET_SQKII_VOUCHERS_TEXT_6')"
      ></div>
    </q-form>
  </Dialog>
</template>
<style lang="scss" scoped>
.bonus {
  background-image: url(/imgs/top-up-bonus.png);

  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
