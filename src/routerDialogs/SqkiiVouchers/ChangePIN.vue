<script lang="ts" setup>
import { useAsync, useTick } from '@composables';
import { successNotify, timeCountDown } from '@helpers';
import { VOUCHERS } from '@repositories';
import { useForm } from 'vee-validate';
import type { IAPIVouchersResponseError } from '@types';
import * as yup from 'yup';

const { t } = useI18n();
const { closeAllDialog, openDialog } = useMicroRoute();
const { now } = useTick();

const stage = ref<1 | 2>(1);
const pin_code = ref('');
const error = ref('');
const locked_until = ref('');

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  return +new Date(locked_until.value) - now.value;
});

const validationSchema = computed(() => {
  const schema = {
    1: {
      old_pin: yup
        .string()
        .required(t('PIN_REQUIRED'))
        .length(6, t('SIGNUP_FORM_OTP_INVALID')),
    },
    2: {
      pin_code: yup
        .string()
        .required(t('CONFIRM_PIN_REQUIRED'))
        .length(6, t('SIGNUP_FORM_OTP_INVALID')),
    },
  };
  return yup.object().shape({ ...schema[stage.value] });
});

const { handleSubmit, values, setTouched, setFieldValue } = useForm({
  initialValues: {
    old_pin: '',
    pin_code: '',
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  if (stage.value === 1) {
    const { data } = await VOUCHERS.changePIN({
      old_pin: values.old_pin,
    });
    return data;
  }
  const { data } = await VOUCHERS.changePIN({
    old_pin: values.old_pin,
    pin_code: values.pin_code,
  });
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onSuccess() {
    successNotify({
      message: t('CHANGE_PIN_SUCCESS'),
    });
    closeAllDialog();
  },
  onError(err: IAPIVouchersResponseError) {
    const { data, message } = err;
    switch (true) {
      case data?.info === 'temp_locked':
        error.value = t('LINK_KEE_MAX_CREDENTIALS');
        locked_until.value = data.locked_until;
        break;
      case data?.info === 'incorrect_pin':
        error.value = t('PIN_INVALID_CREDENTIALS_ATTEMPTS');
        break;
      case !!data?.verify:
        pin_code.value = values.old_pin;
        setTouched(false);
        stage.value = 2;
        break;
      default:
        error.value = t(message);
        break;
    }
  },
});

watch(
  values,
  () => {
    error.value = '';
  },
  { deep: true }
);
</script>
<template>
  <Dialog>
    <template #btnTopLeft>
      <Button
        v-if="stage === 2"
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="
          stage = 1;
          setFieldValue('pin_code', '');
        "
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div
        v-html="stage === 1 ? t('CHANGE_PIN_HEADER') : t('CHANGE_PIN_HEADER_1')"
      ></div>
    </template>
    <q-form class="text-center">
      <div
        class="text-sm mb-5"
        v-html="stage === 1 ? t('CHANGE_PIN_DESC') : t('CHANGE_PIN_DESC_1')"
      ></div>
      <section v-show="stage === 1">
        <VeeOTP
          class="mb-5"
          name="old_pin"
          :num-inputs="6"
          :error="!!error"
          input-type="password"
        />
      </section>
      <section v-show="stage === 2">
        <VeeOTP
          class="mb-5"
          name="pin_code"
          :num-inputs="6"
          :error="!!error"
          input-type="password"
        />
      </section>
      <div
        class="card-error mt-2 mb-5 text-center"
        v-if="!!error"
        v-html="t(error)"
      ></div>

      <div
        v-if="stage === 1"
        class="underline text-link text-sm mb-5"
        @click="
          closeAllDialog();
          openDialog('forgot_pin');
        "
        v-html="t('CHANGE_PIN_DESC_2')"
      ></div>
      <Button
        :label="
          countdown > 0
            ? timeCountDown(countdown)
            : stage === 1
            ? t('CHANGE_PIN_BTN_SUBMIT')
            : t('CHANGE_PIN_BTN_SUBMIT_1')
        "
        :loading="loading"
        @click="onSubmit"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
