<script lang="ts" setup>
import { useMediaDevice, useTrackData } from '@composables';
import { errorNotify } from '@helpers';

interface Emits {
  (e: 'scan', code: string): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();
const { track } = useTrackData();

const streamVideo = ref<MediaStream | null>(null);

const { request } = useMediaDevice(
  {
    video: {
      facingMode: 'environment',
    },
    audio: false,
  },
  requestCameraSuccess,
  requestCameraFailed
);

function requestCameraSuccess(stream: MediaStream) {
  streamVideo.value = stream;
  closeDialog('camera_permission');
  track('camera_pop_up', {
    result: false,
  });
}

function requestCameraFailed() {
  errorNotify({
    message: t('CAMERA_SCAN_PROMO_TOPUP_NOT_DETECTED'),
    timeout: 8000,
  });
  closeDialog('camera_permission');
  track('camera_pop_up', {
    result: false,
  });
}

function onDecode(code: string) {
  emits('scan', code);
  closeDialog('scan_top_up_promo');
}

onMounted(async () => {
  await nextTick();
  if (!!LocalStorage.getItem('camera_permission')) request();
  else {
    openDialog('camera_permission', {
      onRequest: request,
      description: t('SQKII_VOUCHERS_CAMERA_PERMISSION_DESC'),
    });
  }
});
</script>

<template>
  <div class="scan flex flex-col flex-nowrap fullscreen overflow-hidden">
    <div
      class="relative flex justify-center items-center w-full h-16 px-20 mb-5 shrink-0"
    >
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="closeDialog('scan_top_up_promo')"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold text-center w-full"
        v-html="t('SCAN_TOP_UP_PROMO_HEADER')"
      ></div>
    </div>
    <div
      class="flex flex-col flex-nowrap justify-center items-center overflow-y-auto px-10 pb-5 text-center"
    >
      <div class="size-[297px] bg-[#555] rounded-lg mx-auto relative mb-10">
        <div
          class="w-full h-full flex justify-center items-center"
          v-if="!streamVideo"
        >
          <q-spinner color="primary" size="5em" :thickness="3" />
        </div>
        <StreamBarcodeReader
          v-if="streamVideo"
          :stream="streamVideo"
          @decode="onDecode"
          class="!w-full !h-full rounded-lg"
        />
      </div>
      <div class="px-10 mb-7" v-html="t('SCAN_TOP_UP_PROMO_DESC')"></div>
      <Button
        :label="t('SCAN_TOP_UP_PROMO_BTN_ID')"
        variant="purple"
        @click="closeDialog('scan_top_up_promo')"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.scan {
  background: linear-gradient(
      180deg,
      #1f7c90 -13.42%,
      rgba(145, 36, 254, 0) 50%
    ),
    #090422;
}
</style>
