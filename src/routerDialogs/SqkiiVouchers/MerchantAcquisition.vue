<script lang="ts" setup>
import { useForm } from 'vee-validate';
import { errorNotify, isMatchSeasonNumber } from '@helpers';
import { USER } from '@repositories';
import { useAsync } from '@composables';
import type { IAPIResponseError, ICountryRegion } from '@types';
import * as yup from 'yup';

const { t } = useI18n();
const { openDialog, closeDialog } = useMicroRoute();

const country = ref<ICountryRegion>();

const validationSchema = yup.object({
  name: yup.string().required(t('NAME_REQUIRED')),
  email: yup.string().required(t('EMAIL_REQUIRED')).email(t('EMAIL_INVALID')),
  mobile_number: yup
    .string()
    .required(t('MOBILE_NUMBER_REQUIRED'))
    .test(
      'mobile-number',
      t('MERCHANT_ACQUISITION_INVALIDMOBILENUMBER'),
      (value) => {
        const number = country.value?.code + value;
        return isMatchSeasonNumber(number, 'SG');
      }
    ),
  name_of_business: yup.string().required(t('NAME_OF_BUSINESS_REQUIRED')),
});

const { handleSubmit } = useForm({
  initialValues: {
    name: '',
    email: '',
    mobile_number: '',
    name_of_business: '',
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  const mobile_number = `${country.value?.code}${values.mobile_number}`.replace(
    /\+/g,
    ''
  );
  const payload = { ...values, mobile_number };
  const { data } = await USER.submitSVReferral(payload);
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    closeDialog('merchant_acquisition');
    openDialog('merchant_acquisition_success');
  },
  onError(err: IAPIResponseError) {
    const { error_message } = err;
    errorNotify({
      message: error_message,
    });
  },
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('MERCHANT_ACQUISITION_HEADER')"></div>
    </template>
    <q-form @submit="onSubmit" class="text-center">
      <div class="text-sm mb-5" v-html="t('MERCHANT_ACQUISITION_DESC_1')"></div>
      <div class="px-5 mb-5">
        <VeeInput name="name" :label="t('NAME')" class="mb-5" autofocus />
        <VeeInput name="email" :label="t('EMAIL_ADDRESS')" class="mb-5" />
        <VeeInputCountry
          class="mb-5"
          :label="t('MOBILE_NUMBER')"
          name="mobile_number"
          @update:country="country = $event"
          autofocus
          :customCountries="[
            {
              name: 'Singapore',
              code: '+65',
              iso: 'SG',
              flag: 'https://cdn.kcak11.com/CountryFlags/countries/sg.svg',
              mask: ['#### ####'],
              currency: 'SGD',
              currencyName: 'Singapore Dollar (SGD)',
              url: 'https://huntthemouse.sqkii.com',
            },
          ]"
        />
        <VeeInput
          name="name_of_business"
          :label="t('NAME_BUSINESS')"
          class="mb-5"
        />
      </div>
      <div
        class="text-sm opacity-70 mb-5"
        v-html="t('MERCHANT_ACQUISITION_DESC_2')"
      ></div>
      <Button :label="t('SUBMIT')" type="submit" :loading="loading" />
    </q-form>
  </Dialog>
</template>
