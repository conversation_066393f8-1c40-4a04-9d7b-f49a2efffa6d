<script lang="ts" setup>
import { useAsync, useTick, useTrackData } from '@composables';
import { VOUCHERS } from '@repositories';
import { useForm } from 'vee-validate';
import { timeCountDown } from '@helpers';
import { useUserStore } from '@stores';
import type { IAPIVouchersResponseError } from '@types';
import * as yup from 'yup';

interface Props {
  amount: number;
  promo_code?: string;
}

const props = defineProps<Props>();

const storeUser = useUserStore();

const { user } = storeToRefs(storeUser);
const { t } = useI18n();
const { closeDialog, closeAllDialog, openDialog } = useMicroRoute();
const { now } = useTick();
const { track } = useTrackData();

const error = ref('');
const locked_until = ref('');

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  return +new Date(locked_until.value) - now.value;
});

const validationSchema = yup.object().shape({
  pin_code: yup
    .string()
    .required(t('PIN_REQUIRED'))
    .length(6, t('SIGNUP_FORM_OTP_INVALID')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    pin_code: '',
  },
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  if (!user.value) return;
  const payload = {
    pin_code: values.pin_code,
    amount: props.amount,
    promo_code: props.promo_code,
    sdk_linking: {
      user_id: user.value.id,
    },
    campaign: process.env.CAMPAIGN_NAME || '',
  };
  const { data } = await VOUCHERS.topUp(payload);
  return data;
});

const {
  loading,
  status,
  execute: onSubmit,
} = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onSuccess(data) {
    closeAllDialog();
    openDialog('top_up_result', {
      data,
      status: 'processing',
    });
  },
  onError(err: IAPIVouchersResponseError) {
    const { data, message } = err;
    switch (true) {
      case data?.info === 'temp_locked':
        error.value = t('LINK_KEE_MAX_CREDENTIALS');
        locked_until.value = data.locked_until;
        break;
      case data?.info === 'incorrect_pin':
        error.value = t('PIN_INVALID_CREDENTIALS_ATTEMPTS');
        break;
      default:
        error.value = t(message);
        break;
    }
  },
  onSettled() {
    track('authorization_pin', {
      type: 'topup',
      status: status.value,
    });
  },
});

function handleBack() {
  if (loading.value) return;
  closeDialog('enter_pin_top_up');
}

watch(
  values,
  () => {
    error.value = '';
  },
  { deep: true }
);
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="handleBack"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('ENTER_PIN_TOPUP_HEADER')"></div>
    </template>
    <q-form class="text-center">
      <div class="text-sm mb-5" v-html="t('ENTER_PIN_TOPUP_DESC')"></div>
      <VeeOTP
        class="mb-5"
        name="pin_code"
        :num-inputs="6"
        :error="!!error"
        input-type="password"
      />
      <div
        class="card-error mt-2 mb-5 text-center"
        v-if="!!error"
        v-html="t(error)"
      ></div>

      <div
        class="underline text-link text-sm mb-5"
        @click="
          closeAllDialog();
          openDialog('forgot_pin');
        "
        v-html="t('ENTER_PIN_TOPUP_DESC_1')"
      ></div>
      <Button
        :label="
          countdown > 0
            ? timeCountDown(countdown)
            : t('ENTER_PIN_TOPUP_BTN_SUBMIT')
        "
        :disable="countdown > 0"
        :loading="loading"
        @click="onSubmit"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
