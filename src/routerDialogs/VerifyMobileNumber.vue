<script lang="ts" setup>
import { useBAStore, useUserStore } from '@stores';
import { useAsync, useClick, useTick } from '@composables';
import { useForm } from 'vee-validate';
import { AUTH } from '@repositories';
import {
  FULL_DATE_TIME_24H_FORMAT,
  dateTimeFormat,
  timeCountDown,
} from '@helpers';
import type { IAPIResponseError } from '@types';
import * as yup from 'yup';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeBA = useBAStore();

const { user, seasonCode } = storeToRefs(storeUser);
const { t } = useI18n();
const { now } = useTick();
const { openDialog } = useMicroRoute();

useClick('ZALO', () => {
  openDialog('zalo');
});

useClick('resendOTP', async () => {
  if (+new Date(values.next_otp_at) > now.value) return;
  await resendOTP();
});

const { handleSubmit, values, setFieldError, setFieldValue } = useForm({
  initialValues: {
    otp: '',
    next_otp_at: '',
    expire_at: '',
  },
  validationSchema: {
    otp: yup
      .string()
      .required(t('SIGNUP_FORM_OTP_REQUIRED'))
      .length(6, t('SIGNUP_FORM_OTP_INVALID')),
  },
});

const callback = handleSubmit(async (values) => {
  const { data } = await AUTH.verifyMobileNumber({
    otp: values.otp,
  });
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  async onSuccess() {
    await storeUser.fetchUser();
    await storeBA.fetchBrandAction();
    emits('close');
  },
  onError(err: IAPIResponseError) {
    handleErrors(err);
  },
});

const { execute: resendOTP } = useAsync({
  async fn() {
    const { data } = await AUTH.verifyMobileNumber({});
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    setFieldValue('expire_at', data.expire_at);
    setFieldValue('next_otp_at', data.next_otp_at);
  },
  onError(err: IAPIResponseError) {
    handleErrors(err);
  },
});

function handleErrors(err: IAPIResponseError) {
  const { data, error_message } = err;
  switch (error_message) {
    case 'invalid_otp':
      if (data.attempts === 4) setFieldError('otp', t('OTP_LIMIT_4'));
      else setFieldError('otp', t('INVALID_OTP'));
      break;
    case 'otp_limited':
      setFieldError('otp', t('REACH_LIMIT_RESEND'));
      break;
    default:
      setFieldError('otp', t(error_message));
      break;
  }
}

onMounted(async () => {
  await resendOTP();
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('OTP_HEADER')"></div>
    </template>

    <q-form @submit="onSubmit" class="flex flex-col flex-nowrap">
      <div class="text-base text-center mb-5">
        <span v-html="t('SIGNUP_FORM_OTPTEXT_1')"></span>
        <br />
        <b class="text-xl">+{{ user?.mobile_number }}</b>
        <br />
        <span v-html="t('SIGNUP_FORM_OTPTEXT_3')"></span>
      </div>
      <VeeOTP class="mb-5" name="otp" :num-inputs="6" />
      <div class="text-xs text-center mb-3" v-if="values.expire_at">
        {{
          t('OTP_VALID_UNTIL', {
            TIME: dateTimeFormat(values.expire_at, FULL_DATE_TIME_24H_FORMAT),
          })
        }}
      </div>
      <div
        class="text-xs text-center mb-5"
        v-html="
          +new Date(values.next_otp_at) > now
            ? t('SIGNUP_FORM_OTPTEXT_RESEND', {
                TIME: timeCountDown(+new Date(values.next_otp_at) - now),
              })
            : t('SIGNUP_FORM_OTPTEXT_RESEND_NOCOUNTDOWN')
        "
      ></div>
      <div class="text-center mb-5">
        <Button
          type="submit"
          :loading="loading"
          :label="t('SIGNUP_FORM_BUTTON')"
        />
      </div>
      <div
        class="text-xs text-center"
        v-if="['VN'].includes(seasonCode)"
        v-html="t('SIGNUP_FORM_ZALO')"
      ></div>
      <!-- <div class="column flex-nowrap text-center mt-20">
        <div class="q-mx-auto mb-16">
          <OTP
            :should-auto-focus="true"
            :num-inputs="6"
            @on-change="onChangeOTP"
            :error="!!errorOTP"
          />
        </div>

        <div
          class="card-error mb-12"
          v-if="errorOTP"
          v-html="t(errorOTP)"
        ></div>

        <div class="text-xs mb-12" v-if="expireAt">
          {{
            t('OTP_VALID_UNTIL', {
              otp_expiration: helpers.converDateTime(
                expireAt,
                'DD MMM YYYY, hh:mma'
              ),
            })
          }}
        </div>

        <div
          class="text-xs"
          v-if="errorOTP === 'OTP_ERROR_LIMIT_ZALO'"
          v-html="
            t('OTP_LIMIT_ZALO', {
              TIME: `${
                helpers.convertMilisecondsToDays(helpers.getNextDay(now)).hours
              }h ${helpers.convertMilisecondsToDays(now).mins}m`,
            })
          "
        ></div>

        <div
          class="text-xs"
          v-else-if="
            errorOTP === 'reach_limit_resend' && countdownNextOtpAt > 0
          "
          v-html="
            t('OTP_LOCKED_NUMBER', {
              TIME: `${
                helpers.convertMilisecondsToDays(countdownNextOtpAt * 1000).mins
              }m ${
                helpers.convertMilisecondsToDays(countdownNextOtpAt * 1000).secs
              }s`,
            })
          "
        ></div>

        <div
          class="text-xs"
          v-else-if="errorOTP !== 'reach_limit_resend'"
          v-html="
            countdownNextOtpAt > 0
              ? t('SIGNUP_FORM_OTPTEXT_RESEND', {
                  TIME: countdownNextOtpAt,
                })
              : t('SIGNUP_FORM_OTPTEXT_RESEND_NOCOUNTDOWN')
          "
        ></div>

        <Button
          @click="actionBtn"
          :disable="otpCode.length < 6 || !!errorOTP"
          :loading="loading"
          class="q-mx-auto mt-20"
          :label="t('SIGNUP_FORM_BUTTON')"
        />

        <div
          class="text-xs color-white text-center mt-20"
          v-html="t('SIGNUP_FORM_ZALO')"
        ></div>
      </div> -->
    </q-form>
  </Dialog>
</template>
