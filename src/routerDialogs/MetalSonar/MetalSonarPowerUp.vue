<script lang="ts" setup>
import { useDialogStore } from '@stores';
import { useTrackData } from '@composables';
import { SlideIllustration } from '@components';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeDialog = useDialogStore();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

const checkbox = ref(false);

function onClose() {
  emits('close');
  storeDialog.metalSonarPowerUpTicked = true;
  if (checkbox.value)
    LocalStorage.set('metal_sonar_power_up', new Date().toISOString());
  else LocalStorage.remove('metal_sonar_power_up');
}
</script>

<template>
  <Dialog
    @close="
      onClose();
      track('auto_window_buttons', {
        screen_type: 'metal_sonar_power_up',
        action: 'x',
      });
    "
  >
    <template #header>
      <div v-html="t('METAL_SONAR_POWERUP_HEADING')"></div>
    </template>
    <div class="text-center">
      <div
        class="text-lg font-bold mb-5"
        v-html="t('METAL_SONAR_POWERUP_TITLE')"
      ></div>

      <SlideIllustration
        class="mb-5"
        :illustrations="
          [
            'sonar/intro_1_',
            'sonar/intro_2_',
            'sonar/intro_3_',
            'sonar/intro_4_',
          ].map((i) => ({ name: i, type: 'png' }))
        "
        multiple-lang
      />

      <div class="text-sm mb-5" v-html="t('METAL_SONAR_POWERUP_DESC')"></div>

      <div class="flex flex-nowrap gap-4 mb-5">
        <Button
          class="flex-1"
          size="max-content"
          :label="t('METAL_SONAR_POWERUP_BUTTON_LATER')"
          variant="purple"
          @click="
            onClose();
            track('auto_window_buttons', {
              screen_type: 'metal_sonar_power_up',
              action: 'button',
            });
          "
        />
        <Button
          class="flex-1"
          size="max-content"
          :label="t('METAL_SONAR_POWERUP_BUTTON_CHECKITOUT')"
          @click="
            onClose();
            openDialog('sonar_terms');
            track('auto_window_other', {
              screen_type: 'metal_sonar_power_up',
              other_button: 'check_it_out',
            });
          "
        />
      </div>
      <q-checkbox
        v-model="checkbox"
        :label="t('METAL_SONAR_POWERUP_CHECKBOX')"
        @click="
          track('auto_window_other', {
            screen_type: 'metal_sonar_power_up',
            other_button: 'checkbox',
          })
        "
      />
    </div>
  </Dialog>
</template>
