<script lang="ts" setup>
import { SlideIllustration } from '@components';

const { t } = useI18n();
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('SONAR_TERMS_TITLE')"></div>
    </template>
    <template #icon-center>
      <Icon
        class="absolute -top-16 left-1/2 -translate-x-1/2 pointer-events-none"
        name="metal-sonar"
        :size="75"
      />
    </template>
    <div class="relative text-center">
      <div class="text-sm mb-2" v-html="t('SONAR_TERMS_DESC')"></div>
      <div
        class="frame-discount-powerup text-lg font-bold mb-5"
        v-html="t('SONAR_TERMS_DESC_1')"
      ></div>
      <SlideIllustration
        class="mb-5"
        :illustrations="
          [
            'sonar/intro_1_',
            'sonar/intro_2_',
            'sonar/intro_3_',
            'sonar/intro_4_',
          ].map((i) => ({ name: i, type: 'png' }))
        "
        multiple-lang
      />

      <div class="text-sm" v-html="t('SONAR_TERMS_DESC_2')"></div>
    </div>
  </Dialog>
</template>
