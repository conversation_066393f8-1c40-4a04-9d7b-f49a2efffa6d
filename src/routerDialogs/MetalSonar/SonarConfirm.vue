<script lang="ts" setup>
import { useAsync, useCoinSonar, useTrackData } from '@composables';
import { METAL_SONAR } from '@repositories';
import { useMapStore, useUserStore } from '@stores';
import type {
  IMetalSonar,
  IMetalSonarPrice,
  ISilverCoinProperties,
  IAPIResponseError,
} from '@types';

interface Props {
  selectedPrice: IMetalSonarPrice;
  selectedCircle: ISilverCoinProperties;
}

interface Emits {
  (e: 'close'): void;
  (e: 'back'): void;
  (e: 'getCrystals'): void;
  (e: 'usedMetalSonar', data: IMetalSonar): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeMap = useMapStore();

const { t } = useI18n();
const { crystals } = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { handleError } = useCoinSonar();
const { track } = useTrackData();

const { loading, execute: handleConfirmSonar } = useAsync({
  fn: async () => {
    track('metal_sonar_buttons', {
      screen: 'confirm',
      button: 'accept',
    });

    if (
      crystals.value < props.selectedPrice.price &&
      !props.selectedPrice.can_use_power_up
    ) {
      emits('getCrystals');
      return;
    }
    storeMap.hideSonarHighlight = true;
    const [lng, lat] = lastLocations.value;
    const { data } = await METAL_SONAR.use({
      lng,
      lat,
      radius: props.selectedPrice.radius,
      id: props.selectedCircle.properties._id,
      item_id: props.selectedPrice.item_id,
    });
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    storeUser.fetchUser();
    storeUser.fetchInventory();
    emits('usedMetalSonar', data);
  },
  onError(error: IAPIResponseError) {
    handleError(error);
    emits('close');
  },
});
</script>
<template>
  <Dialog hide-close>
    <template #header> {{ t('SONAR_CONFIRM_TITLE') }}</template>
    <template #icon-center>
      <Icon
        class="absolute -top-16 left-1/2 -translate-x-1/2 pointer-events-none"
        name="metal-sonar"
        :size="75"
      />
    </template>
    <template #btnTopLeft>
      <Button
        shape="square"
        variant="secondary"
        @click="
          emits('back');
          track('metal_sonar_buttons', {
            screen: 'confirm',
            button: 'back',
          });
        "
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <div class="text-center">
      <div
        class="text-sm mb-5"
        v-html="
          t('SONAR_CONFIRM_DESC', {
            RADIUS: selectedPrice.radius,
          })
        "
      ></div>
      <Button
        class="mb-8"
        :title="t('SONAR_CONFIRM_BUTTON')"
        :amount="selectedPrice.price"
        :resource-type="
          selectedPrice.can_use_power_up ? 'metal-sonar' : 'dbs_crystal'
        "
        :loading="loading"
        @click="handleConfirmSonar"
      />
    </div>
  </Dialog>
</template>
