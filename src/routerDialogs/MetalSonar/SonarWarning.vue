<script lang="ts" setup>
import { useDialogStore } from '@stores';
import { useTrackData } from '@composables';
import gsap, { Power2 } from 'gsap';

const storeDialog = useDialogStore();

const { t } = useI18n();
const { closeDialog } = useMicroRoute();
const { track } = useTrackData();

function goToSonarGUI() {
  gsap.to(['.gui-top-right', '.gui-bottom-right'], {
    duration: 0.5,
    x: 100,
    ease: Power2.easeInOut,
  });
  gsap.to(['.gui-top-left'], {
    duration: 0.5,
    x: -100,
    ease: Power2.easeInOut,
  });
  // gsap.to(['.toggle-popup', '.toggle-popup-bottom'], {
  //   duration: 0.5,
  //   y: -300,
  //   ease: Power2.easeInOut,
  // });
  gsap.to('.marquee', {
    duration: 0.5,
    y: 200,
    ease: Power2.easeInOut,
  });

  storeDialog.showCoinSonarGUI = true;

  track('metal_sonar_buttons', {
    screen_type: 'sonar_warning',
    button: 'next',
  });

  closeDialog('sonar_warning');
}
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('SONAR_WARNING_TITLE')"></div>
    </template>
    <template #btnTopLeft>
      <Button
        shape="square"
        variant="secondary"
        @click="
          closeDialog('sonar_warning');
          track('metal_sonar_buttons', {
            screen_type: 'sonar_warning',
            button: 'back',
          });
        "
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #icon-center>
      <Icon
        class="absolute -translate-x-1/2 pointer-events-none -top-16 left-1/2"
        name="metal-sonar"
        :size="75"
      />
    </template>
    <div class="relative text-center">
      <Icon class="mx-auto mb-5" name="sonar-warning" :size="100" />
      <div class="mb-5 text-sm" v-html="t('SONAR_WARNING_DESC')"></div>
      <Button :label="t('SONAR_WARNING_BUTTON_AGREE')" @click="goToSonarGUI" />
    </div>
  </Dialog>
</template>
