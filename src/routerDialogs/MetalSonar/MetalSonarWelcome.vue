<script lang="ts" setup>
import { SlideIllustration } from '@components';
import { useUserStore } from '@stores';

const storeUser = useUserStore();

const { t } = useI18n();
const { closeDialog } = useMicroRoute();
const emits = defineEmits<{
  (e: 'letGo'): void;
}>();
function letgo() {
  closeDialog('metal_sonar_welcome');
  emits('letGo');
}

onMounted(() => {
  storeUser.updateOnboarding('first_metal_sonar');
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('METAL_SONAR_TITLE')"></div>
    </template>
    <template #icon-center>
      <Icon
        class="absolute -top-16 left-1/2 -translate-x-1/2 pointer-events-none"
        name="metal-sonar"
        :size="85"
      />
    </template>
    <div class="relative text-center">
      <div class="text-sm mb-2" v-html="t('METAL_SONAR_DESC')"></div>
      <div
        class="frame-discount-powerup text-lg font-bold mb-5"
        v-html="t('METAL_SONAR_DESC_1')"
      ></div>
      <SlideIllustration
        class="mb-5"
        :illustrations="
          [
            'coin_sonar_1',
            'coin_sonar_2',
            'coin_sonar_3',
            'coin_sonar_4',
            'coin_sonar_5',
          ].map((img) => ({
            name: img,
            type: 'png',
          }))
        "
      />
      <!-- <div class="w-full aspect-video bg-white mb-5"></div> -->
      <div class="text-sm mb-5" v-html="t('METAL_SONAR_DESC_2')"></div>
      <Button :label="t('LET_GO')" @click="letgo" />
    </div>
  </Dialog>
</template>
