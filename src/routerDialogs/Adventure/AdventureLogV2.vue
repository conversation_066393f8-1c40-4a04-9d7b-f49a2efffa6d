<script lang="ts" setup>
import { useTrackData } from '@composables';
import { useUserStore } from '@stores';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { timelines, currentSeason } = storeToRefs(storeUser);
const { openDialog } = useMicroRoute();
const { t } = useI18n();
const { trackTime, track } = useTrackData();

const adventureLogs = computed(() => {
  const ongoing = timelines.value
    .filter((t) => t.status === 'ongoing')
    .reverse();
  const past = timelines.value.filter((t) => t.status === 'ended').reverse();
  return { ongoing, past };
});

onBeforeUnmount(() => {
  trackTime('adventurelog');
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('ADVENTURELOG_POPUP_HEADING')"></div>
    </template>
    <div class="relative">
      <div
        class="text-center text-base font-bold mb-1"
        v-html="t('ADVENTURELOG_POPUP_DESCRIPTION_1')"
      ></div>
      <div
        class="text-center text-sm"
        v-html="t('ADVENTURELOG_POPUP_DESCRIPTION_2')"
      ></div>
      <template v-if="!!adventureLogs.ongoing.length">
        <div
          class="mt-10 opacity-80 ml-2 text-sm"
          v-html="t('ADVENTURELOG_POPUP_CURRENT')"
        ></div>
        <div class="flex flex-col gap-12">
          <div
            class="card-log current mt-2"
            v-for="l in adventureLogs.ongoing"
            :key="l._id"
            @click="
              emits('close');
              openDialog('time_line_v2', {
                defaultInitialSlide: timelines.findIndex(
                  (t) => t._id === l._id
                ),
              });
              track('adventurelog_city', {
                city: l.city,
              });
            "
          >
            <Icon class="sqkii-smile pointer-events-none" name="sqkii-smile" />
            <Icon class="absolute top-3 right-3" name="current-location" />

            <div
              class="text-sm"
              v-html="t(l.adventure_log_date || 'Missing adventure log date?')"
            ></div>
            <div
              class="text-sm font-bold"
              v-html="
                t(l.adventure_log_title || 'Missing adventure log title?')
              "
            ></div>
            <div
              class="text-sm"
              v-html="
                t(l.adventure_log_content || 'Missing adventure log content?')
              "
            ></div>
          </div>
        </div>
      </template>

      <div
        class="mt-3 opacity-80 ml-3 text-sm"
        v-html="t('ADVENTURELOG_POPUP_COMPLETED')"
      ></div>

      <div
        class="card-log mt-2"
        v-for="(l, index) in adventureLogs.past"
        :key="l._id"
        @click="
          emits('close');
          openDialog('time_line_v2', {
            defaultInitialSlide: timelines.findIndex((t) => t._id === l._id),
          });
          track('adventurelog_city', {
            city: l.city,
          });
        "
      >
        <Icon
          v-if="index === 0 && currentSeason?.status === 'ended'"
          name="sqkii-smile"
          class="absolute w-[50px] right-0 -top-[45px] pointer-events-none"
        />
        <Icon class="absolute top-3 right-3" name="location" />
        <div
          class="text-sm"
          v-html="t(l.adventure_log_date || 'Missing adventure log date?')"
        ></div>
        <div
          class="text-sm font-bold"
          v-html="t(l.adventure_log_title || 'Missing adventure log title?')"
        ></div>
        <div
          class="text-sm"
          v-html="
            t(l.adventure_log_content || 'Missing adventure log content?')
          "
        ></div>
      </div>
      <div
        class="underline text-link mt-3 text-center"
        v-html="t('ADVENTURELOG_POPUP_MOREHUNTS')"
        @click="
          emits('close');
          openDialog('time_line_v2');
        "
      ></div>
    </div>
  </Dialog>
</template>

<style lang="scss" scoped>
.card-log {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 12px;
  border-radius: 4px;
  background: #091a3c;
  &.current {
    background: linear-gradient(
      180deg,
      rgba(147, 75, 218, 0.8) 0%,
      #511d85 100%
    );
  }

  & .sqkii-smile {
    position: absolute;
    right: 0px;
    top: -45px;
    width: 50px;
    height: 45px;
  }
}
</style>
