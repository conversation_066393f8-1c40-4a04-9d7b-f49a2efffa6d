<script setup lang="ts">
import { useTick } from '@composables';
import { timeCountDown } from '@helpers';

const { openDialog, push, closeAllDialog } = useMicroRoute();
const { t } = useI18n();
const { now } = useTick();

interface Props {
  lock_until?: string;
  type: 'banned' | 'locked';
}

interface Emits {
  (e: 'close'): void;
}
const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const countdown = computed(() => {
  if (!props.lock_until) return 0;
  return +new Date(props.lock_until) - now.value;
});

function handleBackToLogin() {
  emits('close');
  openDialog('login');
}

function handlBackToMap() {
  closeAllDialog();
  push('home');
}
</script>

<template>
  <div
    class="fullscreen bg-[#090422] column flex-nowrap items-center text-center justify-center p-[40px]"
  >
    <Icon name="sentosa_timii_locked_account" :size="164" class="mb-4" />
    <div
      class="font-bold text-lg mb-4"
      v-html="
        type === 'banned'
          ? t('LOGIN_ACCOUNTBANNED_TITLE')
          : t('LOGIN_ACCOUNTLOCKED_TITLE')
      "
    ></div>
    <div
      class="text-sm mb-6"
      v-html="
        type === 'banned'
          ? t('LOGIN_ACCOUNTBANNED_DESC')
          : t('LOGIN_ACCOUNTLOCKED_DESC')
      "
    ></div>

    <div class="mb-6">
      <Button @click="handleBackToLogin" :disable="countdown > 0">
        {{ t('LOGIN_ACCOUNTLOCKED_BUTTON') }}
        <span v-if="countdown > 0" class="whitespace-nowrap ml-2">
          ({{ timeCountDown(countdown) }})
        </span>
      </Button>
    </div>

    <div
      @click="handlBackToMap"
      class="text-sm underline text-link"
      v-html="t('LOGIN_ACCOUNTLOCKED_BACKTOMAP')"
    ></div>
  </div>
</template>
