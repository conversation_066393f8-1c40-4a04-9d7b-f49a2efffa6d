<script lang="ts" setup>
import { useUserStore } from '@stores';
import { USER } from '@repositories';
import { useAsync, useTrackData } from '@composables';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { t } = useI18n();
const { track } = useTrackData();

const { loading, execute: handleLogout } = useAsync({
  fn: async () => {
    await USER.logout();
    storeUser.logout();
    track('settings_logout_popup', {
      action: 'confirm_log_out',
    });
    emits('close');
  },
});
</script>

<template>
  <Dialog :hide-close="true">
    <template #btnTopLeft>
      <Button shape="square" variant="secondary" @click="emits('close')">
        <Icon name="arrow-left" :size="14"></Icon>
      </Button>
    </template>

    <template #header>
      <div v-html="t('SETTINGS_LEAVINGPOPUP_HEADING')"></div>
    </template>

    <div
      class="text-sm text-center mb-5"
      v-html="t('SETTINGS_LEAVINGPOPUP_DESC')"
    ></div>

    <div class="text-center">
      <Button
        :loading="loading"
        :label="t('SETTINGS_BUTTON_LOGOUT')"
        @click="handleLogout"
      ></Button>
    </div>
  </Dialog>
</template>
