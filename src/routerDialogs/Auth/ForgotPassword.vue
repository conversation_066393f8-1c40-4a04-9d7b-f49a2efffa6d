<script lang="ts" setup>
import {
  FULL_DATE_TIME_24H_FORMAT,
  dateTimeFormat,
  formatMobileNumber,
  timeCountDown,
} from '@helpers';
import { useAsync, useTick, useTrackData } from '@composables';
import { useForm } from 'vee-validate';
import {
  REGEX_DIGIT,
  REGEX_LOWERCASE,
  REGEX_SPECIAL,
  REGEX_UPPERCASE,
} from '@constants';
import { identity, omit, pickBy } from 'lodash';
import { AUTH } from '@repositories';
import type { ICountryRegion, IAPIResponseError } from '@types';
import * as yup from 'yup';
import { TurnstileCaptcha } from '@components';

type TFormStep = 1 | 2 | 3;

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { now } = useTick();
const { track } = useTrackData();

const isTestingEnvironment = process.env.IS_TESTING_ENV as unknown as boolean;
const step = ref<TFormStep>(1);
const country = ref<ICountryRegion>();
const matched = ref(false);
const captchaRef = ref();

const validationSchema = computed(() => {
  const schema = {
    1: {
      mobile_number: yup.string().required(t('MOBILE_NUMBER_REQUIRED')),
      // .test('mobile-number', t('MOBILE_NUMBER_INVALID'), (value) => {
      //   const number = country.value?.code + value;
      //   return isMatchSeasonNumber(number);
      // }),
    },
    2: {
      otp: yup
        .string()
        .required(t('SIGNUP_FORM_OTP_REQUIRED'))
        .length(6, t('SIGNUP_FORM_OTP_INVALID')),
    },
    3: {
      password: yup
        .string()
        .required(t('PASSWORD_REQUIRED'))
        .test('password', t('PASSWORD_INVALID'), (value) => {
          const valid = {
            text_length: value.length >= 8,
            lowercase: REGEX_LOWERCASE.test(value),
            uppercase: REGEX_UPPERCASE.test(value),
            digit: REGEX_DIGIT.test(value),
            special: REGEX_SPECIAL.test(value),
          };
          matched.value = Object.values(valid).every(Boolean);
          return Object.values(valid).every(Boolean);
        }),
      cf_password: yup
        .string()
        .required(t('RE_ENTER_PASSWORD_REQUIRED'))
        .oneOf([yup.ref('password')], t('PASSWORD_NOT_MATCH')),
    },
  };

  return yup.object().shape({ ...schema[step.value] });
});

const initialValues = computed(() => {
  return {
    mobile_number: '',
    otp: '',
    password: '',
    cf_password: '',
    expire_at: '',
    next_otp_at: '',
    captcha_token: '',
  };
});

const {
  handleSubmit,
  values,
  setFieldError,
  setFieldValue,
  setTouched,
  validate,
} = useForm({
  initialValues: initialValues.value,
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  const mobile_number = `${country.value?.code}${values.mobile_number}`.replace(
    /\+/g,
    ''
  );
  const payload = omit(
    pickBy(
      {
        ...values,
        mobile_number,
        captcha_token: isTestingEnvironment
          ? 'XXXX.DUMMY.TOKEN.XXXX'
          : values.captcha_token,
      },
      identity
    ),
    ['cf_password', 'next_otp_at', 'expire_at']
  );

  const { data } = await AUTH.forgotPw(payload);
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const { valid } = await validate();
    const data = await callback();
    if (step.value === 2)
      track('forgotpassword_enterotp', {
        action: 'forgotpassword_enterotp_next',
      });
    if (step.value === 3 && valid) {
      track('forgotpassword_resetpassword', {
        action: 'forgotpassword_resetpassword_next',
      });
      emits('close');
      openDialog('login', {
        resetPW: true,
      });
    }
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    if (data.next_otp_at && data.expire_at) {
      setFieldValue('expire_at', data.expire_at);
      setFieldValue('next_otp_at', data.next_otp_at);
      track('forgotpassword_entermobilenumber', {
        action: 'forgotpassword_entermobilenumber_sendotp',
      });
    }
    setTouched(false);
    step.value++;
  },
  onError(err: IAPIResponseError) {
    handleErrors(err);
  },
});

function handleErrors(err: IAPIResponseError) {
  const { data, error_message } = err;
  switch (error_message) {
    case 'invalid_mobile_number':
    case 'Invalid value':
      step.value++;
      setTouched(false);
      break;
    case 'invalid_otp':
      if (data.attempts === 4) setFieldError('otp', t('OTP_LIMIT_4'));
      else setFieldError('otp', t('INVALID_OTP'));
      break;
    case 'otp_limited':
      setFieldError(
        step.value === 1 ? 'mobile_number' : 'otp',
        t('REACH_LIMIT_RESEND')
      );
      break;
    default:
      setFieldError('otp', t(error_message));
      break;
  }
  resetCaptcha();
}

const { execute: resendOTP } = useAsync({
  async fn() {
    const mobile_number =
      `${country.value?.code}${values.mobile_number}`.replace(/\+/g, '');
    const { data } = await AUTH.resendOTP({
      mobile_number,
      captcha_token: isTestingEnvironment
        ? 'XXXX.DUMMY.TOKEN.XXXX'
        : values.captcha_token,
      type: 'forgot_password',
    });
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    if (data.next_otp_at && data.expire_at) {
      setFieldValue('expire_at', data.expire_at);
      setFieldValue('next_otp_at', data.next_otp_at);
    }
    setFieldValue('otp', '');
  },
  onError(err: IAPIResponseError) {
    handleErrors(err);
  },
});

function handleBack() {
  switch (step.value) {
    case 2:
      setFieldValue('otp', '');
      track('forgotpassword_enterotp', {
        action: 'forgotpassword_enterotp_back',
      });
      step.value = 1;
      break;
    case 3:
      setFieldValue('password', '');
      setFieldValue('cf_password', '');
      track('forgotpassword_resetpassword', {
        action: 'forgotpassword_resetpassword_back',
      });
      step.value = 2;
      break;
    default:
      emits('close');
      openDialog('login');
      track('forgotpassword_entermobilenumber', {
        action: 'forgotpassword_entermobilenumber_back',
      });
      break;
  }
}

async function handleClick(e: Event) {
  const target = e.target as HTMLElement;
  switch (target.id) {
    case 'resendOTP':
      if (+new Date(values.next_otp_at) > now.value) return;
      track('forgotpassword_enterotp', {
        action: 'forgotpassword_enterotp_resendotp',
      });
      await resendOTP();
      break;
    default:
      break;
  }
}

watch(step, resetCaptcha);

function handleCaptchaSuccess(token: string) {
  setFieldValue('captcha_token', token);
}

function handleCaptchaError() {
  setFieldValue('captcha_token', '');
}

function resetCaptcha() {
  if (captchaRef.value) captchaRef.value.reset();
  setFieldValue('captcha_token', '');
}

onMounted(async () => {
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>

<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button shape="square" variant="secondary" @click="handleBack">
        <Icon name="arrow-left" :size="14"></Icon>
      </Button>
    </template>

    <template #header>
      <div class="text-capitalize" v-html="t('FORGOTPASSWORD_TITLE')"></div>
    </template>

    <q-form @submit="onSubmit" class="flex flex-col">
      <section v-show="step === 1">
        <div
          class="text-sm text-center mb-5"
          v-html="t('FORGOTPASSWORD_DESC')"
        ></div>
        <VeeInputCountry
          class="mb-5"
          :label="t('MOBILE_NUMBER')"
          name="mobile_number"
          @update:country="
            country = $event;
            track('forgotpassword_entermobilenumber', {
              action: 'forgotpassword_entermobilenumber_countrycode',
            });
          "
          autofocus
        />
      </section>

      <section v-show="step === 2">
        <div class="text-md text-center mb-5">
          <span v-html="t('FORGOTPASSWORD_ACCOUNTEXISTS_1')"></span><br />
          <b class="text-xl">
            {{
              formatMobileNumber('+' + country?.code + values?.mobile_number)
            }}
          </b>
          <br />
          <span v-html="t('FORGOTPASSWORD_ACCOUNTEXISTS_3')"></span>
        </div>
        <VeeOTP class="mb-5" name="otp" :num-inputs="6" />
        <div class="text-xs text-center mb-3" v-if="values.expire_at">
          {{
            t('OTP_VALID_UNTIL', {
              TIME: dateTimeFormat(values.expire_at, FULL_DATE_TIME_24H_FORMAT),
            })
          }}
        </div>
        <div
          class="text-xs text-center mb-5"
          v-html="
            +new Date(values.next_otp_at) > now
              ? t('SIGNUP_FORM_OTPTEXT_RESEND', {
                  TIME: timeCountDown(+new Date(values.next_otp_at) - now),
                })
              : t('SIGNUP_FORM_OTPTEXT_RESEND_NOCOUNTDOWN')
          "
        ></div>
      </section>
      <section v-show="step == 3">
        <div
          class="text-sm text-center mb-5"
          v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_1')"
        ></div>
        <VeeInput
          class="mb-5"
          name="password"
          :label="t('PASSWORD')"
          type="password"
        />
        <Requirements
          class="mb-3 -mt-4"
          v-show="values.password && !matched"
          :password="values.password"
          @valid="matched = $event"
        />

        <VeeInput
          class="mb-5"
          name="cf_password"
          :label="t('RE_ENTER_PASSWORD')"
          type="password"
        />
      </section>

      <TurnstileCaptcha
        v-if="!isTestingEnvironment"
        ref="captchaRef"
        class="mb-5"
        @success="handleCaptchaSuccess"
        @error="handleCaptchaError"
      />

      <div class="text-center">
        <Button
          :disable="!values.captcha_token && !isTestingEnvironment"
          type="submit"
          :loading="loading"
          :label="
            step === 1
              ? t('FORGOTPASSWORD_BUTTON_SENDOTP')
              : t('FORGOTPASSWORD_BUTTON_NEXT')
          "
        />
      </div>
    </q-form>
  </Dialog>
</template>
