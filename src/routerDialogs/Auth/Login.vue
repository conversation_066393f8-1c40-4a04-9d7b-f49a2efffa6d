<script lang="ts" setup>
import { AUTH, USER } from '@repositories';
import { useUserStore, useVouchersStore } from '@stores';
import { useAsync, useClick, useGlobal, useTrackData } from '@composables';
import { useForm } from 'vee-validate';
import { TurnstileCaptcha } from '@components';
import type { IAPIResponseError, ICountryRegion } from '@types';
import * as yup from 'yup';

interface Props {
  resetPW?: boolean;
}

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();
defineProps<Props>();

const storeUser = useUserStore();
const storeVouchers = useVouchersStore();

const { trackUserLocation } = useGlobal();
const { openDialog } = useMicroRoute();
const { t, locale } = useI18n();
const { track } = useTrackData();

const isTestingEnvironment = process.env.IS_TESTING_ENV as unknown as boolean;
const error = ref('');
const country = ref<ICountryRegion>();
const captchaRef = ref();

useClick('SIGNUP', () => {
  track('login_buttons', {
    button: 'sign_up',
  });
  emits('close');
  openDialog('signup');
});

const validationSchema = yup.object({
  mobile_number: yup.string().required(t('MOBILE_NUMBER_REQUIRED')),
  // .test('mobile-number', t('SIGNUP_FORM_INVALIDMOBILENUMBER'), (value) => {
  //   const number = country.value?.code + value;
  //   return isMatchSeasonNumber(number);
  // }),
  password: yup.string().required(t('PASSWORD_REQUIRED')),
});

const { handleSubmit, values, setFieldValue } = useForm({
  initialValues: {
    mobile_number: '',
    password: '',
    captcha_token: '',
  },
  validationSchema,
});

watch(
  [() => values.mobile_number, () => values.password],
  () => {
    error.value = '';
  },
  { deep: true }
);

const callback = handleSubmit(async (values) => {
  const mobile_number = `${country.value?.code}${values.mobile_number}`.replace(
    /\+/g,
    ''
  );
  const payload = {
    ...values,
    mobile_number,
    captcha_token: isTestingEnvironment
      ? 'XXXX.DUMMY.TOKEN.XXXX'
      : values.captcha_token,
  };
  const { data } = await AUTH.login(payload);
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  async onSuccess(data) {
    if (!data) return;
    storeUser.setUser(data.user);
    storeUser.setToken(data.token);
    LocalStorage.set(
      `${process.env.APP_NAME}_SQKII_VOUCHER_TOKEN`,
      data.user.sv_token
    );
    const l = data.user?.lang || process.env.APP_LANGUAGE_CODE;
    storeVouchers.token = data.user.sv_token;
    LocalStorage.set('lang', l);
    await USER.changeLang(l);
    storeUser.updateUser({ lang: l });
    locale.value = l;
    trackUserLocation('login');
    track('login_buttons', {
      button: 'login',
    });
    emits('close');
    // window.location.reload();
  },
  onError(err: IAPIResponseError) {
    const { data, error_message } = err;

    switch (true) {
      case !!data?.info?.lock_until:
      case error_message === 'banned_number':
        emits('close');
        openDialog('locked_account', {
          lock_until: data?.info?.lock_until,
          type: error_message === 'banned_number' ? 'banned' : 'locked',
        });
        break;
      case error_message === 'invalid_credentials':
        error.value = t('LOGIN_ERROR', {
          ATTEMPTS: data.info?.remaining_attempts,
        });
        break;
      default:
        error.value = t(error_message);
        break;
    }

    resetCaptcha();
  },
});

function handleForgotPW() {
  emits('close');
  openDialog('forgot_password');
  track('login_buttons', {
    button: 'forgot_password',
  });
}

function handleCaptchaSuccess(token: string) {
  setFieldValue('captcha_token', token);
}

function handleCaptchaError() {
  setFieldValue('captcha_token', '');
}

function resetCaptcha() {
  if (captchaRef.value) captchaRef.value.reset();
  setFieldValue('captcha_token', '');
}
</script>

<template>
  <Dialog
    @close="
      emits('close');
      track('login_buttons', {
        button: 'close',
      });
    "
  >
    <template #header>
      <div v-html="t('LOGIN_TITLE')"></div>
    </template>
    <q-form @submit="onSubmit" class="flex flex-col flex-nowrap">
      <div
        v-if="resetPW"
        class="text-center text-sm mb-5 bg-[#03833E] rounded p-2"
        v-html="t('LOGIN_RESETPW_DESC')"
      ></div>
      <div
        v-else
        class="text-center text-sm mb-5"
        v-html="t('LOGIN_DESCRIPTION')"
      ></div>

      <VeeInputCountry
        class="mb-5"
        :label="t('MOBILE_NUMBER')"
        name="mobile_number"
        @update:country="country = $event"
        :error="!!error"
        autofocus
      />
      <VeeInput
        name="password"
        :label="t('PASSWORD')"
        type="password"
        class="mb-5"
        :error="!!error"
      />

      <div
        class="text-xs text-right mt-2 underline"
        @click="handleForgotPW"
        v-html="t('LOGIN_FORGOTPASSWORD')"
      ></div>

      <div
        class="card-error mt-2 text-center"
        v-if="!!error"
        v-html="t(error)"
      ></div>

      <TurnstileCaptcha
        v-if="!isTestingEnvironment"
        ref="captchaRef"
        class="mb-5"
        @success="handleCaptchaSuccess"
        @error="handleCaptchaError"
      />
      <div class="text-center mt-5">
        <Button
          :disable="!values.captcha_token && !isTestingEnvironment"
          :label="t('LOGIN_BUTTON')"
          type="submit"
          :loading="loading"
        />
      </div>

      <div class="text-xs text-center mt-5" v-html="t('LOGIN_SIGNUP')"></div>
    </q-form>
  </Dialog>
</template>
