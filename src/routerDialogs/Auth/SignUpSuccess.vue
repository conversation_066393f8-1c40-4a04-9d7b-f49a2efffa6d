<script setup lang="ts">
import { formatMobileNumber, metaPixelTacking } from '@helpers';
import { useTrackData } from '@composables';
import type { IUser } from '@types';
import gsap, { Linear } from 'gsap';

const tl = gsap.timeline();

interface Emits {
  (e: 'close'): void;
}

interface Props {
  user: IUser;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

function startAnimation() {
  tl.fromTo(
    '.upload_flare',
    {
      scale: 0,
    },
    {
      scale: 1,
      duration: 1,
      delay: 0.5,
    }
  ).fromTo(
    '.upload_flare',
    {
      rotate: 0,
    },
    {
      rotate: 720,
      duration: 20,
      repeat: -1,
      ease: Linear.easeNone,
    },
    '-=1'
  );

  gsap.fromTo(
    '.text',
    {
      scale: 0,
    },
    {
      scale: 1,
      duration: 0.5,
      stagger: 0.2,
    }
  );

  gsap.fromTo(
    '.upload_star',
    {
      opacity: 0,
    },
    {
      opacity: 1,
      yoyo: true,
      repeat: -1,
      delay: 1,
      duration: 1,
    }
  );
}

function handleAction() {
  // if (!!settings.value?.zalo_daily_quota?.remainingQuota)
  //   openDialog('verify_mobile_number');
  // push('offer_wall', {
  //   scrollToElement: '#verify_mobile_number',
  // });
  emits('close');
  track('sign_up_buttons', {
    screen: 'account_created',
    button: 'verify',
  });
}

onMounted(async () => {
  await nextTick();
  metaPixelTacking('CreateAccount');
  startAnimation();
});

onBeforeUnmount(() => {
  tl?.kill();
  gsap.killTweensOf('.upload_star');
});
</script>
<template>
  <div
    class="fullscreen signup-success column flex-nowrap items-center text-center justify-between py-[40px]"
  >
    <div class="font-bold text-lg" v-html="t('SIGNUP_CREATED_TITLE')"></div>

    <div class="absolute absolute-center full-width flex flex-center h-[100vw]">
      <Icon
        class="upload_flare absolute pointer-none top-0 left-0 w-[100vw]"
        name="upload_flare"
      />
      <Icon
        class="upload_star full-width absolute pointer-none top-0 left-0 z-10"
        name="star_frame"
      />
    </div>

    <div class="px-5 text">
      <div class="text-sm" v-html="t('SIGNUP_CREATED_DESC')"></div>
      <div
        class="text-2xl font-bold mt-2"
        v-html="t('SIGNUP_CREATED_HUNTER')"
      ></div>
      <div class="row flex-nowrap items-center mt-1">
        <div
          class="font-bold whitespace-nowrap"
          style="font-size: 15vw; line-height: 18vw"
        >
          {{ user?.hunter_id }}
        </div>
        <div @click="openDialog('dont_like_hunter_id')">
          <Icon name="question-mark" :size="20" class="ml-3" />
        </div>
      </div>
      <div class="text-lg mt-1">
        {{ formatMobileNumber('+' + user?.mobile_number) }}
      </div>
    </div>
    <div class="flex justify-center flex-col items-center gap-5">
      <!-- <div
        class="text-sm px-10"
        v-html="
          !settings?.zalo_daily_quota?.remainingQuota
            ? t('SIGNUP_CREATED_OTP_LIMIT', {
                LIMIT: settings?.zalo_daily_quota?.dailyQuota,
              })
            : t('SIGNUP_CREATED_OTP')
        "
      ></div> -->
      <Button @click="handleAction" :label="t('BUTTON_BACKTOMAP')" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.signup-success {
  overflow-x: hidden;
  background-color: #090422;
}
</style>
