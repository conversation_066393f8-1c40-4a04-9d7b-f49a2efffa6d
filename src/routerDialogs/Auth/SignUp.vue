<script lang="ts" setup>
import {
  useAsync,
  useClick,
  useGlobal,
  useTick,
  useTrackData,
} from '@composables';
import { useUserStore } from '@stores';
import { useForm } from 'vee-validate';
import { AUTH } from '@repositories';
import {
  REGEX_DIGIT,
  REGEX_UPPERCASE,
  REGEX_LOWERCASE,
  REGEX_SPECIAL,
} from '@constants';
import {
  dateTimeFormat,
  FULL_DATE_TIME_12H_FORMAT_IN_SECOND,
  timeCountDown,
} from '@helpers';
import { identity, last, omit, pickBy } from 'lodash';
import type { ICountryRegion, IAPIResponseError } from '@types';
import * as yup from 'yup';
import dayjs from 'dayjs';
import { TurnstileCaptcha } from '@components';

type TFormStep = 1 | 2 | 3;

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { trackUserLocation } = useGlobal();
const { seasonCode } = storeToRefs(storeUser);
const { openDialog, closeAllDialog, currentPath, push } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();
const { now } = useTick();

const isTestingEnvironment = process.env.IS_TESTING_ENV as unknown as boolean;

useClick('LOGIN', () => {
  track('sign_up_buttons', {
    screen: 'enter_mobile_number',
    button: 'login_here',
  });
  emits('close');
  openDialog('login');
});

useClick('TC', () => {
  track('sign_up_buttons', {
    screen: 'enter_password',
    button: 'tac',
  });
  openDialog('tac', {
    onClose: () => {
      setFieldValue('tac', true);
    },
  });
});

const step = ref<TFormStep>(1);
const country = ref<ICountryRegion>();
const matched = ref(false);
// const COUNTRY_CODE = computed(() => process.env.APP_COUNTRY_CODE as TSeasonISO);
const captchaRef = ref();

const validationSchema = computed(() => {
  const schema = {
    1: {
      mobile_number: yup.string().required(t('MOBILE_NUMBER_REQUIRED')),
      // .test(
      //   'mobile-number',
      //   t('SIGNUP_FORM_INVALIDMOBILENUMBER'),
      //   (value) => {
      //     const number = country.value?.code + value;
      //     return isMatchSeasonNumber(number, COUNTRY_CODE.value);
      //   }
      // ),
    },
    2: {
      otp: yup
        .string()
        .required(t('SIGNUP_FORM_OTP_REQUIRED'))
        .length(6, t('SIGNUP_FORM_OTP_INVALID')),
    },
    3: {
      password: yup
        .string()
        .required(t('PASSWORD_REQUIRED'))
        .test('password', t('PASSWORD_INVALID'), (value) => {
          const valid = {
            text_length: value.length >= 8,
            lowercase: REGEX_LOWERCASE.test(value),
            uppercase: REGEX_UPPERCASE.test(value),
            digit: REGEX_DIGIT.test(value),
            special: REGEX_SPECIAL.test(value),
          };
          matched.value = Object.values(valid).every(Boolean);
          return Object.values(valid).every(Boolean);
        }),
      cf_password: yup
        .string()
        .required(t('RE_ENTER_PASSWORD_REQUIRED'))
        .oneOf([yup.ref('password')], t('PASSWORD_NOT_MATCH')),
      tac: yup.boolean().oneOf([true], t('SIGNUP_TNC_ERROR')),
    },
  };

  return yup.object().shape({ ...schema[step.value] });
});

const initialValues = computed(() => {
  return {
    mobile_number: '',
    otp: '',
    password: '',
    cf_password: '',
    next_otp_at: '',
    expire_at: '',
    captcha_token: '',
    tac: false,
  };
});

const { handleSubmit, values, setFieldError, setTouched, setFieldValue } =
  useForm({
    initialValues: initialValues.value,
    validationSchema,
  });

const callback = handleSubmit(async (values) => {
  const mobile_number = `${country.value?.code}${values.mobile_number}`.replace(
    /\+/g,
    ''
  );
  const payload = omit(
    pickBy(
      {
        ...values,
        mobile_number,
        captcha_token: isTestingEnvironment
          ? 'XXXX.DUMMY.TOKEN.XXXX'
          : values.captcha_token,
      },
      identity
    ),
    ['cf_password', 'tac', 'next_otp_at']
  );

  const { data } = await AUTH.signup(payload);
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    if (step.value === 1)
      track('sign_up_buttons', {
        screen: 'enter_mobile_number',
        button: 'next',
      });
    if (step.value === 3)
      track('sign_up_buttons', {
        screen: 'enter_password',
        button: 'create',
      });
    const data = await callback();
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    if (data.expire_at) {
      setFieldValue('expire_at', data.expire_at);
    }
    if (data.next_otp_at) {
      setFieldValue('next_otp_at', data.next_otp_at);
      step.value++;
      setTouched(false);
      return;
    }
    storeUser.setUser(data);
    trackUserLocation('login');
    if (last(currentPath.value.split('/')) !== 'home') push('/home');
    closeAllDialog();
    openDialog('signup_success', {
      user: data,
    });
  },
  onError(err: IAPIResponseError) {
    handleErrors(err);
  },
});

const { execute: resendOTP, loading: resendOTPLoading } = useAsync({
  async fn() {
    const { data } = await AUTH.resendOTP({
      mobile_number: `${country.value?.code}${values.mobile_number}`.replace(
        /\+/g,
        ''
      ),
      type: 'register',
      captcha_token: isTestingEnvironment
        ? 'XXXX.DUMMY.TOKEN.XXXX'
        : values.captcha_token,
    });
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    setFieldValue('next_otp_at', data.next_otp_at);
    setFieldValue(
      'expire_at',
      data.expire_at || dayjs().add(3, 'minute').toDate().toISOString()
    );
  },
  onError(err: IAPIResponseError) {
    handleErrors(err);
  },
});

function handleErrors(err: IAPIResponseError) {
  const { data, error_message } = err;
  switch (error_message) {
    case 'invalid_password':
      step.value++;
      setTouched(false);
      break;
    case 'invalid_otp':
      if (data?.attempts === 4) setFieldError('otp', t('OTP_LIMIT_4'));
      else setFieldError('otp', t('INVALID_OTP'));
      break;
    case 'mobile_number_existed':
    case 'Invalid value':
      step.value++;
      setTouched(false);
      // setFieldError('mobile_number', t('SIGNUP_FORM_MOBILE_ERROR'));
      break;
    case 'otp_locked': {
      setFieldError('otp', t('OTP_LOCKED'));
      break;
    }
    default:
      setFieldError('mobile_number', t(error_message));
      break;
  }
  resetCaptcha();
}

function onBack() {
  if (step.value === 3) {
    setFieldValue('password', '');
    setFieldValue('cf_password', '');
    track('sign_up_buttons', {
      screen: 'enter_password',
      button: 'back',
    });
  }
  if (step.value === 2) {
    setFieldValue('otp', '');
    track('sign_up_buttons', {
      screen: 'otp',
      button: 'back',
    });
  }
  if (seasonCode.value === 'VN') step.value = 1;
  else step.value--;
}

async function handleClick(e: Event) {
  const target = e.target as HTMLElement;
  switch (target.id) {
    case 'resendOTP':
      if (+new Date(values.next_otp_at) > now.value || resendOTPLoading.value)
        return;

      await resendOTP();
      break;
    default:
      break;
  }
}

watch(step, resetCaptcha);

function handleCaptchaSuccess(token: string) {
  setFieldValue('captcha_token', token);
}

function handleCaptchaError() {
  setFieldValue('captcha_token', '');
}

function resetCaptcha() {
  if (captchaRef.value) captchaRef.value.reset();
  setFieldValue('captcha_token', '');
}

onMounted(async () => {
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>

<template>
  <Dialog
    @close="
      emits('close');
      track('sign_up_buttons', {
        screen: step === 3 ? 'enter_password' : 'enter_mobile_number',
        button: 'close',
      });
    "
  >
    <template #btnTopLeft v-if="step === 2">
      <Button shape="square" variant="secondary" @click="onBack">
        <Icon name="arrow-left" :size="14"></Icon>
      </Button>
    </template>
    <template #header>
      <div v-html="t('SIGNUP_FORM_TITLE')"></div>
    </template>

    <q-form @submit="onSubmit" class="flex flex-col flex-nowrap">
      <section v-show="step === 1">
        <div class="text-center mb-4" v-html="t('SIGNUP_FORM_DESC')"></div>

        <VeeInputCountry
          class="mb-5"
          :label="t('MOBILE_NUMBER')"
          name="mobile_number"
          @update:country="country = $event"
          autofocus
          :customCountries="[
            {
              name: 'Singapore',
              code: '+65',
              iso: 'SG',
              flag: 'https://cdn.kcak11.com/CountryFlags/countries/sg.svg',
              mask: ['#### ####'],
              currency: 'SGD',
              currencyName: 'Singapore Dollar (SGD)',
              url: 'https://huntthemouse.sqkii.com',
            },
          ]"
        />
      </section>
      <section v-show="step === 2">
        <div class="text-sm text-center mb-5">
          <span v-html="t('SIGNUP_FORM_OTPTEXT_1')"></span><br />
          <b class="text-xl">{{ country?.code }} {{ values.mobile_number }}</b>
          <br />
          <span v-html="t('SIGNUP_FORM_OTPTEXT_3')"></span>
        </div>
        <VeeOTP class="mb-5" name="otp" :num-inputs="6" autofocus />

        <div
          class="text-xs text-center mb-3"
          v-if="+new Date(values.next_otp_at) > now"
        >
          {{
            t('OTP_VALID_UNTIL', {
              TIME: dateTimeFormat(
                (values.expire_at && new Date(values.expire_at)) ||
                  dayjs().add(3, 'minute').toDate().toISOString(),
                FULL_DATE_TIME_12H_FORMAT_IN_SECOND
              ),
              TIMEZONE: dayjs().format('Z').replace(':00', ''),
            })
          }}
        </div>
        <div
          class="text-xs text-center mb-5"
          v-html="
            +new Date(values.next_otp_at) > now
              ? t('SIGNUP_FORM_OTPTEXT_RESEND', {
                  TIME: timeCountDown(+new Date(values.next_otp_at) - now),
                })
              : t('SIGNUP_FORM_OTPTEXT_RESEND_NOCOUNTDOWN')
          "
        ></div>
      </section>
      <section v-show="step == 3">
        <div
          class="text-sm text-center mb-5"
          v-html="t('SIGNUP_FORM_PASSWORDDESC')"
        ></div>
        <VeeInput
          class="mb-5"
          name="password"
          :label="t('PASSWORD')"
          type="password"
        />
        <Requirements
          class="mb-3 -mt-4"
          v-if="values.password && !matched"
          :password="values.password"
          @valid="matched = $event"
        />

        <VeeInput
          class="mb-5"
          name="cf_password"
          :label="t('RE_ENTER_PASSWORD')"
          type="password"
        />

        <VeeCheckbox
          class="mb-1"
          name="tac"
          :label="t('SIGNUP_TNC_CHECKBOX')"
        />
      </section>

      <TurnstileCaptcha
        v-if="!isTestingEnvironment"
        ref="captchaRef"
        class="mb-5"
        @success="handleCaptchaSuccess"
        @error="handleCaptchaError"
      />

      <div class="text-center mt-2">
        <Button
          :disable="!values.captcha_token && !isTestingEnvironment"
          :label="
            step === 3
              ? t('SIGNUP_FORM_CREATEACCBUTTON')
              : t('SIGNUP_FORM_BUTTON')
          "
          :loading="loading"
          type="submit"
        />
      </div>
      <div
        class="text-xs text-center mt-5"
        v-if="step === 1"
        v-html="t('SIGNUP_FORM_LOGIN')"
      ></div>
    </q-form>
  </Dialog>
</template>
