<script lang="ts" setup>
interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { openDialog, closeDialog } = useMicroRoute();

function handleClose() {
  emits('close');
}

function handleSignUp() {
  closeDialog('guest_account');
  openDialog('signup');
}
</script>
<template>
  <Dialog @close="handleClose">
    <template #header>
      <div v-html="t('GUEST_ACCOUNT_HEADER')"></div>
    </template>
    <div class="text-center">
      <div class="text-sm mb-5 px-5" v-html="t('GUEST_ACCOUNT_DESC')"></div>
      <div class="flex gap-4 flex-nowrap">
        <Button
          class="flex-1"
          size="max-content"
          :label="t('GUEST_ACCOUNT_BTN_LATER')"
          variant="purple"
          @click="handleClose"
        />
        <Button
          size="max-content"
          class="flex-1"
          :label="t('GUEST_ACCOUNT_BTN_SIGN_UP')"
          @click="handleSignUp"
        />
      </div>
    </div>
  </Dialog>
</template>
