<script lang="ts" setup>
import { useAsync, useTrackData } from '@composables';
import { errorNotify } from '@helpers';
import { CAPITALAND } from '@repositories';
import { useBAStore, useMapStore, useUserStore } from '@stores';
import { IAmenities, IAPIResponseError } from '@types';

interface Props {
  amenity: IAmenities;
}

const props = defineProps<Props>();

const storeMap = useMapStore();
const storeUser = useUserStore();
const storeBA = useBAStore();

const { isEnabledGPS } = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { track } = useTrackData();
const { openDialog, closeDialog } = useMicroRoute();
const { t } = useI18n();

const slide = ref(1);
const autoplay = ref(true);
const isHoursExpanded = ref(false);

const { execute: amenityCheckin, loading } = useAsync({
  fn: async () => {
    if (props.amenity.claimed_at) {
      errorNotify({
        message: t('CAPITALAND_AMENITIES_ALREADY_CLAIMED'),
      });
      return;
    }

    if (!isEnabledGPS.value) {
      errorNotify({
        message: t('CAPITALAND_AMENITIES_GPS_DISABLED'),
      });
      return;
    }

    const [lng, lat] = lastLocations.value;
    const { data } = await CAPITALAND.checkinAmenities({
      lng,
      lat,
      unique_id: props.amenity.unique_id,
    });
    return data;
  },
  onSuccess: async (data) => {
    if (!data) return;
    track('capitaland_amenity_checkin', {
      rewards: data.crystal,
    });
    await Promise.all([
      storeUser.fetchCapitalandAmenities(),
      storeUser.fetchUser(),
      storeBA.fetchBrandAction(),
    ]);
    closeDialog('amenities');
    openDialog('promo_success', {
      crystal: data.crystal,
    });
  },
  onError: (error: IAPIResponseError) => {
    const { error_message, data } = error;
    switch (error_message) {
      case 'not_in_range':
        errorNotify({
          message: t('CAPITALAND_AMENITIES_NOT_IN_RANGE', {
            DISTANCE: data.required_distance,
          }),
        });
        break;

      case 'claimed':
        errorNotify({
          message: t('CAPITALAND_AMENITIES_ALREADY_CLAIMED'),
        });
        break;
      default:
        errorNotify({
          message: error_message,
        });
    }
  },
});
</script>
<template>
  <Dialog>
    <template #header>
      <div class="text-lg font-bold" v-html="t(amenity.name)"></div>
      <div class="text-sm" v-html="t(amenity.address)"></div>
    </template>
    <q-carousel
      animated
      v-model="slide"
      infinite
      :arrows="amenity.images.length > 1"
      :autoplay="2000"
      transition-prev="slide-right"
      transition-next="slide-left"
      swipeable
      @mouseenter="autoplay = false"
      @mouseleave="autoplay = true"
      class="bg-transparent h-[unset] aspect-video mb-2"
    >
      <q-carousel-slide
        v-for="(img, index) in amenity.images"
        :key="index"
        :name="index + 1"
        class="bg-cover rounded-md bg-[#091A3B50] !p-0"
      >
        <Icon type="url" :name="img" lazy class="!w-full !h-full rounded-md" />
      </q-carousel-slide>
    </q-carousel>
    <div class="text-sm mb-4" v-html="t(amenity.description)"></div>
    <div class="flex items-start flex-nowrap gap-2" v-if="amenity.promo_text">
      <Icon name="progress" class="!w-6 !h-6" />
      <div class="-mt-1" v-html="t(amenity.promo_text)"></div>
    </div>
    <div class="amenity-box">
      <div class="text-sm mb-2" v-html="t('CAPITALAND_AMENITIES_DESC_1')"></div>
      <div class="flex items-center gap-2 mb-5">
        <div class="text-sm" v-html="t('CAPITALAND_AMENITIES_DESC_2')"></div>
        <div
          class="flex items-center gap-2 bg-[#6f1190] rounded-md p-1 border border-[#ba69d7]"
        >
          <Icon name="crystal" :size="22" />
          <div v-html="amenity.reward"></div>
        </div>
      </div>
      <Button
        :label="t('CAPITALAND_AMENITIES_BTN')"
        class="!w-full mb-2"
        @click="amenityCheckin"
        :loading="loading"
        :disable="!amenity.canCheckIn"
        :class="{
          'opacity-50': !isEnabledGPS || !!amenity.claimed_at,
        }"
      />
      <Icon name="star_1" class="absolute top-0 left-2 w-[30px]" />
      <Icon name="star_2" class="absolute bottom-5 right-2 w-[30px]" />
    </div>
    <div class="bg-[#091A3B] p-4 rounded-md mx-5">
      <div
        class="flex items-center justify-between cursor-pointer"
        @click="isHoursExpanded = !isHoursExpanded"
      >
        <div class="text-sm" v-html="t('CAPITALAND_AMENITIES_DESC_3')"></div>
        <Icon
          name="arrow-down"
          class="!w-5 !h-5 transition-transform duration-300"
          :class="{ 'rotate-180': isHoursExpanded }"
        />
      </div>
      <div
        class="overflow-hidden transition-all duration-300 ease-in-out"
        :class="isHoursExpanded ? 'max-h-96 mt-3' : 'max-h-0'"
      >
        <div v-html="t(amenity.display_opening_hours)"></div>
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.amenity-box {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px 10% 40px 10%;
  background-image: url(/imgs/action-base.png);
  width: calc(100% + 60px);
  margin-left: -30px;
  margin-right: -30px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
