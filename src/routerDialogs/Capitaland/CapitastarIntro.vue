<script lang="ts" setup>
import { SlideIllustration } from '@components';

const { t } = useI18n();

const STEPS = computed(() => {
  if (Platform.is.ios)
    return [
      t('CAPITASTAR_INTRO_STEP_1_1'),
      t('CAPITASTAR_INTRO_STEP_2'),
      t('CAPITASTAR_INTRO_STEP_3'),
      t('CAPITASTAR_INTRO_STEP_4'),
    ];
  return [
    t('CAPITASTAR_INTRO_STEP_1_2'),
    t('CAPITASTAR_INTRO_STEP_2'),
    t('CAPITASTAR_INTRO_STEP_3'),
    t('CAPITASTAR_INTRO_STEP_4'),
  ];
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('CAPITASTAR_INTRO_HEADER')"></div>
    </template>
    <SlideIllustration
      class="mb-4"
      :illustrations="
        ['capitastar_1', 'capitastar_2', 'capitastar_3', 'capitastar_4'].map(
          (img) => ({
            name: img,
            type: 'png',
          })
        )
      "
    />

    <div class="space-y-10 overflow-hidden">
      <div
        v-for="(step, idx) in STEPS"
        :key="`step_${idx}`"
        class="flex flex-nowrap gap-3"
      >
        <div class="relative">
          <span class="line" />
          <p class="step">{{ idx + 1 }}</p>
        </div>
        <div class="text-base font-bold" v-html="step"></div>
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.line {
  position: absolute;
  left: 15px;
  width: 4px;
  height: 100vh;
  background: #1d1e58;
  bottom: calc(100% - 20px);
  z-index: -1;
}
.step {
  width: 34px;
  height: 34px;
  min-width: 34px;
  min-height: 34px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  color: #000000;
  font-weight: 700;
  justify-content: center;
  position: relative;
  z-index: 2;
  background: linear-gradient(180deg, #38e7d2 0%, #1da1d1 100%);
  &::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    z-index: 3;
    border-radius: 50%;
    border: 1px solid #000000;
  }
}
</style>
