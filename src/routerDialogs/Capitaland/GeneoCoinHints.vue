<script lang="ts" setup>
import { useTick } from '@composables';
import { timeCountDown } from '@helpers';
import { SentosaGoldenCoin, SentosaGoldenCoinHint } from '@types';

interface Emits {
  (e: 'close'): void;
}

interface Props {
  coin: SentosaGoldenCoin;
  capitalandGeneoCoin: SentosaGoldenCoin[];
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { now } = useTick();

const selectedCoin = ref(props.coin._id);

const activeCoins = computed(() => {
  return props.capitalandGeneoCoin.filter((coin) => {
    return ['ongoing', 'scheduled'].includes(coin.status);
  });
});

const currentCoin = computed(() =>
  props.capitalandGeneoCoin.find((coin) => coin._id === selectedCoin.value)
);

const hints = computed(() => {
  if (!currentCoin.value?.hints?.length) return [];
  return currentCoin.value.hints.sort((h1, h2) => {
    const getPriority = (hint: SentosaGoldenCoinHint) => {
      const isUnlocked = !!hint.content;
      const isSocialMedia = hint.is_social_media;

      if (isSocialMedia && isUnlocked) return 1;
      if (!isSocialMedia && isUnlocked) return 2;
      if (isSocialMedia && !isUnlocked) return 3;
      if (!isSocialMedia && !isUnlocked) return 4;
      return 5;
    };

    return getPriority(h1) - getPriority(h2);
  });
});
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        class="absolute -top-2 -left-2"
        shape="square"
        variant="secondary"
        @click="emits('close')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('CRYSTAL_COIN_HINTS_HEADER')"></div>
    </template>
    <q-select
      class="q-dark-select !mb-5"
      v-model="selectedCoin"
      :options="activeCoins"
      option-label="name"
      option-value="_id"
      options-selected-class="coin-selected"
      borderless
      behavior="menu"
      dense
      popup-content-class="dark-popup"
      emit-value
      map-options
      label="Coin"
    >
    </q-select>

    <div
      class="px-5 mb-5 text-base italic text-center"
      v-html="t('CRYSTAL_COIN_HINTS_DESC_4')"
    ></div>

    <div class="flex flex-col gap-5 wrapper" v-if="hints.length">
      <template v-for="(h, index) in hints" :key="h._id">
        <div
          class="flex flex-col gap-2 bg-[#091A3C] p-3 rounded"
          :class="{
            'social-media-hint': h.content,
            'locked-social-media-hint': !h.content,
          }"
        >
          <div class="flex items-center gap-1">
            <Icon :name="'social-media-hint'" />
            <div
              class="text-sm opacity-70"
              v-html="t('CRYSTAL_COIN_HINTS_DESC_2', { ORDER: index + 1 })"
            ></div>
          </div>
          <div class="ml-1 text-base font-medium">
            <span v-if="h.content" v-html="h.content"></span>
            <!-- <span
              class="opacity-50"
              v-html="
                t('CRYSTAL_COIN_HINTS_DESC_3', {
                  TIME: timeCountDown(+new Date(h.available_at) - now),
                })
              "
            >
            </span>-->
            <span v-if="!h.content">
              <span class="text-gray-500"> ??? </span>
              <br />
              <span class="text-white">
                Get this hint on Sqkii’s Instagram, Facebook page, or Telegram
                channel.
              </span>
              <br />
              <span
                class="text-white"
                v-if="
                  +new Date(h.available_at) && +new Date(h.available_at) > now
                "
              >
                Next hint drops in
                {{ timeCountDown(+new Date(h.available_at) - now) }}
              </span>
            </span>
          </div>
        </div>
      </template>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.wrapper {
  border-radius: 8px;
  border: 2px solid #212570;
  background: #03010f;
  box-shadow: 0px -16px 20px 0px rgba(0, 0, 0, 0.25) inset;
  padding: 8px;
}
.exclusive-hint {
  border: 2px solid #38cfe5;
  box-shadow: 0px 0px 7px 0px #11d1f9;
}

.social-media-hint {
  border: 2px solid #fc5ac9;
  box-shadow: 0px 0px 7px 0px #fc78d6;
}

.locked-exclusive-hint {
  border: 2px solid #38cfe5;
  box-shadow: 0px 0px 7px 0px #11d1f9;
  background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.2) 0%,
      rgba(0, 0, 0, 0.2) 100%
    ),
    #091a3c;
  opacity: 0.5;
}

.locked-social-media-hint {
  border: 2px solid #fc5ac9;
  box-shadow: 0px 0px 7px 0px #fc78d6;
  background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.2) 0%,
      rgba(0, 0, 0, 0.2) 100%
    ),
    #091a3c;
  opacity: 0.5;
}
</style>
<style lang="scss">
.q-dark-select {
  background-color: #04081d;
  height: 44px;
  border-radius: 10px;
  padding-left: 16px;
  margin: 0 10px;
  .q-field__native {
    color: white;
  }
  .q-field__label {
    z-index: 9999;
    color: white;
    opacity: 0.5;
  }
  .q-field__append {
    color: white;
  }
}
.dark-popup {
  max-height: 200px !important;
  width: calc(100% - 108px) !important;
  background: #04081d;
  color: #ffffff;
  margin: 5px 0 !important;
  left: 50% !important;
  transform: translateX(-50%);
  .q-item__section {
    .q-img--menu {
      margin-top: 8px;
    }
  }

  .q-item--active {
    color: #00e0ff;
    font-weight: bold;
  }
}
</style>
