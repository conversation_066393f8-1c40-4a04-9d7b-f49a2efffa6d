<script setup lang="ts">
import { useCapitalandHooks } from '@composables';
import { useDialogStore, useMapStore } from '@stores';
import { IAmenities } from '@types';
import { onClickOutside } from '@vueuse/core';
import gsap, { Linear } from 'gsap';

const storeDialog = useDialogStore();
const storeMap = useMapStore();

const { amenitiesMissions } = useCapitalandHooks();
const { t } = useI18n();
const { openDialog } = useMicroRoute();

const bottomSheetRef = ref(null);

onClickOutside(bottomSheetRef, () => {
  closeBottomSheet();
});

function openBottomSheet() {
  gsap.to(bottomSheetRef.value, {
    y: '0%',
    duration: 0.3,
    ease: Linear.easeIn,
  });
  storeDialog.showBottomSheet = true;
}

function closeBottomSheet() {
  gsap.to('.bottom-sheet', {
    y: '100%',
    duration: 0.3,
    ease: Linear.easeOut,
    onComplete: () => {
      storeDialog.showBottomSheet = false;
    },
  });
}

watch(
  () => storeDialog.showBottomSheet,
  (value) => {
    if (value) openBottomSheet();
    else closeBottomSheet();
  },
  { immediate: true }
);

function goToAmenity(amenity: IAmenities) {
  storeMap.mapIns?.panTo(
    [amenity.location.coordinates[0], amenity.location.coordinates[1]],
    {
      animate: true,
      duration: 1500,
    }
  );
  openDialog('amenities', {
    amenity,
  });
}
</script>

<template>
  <div
    ref="bottomSheetRef"
    v-bottom-sheet="{
      minHeight: 25,
      maxHeight: 50,
      initialHeight: 40,
      snapPoints: [20, 25, 30, 35, 40, 35, 50],
      animationDuration: 300,
      velocityThreshold: 0.5,
    }"
    class="bottom-sheet"
  >
    <div
      class="w-full h-7 bg-[#5D3AC04D] flex justify-center items-center bottom-sheet-handle"
    >
      <div class="w-20 h-2 rounded-lg bg-[#200d37]"></div>
    </div>
    <div
      class="flex flex-nowrap flex-col gap-2 h-[calc(100%-30px)] overflow-y-auto p-5"
    >
      <div
        class="bg-[#5D3AC04D] rounded-[10px] p-4 flex flex-col gap-3"
        v-for="amenity in amenitiesMissions"
        :key="amenity._id"
        @click="goToAmenity(amenity)"
      >
        <div class="text-base font-bold" v-html="t(amenity.name)"></div>
        <div
          class="text-sm"
          v-html="
            t('AMENITY_BOTTOM_SHEET_DESC_1', {
              DISTANCE: amenity.distanceInMeters?.toFixed(0),
            })
          "
        ></div>
        <q-separator class="bg-white opacity-50" />
        <div class="text-sm" v-html="t(amenity.address)"></div>
        <!-- <div class="text-sm space-x-1" v-if="amenity.canCheckIn">
          <span v-html="t('AMENITY_BOTTOM_SHEET_DESC_2')"></span>
          <span
            class="opacity-70"
            v-html="
              t('AMENITY_BOTTOM_SHEET_DESC_3', { TIME: amenity.closed_at })
            "
          ></span>
        </div>
        <div
          class="text-sm"
          v-else
          v-html="t('AMENITY_BOTTOM_SHEET_DESC_4')"
        ></div> -->
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.bottom-sheet {
  position: relative;
  border-radius: 8px 8px 0px 0px;
  background: #200d37;
  transform: translateY(100%);
}
</style>
