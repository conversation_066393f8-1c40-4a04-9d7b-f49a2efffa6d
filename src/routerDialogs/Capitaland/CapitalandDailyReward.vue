<script lang="ts" setup>
import { useDialogStore, useMapStore, useUserStore } from '@stores';
import { CapitalandDailyRewardItem } from '@routerDialogs';
import { playSFX, useAsync, useTrackData } from '@composables';
import { CAPITALAND } from '@repositories';
import { useWindowSize } from '@vueuse/core';
import { errorNotify } from '@helpers';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination } from 'swiper/modules';
import type { IAPIResponseError, SentosaGoldenCoin } from '@types';
import { Swiper as ISwiper } from 'swiper/types';
import 'swiper/css';
import 'swiper/css/pagination';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeMap = useMapStore();
const storeDialog = useDialogStore();

const { capitalandDailyRewards, capitalandGeneoCoin, isEnabledGPS, user } =
  storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { openDialog, closeAllDialog } = useMicroRoute();
const { t } = useI18n();
const { width } = useWindowSize();
const { track } = useTrackData();

const pagination = {
  clickable: true,
  dynamicBullets: true,
};
const modules = [Pagination];

const mySwiper = ref<ISwiper | undefined>();
const slide = ref(0);

function filterDays(index: number, day: number) {
  const dayRanges = [
    [1, 2, 3, 4],
    [8, 9, 10, 11],
    [15, 16, 17, 18],
    [22, 23, 24, 25],
    [29, 30],
  ];
  return dayRanges[index]?.includes(day);
}

const flatDailyRewards = computed(() => capitalandDailyRewards.value.flat());

const currentDay = computed(() => {
  const date = new Date();
  return date.getDate();
});

const day = computed(() => {
  const rewards = flatDailyRewards.value.filter((d) => !d.claimed_at);
  return rewards.at(0)?.day || 1;
});

const checkedIn = computed(() => {
  const rewards = flatDailyRewards.value.filter((d) => d.claimed_at);
  const lastReward = rewards.at(-1);
  if (!lastReward) return false;
  const lastDate = new Date(lastReward.claimed_at);
  return lastDate.getDate() === currentDay.value;
});

function onSwiper(swiper: ISwiper) {
  mySwiper.value = swiper;
}

const { execute: dailyCheckin, loading } = useAsync({
  fn: async () => {
    const [lng, lat] = lastLocations.value;
    const { data } = await CAPITALAND.checkin({
      lng,
      lat,
    });
    return data;
  },
  onSuccess: async (data) => {
    if (!data) return;
    track('captitaland_daily_rewards_receive', {
      captitaland_daily_reward: data.reward_type,
      captitaland_daily_day: data.day,
    });
    await storeUser.fetchCapitalandDailyRewards();
    await storeUser.fetchCapitalandGeneoCoin();
    await storeUser.fetchUser();
    if (data.reward_type === 'crystal')
      openDialog('promo_success', {
        crystal: data.amount,
      });
    else
      openDialog('capitaland_daily_reward_claimed', {
        type: data.reward_type,
        data,
      });
  },
  onError: (error: IAPIResponseError) => {
    const { error_message } = error;
    switch (error_message) {
      case 'not_in_zone':
        errorNotify({
          message: t('CAPITALAND_CHECKIN_ERROR_LOCATION'),
        });
        break;
      default:
        errorNotify({
          message: error_message,
        });
        break;
    }
  },
});

function openHint(coin: SentosaGoldenCoin) {
  playSFX('button');
  switch (coin.status) {
    case 'scheduled':
      errorNotify({
        message: t('CAPITALAND_NO_HINTS_ERROR'),
      });
      break;
    case 'found':
    case 'verifying':
    case 'forfeited':
      errorNotify({
        message: t('CAPITALAND_COIN_NO_LONGER_ERROR'),
      });
      break;
    default:
      openDialog('geneo_coin_hints', {
        coin,
        capitalandGeneoCoin,
      });
      break;
  }
}

function onClose() {
  emits('close');
  if (!checkedIn.value && !isEnabledGPS.value)
    errorNotify({
      message: t('CAPITALAND_REMINDER_GPS'),
      timeout: 1000 * 60 * 5,
    });
}

onMounted(async () => {
  storeDialog.showedSentosaDailyReward = true;
  track('capitaland_daily_rewards_pop');
  await Promise.all([
    storeUser.fetchCapitalandDailyRewards(),
    storeUser.fetchCapitalandGeneoCoin(),
  ]);

  const currentSlide = capitalandDailyRewards.value.findIndex((weeks) =>
    weeks.some((d) => d.day === day.value)
  );

  slide.value = currentSlide !== -1 ? currentSlide : 0;
});
</script>
<template>
  <Dialog @close="onClose">
    <template #header>
      <div v-html="t('CAPITALAND_DAILY_REWARD_HEADER')"></div>
    </template>
    <div class="text-center">
      <div
        class="px-5 mb-5 text-sm"
        v-html="t('CAPITALAND_DAILY_REWARD_TITLE')"
      ></div>
      <div class="relative">
        <Icon name="star_1" class="absolute -left-3 -top-2 z-10 !w-[30px]" />
        <Icon
          name="star_2"
          class="absolute -bottom-2 -right-2 z-10 !w-[30px]"
        />
        <q-carousel
          animated
          v-model="slide"
          infinite
          swipeable
          class="h-[unset] bg-transparent text-[#141414] w-full mb-10"
        >
          <template
            v-for="(weeks, index) in capitalandDailyRewards"
            :key="index"
          >
            <q-carousel-slide :name="index" class="bg !px-3 !pt-4">
              <div
                class="relative z-10 flex flex-col justify-between w-full h-full gap-5 px-2 flex-nowrap"
              >
                <div class="flex items-center justify-around">
                  <CapitalandDailyRewardItem
                    v-for="d in weeks.filter((d) => filterDays(index, d.day))"
                    :key="d.day"
                    :item="d"
                    :day="day"
                  />
                </div>
                <div
                  class="flex justify-center"
                  :class="{
                    'gap-10':
                      weeks.filter((d) => !filterDays(index, d.day)).length ===
                      2,
                    'gap-5':
                      weeks.filter((d) => !filterDays(index, d.day)).length ===
                      3,
                  }"
                >
                  <CapitalandDailyRewardItem
                    v-for="d in weeks.filter((d) => !filterDays(index, d.day))"
                    :key="d.day"
                    :item="d"
                    :day="day"
                    reverse
                  />
                </div>
              </div>
            </q-carousel-slide>
          </template>
        </q-carousel>
      </div>
      <div
        class="relative flex items-center justify-center gap-3"
        v-if="capitalandDailyRewards.length > 1"
        :class="{
          'mb-10': capitalandDailyRewards.length > 1,
          'mb-5': capitalandDailyRewards.length === 1,
        }"
      >
        <div
          class="bg-white rounded-full opacity-50 size-2"
          :class="{
            '!opacity-100 !bg-[#00e0ff] w-4': slide === i - 1,
          }"
          v-for="i in capitalandDailyRewards.length"
          :key="i"
          @click="slide = i - 1"
        ></div>
        <div class="absolute flex items-center justify-between w-full">
          <div
            @click="
              playSFX('button');
              slide =
                slide <= 0 ? capitalandDailyRewards.length - 1 : slide - 1;
            "
            class="w-[64px] h-[46px] bg-contain"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
            }"
          ></div>
          <div
            @click="
              playSFX('button');
              slide =
                slide >= capitalandDailyRewards.length - 1 ? 0 : slide + 1;
            "
            class="w-[64px] h-[46px] bg-contain rotate-180"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
            }"
          ></div>
        </div>
      </div>
      <Button
        v-if="!checkedIn || storeMap?.devTools?.dailyRewardMode"
        :label="t('CAPITALAND_DAILY_REWARD_BTN_CHECKIN')"
        class="!w-[250px]"
        :loading="loading"
        @click="dailyCheckin"
      />
      <q-separator class="my-5 w-[150px] h-0.5 mx-auto bg-white opacity-30" />

      <div class="mb-5 text-sm" v-html="t('CAPITALAND_DAILY_DESC_1')"></div>

      <Swiper
        :slides-per-view="width / 315"
        :initial-slide="1"
        :pagination="pagination"
        :modules="modules"
        :space-between="20"
        centered-slides
        class="capitaland-coin-swiper"
        :class="{
          '!h-[150px]': capitalandGeneoCoin.length > 1,
          '!h-[80px]': capitalandGeneoCoin.length === 1,
        }"
        @swiper="onSwiper"
      >
        <SwiperSlide
          v-for="(coin, index) in capitalandGeneoCoin"
          :key="index"
          v-slot="{ isActive }"
        >
          <div
            @click="openHint(coin)"
            class="flex items-center justify-between slide-item"
            :class="{
              'slide-item-active': isActive,
              'pointer-events-none': !isActive,
            }"
          >
            <div class="flex items-center gap-3">
              <div class="relative siz-[30px] rounded-full">
                <Icon name="capitaland-coin" :size="30" />
              </div>
              <div class="flex flex-col items-start">
                <div
                  class="text-sm"
                  :class="{
                    'opacity-70': ['verifying', 'found', 'forfeited'].includes(
                      coin.status
                    ),
                  }"
                >
                  {{
                    coin.status !== 'scheduled' ||
                    (coin.status === 'scheduled' && coin.hints?.length)
                      ? coin.name
                      : '???'
                  }}
                </div>
                <div class="text-sm">
                  <template v-if="coin.status === 'ongoing'">
                    <span
                      class="opacity-50"
                      v-html="t('SENTOSA_CRYSTAL_DETECTOR_DESC_2')"
                    ></span>
                  </template>
                  <template v-else-if="coin.status === 'scheduled'">
                    <span
                      class="opacity-50"
                      v-html="t('SENTOSA_CRYSTAL_DETECTOR_DESC_3')"
                    ></span>
                  </template>
                  <template v-else>
                    <span class="capitalize" :class="[coin.status]">
                      {{ coin.status }}
                    </span>
                  </template>
                </div>
              </div>
            </div>
            <Icon name="arrow-left" class="rotate-180" v-if="isActive" />
          </div>
        </SwiperSlide>

        <div
          class="absolute z-50 flex justify-between w-full bottom-7"
          v-if="capitalandGeneoCoin.length > 1"
        >
          <div
            @click="
              playSFX('button');
              mySwiper?.slidePrev();
            "
            class="w-[64px] h-[46px] bg-contain"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
              opacity: mySwiper?.activeIndex === 0 ? 0.5 : 1,
              pointerEvents: mySwiper?.activeIndex === 0 ? 'none' : 'auto',
            }"
          ></div>
          <div
            @click="
              playSFX('button');
              mySwiper?.slideNext();
            "
            class="w-[64px] h-[46px] bg-contain rotate-180"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
              opacity:
                mySwiper?.activeIndex === capitalandGeneoCoin.length - 1
                  ? 0.5
                  : 1,
              pointerEvents:
                mySwiper?.activeIndex === capitalandGeneoCoin.length - 1
                  ? 'none'
                  : 'auto',
            }"
          ></div>
        </div>
      </Swiper>
      <!-- <div
        class="px-5 text-sm italic"
        v-html="t('CAPITALAND_DAILY_DESC_2')"
      ></div> -->
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.bg {
  position: relative;
  border-radius: 10px;
  border: 2px solid #445b2a;
  background: #f0eee1;

  width: 100%;
  &::before {
    content: '';
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 245px;
    height: 65px;
    background-image: url('imgs/line-reward-2.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}

.box {
  border-radius: 6px;
  border: 1px solid rgba(215, 27, 232, 0);
  background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.5) 0%,
      rgba(0, 0, 0, 0.5) 100%
    ),
    linear-gradient(90deg, #7147cd 0%, #d834cf 100%);
}

.text-border {
  text-shadow: rgb(65, 6, 60) 2px 0px 0px,
    rgb(65, 6, 60) 1.75517px 0.958851px 0px,
    rgb(65, 6, 60) 1.0806px 1.68294px 0px,
    rgb(65, 6, 60) 0.141474px 1.99499px 0px,
    rgb(65, 6, 60) -0.832294px 1.81859px 0px,
    rgb(65, 6, 60) -1.60229px 1.19694px 0px,
    rgb(65, 6, 60) -1.97998px 0.28224px 0px,
    rgb(65, 6, 60) -1.87291px -0.701566px 0px,
    rgb(65, 6, 60) -1.30729px -1.5136px 0px,
    rgb(65, 6, 60) -0.421592px -1.95506px 0px,
    rgb(65, 6, 60) 0.567324px -1.91785px 0px,
    rgb(65, 6, 60) 1.41734px -1.41108px 0px,
    rgb(65, 6, 60) 1.92034px -0.558831px 0px;
}
.found {
  color: #12ec6d;
}
.verifying {
  color: #c536a5;
}
.forfeited {
  color: #9f1515;
}
.scheduled {
  color: #38cfe5;
}
</style>
<style lang="scss">
.capitaland-coin-swiper {
  .slide-item {
    border-radius: 4px;
    background: #091a3c;
    padding: 8px 10px;
    opacity: 0.6;
    &.slide-item-active {
      border: 1px solid #38cfe5;
      box-shadow: 0px 0px 7px 0px #11d1f9, 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
      opacity: 1;
    }
  }
  .swiper-pagination {
    top: 55px;
    display: flex;
    align-items: center;
    width: 80px !important;
    overflow: visible;
    .swiper-pagination-bullet-active {
      background-color: #00e0ff !important;
      width: 15px !important;
      border-radius: 4px;
    }
    .swiper-pagination-bullet {
      background-color: #ffffff50;
    }
  }
}
</style>
