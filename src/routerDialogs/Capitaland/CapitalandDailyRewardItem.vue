<script lang="ts" setup>
import { numeralFormat } from '@helpers';
import { SentosaDailyReward } from '@types';

interface Props {
  item: SentosaDailyReward;
  day: number;
  reverse?: boolean;
}

withDefaults(defineProps<Props>(), {
  reverse: false,
});

const { t } = useI18n();
</script>
<template>
  <div
    class="flex flex-col items-center justify-center gap-1"
    :class="{
      'flex-col-reverse': reverse,
    }"
  >
    <div
      class="text-sm font-medium text-[#445B2A]"
      v-html="t('REWARD_DAY', { NUMBER: item.day })"
    ></div>
    <div
      class="relative size-12 rounded-full bg-[#445B2A] flex justify-center items-center"
      :class="{
        'bg-white border-2 border-[#445B2A] box-shadow':
          day === item.day || !!item.claimed_at,
      }"
    >
      <q-tooltip
        class="sentosa-reward-tooltip flex items-center gap-1"
        anchor="top middle"
        self="bottom middle"
        :offset="[10, 15]"
      >
        <div class="text-lg font-bold">{{ numeralFormat(item.amount) }}</div>
        <template
          v-if="['capitaland_hint', 'sentosa_hint'].includes(item.reward_type)"
        >
          <Icon name="light-bulb" :size="16" />
        </template>
        <template v-else-if="item.reward_type === 'sentosa_crystal_detector'">
          <Icon name="sentosa-metal-detector" :size="16" />
        </template>
        <template v-else-if="item.reward_type === 'sentosa_metal_detector'">
          <Icon name="sentosa-silver-detector" :size="16" />
        </template>
        <template v-else>
          <div class="flex items-center flex-nowrap">
            <Icon name="crystal" :size="16" />
          </div>
        </template>
      </q-tooltip>
      <template v-if="item.claimed_at">
        <div
          class="absolute bg-[#424242] w-full h-full top-0 left-0 rounded-full opacity-85"
        ></div>
        <svg
          class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="25"
          viewBox="0 0 24 25"
          fill="none"
        >
          <path
            d="M20.4984 2.19214L9.50156 16.6906L3 10.1937L0 13.1937L9.99844 23.1921L24 5.19214L20.4984 2.19214Z"
            fill="#445B2A"
          />
        </svg>
      </template>
      <template
        v-if="['capitaland_hint', 'sentosa_hint'].includes(item.reward_type)"
      >
        <Icon name="light-bulb" :size="25" />
      </template>
      <template v-else-if="item.reward_type === 'sentosa_crystal_detector'">
        <Icon name="sentosa-metal-detector" :size="25" />
      </template>
      <template v-else-if="item.reward_type === 'sentosa_metal_detector'">
        <Icon name="sentosa-silver-detector" :size="25" />
      </template>
      <template v-else>
        <div class="flex items-center flex-nowrap">
          <Icon name="crystal-inner" :size="25" />
        </div>
      </template>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.box-shadow {
  box-shadow: 0px 4px 24px 4px rgba(188, 24, 214, 0.3);
}
</style>
<style lang="scss">
.sentosa-reward-tooltip {
  background-color: #653fc9;
  border: 2px solid #00fdff;
  padding: 0 8px !important;
  overflow: visible !important;

  &::before {
    position: absolute;
    content: '';
    background-image: url('imgs/tooltip-polygon.png');
    width: 15px;
    height: 10px;
    background-size: 100% 100%;
    left: 50%;
    transform: translateX(-50%);
    bottom: -10px;
  }
}
</style>
