<script lang="ts" setup>
import { BrandActionItem } from '@components';
import { useBrandActions } from '@composables';
import { useBAStore } from '@stores';

const brandHooks = useBrandActions();
const storeBA = useBAStore();

const { user_brand_actions, newBrandActions } = storeToRefs(storeBA);
const { push, closeAllDialog } = useMicroRoute();
const { t } = useI18n();

const PROMO = [
  t('LYNDEN_WOODS_MAP_ICON_PROMO_1'),
  t('LYNDEN_WOODS_MAP_ICON_PROMO_2'),
  t('LYNDEN_WOODS_MAP_ICON_PROMO_3'),
];

const brandAction = computed(() => {
  const listBa = [...user_brand_actions.value, ...newBrandActions.value].find(
    (item) => item.group_id === 'cl_2'
  );
  return listBa;
});
</script>
<template>
  <Dialog>
    <template #header>
      <div
        class="text-lg font-bold"
        v-html="t('LYNDEN_WOODS_MAP_ICON_TITLE')"
      ></div>
      <div class="text-sm" v-html="t('LYNDEN_WOODS_MAP_ICON_DESC')"></div>
    </template>
    <Icon name="lynden_woods_kv" :size="200" class="rounded mx-auto mb-5" />
    <div class="flex flex-col gap-5 mb-4">
      <div
        class="flex items-start flex-nowrap gap-2"
        v-for="(text, index) in PROMO"
        :key="index"
      >
        <Icon name="progress" class="!w-6 !h-6" />
        <div class="-mt-1" v-html="text"></div>
      </div>
    </div>
    <BrandActionItem
      class="-mx-2"
      :brandHooks="brandHooks"
      v-if="brandAction"
      :data="brandAction"
      show-type="twinkle"
      :custom-action="
        () => {
          closeAllDialog();
          push('offer_wall');
        }
      "
      from-sponsor
    />
  </Dialog>
</template>
