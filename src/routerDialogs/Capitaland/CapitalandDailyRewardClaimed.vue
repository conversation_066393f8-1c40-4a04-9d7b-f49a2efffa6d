<script lang="ts" setup>
import type { SentosaD<PERSON>y<PERSON><PERSON><PERSON>, SentosaRewardType } from '@types';

interface Props {
  type: SentosaRewardType;
  data: SentosaDailyReward;
}

defineProps<Props>();

const { closeDialog } = useMicroRoute();
const { t } = useI18n();
</script>

<template>
  <div
    class="bg-[#00000030] flex flex-col justify-center items-center p-7"
    @click="closeDialog('capitaland_daily_reward_claimed')"
  >
    <div
      class="text-sm mb-2.5"
      v-html="t('CAPITALAND_DAILY_CLAIMED_TITLE')"
    ></div>
    <div
      class="text-2xl font-bold mb-6"
      v-html="t('CAPITALAND_DAILY_CLAIMED_HINT')"
    ></div>
    <template v-if="type === 'capitaland_hint'">
      <div class="bg p-6 text-center mb-6">
        <div
          class="text-sm text-[#481700] mb-2.5"
          v-html="t('CAPITALAND_DAILY_CLAIMED_DESC_1')"
        ></div>
        <div
          class="bg-[#BB4C02] px-2.5 py-1.5 border border-[#FDC274] w-max mx-auto mb-2.5 rounded"
          v-html="data.hint.coin_name"
        ></div>
        <div
          class="text-2xl font-bold text-[#481700]"
          v-html="data.hint.content"
        ></div>
      </div>
    </template>

    <div
      class="text-sm italic opacity-70"
      v-html="t('CAPITALAND_DAILY_CLAIMED_DESC_2')"
    ></div>
  </div>
</template>
<style lang="scss" scoped>
.bg {
  position: relative;
  background-image: url('imgs/sentosa-daily-bg.png');
  background-size: 100% 100%;
  background-position: center;
  width: 100%;
}
</style>
