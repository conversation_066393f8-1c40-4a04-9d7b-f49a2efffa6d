<script setup lang="ts">
import { useUserStore } from '@stores';
import { useTrackData, useClick, useAsync } from '@composables';
import gsap, { Power1 } from 'gsap';

interface Emits {
  (e: 'close'): void;
  (e: 'tap'): void;
}

const emits = defineEmits<Emits>();

const tl = gsap.timeline();

const storeUser = useUserStore();

const { currentSeason, user, settings, hasGoldenCoin } = storeToRefs(storeUser);
const { track } = useTrackData();
const { t } = useI18n();
const { openDialog } = useMicroRoute();

const lang = {
  vi: 'English',
  en: 'Vietnamese',
  ja: 'Japanese',
};

const { loading, execute: updateOnboarding } = useAsync({
  async fn() {
    await storeUser.updateOnboarding('first_entry');
  },
});

useClick('goSetting', async () => {
  openDialog('lang');
  track('auto_window_other', {
    screen_type: 'welcome_hunter',
    other_button: 'game_language',
  });
  emits('close');
});

useClick('goPastVideo', () => {
  openDialog('past_video');
  track('auto_window_other', {
    screen_type: 'welcome_hunter',
    other_button: 'past_hunts',
  });
  emits('close');
});

async function handleStartHunt() {
  await track('start_hunting');
  await updateOnboarding();
  emits('close');
}

function startAnimation() {
  tl.fromTo(
    '.title',
    {
      opacity: 0,
      y: 10,
    },
    {
      y: 0,
      opacity: 1,
      duration: 0.5,
      delay: 0.5,
      ease: Power1.easeInOut,
    },
    '-=.2'
  )
    .fromTo(
      '.coin',
      {
        opacity: 0,
        scale: 0.5,
      },
      {
        duration: 0.5,
        opacity: 1,
        scale: 1,
      },
      '-=.5'
    )
    .fromTo(
      '.glow',
      {
        opacity: 0,
      },
      {
        opacity: 1,
        duration: 1,
        ease: Power1.easeInOut,
      },
      '-=.5'
    )
    .fromTo(
      ['.text-coin', '.btn-start', '.lang'],
      {
        opacity: 0,
        y: 10,
      },
      {
        y: 0,
        opacity: 1,
        duration: 0.5,
        stagger: 0.5,
        ease: Power1.easeInOut,
      },
      '-=.7'
    );
}

onMounted(async () => {
  await nextTick();
  track('onboarding');
  startAnimation();
});

onBeforeUnmount(() => {
  tl.kill();
});
</script>
<template>
  <Dialog hideClose>
    <template #header>
      <Icon name="logo_htm" class="w-auto h-11" />
    </template>
    <div class="text-center w-full">
      <div
        class="text-sm title mb-10"
        v-html="t('ONBOARDING_INSTRUCTION_1')"
      ></div>

      <div
        class="gap-16"
        :class="{
          'grid grid-cols-2': hasGoldenCoin,
          'flex justify-center items-center': !hasGoldenCoin,
        }"
      >
        <div class="relative flex justify-center mb-7" v-if="hasGoldenCoin">
          <Icon
            class="glow absolute -top-1/4 left-1/2 -translate-x-1/2"
            name="glow-silver-coin"
            :size="150"
          />
          <Icon class="coin z-10" name="golden-coin" :size="100" />
        </div>
        <div class="relative flex justify-center mb-7">
          <Icon
            class="glow absolute -top-1/4 left-1/2 -translate-x-1/2"
            name="glow-silver-coin"
            :size="150"
          />
          <Icon class="coin z-10" name="silver-coin" :size="100" />
        </div>
      </div>

      <div
        class="gap-16"
        :class="{
          'grid grid-cols-2': hasGoldenCoin,
          'flex justify-center items-center': !hasGoldenCoin,
        }"
      >
        <div class="text-coin mb-5" v-if="hasGoldenCoin">
          <div
            class="text-xl"
            v-html="t('ONBOARDING_COINVALUE_GOLDEN_1')"
          ></div>
          <div class="flex justify-center items-end gap-1">
            <span
              class="text-xl font-bold"
              v-html="t('ONBOARDING_COINVALUE_GOLDEN_2')"
            >
            </span>
            <span
              class="text-base"
              v-html="t('ONBOARDING_COINVALUE_GOLDEN_3')"
            ></span>
          </div>
        </div>
        <div class="text-coin mb-5">
          <div
            class="text-xl"
            v-html="t('ONBOARDING_COINVALUE_SILVER_1')"
          ></div>
          <div class="flex justify-center items-end gap-1">
            <span class="text-xl font-bold">
              {{ currentSeason?.silver_reward }}
            </span>
            <span
              class="text-base"
              v-html="t('ONBOARDING_COINVALUE_SILVER_2')"
            ></span>
          </div>
        </div>
      </div>
      <div
        class="text-coin bg-[#09090980] p-2 rounded-lg mb-4"
        v-html="t('ONBOARDING_COIN_PAST_VIDEO')"
      ></div>
      <div class="btn-start">
        <Button
          class="mx-auto"
          :label="t('ONBOARDING_BUTTON')"
          @click="
            handleStartHunt();
            emits('tap');
          "
          :loading="loading"
        />
      </div>
      <div
        v-if="Number(settings?.supported_languages.length) > 1"
        class="my-3 lang"
        v-html="
          t('LANDING_ENGPROMPT_DONOTTRANSLATE', {
            LANG: user && lang[user?.lang],
          })
        "
      ></div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
  margin-top: -100px;
}
</style>
