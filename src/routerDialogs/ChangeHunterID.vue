<script setup lang="ts">
import { useUserStore } from '@stores';
import { useAsync, useTrackData } from '@composables';
import { USER } from '@repositories';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();
const storeUser = useUserStore();

const { user, settings, crystals } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

const feeChangeId = computed(() => {
  if (!user.value || !settings.value) return 0;

  const changeHuntIdFee = settings.value?.change_hunt_id_fee;
  const fee = changeHuntIdFee.find(
    (x) => Number(x?.attempt) <= Number(user.value?.change_hunter_id_attempts)
  );
  return fee?.amount || 0;
});

const { loading, execute: handleChangeHunter } = useAsync({
  async fn() {
    const { data } = await USER.changeHunterId();
    track('settings_change_hunterid_popup', {
      action: !feeChangeId.value
        ? 'confirm_change_hunterid_free'
        : 'confirm_change_hunterid_withcrystals',
    });
    return data;
  },
  async onSuccess({ hunter_id, next_fee }) {
    await storeUser.fetchUser();
    await storeUser.fetchSetting();
    openDialog('change_hunter_id_success', {
      hunter_id,
      next_fee,
    });
    emits('close');
  },
});
</script>

<template>
  <Dialog class="text-center" hide-close crystals>
    <template #btnTopLeft>
      <Button
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="emits('close')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>

    <template #header>
      <div v-html="t('CHANGEHUNTERID_POPUP_HEADING')"></div>
    </template>

    <div class="mb-2 text-sm" v-html="t('CHANGEHUNTERID_POPUP_TEXT_1')"></div>
    <div
      class="mb-2 text-lg font-bold"
      v-html="
        t('CHANGEHUNTERID_POPUP_TEXT_SUGGESTION_1', {
          HUNTER_ID: user?.hunter_id,
        })
      "
    ></div>
    <div class="mb-5" v-html="t('CHANGEHUNTERID_POPUP_TEXT_2')"></div>

    <Button
      class="mx-auto"
      :title="t('CHANGEHUNTERID_POPUP_BUTTON_CONFIRMCHANGE')"
      :amount="feeChangeId"
      :disable="crystals < feeChangeId"
      :loading="loading"
      @click="handleChangeHunter"
    />
  </Dialog>
</template>
