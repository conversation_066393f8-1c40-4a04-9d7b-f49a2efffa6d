<script lang="ts" setup>
import { useDialogStore, useUserStore } from '@stores';
import { useAsync, useClick, useGlobal } from '@composables';
import { BONUS } from '@repositories';

interface Emits {
  (e: 'close'): void;
  (e: 'closeX'): void;
  (e: 'claim'): void;
  (e: 'getMore'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeDialog = useDialogStore();

const { user } = storeToRefs(storeUser);
const { t } = useI18n();
const { isClaimDailyLogin, sameDay, trackUserLocation } = useGlobal();
const { openDialog, push } = useMicroRoute();

useClick('goOfferWall', () => {
  emits('getMore');
  onClose();
  push('offer_wall');
});

const REWARD = [5, 5, 30, 10, 10, 10, 60, 20];

function onClose() {
  storeDialog.dailyLoginTicked = true;
  emits('close');
}

const { loading, execute: claimDailyReward } = useAsync({
  async fn() {
    const { data } = await BONUS.claimDailyReward();
    return data;
  },
  async onSuccess({ crystal }) {
    await storeUser.fetchUser();
    emits('claim');
    trackUserLocation('daily_reward');
    onClose();
    trackUserLocation('daily_login', {
      day: user.value?.claimed_daily_reward.length,
    });
    openDialog('promo_success', {
      crystal,
    });
  },
});
</script>
<template>
  <Dialog
    @close="
      onClose();
      emits('closeX');
    "
  >
    <template #header>
      <div class="text-base font-bold" v-html="t('LOGINBONUS_TITLE')"></div>
    </template>
    <div class="text-center">
      <div
        class="px-5 mb-6"
        v-html="
          user?.claimed_daily_reward?.length === 8
            ? t('LOGINBONUS_DESC_2')
            : sameDay
            ? t('LOGINBONUS_DESC_3')
            : t('LOGINBONUS_DESC_1')
        "
      ></div>
    </div>
    <div class="grid grid-cols-4 gap-2 mb-6">
      <div
        class="item"
        :class="{
          active:
            index === user?.claimed_daily_reward.length && isClaimDailyLogin,
          spec: [2, 6].includes(index),
          'opacity-50':
            user?.claimed_daily_reward &&
            index < user?.claimed_daily_reward.length,
        }"
        v-for="(r, index) in REWARD"
        :key="index"
      >
        <div
          class="mb-1 font-medium text-center"
          v-html="
            t('LOGINBONUS_DAY', {
              DAY: index + 1,
            })
          "
        ></div>
        <template
          v-if="
            user?.claimed_daily_reward &&
            index < user?.claimed_daily_reward.length
          "
        >
          <div class="flex justify-center">
            <Icon name="check-bold" :size="24" />
          </div>
        </template>
        <template v-else>
          <div class="flex items-center justify-center -ml-2 flex-nowrap">
            <Icon name="crystal" :size="36" />
            <div class="-ml-1 text-lg font-bold">{{ r }}</div>
          </div>
        </template>
      </div>
    </div>
    <div v-if="isClaimDailyLogin" class="mb-5 text-center">
      <Button
        :label="
          t('LOGINBONUS_BUTTON_CLAIM', {
            DAY: Number(user?.claimed_daily_reward.length) + 1,
          })
        "
        :loading="loading"
        @click="claimDailyReward"
      />
    </div>

    <div class="text-center" v-html="t('LOGINBONUS_MORECRYSTALS')"></div>
  </Dialog>
</template>
<style lang="scss" scoped>
.item {
  padding: 12px 6px;
  background: rgba(0, 253, 255, 0.2);
  border-radius: 6px;
}
.active {
  border: 2px solid #00fdff;
}
.spec {
  background: rgba(118, 69, 222, 0.8);
}
</style>
