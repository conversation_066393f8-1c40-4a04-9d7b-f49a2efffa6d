<script lang="ts" setup>
import { useDialogStore, useUserStore } from '@stores';
import { useAsync, useClick, useGlobal } from '@composables';
import { BONUS } from '@repositories';

interface Emits {
  (e: 'close'): void;
  (e: 'closeX'): void;
  (e: 'claim'): void;
  (e: 'getMore'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeDialog = useDialogStore();

const {
  hoursBonusCountdown,
  isInTimeRange,
  isTriggerHoursBonus,
  trackUserLocation,
} = useGlobal();
const { t } = useI18n();
const { openDialog, push } = useMicroRoute();

useClick('goOfferWall', () => {
  emits('getMore');
  onClose();
  push('offer_wall');
});

function onClose() {
  storeDialog.hoursBonusTicked = true;
  emits('close');
}

const { loading, execute: claimHourReward } = useAsync({
  async fn() {
    const { data } = await BONUS.claimHourReward();
    return data;
  },
  async onSuccess({ crystal }) {
    await storeUser.fetchUser();
    emits('claim');
    trackUserLocation('hour_reward');
    onClose();
    trackUserLocation('hour_bonus', {
      device_time: new Date().toISOString(),
    });
    openDialog('promo_success', {
      crystal,
    });
  },
});
</script>
<template>
  <Dialog
    @close="
      onClose();
      emits('closeX');
    "
  >
    <template #header>
      <div
        class="text-base font-bold"
        v-html="
          !isTriggerHoursBonus
            ? t('2HRBONUS_TITLE_UNAVAILABLE')
            : t('2HRBONUS_TITLE_AVAILABLE')
        "
      ></div>
    </template>
    <div class="text-center">
      <div class="mb-6" v-html="t('2HRBONUS_DESC')"></div>
      <div class="mb-6 time">
        <div>
          <div class="text">
            {{ hoursBonusCountdown?.manual.hours ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('COUNTDOWN_TIMER_HOURS') }}
          </div>
        </div>
        <div>
          <div class="text">
            {{ hoursBonusCountdown?.manual.minutes ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('COUNTDOWN_TIMER_MINUTES') }}
          </div>
        </div>
        <div>
          <div class="text">
            {{ hoursBonusCountdown?.manual.seconds ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('COUNTDOWN_TIMER_SECONDS') }}
          </div>
        </div>
      </div>
    </div>
    <div class="mb-5 text-center" v-if="!hoursBonusCountdown && isInTimeRange">
      <Button
        :label="t('2HRBONUS_BUTTON_CLAIM')"
        :loading="loading"
        @click="claimHourReward"
      />
    </div>
    <div class="text-center" v-html="t('2HRBONUS_MORECRYSTALS')"></div>
  </Dialog>
</template>
<style lang="scss" scoped>
.time {
  display: flex;
  justify-content: space-between;
  background: linear-gradient(180deg, #129cbb 0%, #6e60cb 76.68%);
  border-radius: 10px;
  padding: 10px 20px;
  .text {
    font-weight: 800;
    font-size: 44px;
    line-height: 44px;
  }
}
@media screen and (max-width: 370px) {
  .time {
    padding: 5px 10px;
    .text {
      font-weight: 800;
      font-size: 32px;
      line-height: 32px;
    }
  }
}
</style>
