<script setup lang="ts">
import { vi, en } from '@helpers';
import { useUserStore } from '@stores';
import { ITAC } from '@types';

interface ITACs {
  [key: string]: ITAC[];
}

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { settings } = storeToRefs(storeUser);
const { t } = useI18n();
const { push } = useRouter();
const { closeDialog, openDialog } = useMicroRoute();

const tab = ref('en');

const onBack = () => {
  push({ path: '/', replace: true });
  closeDialog('tac');
  emits('close');
};

const TACs = computed(() => {
  const data: ITACs = { vi, en };
  if (!settings.value?.supported_languages) return {} as ITACs;
  if (Number(settings.value?.supported_languages.length) === 1)
    return {
      [settings.value?.supported_languages[0]]:
        data[settings.value?.supported_languages[0]],
    };
  return settings.value.supported_languages.reduce((acc, lang) => {
    acc[lang] = data[lang];
    return acc;
  }, {} as ITACs);
});

function handleClick(e: Event) {
  const target = e.target as HTMLElement;
  switch (target.id) {
    case 'sentosa-oulets':
      openDialog('sentosa_outlets');
      break;
    default:
      break;
  }
}

onMounted(() => {
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>

<template>
  <div class="fullscreen tac">
    <div class="tac-header fixed top-0 left-0 z-10">
      <Button
        class="absolute top-2"
        shape="square"
        variant="secondary"
        @click="onBack"
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="absolute text-center top-4 left-1/2 -translate-x-1/2">
        <p
          class="text-lg font-extrabold whitespace-nowrap"
          v-html="t('TERMS_TITLE')"
        ></p>
        <div class="text-xs" v-html="t('TERMS_LASTUPDATED')"></div>
      </div>
    </div>
    <template v-if="Number(settings?.supported_languages.length) > 1">
      <q-tabs v-model="tab" dense class="tab-tc">
        <q-tab
          v-for="lang in settings?.supported_languages || []"
          :key="lang"
          :name="lang"
          :label="lang === 'en' ? t('TERMS_ENGTAB') : t('TERMS_TRANSLATE_TAB')"
        />
      </q-tabs>
      <q-tab-panels
        class="tc-tabs-panels"
        v-model="tab"
        animated
        style="
          background: linear-gradient(
            180deg,
            rgba(81, 29, 133, 0.8) 0%,
            rgba(32, 13, 55, 0.8) 56%
          );
          height: calc(100% - 56px);
          padding-bottom: 15px;
        "
      >
        <q-tab-panel
          :name="key"
          v-for="(key, index) in Object.keys(TACs)"
          :key="index"
        >
          <div class="text-note" v-if="key !== 'en'">
            {{ t('TERMS_TRANSLATED_NOTE') }}
          </div>

          <div
            class="mt-6"
            v-for="({ header, title, contents, styleContent }, index) in TACs[
              key
            ]"
            :key="header"
          >
            <div class="flex gap-1 flex-nowrap text-base font-bold mb-2">
              <span v-html="`${index + 1})`"></span>
              <span v-html="`${header}:`"></span>
            </div>
            <div class="mb-2" v-html="title"></div>
            <ul
              class="ml-5"
              :style="{
                listStyleType: styleContent ? 'none' : 'lower-alpha',
              }"
            >
              <li
                v-for="({ subs, content, style }, idx) in contents"
                :key="idx"
                class="mb-2"
              >
                <span v-html="content"></span>
                <ul class="ml-5 mt-2" v-if="subs?.length">
                  <li
                    :style="{ listStyleType: style }"
                    class="mb-1"
                    v-for="text in subs"
                    :key="text"
                    v-html="text"
                  ></li>
                </ul>
              </li>
            </ul>
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </template>
    <template v-else>
      <div class="w-full h-full overflow-y-auto p-5">
        <div
          v-for="({ header, title, contents, styleContent }, index) in TACs[
            settings?.supported_languages[0] ?? ''
          ]"
          :key="header"
        >
          <div class="flex gap-1 flex-nowrap text-base font-bold mb-2">
            <span v-html="`${index + 1})`"></span>
            <span v-html="`${header}:`"></span>
          </div>
          <div class="mb-2" v-html="title"></div>
          <ul
            class="ml-5"
            :style="{
              listStyleType: styleContent ? 'none' : 'lower-alpha',
            }"
          >
            <li
              v-for="({ subs, content, style }, idx) in contents"
              :key="idx"
              class="mb-2"
            >
              <span v-html="content"></span>
              <ul class="ml-5 mt-2" v-if="subs?.length">
                <li
                  :style="{ listStyleType: style }"
                  class="mb-1"
                  v-for="text in subs"
                  :key="text"
                  v-html="text"
                ></li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.tac {
  background: url('/imgs/bg_dark_blur.png') center center no-repeat;
  background-size: 100% 100%;
  background-color: #000;
  backdrop-filter: blur(5px);
  padding-top: 80px;

  &-header {
    height: 80px;
    width: 100%;

    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0 10px;
    .right {
      background: linear-gradient(180deg, rgba(35, 0, 69, 0.8) 0%, #5b1b9b 100%),
        #462472;
      border-radius: 5px;
      margin-top: 14px;
      padding: 7px 12px;
    }
  }

  &-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.text-note {
  font-style: italic;
  font-size: 14px;
  line-height: 18px;
  border-bottom: 1px solid #a886d5;
  padding-bottom: 24px;
}
</style>
<style lang="scss">
.tab-tc {
  margin-top: 20px;
  background: rgba(91, 70, 116, 0.8);
  border-radius: 8px 8px 0px 0px;
  .q-tab-panels {
    background: unset;
  }
  .q-tab {
    border-radius: 8px 8px 0px 0px;
  }
  .q-tab__indicator {
    opacity: 0;
  }
  .q-tab--active {
    background: rgba(81, 29, 133, 0.8);
  }
  .q-tab__label {
    text-transform: capitalize;
    font-size: 16px;
    line-height: 20px;
  }
}
</style>
