<script setup lang="ts">
import { useTrackData } from '@composables';

interface Emits {
  (e: 'close'): void;
  (e: 'allow'): void;
}

const emits = defineEmits<Emits>();
const { t } = useI18n();
const { track } = useTrackData();

//----------------------Functions--------------------------
</script>
<template>
  <Dialog @close="emits('close')" :hide-close="true">
    <template #header>
      <div
        class="text-base font-bold"
        v-html="t('PEDOMETER_ONBOARDING_CONFIRM_HEADER')"
      ></div>
    </template>
    <div
      class="full-width text-center column items-center justify-start px-2.5"
    >
      <p v-html="t('PEDOMETER_ONBOARDING_CONFIRM_CONTENT')"></p>

      <div class="flex items-center gap-3 w-full my-5">
        <Button
          variant="purple"
          class="w-[106px] !min-w-[auto]"
          @click="emits('close')"
          :label="t('BTN_LATER')"
        />
        <Button
          class="!min-w-[auto] flex-1"
          @click="
            track('pedometer', {
              status: 'on',
            });
            emits('allow');
          "
          :label="t('BTN_ALLOW')"
        />
      </div>
    </div>
  </Dialog>
</template>
