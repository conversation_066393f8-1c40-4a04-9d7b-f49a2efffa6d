<script setup lang="ts">
import { useAsync } from '@composables';
import { useForm } from 'vee-validate';
import { useUserStore } from '@stores';
import * as yup from 'yup';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const store = useUserStore();

const { t } = useI18n();

const validationSchema = yup.object({
  goal: yup.number().typeError(t('VAILIDATE_NUMBER')).min(1),
});

const { handleSubmit, values } = useForm<{ goal: number }>({
  initialValues: {},
  validationSchema,
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const callback = handleSubmit(async () => {
      await store.updateUserSettings('pedometer_goal', +values.goal);
      emits('close');
    });
    await callback();
  },
});
//----------------------Functions--------------------------
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div class="text-base font-bold" v-html="t('SET_GOAL_HEADER')"></div>
    </template>
    <div
      class="full-width enter-code text-center column items-center justify-start px-2.5"
    >
      <p v-html="t('SET_GOAL_CONTENT')"></p>

      <div class="mt-4 gap-2.5 column justify-start items-center full-width">
        <div
          class="full-width flex flex-center gap-2.5"
          style="flex-wrap: nowrap"
        >
          <div class="relative" style="flex: 1">
            <q-form @submit="onSubmit" class="text-center">
              <VeeInput
                name="goal"
                :label="t(`ENTER_HERE`)"
                class="mb-5"
                type="number"
                autofocus
              />
              <Button
                type="submit"
                :disable="!values.goal"
                :loading="loading"
                :label="t('BUTTON_CONFIRM')"
              />
            </q-form>
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>
<style scoped lang="scss">
.enter-code {
  .prefix {
    position: absolute;
    left: 0;
    top: 9px;
    width: 50px;
    border-right: 1px solid rgba($color: #ffffff, $alpha: 0.3);
    height: calc(100% - 18px);
  }
}
</style>
