<script setup lang="ts">
import { useTrackData } from '@composables';
import { useUserStore } from '@stores';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { t } = useI18n();
const { track } = useTrackData();

async function turnOff() {
  try {
    track('pedometer', {
      status: 'off',
    });
    await storeUser.updateUserSettings('pedometer', false);
    emits('close');
  } catch (error) {}
}

//----------------------Functions--------------------------
</script>
<template>
  <Dialog @close="emits('close')" :hide-close="true">
    <template #header>
      <div
        class="text-base font-bold"
        v-html="t('PEDOMETER_CONFIRM_TURNOFF_HEADER')"
      ></div>
    </template>
    <div
      class="full-width text-center column items-center justify-start px-2.5"
    >
      <p v-html="t('PEDOMETER_CONFIRM_TURNOFF_CONTENT')"></p>

      <div class="flex items-center gap-3 w-full my-5">
        <Button
          variant="purple"
          class="w-[106px] !min-w-[auto]"
          @click="turnOff"
          :label="t('BUTTON_YES')"
        />
        <Button
          class="!min-w-[auto] flex-1"
          @click="emits('close')"
          :label="t('BTN_TURN_NO_OFF')"
        />
      </div>
    </div>
  </Dialog>
</template>
