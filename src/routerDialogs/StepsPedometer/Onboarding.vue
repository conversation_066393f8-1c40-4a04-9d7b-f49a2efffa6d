<script setup lang="ts">
interface Emits {
  (e: 'close'): void;
  (e: 'started'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();

//----------------------Functions--------------------------
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div
        class="text-base font-bold"
        v-html="t('PEDOMETER_ONBOARDING_HEADER')"
      ></div>
    </template>
    <div class="full-width text-center column items-center justify-start">
      <p v-html="t('PEDOMETER_ONBOARDING_CONTENT')"></p>

      <img class="w-full mx-auto my-5" src="/imgs/step-pedometer-intro.png" />
      <p v-html="t('PEDOMETER_ONBOARDING_NOTE')"></p>
      <Button
        class="my-5"
        @click="emits('started')"
        :label="t('BTN_GET_STARTED')"
      />
      <p class="italic" v-html="t('PEDOMETER_ONBOARDING_NOTE_2')"></p>
    </div>
  </Dialog>
</template>
