<script setup lang="ts">
import { SetGoal, ConfirmTurnOff } from '@routerDialogs';
import { useUserStore } from '@stores';
import { clamp, throttle } from 'lodash';
import { errorNotify, tryShare2 } from '@helpers';
import { useMotionTracking, useTrackData } from '@composables';
import dayjs from 'dayjs';
import { BRAND_SOV } from '@constants';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const {
  user,
  pedometerProgress: storeProgress,
  currentStep,
  stepUpdated,
} = storeToRefs(storeUser);
const { t } = useI18n();
const { track } = useTrackData();
const { requestDeviceMotion } = useMotionTracking();

const pedometerProgress = ref(storeProgress.value);
// const steps = ref(0);
const distance = ref(0);
const showSetGoal = ref(false);
const showConfirmTurnOff = ref(false);

const p = computed(() =>
  norm(currentStep.value, 0, user.value?.setting.pedometer_goal || 0, 0, 80)
);

function norm(value: number, min: number, max: number, p1: number, p2: number) {
  const v = clamp(value, min, max);
  const p = (v - min) / (max - min);
  return p1 + p * (p2 - p1);
}

function isSameDay(d1: string, d2: string) {
  return dayjs(d1).startOf('day').isSame(dayjs(d2).startOf('day'));
}

function getPedometerProgress() {
  try {
    const dataToday = pedometerProgress.value.find((item) =>
      isSameDay(item.date, new Date().toISOString())
    );
    // steps.value = dataToday?.steps || 0;
    distance.value = dataToday?.distance || 0;
    throttleDrawChart();
  } catch (error) {}
}

const throttleDrawChart = throttle(() => {
  setTimeout(async () => {
    for (let i = 0; i < pedometerProgress.value.length; i++) {
      await new Promise((res: any) => {
        const el1 = document.getElementById(`white_dot_${i}`);
        const el2 = document.getElementById(`white_dot_${i + 1}`);
        const line = document.getElementById(`line_${i + 1}`);
        const progress_step = document.getElementById('progress-step');
        if (!progress_step || !line || !el1 || !el2) return;
        const parentRect = progress_step.getBoundingClientRect();
        const pointA = {
          x: el1.getBoundingClientRect().x - parentRect.x,
          y: el1.getBoundingClientRect().y - parentRect.y,
        };
        const pointB = {
          x: el2.getBoundingClientRect().x - parentRect.x,
          y: el2.getBoundingClientRect().y - parentRect.y,
        };
        const length = Math.sqrt(
          Math.pow(pointB.x - pointA.x, 2) + Math.pow(pointB.y - pointA.y, 2)
        );
        const angle =
          (Math.atan2(pointB.y - pointA.y, pointB.x - pointA.x) * 180) /
          Math.PI;
        line.style.transform = `rotate(${angle}deg)`;
        line.style.transformOrigin = '0 0';
        line.style.left = pointA.x + 1.5 + angle / 12 + 'px';
        line.style.top = pointA.y + 1.5 + 'px';
        line.style.width = length + 'px';
        setTimeout(() => {
          res('ok');
        }, 100);
      });
    }
  }, 800);
}, 5000);

async function turnOn() {
  try {
    track('pedometer', {
      status: 'on',
    });
    await requestDeviceMotion();

    // await store.updateUserSettings('pedometer', true);
    // successNotify({
    //   message: t('NOTIFY_TURNON_PEDOMETER'),
    // });
  } catch (error) {}
}

async function shareProgress() {
  const res = await tryShare2({
    text: t(
      currentStep.value < (user.value?.setting.pedometer_goal || 0)
        ? 'SHARE_MESSAGE_NOT_ACHIEVED'
        : 'SHARE_MESSAGE_ACHIEVED',
      {
        GOAL: user.value?.setting.pedometer_goal.toLocaleString(),
        STEPS: currentStep.value.toLocaleString(),
        URL: process.env.APP_END_POINT,
      }
    ),
  });
  if (res.status === 'not_supported') {
    errorNotify({
      message: t('SHARE_NOT_SUPPORT'),
    });
  }
}

function onClickEvery(e: Event) {
  const target = e.target as HTMLElement;
  switch (target.id) {
    case 'turn_on_pedometer':
      turnOn();
      break;
    default:
      break;
  }
}

watch(
  () => user.value?.setting.pedometer_goal,
  () => {
    pedometerProgress.value = storeProgress.value;
    setTimeout(() => {
      throttleDrawChart();
    }, 300);
  }
);

watch(stepUpdated, (val) => {
  if (val) {
    pedometerProgress.value = storeProgress.value;
    setTimeout(() => {
      throttleDrawChart();
    }, 300);
    storeUser.stepUpdated = false;
  }
});

onMounted(() => {
  document.addEventListener('click', onClickEvery);
  getPedometerProgress();
  const doc = document.getElementsByClassName('q-notifications__list');
  for (let i = 0; i < doc.length; i++) {
    (doc[i] as HTMLElement).style.zIndex = '999999';
  }
});

onBeforeUnmount(() => {
  const doc = document.getElementsByClassName('q-notifications__list');
  for (let i = 0; i < doc.length; i++) {
    (doc[i] as HTMLElement).style.zIndex = '9500';
  }
  document.removeEventListener('click', onClickEvery);
});
</script>
<template>
  <Dialog @close="emits('close')">
    <q-dialog
      :maximized="true"
      :model-value="showSetGoal"
      :persistent="true"
      class="z-[222222]"
    >
      <SetGoal @close="showSetGoal = false" />
    </q-dialog>
    <q-dialog
      :maximized="true"
      :model-value="showConfirmTurnOff"
      :persistent="true"
      class="z-[222222]"
    >
      <ConfirmTurnOff @close="showConfirmTurnOff = false" />
    </q-dialog>
    <template #header>
      <div class="text-base font-bold" v-html="t('TRACK_STEP_HEADER')"></div>
    </template>
    <div
      class="full-width track-step text-center column items-center justify-start"
    >
      <div
        v-if="!user?.setting.pedometer"
        v-html="t('TURNOFF_CONTENT')"
        class="bg-[#981515] rounded-[5px] mb-5 py-1.5 px-2.5"
      ></div>
      <div class="relative mx-auto mt-[40px]">
        <Icon
          :name="`/sov/pedometer/${BRAND_SOV.DBS}`"
          :size="50"
          class="absolute top-[3px] left-[50%] translate-x-[-50%] translate-y-[-100%]"
        />
        <div class="column flex-center absolute-full text-center">
          <p class="text-3xl text-[#00E0FF] font-bold">
            {{ Math.min(currentStep, user?.setting.pedometer_goal || 0) }}
          </p>
          <p>
            /{{ user?.setting.pedometer_goal.toLocaleString() }}
            {{ t('STEPS') }}
          </p>
          <img src="/icons/steps.png" width="30" />
        </div>
        <q-circular-progress
          :value="80"
          :thickness="0.08"
          size="150px"
          color="white"
          class="rotate-[216deg] absolute top-0 left-0"
        />
        <q-circular-progress
          :value="p"
          size="150px"
          :thickness="0.08"
          color="#00e0ff"
          class="rotate-[216deg] text-[#00e0ff]"
        />
      </div>
      <p class="flex items-center gap-1 mb-5 my-2">
        <img src="/icons/travel.png" width="20" />{{ t('DISTANCE_CONTENT') }}
        {{ (distance / 1000).toFixed(2) }} km
      </p>
      <p class="mb-2.5">{{ t('WEEKLY_PROGRESS') }}</p>
      <div class="relative w-full" :key="user?.setting.pedometer_goal">
        <div v-if="!!pedometerProgress.length">
          <div
            class="line"
            :id="`line_${index}`"
            v-for="index in pedometerProgress.length - 1"
            :key="`line_${index}`"
          ></div>
        </div>
        <div
          class="track-step-progress flex items-center justify-between w-full py-2.5"
          id="progress-step"
        >
          <div
            class="text-center justify-center column items-center gap-[5px]"
            v-for="(item, index) in pedometerProgress"
            :key="item.date"
          >
            <p class="text-xs">{{ item.weekday }}</p>
            <q-tooltip
              style="
                background: url(/imgs/tooltip_bg.png) no-repeat !important;
                background-size: 100% 100%;
              "
              class="text-center"
              anchor="top middle"
              self="center middle"
            >
              <p>
                {{ item.steps }} steps <br />{{
                  (item.distance / 1000).toFixed(2)
                }}km
              </p>
            </q-tooltip>
            <div class="relative">
              <div
                v-if="isSameDay(item.date, new Date().toISOString())"
                class="current_dot rounded-[50%] w-[10px] h-[10px] absolute left-[-4px] pointer-events-none"
                :style="`top:calc(${
                  100 -
                  Math.min(
                    (item.steps / (user?.setting.pedometer_goal || 1)) * 100,
                    100
                  )
                }% - 2px)  `"
              ></div>
              <div
                :id="`white_dot_${index}`"
                class="bg-white rounded-[50%] w-[6px] h-[6px] absolute left-[-2px]"
                :style="`top:  ${
                  100 -
                  Math.min(
                    (item.steps / (user?.setting.pedometer_goal || 1)) * 100,
                    100
                  )
                }%`"
              ></div>

              <img
                src="/imgs/steps/line.png"
                height="35"
                width="2"
                class="min-h-[35px]"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center gap-3 w-full my-5">
        <Button
          variant="purple"
          class="w-[106px] !min-w-[auto]"
          @click="showSetGoal = true"
          :label="t('BTN_EDIT_GOAL')"
        />
        <Button
          style="flex: 1"
          class="!min-w-[auto]"
          @click="shareProgress"
          :label="t('BTN_SHARE_PROGRESS')"
        />
      </div>
      <p
        class="underline text-[#58E9E0]"
        @click="showConfirmTurnOff = true"
        v-if="!!user?.setting.pedometer"
      >
        {{ t('TURN_OFF_STEP') }}
      </p>
      <p class="underline text-[#58E9E0]" @click="turnOn" v-else>
        {{ t('TURN_ON_STEP') }}
      </p>
    </div>
  </Dialog>
</template>
<style scoped lang="scss">
.track-step {
  &-progress {
    background: url(/imgs/steps/horizontal_line.png),
      url(/imgs/steps/horizontal_line.png);
    background-size: 100% 2px, 100% 2px;
    background-position: top center, bottom center;
    background-repeat: repeat-x;
    .current_dot {
      border: 1px solid white;
    }
  }
  .line {
    position: absolute;
    background-color: #00e0ff;
    height: 3px; /* Độ dày đường */
    width: 0;
    transition: width 0.2s;
  }
}
</style>
<style>
.q-tooltip {
  z-index: 99999999;
}
</style>
