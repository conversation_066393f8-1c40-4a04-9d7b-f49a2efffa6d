<script lang="ts" setup>
import { InventoryItem } from '@types';
import { InventoryItemType } from '@constants';
import { SlideIllustration } from '@components';
import { useInventory } from '@composables';
import { useUserStore } from '@stores';

interface Props {
  inventoryItem: InventoryItem;
}

const props = defineProps<Props>();

const storeUser = useUserStore();

const { isEnabledGPS } = storeToRefs(storeUser);
const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();
const {
  handleTrialSilverShrink,
  handleTrialCoinSonar,
  handleTrialMetalDetector,
  handleTrialBeacon,
  remindTriggerGPS,
} = useInventory();

const HEADER = {
  [InventoryItemType.SHRINK]: t('INVENTORY_HTU_SILVER_SHRINK_HEADER'),
  [InventoryItemType.SHRINK_LITE]: t('INVENTORY_HTU_SILVER_SHRINK_LITE_HEADER'),
  [InventoryItemType.COIN_SONAR]: t('INVENTORY_HTU_COIN_SONAR_HEADER'),
  [InventoryItemType.METAL_DETECTOR]: t('INVENTORY_HTU_METAL_DETECTOR_HEADER'),
  [InventoryItemType.BEACON]: t('INVENTORY_HTU_BEACON_HEADER'),
};

const DESC = {
  [InventoryItemType.SHRINK]: t('INVENTORY_HTU_SILVER_SHRINK_DESC'),
  [InventoryItemType.SHRINK_LITE]: t('INVENTORY_HTU_SILVER_SHRINK_LITE_DESC'),
  [InventoryItemType.COIN_SONAR]: t('INVENTORY_HTU_COIN_SONAR_DESC'),
  [InventoryItemType.METAL_DETECTOR]: t('INVENTORY_HTU_METAL_DETECTOR_DESC'),
  [InventoryItemType.BEACON]: t('INVENTORY_HTU_BEACON_DESC'),
};

const ILUSTRATION = {
  [InventoryItemType.SHRINK]: [
    'htu_silver_shrink_1',
    'htu_silver_shrink_2',
    'htu_silver_shrink_3',
  ],
  [InventoryItemType.SHRINK_LITE]: [
    'htu_silver_shrink_1',
    'htu_silver_shrink_2',
    'htu_silver_shrink_3',
  ],
  [InventoryItemType.COIN_SONAR]: [
    'htu_coin_sonar_1',
    'htu_coin_sonar_2',
    'htu_coin_sonar_3',
  ],
  [InventoryItemType.METAL_DETECTOR]: [
    'htu_metal_detector_1',
    'htu_metal_detector_2',
    'htu_metal_detector_3',
    'htu_metal_detector_4',
    'htu_metal_detector_5',
  ],
  [InventoryItemType.BEACON]: [
    'htu_beacon_1',
    'htu_beacon_2',
    'htu_beacon_3',
    'htu_beacon_4',
  ],
};

function handleHowToUse() {
  if (!isEnabledGPS.value) {
    remindTriggerGPS();
    return;
  }

  const handler = {
    [InventoryItemType.SHRINK]: handleTrialSilverShrink,
    [InventoryItemType.SHRINK_LITE]: handleTrialSilverShrink,
    [InventoryItemType.COIN_SONAR]: handleTrialCoinSonar,
    [InventoryItemType.METAL_DETECTOR]: handleTrialMetalDetector,
    [InventoryItemType.BEACON]: handleTrialBeacon,
  };
  const fn = handler[props.inventoryItem.item_type];
  closeDialog('inventory_how_to_use');
  fn();
}

function handleBack() {
  closeDialog('inventory_how_to_use');
  openDialog('inventory_detail', { inventoryItem: props.inventoryItem });
}
</script>
<template>
  <Dialog>
    <template #btnTopLeft>
      <Button shape="square" variant="secondary" @click="handleBack">
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="HEADER[inventoryItem.item_type]"></div>
    </template>
    <div class="text-center">
      <SlideIllustration
        class="mb-5"
        :illustrations="
          ILUSTRATION[inventoryItem.item_type].map((img) => ({
            name: `inventory/${img}`,
            type: 'png',
          }))
        "
      />
      <div class="text-sm mb-5" v-html="DESC[inventoryItem.item_type]"></div>
      <Button :label="t('INVENTORY_HTU_BTN_TRIAL')" @click="handleHowToUse" />
    </div>
  </Dialog>
</template>
