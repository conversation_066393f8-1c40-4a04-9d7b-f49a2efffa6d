<script lang="ts" setup>
import { useInventory } from '@composables';
import { InventoryItem } from '@types';

interface Props {
  inventoryItem: InventoryItem;
  quantity: number;
}

const props = defineProps<Props>();

const { itemAssetNames, itemDisplayNames, loading, handleDiscardItem } =
  useInventory();
const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();

function handleClose() {
  if (loading.value) return;
  closeDialog('inventory_confirm_discard');
  openDialog('inventory_discard', { inventoryItem: props.inventoryItem });
}
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('INVENTORY_CONFIRM_DISCARD_HEADER')"></div>
    </template>
    <div class="text-center px-5">
      <div
        class="text-sm mb-2"
        v-html="
          t('INVENTORY_CONFIRM_DISCARD_DESC', {
            QUANTITY: quantity,
            NAME: itemDisplayNames[inventoryItem.item_type],
          })
        "
      ></div>
      <Icon
        :name="itemAssetNames[inventoryItem.item_type]"
        :size="97"
        class="mb-5 mx-auto"
      />
      <div class="flex justify-center items-center gap-4 w-full flex-nowrap">
        <Button
          variant="purple"
          :label="t('INVENTORY_DISCARD_BTN_CANCEL')"
          @click="handleClose"
        />
        <Button
          :loading="loading"
          :label="t('INVENTORY_CONFIRM_DISCARD_BTN_CONFIRM')"
          @click="handleDiscardItem(inventoryItem, quantity)"
        />
      </div>
    </div>
  </Dialog>
</template>
