<script lang="ts" setup>
import {
  delay,
  useCoinSonar,
  useInventory,
  useMetalDetector,
} from '@composables';
import { InventoryItemType } from '@constants';
import { useDialogStore } from '@stores';
import { InventoryItem } from '@types';
import { last } from 'lodash';

interface Props {
  inventoryItem: InventoryItem;
}

const props = defineProps<Props>();

const storeDialog = useDialogStore();

const { t } = useI18n();
const { closeDialog, openDialog, push, currentPath } = useMicroRoute();
const { itemDisplayNames, itemAssetNames, getItemExpirationText, totalItems } =
  useInventory();
const { handleRequestCoinSonar, isInPolygon } = useCoinSonar();
const { metalDetectorAction } = useMetalDetector();

const isHome = computed(() => last(currentPath.value.split('/')) === 'home');

const itemDisplayName = computed(() => {
  const baseName = itemDisplayNames[props.inventoryItem.item_type];

  return isCoinSonarItem.value
    ? `${baseName} (${props.inventoryItem.radius}m)`
    : baseName;
});

const isBeaconItem = computed(
  () => props.inventoryItem.item_type === InventoryItemType.BEACON
);

const isCoinSonarItem = computed(
  () => props.inventoryItem.item_type === InventoryItemType.COIN_SONAR
);

const isBeaconUnavailable = computed(
  () => isBeaconItem.value && totalItems.value.totalBeacon <= 0
);

const isCoinSonarOutsidePolygon = computed(
  () => isCoinSonarItem.value && !isInPolygon.value
);

const disabled = computed(
  () => isBeaconUnavailable.value || isCoinSonarOutsidePolygon.value
);

const errorMsg = computed(() =>
  isCoinSonarOutsidePolygon.value ? t('SONAR_ERROR_NOT_IN_POLYGON') : ''
);

// function openDiscard() {
//   closeDialog('inventory_detail');
//   openDialog('inventory_discard', {
//     inventoryItem: props.inventoryItem,
//   });
// }

function openHowToUse() {
  closeDialog('inventory_detail');
  openDialog('inventory_how_to_use', {
    inventoryItem: props.inventoryItem,
  });
}

const activateShrinkItem = () => {
  storeDialog.inventoryItem = props.inventoryItem;
  storeDialog.showSilverCoinSelectCircle = true;
};

const activateMetalDetector = () => {
  storeDialog.inventoryItem = props.inventoryItem;
  metalDetectorAction();
};

const activateBeacon = () => {
  storeDialog.showBeaconGUI = true;
};

async function handleUse() {
  const itemActionHandlers = {
    [InventoryItemType.SHRINK]: activateShrinkItem,
    [InventoryItemType.SHRINK_LITE]: activateShrinkItem,
    [InventoryItemType.COIN_SONAR]: () => handleRequestCoinSonar(true),
    [InventoryItemType.METAL_DETECTOR]: activateMetalDetector,
    [InventoryItemType.BEACON]: activateBeacon,
  };

  const actionHandler = itemActionHandlers[props.inventoryItem.item_type];
  if (!actionHandler) return;

  closeDialog('inventory_detail');

  if (!isHome.value) push(-1);
  await delay(500);
  actionHandler();
}
</script>
<template>
  <Dialog>
    <template #header>
      <div class="text-center">
        <div class="text-lg font-bold" v-html="itemDisplayName"></div>
        <div
          class="text-[#FF7878] mt-1 text-sm"
          v-html="getItemExpirationText(inventoryItem)"
        ></div>
      </div>
    </template>
    <div class="text-center px-5 relative">
      <!-- <Button
        shape="square"
        variant="secondary"
        class="absolute top-0 right-0"
        @click="openDiscard"
      >
        <Icon name="icons/delete" :size="16" />
      </Button> -->
      <Icon
        :name="itemAssetNames[inventoryItem.item_type]"
        :size="97"
        class="mx-auto mb-3"
      />
      <div
        class="rounded-[6px] w-max mx-auto py-2 px-4 bg-[#09090980]"
        v-html="
          t('INVENTORY_DETAIL_OWNED', {
            QUANTITY: inventoryItem.quantity,
          })
        "
      ></div>
      <div class="my-5" v-html="t('INVENTORY_DETAIL_DESC')"></div>

      <div
        class="mb-5 p-2 bg-[#981515] text-white"
        v-if="errorMsg"
        v-html="errorMsg"
      ></div>

      <div class="flex items-center gap-4 w-full flex-nowrap">
        <Button
          class="!w-max !min-w-[auto]"
          variant="purple"
          :label="t('INVENTORY_DETAIL_BTN_HOW_TO_USE')"
          @click="openHowToUse"
        />
        <Button
          class="flex-1"
          :label="t('INVENTORY_DETAIL_BTN_USE')"
          :disable="disabled"
          @click="handleUse"
        />
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.max-stack {
  background: linear-gradient(
    180deg,
    rgba(197, 54, 54, 0.64) 0%,
    rgba(215, 47, 47, 0.8) 65.22%
  );
}
</style>
