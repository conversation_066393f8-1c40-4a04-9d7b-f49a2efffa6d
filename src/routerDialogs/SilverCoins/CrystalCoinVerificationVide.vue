<script setup lang="ts">
import { useTick, useTrackData } from '@composables';
import { getSocials, timeCountDown } from '@helpers';
import { useUserStore } from '@stores';
import { SentosaGoldenCoin } from '@types';

interface Props {
  coin?: SentosaGoldenCoin;
}

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();
const props = defineProps<Props>();

const storeUser = useUserStore();

const { t } = useI18n();
const { now } = useTick();
const { track } = useTrackData();
const { currentCrystalCoinUpdate } = storeToRefs(storeUser);

const _coin = computed(() => props.coin || currentCrystalCoinUpdate.value);

const submitedAt = computed(() =>
  _coin.value?.verifying_at ? +new Date(_coin.value?.verifying_at) : +new Date()
);

const countdown = computed(() => {
  const days = 2 * 24 * 3600 * 1000; // 2 days
  const cd = submitedAt.value + days;
  return timeCountDown(cd - now.value);
});
</script>
<template>
  <Dialog
    @close="
      track('silvercoin_coinfound', {
        action: 'silvercoin_coinfound_close',
      });
      emits('close');
    "
  >
    <template #header>
      <div v-html="_coin?.name"></div>
    </template>

    <div class="px-2 text-center">
      <div
        class="mb-10 text-lg font-bold"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_1')"
      ></div>
      <div class="silver-coin">
        <Icon class="mt-10" name="capitaland-coin" :size="170" />
      </div>

      <div
        class="mb-1 -mt-10 text-sm"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_2')"
      ></div>

      <div class="mb-5 text-2xl font-bold">
        {{ countdown }}
      </div>

      <div class="text-center">
        <div
          class="mb-5 text-sm"
          v-html="t('SILVERCOIN_SILVERCOINFOUND_CTA')"
        ></div>
        <div class="flex items-center justify-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
            @click="
              track('silvercoin_coinfound', {
                action: 'silvercoin_coinfound_social',
                link,
                type: icon,
              })
            "
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
  margin-top: -100px;
}
</style>
