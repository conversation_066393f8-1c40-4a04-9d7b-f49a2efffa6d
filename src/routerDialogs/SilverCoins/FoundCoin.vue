<script lang="ts" setup>
import { Plyr } from '@components';
import { useClick, useTrackData } from '@composables';
import { dateTimeFormat } from '@helpers';
import type { ISilverCoin } from '@types';
import Physics2DPlugin from 'gsap/Physics2DPlugin';
import gsap from 'gsap';
gsap.registerPlugin(Physics2DPlugin);

interface Props {
  type: 'golden' | 'silver';
  coin: ISilverCoin;
}

interface Emits {
  (event: 'close'): void;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

const videoRef = ref();

useClick('goTimeline', () => {
  track('silvercoin_verified', {
    action: 'silvercoin_verified_instalink',
  });
  emits('close');
  openDialog('time_line_v2');
});

function generateDot() {
  const collection = document.getElementsByClassName('dot');
  for (let i = 0; i < collection.length; i++) {
    const dot = collection[i];
    gsap.set(dot, {
      opacity: 1,
    });
    const tl = gsap
      .timeline({
        onComplete() {
          dot.remove();
          tl.kill();
        },
      })
      .to(dot, {
        top: gsap.utils.random(-50, -100) + 'px',
        left: gsap.utils.random(-window.innerWidth / 2, window.innerWidth / 2),
        duration: gsap.utils.random(0.5, 2),
      })
      .to(
        dot,
        {
          duration: 5,
          rotationX: `-=${gsap.utils.random(720, 1440)}`,
          rotationZ: `+=${gsap.utils.random(720, 1440)}`,
          physics2D: {
            angle: gsap.utils.random(-80, 50),
            velocity: gsap.utils.random(10, 100),
            gravity: 120,
          },
        },
        0
      );
  }
}

function handleWatch() {
  const scrollContainers = document.getElementsByClassName('dialog-content');
  if (scrollContainers.length) {
    scrollContainers[0].scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    videoRef.value?.play();
    track('silvercoin_verified', {
      action: 'silvercoin_verified_playvideo',
    });
  }
}

onMounted(async () => {
  await nextTick();
  let timeout = setTimeout(() => {
    generateDot();
    clearTimeout(timeout);
  }, 200);
});
</script>

<template>
  <Dialog
    @close="
      track('silvercoin_verified', {
        action: 'silvercoin_verified_close',
      });
      emits('close');
    "
  >
    <template #header>
      <div
        v-html="
          type === 'silver'
            ? t('SILVERCOIN_COINFOUNDVIDEO_TITLE', {
                ORDER: coin.coin_number,
                BRAND_NAME:
                  `${coin.prefix || ''} ${coin.brand_name}` || 'Silver',
              })
            : t('SQKII_GOLD_COIN_TITLE')
        "
      ></div>
    </template>

    <div class="px-2 pb-5 text-center coin-found">
      <div id="emitter">
        <Icon
          v-for="i in 70"
          :key="i"
          class="opacity-0 dot"
          :name="`emitter/${Math.floor(gsap.utils.random(0, 2))}`"
        />
      </div>
      <div class="mb-4 text-sm font-bold">
        {{ t('SILVERCOIN_COINFOUNDVIDEO_1') }}
      </div>
      <div class="relative contain-video">
        <Plyr
          :source="coin.videos[0]"
          ref="videoRef"
          auto-play
          @play="
            track('silvercoin_verified', {
              action: 'silvercoin_verified_playvideo',
            })
          "
        />
        <Icon
          v-if="type === 'golden'"
          class="icon"
          name="sqkii-golden-mouse"
          width="60"
        />
        <Icon v-else class="icon" name="sqkii-mouse" width="60" />
      </div>
      <div
        class="mb-2 text-sm font-bold"
        v-html="t('SILVERCOIN_COINFOUNDVIDEO_2')"
      ></div>

      <div class="mb-2 text-2xl" v-if="type === 'golden'">
        {{ coin.winner_name }}
      </div>
      <div class="mb-2 text-2xl" v-else>
        {{ coin.winner_info?.first_name }} {{ coin.winner_info?.last_name }}
      </div>
      <div
        class="mb-4 text-sm font-bold"
        v-html="
          t('SILVERCOIN_COINFOUNDVIDEO_TIMESTAMP', {
            TIME: dateTimeFormat(+new Date(coin.found_at)),
          })
        "
      ></div>
      <div
        class="p-2 rounded-lg bg-[#091a3b] mb-5"
        v-if="
          !!coin.winner_info?.prize_amount &&
          !!coin.winner_info?.fulfillment_date &&
          ['cash', 'bank'].includes(coin.winner_info?.fulfillment_type)
        "
        v-html="
          t(`note_${coin.winner_info?.fulfillment_type}`, {
            PRIZE: coin.winner_info?.prize_amount,
            DATE: coin.winner_info?.fulfillment_date,
          })
        "
        @click="handleWatch"
      ></div>
      <div
        class="text-sm"
        v-html="t('SILVERCOIN_COINFOUNDVIDEO_OTHERHUNTERS')"
      ></div>
    </div>
  </Dialog>
</template>
<style lang="scss">
#emitter {
  position: absolute;
  left: 50%;
  top: 25%;
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 999999;
  img {
    width: 10px;
    height: 15px;
    margin: -2px 0 0 -2px;
    border-radius: 1px;
    position: absolute;
    top: 10vw;
    left: -20%;
    transform-style: preserve-3d;
  }
}
.coin-found {
  .contain-video {
    position: relative;
    width: 170px;
    height: 250px;
    margin: 0 auto 15px;
    .icon {
      position: absolute;
      bottom: -25px;
      right: -25px;
      pointer-events: none;
    }
    .plyr {
      min-width: unset;
      height: 100%;
      height: 100%;
      border-radius: 5px;
      min-height: unset !important;
      &__controls {
        padding: 0;
      }
      &__control--overlaid {
        width: 40px !important;
        height: 40px;
        background: transparent !important;
        border: 4px white solid;
        svg {
          width: 16px;
          height: 16px;
          left: calc(50% - (16px / 2));
          top: calc(50% - (16px / 2));
        }
      }
    }

    .plyr__video-embed,
    .plyr__video-wrapper--fixed-ratio {
      aspect-ratio: 9/16 !important;
    }
    .plyr__controls__item {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .plyr__controls {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
