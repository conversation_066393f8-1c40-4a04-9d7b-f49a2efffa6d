<script lang="ts" setup>
import { useTrackData } from '@composables';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { trackTime, track } = useTrackData();

function onClose() {
  trackTime('about_silver');
  track('silvercoin_info', {
    action: 'silvercoin_info_close',
  });
  emits('close');
}
</script>

<template>
  <Dialog @close="onClose">
    <template #header>
      <div v-html="t('SILVERCOINPOPUP_ABOUT_1')"></div>
    </template>
    <div class="text-center px-2">
      <Icon class="gif-wrapper mb-5" name="gif/silver_capture_new" type="gif" />
      <div class="text-sm" v-html="t('SILVERCOINPOPUP_ABOUT_2')"></div>
    </div>
  </Dialog>
</template>

<style lang="scss" scoped>
img {
  width: 100%;
  height: auto;
}
.gif-wrapper {
  position: relative;
  border-radius: 12px;
  background-color: #000;
  width: 100%;
  overflow: hidden;
  border: 1px solid rgba($color: #fff, $alpha: 0.25);
}
</style>
