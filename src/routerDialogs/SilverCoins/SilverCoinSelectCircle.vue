<script lang="ts" setup>
import { delay, useMapHelpers, useSilverCoin } from '@composables';
import { useDialogStore, useMapStore } from '@stores';
import { InventoryItem, ISilverCoin } from '@types';

const storeDialog = useDialogStore();
const storeMap = useMapStore();

const { inventoryItem } = storeToRefs(storeDialog);
const { coinSonar } = storeToRefs(storeMap);
const { groupedSilverCoins } = storeToRefs(storeMap);
const { fitBounds } = useMapHelpers();
const { t } = useI18n();

const showDialog = ref(false);
const slide = ref(groupedSilverCoins.value.ongoing[0]?.properties._id || '');

const ongoingSilverCoins = computed(() => {
  return groupedSilverCoins.value.ongoing;
});

watch(
  slide,
  (val) => {
    const circle = ongoingSilverCoins.value.find(
      (c) => c.properties._id === val
    );
    if (!circle) return;
    coinSonar.value.selectedCircle = circle;
    const { center, radius } = circle.properties.freeCircle;
    fitBounds({
      lng: center.lng,
      lat: center.lat,
      radius: radius * 1.2,
    });
  },
  {
    immediate: true,
  }
);

const coin = computed(() => {
  const circle = ongoingSilverCoins.value.find(
    (c) => c.properties._id === slide.value
  );
  return circle?.properties as ISilverCoin;
});

const computedInventoryItem = computed(
  () => inventoryItem.value as InventoryItem
);

const { handleShrinkCircle } = useSilverCoin(coin, computedInventoryItem);

async function handleUseShrink() {
  storeDialog.showSilverCoinSelectCircle = false;
  await delay(500);
  handleShrinkCircle();
}

onMounted(async () => {
  await delay(500);
  showDialog.value = true;
});
</script>

<template>
  <div class="fixed fullscreen silver-coin-select-circle">
    <div
      class="banner absolute w-[70%] top-12 left-1/2 -translate-x-1/2 z-10"
      v-html="t('SILVERCOIN_BANNER_TEXT')"
    ></div>
    <Button
      class="sonar-arrow-left fixed top-3 left-3 z-[9999]"
      shape="square"
      variant="secondary"
      @click="storeDialog.showSilverCoinSelectCircle = false"
    >
      <Icon name="arrow-left" />
    </Button>
    <q-dialog v-model="showDialog" position="bottom" seamless>
      <div
        class="silver-coin-trial-dialog-container pt-10"
        :style="{
          backgroundImage: 'url(/imgs/bg-coin-sonar.png)',
        }"
      >
        <div class="flex flex-col justify-center items-center">
          <section class="w-full h-full text-center">
            <q-carousel
              v-model="slide"
              swipeable
              animated
              padding
              arrows
              class="bg-transparent mb-5"
            >
              <q-carousel-slide
                v-for="c in ongoingSilverCoins"
                :key="c.properties._id"
                :name="c.properties._id"
              >
                <div
                  class="text-sm p-4 bg-[#04081D] w-full rounded-xl"
                  v-html="
                    t('SILVERCOINPOPUP_COIN_1', {
                      ORDER: c.properties.coin_number,
                      BRAND_NAME:
                        `${c.properties.prefix || ''} ${
                          c.properties.brand_name
                        }` || 'Silver',
                    })
                  "
                ></div>
              </q-carousel-slide>
            </q-carousel>
            <Button
              :label="t('SONAR_METAL_SELECTED_CIRCLE_BUTTON')"
              class="!w-[210px]"
              @click="handleUseShrink"
            />
          </section>
        </div>
      </div>
    </q-dialog>
  </div>
</template>
<style lang="scss" scoped>
.banner {
  text-align: center;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid #b663e9;
  background: linear-gradient(261deg, #341c7c 1.58%, #1a0950 60.32%);
}
</style>
