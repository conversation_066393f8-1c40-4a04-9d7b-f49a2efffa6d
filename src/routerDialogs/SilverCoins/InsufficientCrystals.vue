<script lang="ts" setup>
import { useBAStore, useMapStore } from '@stores';
import { BrandActionItem } from '@components';
import { useBrandActions, useTrackData } from '@composables';
import type { IBrandAction } from '@types';

interface Props {
  dataBA?: IBrandAction;
}

interface Emits {
  (event: 'close'): void;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const storeBA = useBAStore();
const mapStore = useMapStore();

const { newBrandActions, isBANew } = storeToRefs(storeBA);
const { push, closeAllDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();
const brandHooks = useBrandActions();
const randomNewBA = computed(() => {
  if (!isBANew.value) return;
  const filterdBrandAction = newBrandActions.value.filter(
    (item) =>
      (!item.release_date || +new Date(item.release_date) < Date.now()) && // check release date
      (!item.close_date || +new Date(item.close_date) > Date.now())
  );
  return filterdBrandAction[
    Math.floor(Math.random() * filterdBrandAction.length)
  ] as IBrandAction;
});

function goToBA() {
  closeAllDialog();
  push('offer_wall');
  track('circle_BA_see_more');
  track('low_crystals_popup', {
    action: 'other_BA',
  });
}

onMounted(() => {
  mapStore.fetchBrandActionPhycical();
});
</script>

<template>
  <Dialog
    class="insufficient-crystals"
    @close="
      emits('close');
      track('circle_BA_close');
    "
    crystals
    :px="20"
  >
    <template #header>
      <div v-html="t('SHRINKINGCIRCLE_NOCRYSTALS_1')"></div>
    </template>
    <div class="text-center box">
      <div class="mb-4 text-sm">
        {{ t('SHRINKINGCIRCLE_NOCRYSTALS_2') }}
      </div>
      <div class="flex flex-col items-center justify-center" v-if="dataBA">
        <BrandActionItem
          :data="dataBA"
          class="mb-[30px]"
          :brandHooks="brandHooks"
        />
      </div>
      <div
        class="flex flex-col items-center justify-center"
        v-if="!dataBA && randomNewBA"
      >
        <BrandActionItem
          :data="randomNewBA"
          class="mb-[30px]"
          :brandHooks="brandHooks"
        />
      </div>
      <Button
        class="mx-auto"
        :label="t('SHRINKINGCIRCLE_VIEWBA_BUTTON')"
        @click="goToBA"
      />
    </div>
  </Dialog>
</template>

<style lang="scss" scoped>
.box {
  .brand-item {
    background: #001c45;
    background-size: 100% 100% !important;
    width: 100%;
    padding: 10px;
    margin-bottom: 30px;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 80%;
      height: 12px;
      left: -5px;
      transform: skew(25deg);
      bottom: -12px;
      background: linear-gradient(180deg, #15618c 0%, #042f56 100%);
      border-radius: 2px;
    }
    &::after {
      content: '';
      position: absolute;
      width: 80%;
      height: 12px;
      right: -5px;
      transform: skew(-25deg);
      bottom: -12px;
      background: linear-gradient(180deg, #15618c 0%, #042f56 100%);
      border-radius: 2px;
    }
  }
}
</style>
