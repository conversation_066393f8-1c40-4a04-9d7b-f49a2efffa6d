<script lang="ts" setup>
import { getSocials } from '@helpers';
import { useClick, useTrackData } from '@composables';
import { useUserStore } from '@stores';
import { SentosaGoldenCoin } from '@types';

interface Props {
  coin?: SentosaGoldenCoin;
}

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const props = defineProps<Props>();

const { openDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();

const storeUser = useUserStore();

const { currentCrystalCoinUpdate } = storeToRefs(storeUser);

const _coin = computed(() => props.coin || currentCrystalCoinUpdate.value);

useClick('goTAC', () => {
  track('crystalcoin_forfeited', {
    action: 'crystalcoin_forfeited_tncs',
  });
  openDialog('tac');
});
</script>

<template>
  <Dialog
    @close="
      track('crystalcoin_forfeited', {
        action: 'crystalcoin_forfeited_close',
      });
      emits('close');
    "
  >
    <template #header>
      <div v-html="_coin?.name"></div>
    </template>
    <div class="text-center px-2">
      <div class="text-lg font-bold mb-6">
        {{ t('SILVERCOINFORFEITED_1') }}
      </div>
      <div class="text-sm mb-6" v-html="t('SILVERCOINFORFEITED_2')"></div>
      <div class="text-center">
        <div
          class="text-sm mb-5"
          v-html="t('SILVERCOIN_SILVERCOINNOTFOUND_CTA')"
        ></div>
        <div class="flex justify-center items-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
            @click="
              track('crystalcoin_forfeited', {
                action: 'crystalcoin_forfeited_social',
                link,
                type: icon,
              })
            "
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>
    </div>
  </Dialog>
</template>
