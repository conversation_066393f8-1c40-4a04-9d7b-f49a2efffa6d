<script lang="ts" setup>
import { useCapitalandHooks, useTick, useTrackData } from '@composables';
import { errorNotify, getSocials, timeCountDown, tryShare2 } from '@helpers';
import { useUserStore } from '@stores';
import { SentosaGoldenCoin } from '@types';

interface Props {
  coin?: SentosaGoldenCoin;
}

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();
const props = defineProps<Props>();

const { openDialog, closeDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();
const { now } = useTick();

const storeUser = useUserStore();

const { capitalandGeneoCoin } = storeToRefs(storeUser);
const { geojsonKey, getGeojsonNumber } = useCapitalandHooks();

const selectedCoin = ref<SentosaGoldenCoin | undefined>(undefined);

const nextHint = computed(() => {
  if (!selectedCoin.value?.hints?.length) return undefined;
  return selectedCoin.value.hints.find((h) => !h.unlocked_at);
});

const activeCoins = computed(() => {
  const currentGeojsonNumber = getGeojsonNumber(geojsonKey.value);
  return capitalandGeneoCoin.value.filter((coin) => {
    const coinGeojsonNumber = getGeojsonNumber(coin.geojson_file);
    return currentGeojsonNumber >= coinGeojsonNumber;
  });
});

const nextCoin = computed(() => {
  return capitalandGeneoCoin.value.find((coin) => coin.status === 'scheduled');
});

const countdown = computed(() => {
  if (!selectedCoin.value) return '00h 00m 00s';
  const days = 2 * 24 * 3600 * 1000; // 2 days (48 hours)
  const cd = +new Date(selectedCoin.value?.found_at) + days;
  const remaining = cd - now.value;

  if (remaining <= 0) {
    return '00h 00m 00s';
  }

  const hours = Math.floor(remaining / (1000 * 60 * 60));
  const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
  return `${hours}h ${minutes}m ${seconds}s`;
});

async function handleShare() {
  const res = await tryShare2({
    text: t('CRYSTAL_COIN_SHARE_MESSAGE', {
      NAME: selectedCoin.value?.name,
      URL: process.env.APP_END_POINT,
    }),
  });
  track('crystalcoin_share', {
    status: res.status,
  });
  if (res.status === 'not_supported') {
    errorNotify({
      message: t('SHARE_NOT_SUPPORT'),
    });
  }
}

const goToFoundCoin = (e: Event) => {
  const target = e.target as HTMLElement;
  if (!target) return;
  if (target.id === 'goToFoundCoin') {
    track('crystalcoin_popup', {
      action: 'crystalcoin_popup_foundcoin',
    });
    closeDialog('crystal_coin_info');
    openDialog('enter_serial_number', {
      fromMap: true,
    });
  }
};

onMounted(async () => {
  await nextTick();
  selectedCoin.value = props.coin || activeCoins.value[0];
  addEventListener('click', goToFoundCoin);
});

onBeforeUnmount(() => {
  removeEventListener('click', goToFoundCoin);
});
</script>

<template>
  <Dialog
    v-if="selectedCoin"
    @close="
      track('crystalcoin_info', {
        action: 'crystalcoin_info_close',
      });
      emits('close');
    "
  >
    <template #btnTopLeft>
      <Button shape="square" variant="secondary" @click="handleShare">
        <Icon name="share" />
      </Button>
    </template>
    <template #header>
      <div v-html="selectedCoin?.name"></div>
    </template>
    <q-select
      class="q-dark-select !mb-5"
      v-model="selectedCoin"
      :options="activeCoins"
      option-label="name"
      option-value="_id"
      options-selected-class="coin-selected"
      borderless
      behavior="menu"
      dense
      popup-content-class="dark-popup"
      label="Coin"
    >
    </q-select>
    <div class="relative px-2 text-center -mt-5">
      <div class="silver-coin">
        <div class="absolute top-8">
          <template
            v-if="['ongoing', 'scheduled'].includes(selectedCoin.status)"
          >
            <div
              class="mb-1 text-sm"
              v-html="t('CRYSTAL_COIN_INFO_DESC_1')"
            ></div>
            <div
              class="text-2xl font-bold"
              v-html="t('CRYSTAL_COIN_INFO_DESC_2')"
            ></div>
            <div
              class="italic text-sm"
              v-html="t('CRYSTAL_COIN_INFO_DESC_10')"
            ></div>
          </template>
          <template v-if="selectedCoin.status === 'forfeited'">
            <div
              class="font-bold text-2xl"
              v-html="t('CRYSTAL_COIN_INFO_DESC_6')"
            ></div>
          </template>
          <template v-if="selectedCoin.status === 'verifying'">
            <div
              class="font-bold text-2xl"
              v-html="t('CRYSTAL_COIN_INFO_DESC_9')"
            ></div>
          </template>
          <template v-if="selectedCoin.status === 'found'">
            <div
              class="mb-1 text-sm"
              v-html="t('SILVERCOIN_COINFOUNDVIDEO_2')"
            ></div>
            <div
              class="mb-5 text-2xl font-bold"
              v-html="selectedCoin.winner_info.winner_name"
            ></div>
          </template>
        </div>
        <Icon class="mt-12 w-[40%]" name="capitaland-coin" />
      </div>
      <div class="bg-[#091a3b] rounded p-3 gap-2 mb-5 -mt-10">
        <!-- <div v-html="t('CRYSTAL_COIN_INFO_DESC_3')"></div> -->
        <div class="text-center">
          <div
            v-if="selectedCoin?.status === 'ongoing'"
            class="text-sm mb-5"
            v-html="
              selectedCoin.hints.every((h) => h.unlocked_at)
                ? t('CRYSTAL_COIN_INFO_DESC_3')
                : t('CRYSTAL_COIN_INFO_DESC_5', {
                    TIME: timeCountDown(
                      +new Date(nextHint?.available_at || 0) - now
                    ),
                  })
            "
          ></div>

          <div
            v-else-if="selectedCoin?.status === 'scheduled'"
            class="text-sm mb-5"
            v-html="
              t('CRYSTAL_COIN_INFO_DESC_4', {
                TIME: timeCountDown(
                  +new Date(selectedCoin?.drop_at || 0) - now
                ),
              })
            "
          ></div>
          <div
            v-else-if="
              ['forfeited', 'verifying', 'found'].includes(
                selectedCoin?.status
              ) && !!nextCoin
            "
            class="text-sm mb-5"
            v-html="
              t('CRYSTAL_COIN_INFO_DESC_7', {
                TIME: timeCountDown(+new Date(nextCoin?.drop_at || 0) - now),
              })
            "
          ></div>
          <div
            v-else-if="
              ['forfeited', 'found'].includes(selectedCoin?.status) && !nextCoin
            "
            class="text-sm mb-5"
            v-html="t('CRYSTAL_COIN_INFO_DESC_11')"
          ></div>
          <div
            v-else-if="
              ['verifying'].includes(selectedCoin?.status) && !nextCoin
            "
            class="text-sm mb-5"
            v-html="
              t('CRYSTAL_COIN_INFO_DESC_8', {
                TIME: countdown,
              })
            "
          ></div>

          <div class="flex justify-center items-center gap-4">
            <a
              :href="link"
              target="_blank"
              rel="noopener noreferrer"
              v-for="{ link, icon } in getSocials()"
              :key="icon"
              @click="
                track('crystalcoin_info', {
                  action: 'crystalcoin_info_social',
                  link,
                  type: icon,
                })
              "
            >
              <Icon :name="icon" :size="25" />
            </a>
          </div>
        </div>
      </div>
      <div class="flex w-full justify-center flex-nowrap gap-2 mb-5">
        <Button
          :label="t('CRYSTAL_COIN_INFO_BTN_1')"
          variant="purple"
          @click="
            closeDialog('crystal_coin_info');
            track('crystalcoin_info', {
              action: 'daily_hints',
            });
            openDialog('geneo_coin_hints', {
              coin: selectedCoin,
              capitalandGeneoCoin,
            });
          "
        />
        <Button
          :label="t('CRYSTAL_COIN_INFO_BTN_2')"
          @click="
            track('crystalcoin_info', {
              action: 'daily_reward',
            });
            closeDialog('crystal_coin_info');
            openDialog('capitaland_daily_reward');
          "
        />
      </div>
      <div
        class="text-sm mb-5"
        v-html="t('CRYSTAL_COIN_INFO_COIN_FOUND')"
      ></div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
}
</style>
