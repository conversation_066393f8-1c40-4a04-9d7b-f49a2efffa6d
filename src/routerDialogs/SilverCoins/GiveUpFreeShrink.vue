<script lang="ts" setup>
import { useTrackData } from '@composables';
import { useUserStore } from '@stores';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { settings } = storeToRefs(storeUser);
const { closeAllDialog, openDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();

function handleGiveUp() {
  storeUser.updateOnboarding('shrink_silver_coin');
  track('quit_free_shrink_popup', {
    action: 'free_shrink_no',
  });
  closeAllDialog();
  openDialog('telegram');
}
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('GIVE_UP_SHRINK_HEADER')"></div>
    </template>
    <div class="text-center mt-5">
      <div
        class="text-sm mb-5"
        v-html="
          t('GIVE_UP_SHRINK_DESC', {
            QUANTITY: settings?.shrink_circle_price,
          })
        "
      ></div>
      <div class="flex flex-nowrap gap-4">
        <Button
          size="max-content"
          :label="t('GIVE_UP_SHRINK_BTN_1')"
          variant="purple"
          @click="handleGiveUp"
        />
        <Button
          size="max-content"
          class="flex-1"
          :label="t('GIVE_UP_SHRINK_BTN_2')"
          block
          @click="
            track('quit_free_shrink_popup', {
              action: 'free_shrink_yes',
            });
            emits('close');
          "
        />
      </div>
    </div>
  </Dialog>
</template>
