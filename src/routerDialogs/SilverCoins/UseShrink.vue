<script lang="ts" setup>
import { useUserStore, useMapStore, useDialogStore } from '@stores';
import { MAP } from '@repositories';
import {
  useAsync,
  useGlobal,
  useMapHelpers,
  useSystemMessage,
  useTick,
  useTrackData,
} from '@composables';
import { errorNotify } from '@helpers';
import type { IAPIResponseError, IDiscountPrice, ISilverCoin } from '@types';
import dayjs from 'dayjs';
import { BRAND_SOV, InventoryItemType } from '@constants';

interface Props {
  powerUpType: InventoryItemType.SHRINK | InventoryItemType.SHRINK_LITE;
  itemId?: string;
  coin: ISilverCoin;
  location?: { lat: number; lng: number };
  discount?: IDiscountPrice;
}

interface Emits {
  (event: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeMap = useMapStore();
const storeUser = useUserStore();
const storeDialog = useDialogStore();

const { trackUserLocation } = useGlobal();
const { closeAllDialog, openDialog } = useMicroRoute();
const { t } = useI18n();
const { now } = useTick();
const { track } = useTrackData();
const { forceTriggermessage } = useSystemMessage();
const { fitBounds } = useMapHelpers();

const { loading, execute: shrinkCircle } = useAsync({
  async fn() {
    if (props.powerUpType === InventoryItemType.SHRINK) {
      const { data } = await MAP.userShrinkPowerUp({
        coin_id: props.coin._id,
        ...(props.location || {}),
        item_id: props.itemId,
      });
      return data;
    } else {
      const { data } = await MAP.userShrinkLitePowerUp({
        coin_id: props.coin._id,
        ...(props.location || {}),
        item_id: props.itemId,
      });
      return data;
    }
  },
  async onSuccess(data) {
    closeAllDialog();
    await storeUser.fetchUser();
    await storeMap.fetchSilverCoin();
    storeUser.fetchInventory();
    trackUserLocation('shrink_circle', {
      coin_id: props.coin._id,
    });
    storeUser.fetchDailyMission();
    fitBounds({
      lng: data.paidCircle.center.lng,
      lat: data.paidCircle.center.lat,
      radius: (props.coin.circle.radius - data.bottomMessage.smallest) * 1.2,
    });
    storeDialog.shrinkData = data;
    forceTriggermessage('silver_coin_shrink');
  },
  onError(error) {
    const { error_message } = error as IAPIResponseError;
    errorNotify({
      message: error_message,
    });
  },
});

const openAt = ref(Date.now());

const isExpired = computed(() => {
  return (
    (props.discount?.beacon &&
      dayjs(props.discount?.expire_at).isValid() &&
      dayjs(props.discount?.expire_at).isBefore(now.value)) ||
    dayjs(openAt.value).add(1, 'minutes').isBefore(now.value)
  );
});

function onClose() {
  if (loading.value) return;
  emits('close');
}

watch(isExpired, (value) => {
  if (value) onClose();
});
</script>

<template>
  <Dialog
    @close="
      track('silvercoin_shrink', {
        action: 'silvercoin_shrink_close',
      });
      onClose();
    "
    crystals
  >
    <template #header>
      <div v-html="t('SHRINKINGCIRCLE_CONFIRM_1')"></div>
    </template>
    <div class="px-2 text-center">
      <div
        class="mb-5 text-sm frame"
        v-html="
          discount && discount.possible_smallest > -1
            ? t('SHRINKINGCIRCLE_BANNER', {
                RADIUS: discount.possible_smallest,
              })
            : t('SHRINKINGCIRCLE_BANNER_1')
        "
      ></div>
      <div class="relative size-[100px] mx-auto mb-5">
        <Icon name="frame-circle" :size="100" />
        <Icon
          :name="`/sov/shrink/${BRAND_SOV.DBS}`"
          :size="48"
          class="absolute bottom-0 -left-7"
        />
      </div>
      <div
        class="mb-5 text-sm px-2"
        v-html="
          t('SHRINKINGCIRCLE_CONFIRM_2', {
            ORDER: coin.coin_number,
            BRAND_NAME: `${coin.prefix || ''} ${coin.brand_name}` || 'Silver',
          })
        "
      ></div>

      <div
        class="bg-[#091A3B] rounded-md p-3 flex flex-nowrap justify-center items-center gap-2 mb-5"
        v-if="coin.paidCircle.radius"
      >
        <div
          v-html="
            t('SILVERCOINPOPUP_COIN_PRIVATELY', {
              DISTANCE:
                Number(coin.freeCircle.radius) - Number(coin.paidCircle.radius),
            })
          "
        ></div>
        <Icon
          name="question-mark"
          @click="
            track('silvercoin_moreinfo');
            openDialog('about_silver');
          "
        />
      </div>
      <div class="flex justify-center gap-4 flex-nowrap">
        <Button
          class="!w-20"
          size="max-content"
          variant="purple"
          :label="t('SHRINKINGCIRCLE_CONFIRM_NO')"
          @click="
            track('silvercoin_shrink', {
              action: 'silvercoin_shrink_no',
            });
            onClose();
          "
        />
        <Button
          :label="t('SHRINKINGCIRCLE_CONFIRM_YES')"
          @click="
            track('silvercoin_shrink', {
              action: 'silvercoin_shrink_yes',
            });
            shrinkCircle();
          "
          :loading="loading"
        />
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.frame {
  position: relative;
  padding: 10px 40px;
  margin-left: -35px;
  width: calc(100% + 70px);
  background: linear-gradient(88deg, #bc18d7 40.21%, #a150f1 98.03%);
  &::before {
    position: absolute;
    content: '';
    background-image: url('/imgs/frame-star-bottom.png');
    background-size: cover;
    background-repeat: no-repeat;
    width: 21px;
    height: 22px;
    bottom: -5px;
    left: 5px;
  }
  &::after {
    position: absolute;
    content: '';
    background-image: url('/imgs/frame-star-top.png');
    background-size: cover;
    background-repeat: no-repeat;
    width: 26px;
    height: 30px;
    top: -5px;
    right: 5px;
  }
}
</style>
