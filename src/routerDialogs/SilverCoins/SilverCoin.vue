<script lang="ts" setup>
import { BrandActionItem } from '@components';
import { BRAND_SOV, InventoryItemType } from '@constants';
import {
  useBrandActions,
  useCoinSonar,
  useSilverCoin,
  useTrackData,
} from '@composables';
import { getSovAsset } from '@helpers';
import type { ISilverCoin } from '@types';

interface Props {
  coin: ISilverCoin;
}

interface Emits {
  (event: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();
const brandHooks = useBrandActions();

const { openDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();
const { isInPolygon } = useCoinSonar();

const silverCoin = useSilverCoin(computed(() => props.coin));

const {
  coinData,
  currentCoinWithBA,
  firstTimeState,
  nextShrinkCountdown,
  shrinkPowerUpCountdown,
  powerUpCountdownText,
  canUsePowerUp,
  timeLeftText,
  originalPrice,
  shrinkPrice,
  shouldShowMetalSonarIntro,
  isFreeShrink,
  crystals,
  canActiveShrink,
  canAffordShrink,
  isSmallestPublicCircle,
  hasPaidCircle,
  hasDiscount,
  selectedPowerUp,
  resourceType,
  disableShrinkLite,
  handleShrinkCircle,
  handleClickCountDown,
  handleUseMetalSonar,
  handleStandbyMetalSonar,
  handleShare,
  onClose: handleClose,
  trackAction,
  setupFoundCoinClick,
  initializeComponent,
  cleanup,
  setupCountdownWatch,
} = silverCoin;

const onClose = () => handleClose(() => emits('close'));

setupFoundCoinClick(onClose);

setupCountdownWatch();

onMounted(initializeComponent);
onBeforeUnmount(cleanup);

const canUseCoinSonar = computed(() => {
  if (!selectedPowerUp.value) return false;
  return (
    selectedPowerUp.value.type === InventoryItemType.COIN_SONAR ||
    isInPolygon.value
  );
});
</script>

<template>
  <Dialog
    @close="
      track('circle_pop_up_back');
      onClose();
    "
    :bottomCrystal="!!currentCoinWithBA.dataBA"
    :backdrop-header="!firstTimeState.isFirstTap"
  >
    <template #btnTopLeft>
      <Button shape="square" variant="secondary" @click="handleShare">
        <Icon name="share" />
      </Button>
    </template>
    <template #header>
      <div
        v-if="coin.brand_unique_id === BRAND_SOV.CAPITALAND_SCIENCE_PARK"
        class="text-sm opacity-80"
        v-html="
          t('SILVERCOINPOPUP_COIN_3', {
            NUMBER: coin.coin_number,
          })
        "
      ></div>
      <div
        v-html="
          t('SILVERCOINPOPUP_COIN_1', {
            ORDER: coin.coin_number,
            BRAND_NAME: `${coin.prefix || ''} ${coin.brand_name}` || 'Silver',
          })
        "
      ></div>
    </template>
    <template #sov>
      <Icon :name="getSovAsset('silver_coin_info', BRAND_SOV.DBS)" :size="30" />
    </template>
    <template #icon-center>
      <div
        v-if="isSmallestPublicCircle"
        class="absolute z-10 flex items-center gap-2 px-2 py-1 -translate-x-1/2 rounded-md w-max left-1/2 -top-3"
        style="
          background: linear-gradient(88deg, #bc18d7 40.21%, #a150f1 98.03%);
        "
        @click="openDialog('time_up_coundown')"
      >
        <div class="text-sm font-bold">{{ timeLeftText }} left</div>
        <Icon name="question-mark-p" />
      </div>
    </template>
    <div class="relative px-2 -mt-5 text-center">
      <div class="silver-coin">
        <div class="absolute top-8">
          <div class="mb-1 text-sm" v-html="t('SILVERCOINPOPUP_COIN_2')"></div>
          <div class="mb-5 text-2xl font-bold" v-html="coin.reward"></div>
        </div>
        <Icon class="mt-12 size-[40%]" name="silver-coin" />
      </div>
      <div
        v-if="!hasPaidCircle"
        class="bg-[#091a3b] rounded p-3 flex flex-nowrap justify-center items-center gap-2 mb-5 -mt-10"
      >
        <div
          class="backdrop-first-coin"
          v-if="!firstTimeState.isFirstTap"
        ></div>
        <!-- <div
          class="backdrop-first-metal"
          v-if="shouldShowMetalSonarIntro"
        ></div> -->

        <div
          class="text-sm"
          v-if="isSmallestPublicCircle"
          v-html="t('MAX_PUBLIC_CIRCLE_SHRINK')"
        ></div>
        <div
          class="text-sm"
          v-else-if="coinData.nextShrinkAt"
          v-html="
            t('SILVERCOINPOPUP_COIN_TIMER', {
              COUNTDOWN: nextShrinkCountdown?.toLowerCase(),
            })
          "
        ></div>
        <Icon
          name="question-mark"
          @click="
            track('silvercoin_moreinfo');
            openDialog('about_silver');
          "
        />
      </div>
      <template v-if="hasPaidCircle">
        <div
          class="bg-[#2E3B54] rounded-t-md p-3 flex flex-nowrap justify-center items-center gap-2 -mt-10"
        >
          <div
            class="text-sm"
            v-if="isSmallestPublicCircle"
            v-html="t('MAX_PUBLIC_CIRCLE_SHRINK')"
          ></div>
          <div
            class="text-sm"
            v-else-if="coinData.nextShrinkAt"
            v-html="
              t('SILVERCOINPOPUP_COIN_TIMER', {
                COUNTDOWN: nextShrinkCountdown?.toLowerCase(),
              })
            "
          ></div>
          <Icon
            name="question-mark"
            @click="
              track('silvercoin_moreinfo');
              openDialog('about_silver');
            "
          />
        </div>
        <div
          class="bg-[#091A3B] rounded-b-md p-3 flex flex-nowrap justify-center items-center gap-2 mb-5"
        >
          <div
            v-html="
              t('SILVERCOINPOPUP_COIN_PRIVATELY', {
                DISTANCE:
                  Number(coin.freeCircle.radius) -
                  Number(coin.paidCircle.radius || 0),
              })
            "
          ></div>
        </div>
      </template>
      <div
        v-if="hasDiscount"
        class="mb-5 text-sm frame-discount-powerup"
        v-html="t('BEACON_DISCOUNT_DESC_1')"
      ></div>
      <SilverCoinPowerUpSelection
        v-if="!isFreeShrink"
        class="mb-5"
        v-model="selectedPowerUp"
      />

      <template v-if="selectedPowerUp?.type === InventoryItemType.COIN_SONAR">
        <Button
          class="mb-5 mx-auto !w-[230px] relative z-10"
          :class="{
            'relative z-[9999]': shouldShowMetalSonarIntro,
            'opacity-60': !canUseCoinSonar,
          }"
          :label="
            canUseCoinSonar
              ? t('SILVERCOINPOPUP_USE_METAL_SONAR_BUTTON')
              : t('SILVERCOINPOPUP_STANDBY_METAL_SONAR_BUTTON')
          "
          @click="
            canUseCoinSonar ? handleUseMetalSonar() : handleStandbyMetalSonar()
          "
        />
      </template>

      <template
        v-else-if="
          (selectedPowerUp &&
            [InventoryItemType.SHRINK, InventoryItemType.SHRINK_LITE].includes(
              selectedPowerUp.type
            )) ||
          isFreeShrink
        "
      >
        <Button
          v-if="!canUsePowerUp"
          class="mb-5 mx-auto !w-[230px] relative z-10"
          :title="t('SILVERCOINPOPUP_SHRINK_BUTTON')"
          :resource-type="resourceType"
          :old-amount="hasDiscount ? originalPrice : undefined"
          :amount="isFreeShrink ? 0 : hasDiscount ? shrinkPrice : originalPrice"
          disable
        />

        <Button
          v-else-if="Boolean(shrinkPowerUpCountdown)"
          class="mb-5 mx-auto !w-[230px] opacity-70 relative z-10"
          :label="`${t(
            'SILVERCOINPOPUP_SHRINK_BUTTON'
          )} ${powerUpCountdownText}`"
          @click="handleClickCountDown"
        />

        <Button
          v-else
          class="mb-5 mx-auto !w-[230px] relative z-10"
          :class="{
            'relative z-[9999]': !firstTimeState.isFirstTap,
          }"
          :title="`${t('SILVERCOINPOPUP_SHRINK_BUTTON')}`"
          :resource-type="resourceType"
          :old-amount="hasDiscount ? originalPrice : undefined"
          :amount="isFreeShrink ? 0 : hasDiscount ? shrinkPrice : originalPrice"
          :disable="!canAffordShrink || !canActiveShrink || disableShrinkLite"
          @click="handleShrinkCircle"
        />
      </template>
      <div class="text-sm" v-html="t('SILVERCOINPOPUP_COIN_FOUND')"></div>
      <template v-if="!isFreeShrink">
        <div
          class="flex flex-col items-center justify-center mt-5 flex-nowrap"
          v-if="currentCoinWithBA.dataBA"
        >
          <p class="text-base font-bold" v-html="t('OFFERWALL_QUESTION')"></p>
          <BrandActionItem
            :brandHooks="brandHooks"
            :data="currentCoinWithBA.dataBA"
            showType="noFrame"
            @action="
              trackAction('circle_BA_get_more', {
                user_crystals: crystals,
              })
            "
          />
        </div>
      </template>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.backdrop-first-coin {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 8888;
  background-color: rgba(0, 0, 0, 0.5);
}

.backdrop-first-metal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 8888;
  background-color: rgba(0, 0, 0, 0.5);
}
.share {
  position: absolute;
  top: 50%;
  left: 15px;
  transform: translateY(-50%);
}

.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
}
</style>
