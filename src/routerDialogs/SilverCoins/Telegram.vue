<script lang="ts" setup>
import { useTrackData } from '@composables';

const { t } = useI18n();
const { track } = useTrackData();
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('TELEGRAM_HEADER')"></div>
    </template>
    <div class="text-center">
      <div class="text-sm my-5" v-html="t('TELEGRAM_DESC')"></div>
      <a href="https://t.me/sqkiisg" target="_blank" rel="noopener noreferrer">
        <Button
          :label="t('TELEGRAM_BTN_GO')"
          @click="
            track('telegram_subscription', {
              action: 'directto_telegram',
            })
          "
        />
      </a>
    </div>
  </Dialog>
</template>
