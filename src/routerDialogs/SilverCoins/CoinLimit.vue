<script lang="ts" setup>
import { useClick } from '@composables';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();

const { push } = useMicroRoute();

useClick('goToEnsuringFairness', () => {
  emits('close');
  push('ensuring_fairness_v2');
});
</script>
<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('COIN_LIMIT_TITLE')"></div>
    </template>
    <div class="text-center">
      <div class="px-5 mb-5 text-sm" v-html="t('COIN_LIMIT_DESC_1')"></div>
      <!-- <div class="time">
        <div class="flex flex-col">
          <div class="text">
            {{ coinLimitCountdown?.manual.days ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('COUNTDOWN_TIMER_DAYS') }}
          </div>
        </div>
        <div class="flex flex-col">
          <div class="text">
            {{ coinLimitCountdown?.manual.hours ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('COUNTDOWN_TIMER_HOURS') }}
          </div>
        </div>
        <div class="flex flex-col">
          <div class="text">
            {{ coinLimitCountdown?.manual.minutes ?? '00' }}
          </div>
          <div class="text-xs font-bold">
            {{ t('COUNTDOWN_TIMER_MINUTES') }}
          </div>
        </div>
      </div> -->
      <div class="px-5 text-sm" v-html="t('COIN_LIMIT_DESC_2')"></div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.time {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  background: linear-gradient(
    180deg,
    rgba(200, 107, 233, 0.8) 0%,
    #bb20c9 100%
  );
  border-radius: 10px;
  padding: 10px 20px;
  width: max-content;
  margin: 0 auto 20px;
  .text {
    font-weight: 800;
    font-size: 44px;
    line-height: 44px;
  }
}
@media screen and (max-width: 350px) {
  .time {
    padding: 5px 10px;
    width: 100% !important;
    .text {
      font-weight: 800;
      font-size: 32px;
      line-height: 32px;
    }
  }
}
</style>
