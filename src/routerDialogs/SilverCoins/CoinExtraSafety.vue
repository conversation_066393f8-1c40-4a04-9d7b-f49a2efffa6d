<script lang="ts" setup>
import { useUserStore } from '@stores';
import { useBrandSov, useClick, useTrackData } from '@composables';
import { storeToRefs } from 'pinia';
import type { ISilverCoin } from '@types';

interface Props {
  coin: ISilverCoin;
}

interface Emits {
  (event: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { t } = useI18n();
const { user } = storeToRefs(storeUser);
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

const { randomResult, ready } = useBrandSov('safety_hints_pop_up_flag', {
  id: 'safety_hints_pop_up_character',
  track: false,
});

const checkbox = ref(true);

const TITLES = [
  'SAFETY_HINT1_TITLE',
  'SAFETY_HINT2_TITLE',
  'SAFETY_HINT3_TITLE',
  'SAFETY_HINT4_TITLE',
  'SAFETY_HINT5_TITLE',
  'SAFETY_HINT6_TITLE',
  'SAFETY_HINT7_TITLE',
];

useClick('goTAC', () => {
  track('silvercoin_safetyhint', {
    action: 'silvercoin_safetyhint_tncs',
    checkbox,
  });
  openDialog('tac');
});

function onClose() {
  const safety = `coin_safety_${props.coin._id}_${user.value?.hunter_id}`;
  if (checkbox.value) LocalStorage.set(safety, new Date().toISOString());
  else LocalStorage.remove(safety);
  emits('close');
}

function handleAgree() {
  onClose();
  track('silvercoin_safetyhint', {
    action: 'silvercoin_safetyhint_agree',
  });
  openDialog('silver_coin', {
    coin: props.coin,
  });
}

onMounted(() => {
  track('circle_extra_safety', {
    coin_id: props.coin.coin_id,
    brand_unique_id: props.coin.brand_unique_id,
  });
});
</script>

<template>
  <Dialog
    @close="
      track('silvercoin_safetyhint', {
        action: 'silvercoin_safetyhint_close',
      });
      onClose();
    "
  >
    <template #icon-center>
      <template v-if="ready">
        <Icon
          :name="randomResult.safety_hints_pop_up_character.getAsset('_timii')"
          class="absolute -translate-x-1/2 left-1/2 -top-[55px]"
          :size="60"
        />
      </template>
    </template>
    <template #header>
      <div
        v-html="
          t('SILVERCOINPOPUP_COIN_1', {
            ORDER: coin.coin_number,
            BRAND_NAME: `${coin.prefix || ''} ${coin.brand_name}` || 'Silver',
          })
        "
      ></div>
    </template>
    <div class="px-4">
      <div
        v-for="(title, index) in TITLES"
        :key="index"
        class="mb-3 text-sm"
        v-html="t(title)"
      ></div>

      <div class="my-4 line"></div>

      <div class="mb-4 text-sm text-center">
        <div v-html="t('SILVERCOINPOPUP_SAFETY_AGREE')"></div>
      </div>
      <div class="mb-4 text-center">
        <Button
          :label="t('SILVERCOINPOPUP_SAFETY_BUTTON_AGREE')"
          @click="handleAgree"
        />
      </div>

      <div class="text-center">
        <q-checkbox
          v-model="checkbox"
          :label="t('SILVERCOINPOPUP_SAFETY_CHECKBOX')"
          @click="
            track('silvercoin_safetyhint', {
              action: 'silvercoin_safetyhint_dontshowagain',
              checkbox,
            })
          "
        />
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.line {
  width: calc(100% + 20px);
  height: 1px;
  background: rgba($color: #ffffff, $alpha: 0.5);
  margin-left: -10px;
}
</style>
