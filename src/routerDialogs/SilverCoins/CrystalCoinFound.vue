<script lang="ts" setup>
import { Plyr } from '@components';
import { useClick, useTrackData } from '@composables';
import { dateTimeFormat } from '@helpers';
import type { SentosaGoldenCoin } from '@types';
import Physics2DPlugin from 'gsap/Physics2DPlugin';
import gsap from 'gsap';
gsap.registerPlugin(Physics2DPlugin);

interface Props {
  coin: SentosaGoldenCoin;
}

interface Emits {
  (event: 'close'): void;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

const videoRef = ref();

useClick('goTimeline', () => {
  track('crystalcoin_verified', {
    action: 'crystalcoin_verified_instalink',
  });
  emits('close');
  openDialog('time_line_v2');
});

function generateDot() {
  const collection = document.getElementsByClassName('dot');
  for (let i = 0; i < collection.length; i++) {
    const dot = collection[i];
    gsap.set(dot, {
      opacity: 1,
    });
    const tl = gsap
      .timeline({
        onComplete() {
          dot.remove();
          tl.kill();
        },
      })
      .to(dot, {
        top: gsap.utils.random(-50, -100) + 'px',
        left: gsap.utils.random(-window.innerWidth / 2, window.innerWidth / 2),
        duration: gsap.utils.random(0.5, 2),
      })
      .to(
        dot,
        {
          duration: 5,
          rotationX: `-=${gsap.utils.random(720, 1440)}`,
          rotationZ: `+=${gsap.utils.random(720, 1440)}`,
          physics2D: {
            angle: gsap.utils.random(-80, 50),
            velocity: gsap.utils.random(10, 100),
            gravity: 120,
          },
        },
        0
      );
  }
}

onMounted(async () => {
  await nextTick();
  let timeout = setTimeout(() => {
    generateDot();
    clearTimeout(timeout);
  }, 200);
});
</script>

<template>
  <Dialog
    @close="
      track('silvercoin_verified', {
        action: 'silvercoin_verified_close',
      });
      emits('close');
    "
  >
    <template #header>
      <div v-html="coin.name"></div>
    </template>

    <div class="px-2 pb-5 text-center coin-found">
      <div id="emitter">
        <Icon
          v-for="i in 70"
          :key="i"
          class="opacity-0 dot"
          :name="`emitter/${Math.floor(gsap.utils.random(0, 2))}`"
        />
      </div>
      <div class="mb-4 text-sm font-bold">
        {{ t('SILVERCOIN_COINFOUNDVIDEO_1') }}
      </div>
      <div class="relative contain-video">
        <Plyr
          :source="coin.video_link"
          ref="videoRef"
          auto-play
          @play="
            track('crystalcoin_verified', {
              action: 'crystalcoin_verified_playvideo',
            })
          "
        />
        <Icon class="icon" name="sentosa-golden-mouse" width="60" />
      </div>
      <div
        class="mb-2 text-sm font-bold"
        v-html="t('SILVERCOIN_COINFOUNDVIDEO_2')"
      ></div>

      <div class="mb-2 text-2xl">
        {{ coin.winner_info.winner_name }}
      </div>

      <div
        class="mb-4 text-sm font-bold"
        v-html="
          t('SILVERCOIN_COINFOUNDVIDEO_TIMESTAMP', {
            TIME: dateTimeFormat(+new Date(coin.found_at)),
          })
        "
      ></div>
      <div
        class="text-sm"
        v-html="t('SILVERCOIN_COINFOUNDVIDEO_OTHERHUNTERS')"
      ></div>
    </div>
  </Dialog>
</template>
<style lang="scss">
#emitter {
  position: absolute;
  left: 50%;
  top: 25%;
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 999999;
  img {
    width: 10px;
    height: 15px;
    margin: -2px 0 0 -2px;
    border-radius: 1px;
    position: absolute;
    top: 10vw;
    left: -20%;
    transform-style: preserve-3d;
  }
}
.coin-found {
  .contain-video {
    position: relative;
    width: 170px;
    height: 250px;
    margin: 0 auto 15px;
    .icon {
      position: absolute;
      bottom: -25px;
      right: -25px;
      pointer-events: none;
    }
    .plyr {
      min-width: unset;
      height: 100%;
      height: 100%;
      border-radius: 5px;
      min-height: unset !important;
      &__controls {
        padding: 0;
      }
      &__control--overlaid {
        width: 40px !important;
        height: 40px;
        background: transparent !important;
        border: 4px white solid;
        svg {
          width: 16px;
          height: 16px;
          left: calc(50% - (16px / 2));
          top: calc(50% - (16px / 2));
        }
      }
    }

    .plyr__video-embed,
    .plyr__video-wrapper--fixed-ratio {
      aspect-ratio: 9/16 !important;
    }
    .plyr__controls__item {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .plyr__controls {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
