<script lang="ts" setup>
import { BrandActionItem } from '@components';
import { useBrandActions } from '@composables';
import { BRAND_SOV } from '@constants';
import { useBAStore } from '@stores';
import type { IBrandIcon } from '@types';
import { get } from 'lodash';

interface Emits {
  (e: 'close'): void;
}

interface Props {
  iconData: IBrandIcon;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeBA = useBAStore();

const { user_brand_actions, newBrandActions } = storeToRefs(storeBA);
const brandHooks = useBrandActions();
const { push } = useMicroRoute();
const { t } = useI18n();

const statusIndex = {
  verified: 5,
  new: 4,
  pending: 3,
  rejected: 1,
  claimed: 2,
};

const isReleased = (item: any) => {
  return !item.release_date || +new Date(item.release_date) < Date.now();
};
const isClosed = (item: any) => {
  return !!item.close_date && +new Date(item.close_date) < Date.now();
};
const isSameUniqueIdWithProps = (item: any) => {
  return item.brand_unique_id === props.iconData.brand_unique_id;
};
const isSqkiiVoucherBA = (item: any) => {
  return ['use_sqkii_voucher', 'sqkii_voucher'].includes(item.type);
};
const HARDCODE: any[] = [
  // {
  //   key: 'display.brand_name',
  //   match: 'Mastercard',
  //   ba_unique_id: 'sentosa_3',
  // },
];

const brandAction = computed(() => {
  const isHardcode = HARDCODE.some((item) => {
    const value = get(props.iconData, item.key);
    return value === item.match;
  });

  if (isHardcode) {
    return newBrandActions.value.find(
      (item) => item.unique_id === HARDCODE[0].ba_unique_id
    );
  }

  const listBa = [...user_brand_actions.value, ...newBrandActions.value]
    .filter(
      (item) =>
        isReleased(item) &&
        !isClosed(item) &&
        isSameUniqueIdWithProps(item) &&
        (!props.iconData.display.sv_client || isSqkiiVoucherBA(item))
    )
    .sort((a, b) => {
      return statusIndex[b.status] - statusIndex[a.status];
    });

  return listBa[0];
});

const showMore = ref(false);
const popupHeader = computed(() => {
  return 'BRANDICONS_HEADING';
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div
        v-html="
          t(popupHeader, {
            NAME: iconData.display.brand_name,
          })
        "
      ></div>
    </template>
    <template #icon-center>
      <Icon
        :name="`/sov/sponsor/${BRAND_SOV.DBS}`"
        class="absolute left-1/2 -translate-x-1/2 -top-5 w-[190px] h-10"
      />
    </template>
    <template #special-box>
      <BrandActionItem
        :brandHooks="brandHooks"
        v-if="brandAction"
        :data="brandAction"
        show-type="twinkle"
        from-sponsor
      />
      <!-- TO DO -->
    </template>
    <div class="text-center relative z-[30] mt-[-10px]">
      <div
        class="mb-2 text-sm"
        v-if="!showMore"
        v-html="t('BRANDICONS_LEARNMORE')"
      ></div>
      <div
        class="flex items-center justify-center gap-1"
        :class="showMore ? 'mb-2' : 'mb-6'"
        @click="showMore = true"
      >
        <Icon :name="`map/${iconData.display.brand_icon}`" :size="20" />
        <div class="text-sm" v-html="iconData.display.brand_name"></div>
        <Icon v-if="!showMore" name="icons/arrow-down" :size="16" />
      </div>

      <div v-if="showMore">
        <div
          v-if="!!iconData.display.address"
          class="mb-2 opacity-80"
          v-html="iconData.display.address"
        ></div>
        <div
          class="mb-2 opacity-80"
          v-if="!!Object.keys(iconData.opening_hours).length"
        >
          <div
            class="mb-1 text-sm"
            v-if="Object.values(iconData.opening_hours).some(Boolean)"
            v-html="t('BRANDICONS_OPEN_HOURS')"
          ></div>
          <template
            v-for="key in Object.keys(iconData.opening_hours)"
            :key="key"
          >
            <div
              class="flex justify-center gap-2 text-sm text-left"
              v-if="
                [
                  'monday',
                  'tuesday',
                  'wednesday',
                  'thursday',
                  'friday',
                  'saturday',
                  'sunday',
                  'weekday',
                  'weekend',
                  'eve_holiday',
                  'public_holiday',
                ].includes(key)
              "
            >
              <div
                v-if="iconData.opening_hours[key as keyof IBrandIcon['opening_hours']]"
                class="text-sm"
                v-html="`${t(key)}:`"
              ></div>
              <div
                class="text-sm font-medium"
                v-html="iconData.opening_hours[key as keyof IBrandIcon['opening_hours']]"
              ></div>
            </div>
          </template>
        </div>
        <div
          class="mb-3"
          v-html="
            t(
              `brand_description_${
                iconData.display.sv_client || iconData.brand_unique_id
              }`
            )
          "
        ></div>
        <div
          class="flex items-center justify-center gap-4 mb-6 text-link"
          @click="showMore = false"
        >
          <div class="text-sm" v-html="t('BRANDICONS_SHOWLESS')"></div>
          <Icon
            name="icons/arrow-down"
            :size="16"
            style="transform: rotate(180deg)"
          />
        </div>
      </div>
      <div
        class="text-sm underline text-link"
        @click="
          emits('close');
          push('offer_wall');
        "
        v-html="t('BRANDICONS_OTHERBRANDACTIONS')"
      ></div>
    </div>
  </Dialog>
</template>
