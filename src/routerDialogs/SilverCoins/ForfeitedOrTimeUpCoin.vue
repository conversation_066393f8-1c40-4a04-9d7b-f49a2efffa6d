<script lang="ts" setup>
import { getSocials } from '@helpers';
import { useClick, useTrackData } from '@composables';
import type { ISilverCoin } from '@types';

interface Props {
  coin: ISilverCoin;
  type: 'forfeited' | 'time_up';
}

interface Emits {
  (event: 'close'): void;
}

const props = defineProps<Props>();

const emits = defineEmits<Emits>();

const { openDialog } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();

useClick('goTAC', () => {
  track(
    props.type === 'forfeited' ? 'silvercoin_forfeited' : 'silvercoin_timeup',
    {
      action:
        props.type === 'forfeited'
          ? 'silvercoin_forfeited_tncs'
          : 'silvercoin_timeup_tncs',
    }
  );
  openDialog('tac');
});
</script>

<template>
  <Dialog
    @close="
      track(
        type === 'forfeited' ? 'silvercoin_forfeited' : 'silvercoin_timeup',
        {
          action:
            type === 'forfeited'
              ? 'silvercoin_forfeited_close'
              : 'silvercoin_timeup_close',
        }
      );
      emits('close');
    "
  >
    <template #header>
      <div
        v-html="
          t('SILVERCOIN_SILVERCOINNOTFOUND_1', {
            ORDER: coin.coin_number,
            BRAND_NAME: `${coin.prefix || ''} ${coin.brand_name}` || 'Silver',
          })
        "
      ></div>
    </template>
    <div class="text-center px-2">
      <div class="text-lg font-bold mb-6">
        {{
          type === 'time_up'
            ? t('SILVERCOIN_SILVERCOINNOTFOUND_2')
            : t('SILVERCOINFORFEITED_1')
        }}
      </div>
      <div
        class="text-sm mb-6"
        v-html="
          type === 'time_up'
            ? t('SILVERCOIN_SILVERCOINNOTFOUND_3')
            : t('SILVERCOINFORFEITED_2')
        "
      ></div>
      <div class="text-center">
        <div
          class="text-sm mb-5"
          v-html="t('SILVERCOIN_SILVERCOINNOTFOUND_CTA')"
        ></div>
        <div class="flex justify-center items-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
            @click="
              track(
                type === 'forfeited'
                  ? 'silvercoin_forfeited'
                  : 'silvercoin_timeup',
                {
                  action:
                    type === 'forfeited'
                      ? 'silvercoin_forfeited_social'
                      : 'silvercoin_timeup_social',
                  link,
                  type: icon,
                }
              )
            "
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>
    </div>
  </Dialog>
</template>
