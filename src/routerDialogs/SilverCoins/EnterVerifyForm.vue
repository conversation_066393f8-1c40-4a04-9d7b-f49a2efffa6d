<script lang="ts" setup>
import { Stepper, WinnerAgreement } from '@components';
import { useForm } from 'vee-validate';
import { useAsync, useClick, useGlobal, useTrackData } from '@composables';
import { useUserStore } from '@stores';
import { SILVER_COIN } from '@repositories';
import {
  countries,
  isMatchSeasonNumber,
  successNotify,
  deleteEmptyFields,
} from '@helpers';
import { omit } from 'lodash';
import { copyToClipboard } from 'quasar';
import type { ICountryRegion, IVerification, IWinnerInfoPayload } from '@types';
import * as yup from 'yup';

interface Emits {
  (event: 'close'): void;
}

interface Props {
  step?: FormSteps;
  serial_number?: string;
}

const FORM_STEPS = {
  PERSONAL_INFO: 1,
  PARENT_INFO: 2,
  AGREEMENT: 3,
  BANK_DETAILS: 4,
  VIDEO_VERIFICATION: 5,
  MAILING_PROOF: 6,
  COMPLETION: 7,
} as const;

type FormSteps = (typeof FORM_STEPS)[keyof typeof FORM_STEPS];

const TRACKING_CONFIG = {
  type: {
    [FORM_STEPS.PERSONAL_INFO]: 'silvercoin_winner_enterdetails',
    [FORM_STEPS.AGREEMENT]: 'silvercoin_winnersagreement',
    [FORM_STEPS.VIDEO_VERIFICATION]: 'silvercoin_video',
  },
  action: {
    [FORM_STEPS.PERSONAL_INFO]: 'silvercoin_winner_enterdetails_submit',
    [FORM_STEPS.AGREEMENT]: 'silvercoin_winnersagreement_submit',
    [FORM_STEPS.VIDEO_VERIFICATION]: 'silvercoin_video_submit',
  },
} as const;

const emits = defineEmits<Emits>();
const props = defineProps<Props>();

const storeUser = useUserStore();
const { trackUserLocation } = useGlobal();
const { dataVerification, user } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

const step = ref<FormSteps>(props.step || FORM_STEPS.PERSONAL_INFO);
const country = ref<ICountryRegion>();
const parent_country = ref<ICountryRegion>();

useClick('goTAC', () => {
  openDialog('tac');
});

const AGE_OPTIONS = computed(() => [
  {
    label: t('SILVERCOIN_AGE_OPTIONS_1'),
    value: false,
  },
  {
    label: t('SILVERCOIN_AGE_OPTIONS_2'),
    value: true,
  },
]);

const RESIDENCE_OPTIONS = computed(() => {
  return countries.map((country) => ({
    label: country.name,
    value: country.iso,
  }));
});

// const CURRENCY_OPTIONS = computed(() => {
//   return countries.map((country) => ({
//     label: country.currencyName,
//     value: country.currency,
//   }));
// });

const STEP_HEADERS = {
  [FORM_STEPS.PERSONAL_INFO]: 'SILVERCOIN_FORM_TITLE',
  [FORM_STEPS.PARENT_INFO]: 'SILVERCOIN_PARENT_TITLE',
  [FORM_STEPS.AGREEMENT]: 'SILVERCOIN_WINNERSAGREEMENT',
  [FORM_STEPS.BANK_DETAILS]: 'SILVERCOIN_BANKDETAILSFORM',
  [FORM_STEPS.VIDEO_VERIFICATION]: 'SILVERCOIN_VERIFICATIONVIDEO_TITLE',
  [FORM_STEPS.MAILING_PROOF]: 'SILVERCOIN_MAILING_TITLE',
} as const;

const header = computed(() => {
  return t(STEP_HEADERS[step.value as keyof typeof STEP_HEADERS]) || '';
});

const serialNumber = computed(() => {
  return props.serial_number || dataVerification.value?.coin?.serial_number;
});

const coinId = computed(() => {
  return (
    dataVerification.value?.coin?.coin_id || dataVerification.value?.coin_id
  );
});

const submitButtonLabel = computed(() => {
  const buttonLabels: Partial<Record<FormSteps, string>> = {
    [FORM_STEPS.MAILING_PROOF]: 'SILVERCOIN_MAILING_BUTTON',
    [FORM_STEPS.VIDEO_VERIFICATION]: 'SILVERCOIN_SENTVERIFICATIONVIDEO_BUTTON',
  };

  return t(buttonLabels[step.value] || 'SILVERCOIN_FORM_BUTTON_SUBMIT');
});

const initialValues = computed(() => {
  return {
    // step 1
    serial_number: serialNumber.value,
    first_name: dataVerification.value?.winner_info?.first_name ?? '',
    last_name: dataVerification.value?.winner_info?.last_name ?? '',
    nric: dataVerification.value?.winner_info?.nric ?? '',
    mobile_number: dataVerification.value?.winner_info?.mobile_number ?? '',
    cf_mobile_number: dataVerification.value?.winner_info?.mobile_number ?? '',
    reached_minimum_age:
      dataVerification.value?.winner_info?.reached_minimum_age ?? '',
    email: dataVerification.value?.winner_info?.email ?? '',
    capitastar_account_id:
      dataVerification.value?.winner_info?.capitastar_account_id ?? '',
    // step 2
    parent_first_name:
      dataVerification.value?.winner_info?.parent_first_name ?? '',
    parent_last_name:
      dataVerification.value?.winner_info?.parent_last_name ?? '',
    parent_nric: dataVerification.value?.winner_info?.parent_nric ?? '',
    parent_mobile_number:
      dataVerification.value?.winner_info?.parent_mobile_number ?? '',
    parent_cf_mobile_number:
      dataVerification.value?.winner_info?.parent_mobile_number ?? '',
    parent_email: dataVerification.value?.winner_info?.parent_email ?? '',
    parent_capitastar_account_id:
      dataVerification.value?.winner_info?.parent_capitastar_account_id ?? '',
    // step 3
    accepted_tac: dataVerification.value?.winner_info?.accepted_tac ?? false,
    // step 4
    country: dataVerification.value?.winner_info?.country ?? '',
    currency: dataVerification.value?.winner_info?.currency ?? '',
    bank_account_name:
      dataVerification.value?.winner_info?.bank_account_name ?? '',
    bank_name: dataVerification.value?.winner_info?.bank_name ?? '',
    bank_code: dataVerification.value?.winner_info?.bank_code ?? '',
    branch_code: dataVerification.value?.winner_info?.branch_code ?? '',
    bank_account_number:
      dataVerification.value?.winner_info?.bank_account_number ?? '',
    // step 5
    sent_video: dataVerification.value?.winner_info?.sent_video ?? false,
    // step 6
    submitted_mailing_proof:
      dataVerification.value?.winner_info?.submitted_mailing_proof ?? false,
  };
});

const createMobileNumberValidation = (
  countryRef: Ref<ICountryRegion | undefined>,
  errorMessage: string
) => {
  return yup
    .string()
    .required(t('SILVERCOIN_MOBILENUMBER_1_REQUIRED'))
    .test('mobile-number', errorMessage, (value) => {
      const number = countryRef.value?.code + value;
      return isMatchSeasonNumber(number);
    });
};

const createBasePersonalInfoSchema = () => ({
  first_name: yup.string().required(t('SILVERCOIN_FIRSTNAME_1_REQUIRED')),
  last_name: yup.string().required(t('SILVERCOIN_LASTNAME_1_REQUIRED')),
  nric: yup
    .string()
    .required(t('SILVERCOIN_NRIC_REQUIRED'))
    .length(4, t('SILVERCOIN_NRIC_INVALID')),
  mobile_number: createMobileNumberValidation(
    country,
    t('SILVERCOIN_MOBILENUMBER_1_INVALID')
  ),
  reached_minimum_age: yup.string().required(t('SILVERCOIN_AGE_REQUIRED')),
});

const createStandardPersonalInfoSchema = () => ({
  ...createBasePersonalInfoSchema(),
  cf_mobile_number: yup
    .string()
    .required(t('SILVERCOIN_REENTERMOBILE_REQUIRED'))
    .oneOf([yup.ref('mobile_number')], t('SILVERCOIN_REENTERMOBILE_INVALID')),
});

const createBaseParentInfoSchema = () => ({
  parent_first_name: yup
    .string()
    .required(t('SILVERCOIN_PARENT_FIRSTNAME_REQUIRED')),
  parent_last_name: yup
    .string()
    .required(t('SILVERCOIN_PARENT_LASTNAME_REQUIRED')),
  parent_nric: yup
    .string()
    .required(t('SILVERCOIN_PARENT_NRIC_REQUIRED'))
    .length(4, t('SILVERCOIN_PARENT_NRIC_INVALID')),
  parent_mobile_number: createMobileNumberValidation(
    parent_country,
    t('SILVERCOIN_PARENT_MOBILENUMBER_INVALID')
  ),
});

const createStandardParentInfoSchema = () => ({
  ...createBaseParentInfoSchema(),
  parent_cf_mobile_number: yup
    .string()
    .required(t('SILVERCOIN_PARENT_REENTERMOBILENUMBER_REQUIRED'))
    .oneOf(
      [yup.ref('parent_mobile_number')],
      t('SILVERCOIN_PARENT_REENTERMOBILE_INVALID')
    ),
});

const validationSchema = computed(() => {
  const schemas = {
    [FORM_STEPS.PERSONAL_INFO]: createStandardPersonalInfoSchema(),
    [FORM_STEPS.PARENT_INFO]: createStandardParentInfoSchema(),
    [FORM_STEPS.AGREEMENT]: {
      accepted_tac: yup
        .boolean()
        .oneOf([true], t('SILVERCOIN_ACCEPTED_TAC_REQUIRED')),
    },
    [FORM_STEPS.BANK_DETAILS]: {
      country: yup.string().required(t('SILVERCOIN_BANKACC_COUNTRY_REQUIRED')),
      // currency: yup
      //   .string()
      //   .required(t('SILVERCOIN_BANKACC_CURRENCY_REQUIRED')),
      bank_account_name: yup
        .string()
        .required(t('SILVERCOIN_BANKACC_NAME_REQUIRED')),
      bank_name: yup
        .string()
        .required(t('SILVERCOIN_BANKACC_BANKNAME_REQUIRED')),
      bank_code: yup
        .string()
        .required(t('SILVERCOIN_BANKACC_BANKCODE_REQUIRED')),
      bank_account_number: yup
        .string()
        .required(t('SILVERCOIN_BANKACC_ACCNUM_REQUIRED')),
    },
    [FORM_STEPS.VIDEO_VERIFICATION]: {
      sent_video: yup
        .boolean()
        .oneOf([true], t('SILVERCOIN_SENTVERIFICATIONVIDEO_CHECKBOX_REQUIRED')),
    },
    [FORM_STEPS.MAILING_PROOF]: {
      submitted_mailing_proof: yup
        .boolean()
        .oneOf([true], t('SILVERCOIN_MAILING_CHECKBOX_REQUIRED')),
    },
    [FORM_STEPS.COMPLETION]: {},
  };

  return yup.object().shape(schemas[step.value] || {});
});

const { handleSubmit, values, setTouched, setFieldValue } = useForm({
  initialValues: initialValues.value,
  validationSchema,
});

const callback = handleSubmit(async (values) => {
  const payload = omit(
    {
      ...values,
      mobile_number: country.value?.code + values.mobile_number,
    },
    ['cf_mobile_number', 'parent_cf_mobile_number']
  ) as unknown as Partial<IWinnerInfoPayload>;

  const { data } = await SILVER_COIN.verifyAccept(deleteEmptyFields(payload));
  await storeUser.fetchUser();
  return data;
});

const getNextStep = (
  currentStep: FormSteps,
  data: IVerification
): FormSteps => {
  switch (currentStep) {
    case FORM_STEPS.PERSONAL_INFO:
      const nextAfterPersonal = data.winner_info?.reached_minimum_age
        ? FORM_STEPS.AGREEMENT
        : FORM_STEPS.PARENT_INFO;
      return nextAfterPersonal;

    case FORM_STEPS.AGREEMENT:
      const nextAfterAgreement = FORM_STEPS.BANK_DETAILS;
      return nextAfterAgreement;

    case FORM_STEPS.PARENT_INFO:
      return FORM_STEPS.AGREEMENT;

    case FORM_STEPS.BANK_DETAILS:
      return FORM_STEPS.VIDEO_VERIFICATION;

    case FORM_STEPS.VIDEO_VERIFICATION:
      return FORM_STEPS.MAILING_PROOF;

    case FORM_STEPS.MAILING_PROOF:
      return FORM_STEPS.COMPLETION;

    default:
      return currentStep;
  }
};

const handleStepCompletion = (data: IVerification) => {
  const nextStep = getNextStep(step.value, data);
  step.value = nextStep;

  if (step.value === FORM_STEPS.COMPLETION) {
    openDialog('silver_coin_founder', {
      coin: {
        ...dataVerification.value?.coin,
        coin_number:
          dataVerification.value?.coin_number ||
          dataVerification.value?.coin?.coin_number,
        brand_unique_id:
          dataVerification.value?.brand_unique_id ||
          dataVerification.value?.coin?.brand_unique_id,
        brand_name:
          dataVerification.value?.brand_name ||
          dataVerification.value?.coin?.brand_name,
        prefix:
          dataVerification.value?.prefix ||
          dataVerification.value?.coin?.prefix ||
          '',
        coin_id: coinId.value,
        winner_info: data.winner_info,
        reward:
          dataVerification.value?.coin?.reward ||
          dataVerification.value?.reward,
      },
    });
    storeUser.dataVerification = null;
    emits('close');
  }
};

const trackFormSubmission = () => {
  const trackingType =
    TRACKING_CONFIG.type[step.value as keyof typeof TRACKING_CONFIG.type];
  const trackingAction =
    TRACKING_CONFIG.action[step.value as keyof typeof TRACKING_CONFIG.action];

  if (trackingType && trackingAction) {
    track(trackingType, { action: trackingAction });
  }
};

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    trackFormSubmission();
    const data = await callback();
    return data;
  },
  onSuccess(data) {
    if (!data) return;

    trackUserLocation('enter_serial_number', { step: step.value });
    track('found_coin_info', { coin_id: coinId.value, step: step.value });
    setTouched(false);

    handleStepCompletion(data);
  },
  onError(error) {
    console.error('Form submission error:', error);
  },
});

function handleCopy() {
  copyToClipboard(
    t('SILVERCOIN_VERIFICATIONVIDEO_STEP2_3', {
      HUNTER: user.value?.hunter_id,
      COIN: coinId.value,
    })
  );

  successNotify({
    message: t('SILVERCOIN_WINNERSAGREEMENTPROCESS_COPY_MSG'),
  });
}

function onClose() {
  emits('close');
}

onMounted(() => {
  storeUser.fetchUser();
});
</script>

<template>
  <Dialog :hide-close="step < 5" @close="onClose">
    <template #header>
      <div v-html="header"></div>
    </template>
    <q-form @submit="onSubmit">
      <Stepper class="mb-5" :steps="Object.keys(FORM_STEPS)" :step="step" />
      <section v-show="step === FORM_STEPS.PERSONAL_INFO">
        <div
          class="text-sm text-center mb-5"
          v-html="t('SILVERCOIN_FORM_DESC')"
        ></div>
        <VeeInput
          class="mb-5"
          name="first_name"
          :label="t('SILVERCOIN_FIRSTNAME_1')"
        />
        <VeeInput
          class="mb-5"
          name="last_name"
          :label="t('SILVERCOIN_LASTNAME_1')"
        />
        <VeeInput
          class="mb-5"
          name="nric"
          :label="t('SILVERCOIN_NRIC')"
          :maxlength="4"
        />
        <VeeInputCountry
          class="mb-5"
          name="mobile_number"
          :label="t('SILVERCOIN_MOBILENUMBER_1')"
          :default-country="country"
          @update:country="(c: ICountryRegion) => {
            country = c;
            setFieldValue('country', c.iso)
          }"
        />

        <VeeInputCountry
          class="mb-5"
          name="cf_mobile_number"
          :label="t('SILVERCOIN_REENTERMOBILE')"
          :default-country="country"
          @update:country="(c: ICountryRegion) => {
            country = c;
            setFieldValue('country', c.iso)
          }"
          :disable="!values.mobile_number"
        />

        <div
          class="text-sm italic text-center mb-5"
          v-html="t('SILVERCOIN_FORM_MOBILENUMINFO')"
        ></div>
        <VeeSelect
          class="mb-5"
          name="reached_minimum_age"
          :label="t('SILVERCOIN_AGE')"
          :options="AGE_OPTIONS"
        />
      </section>
      <section v-show="step === FORM_STEPS.PARENT_INFO">
        <div
          class="text-sm text-center mb-5"
          v-html="t('SILVERCOIN_PARENT_DESC')"
        ></div>
        <VeeInput
          class="mb-5"
          name="parent_first_name"
          :label="t('SILVERCOIN_PARENT_FIRSTNAME')"
        />
        <VeeInput
          class="mb-5"
          name="parent_last_name"
          :label="t('SILVERCOIN_PARENT_LASTNAME')"
        />
        <VeeInput
          class="mb-5"
          name="parent_nric"
          :label="t('SILVERCOIN_PARENT_NRIC')"
          :maxlength="4"
        />
        <VeeInputCountry
          class="mb-5"
          name="parent_mobile_number"
          :label="t('SILVERCOIN_PARENT_MOBILENUMBER')"
          @update:country="parent_country = $event"
        />
        <VeeInputCountry
          class="mb-5"
          name="parent_cf_mobile_number"
          :label="t('SILVERCOIN_PARENT_REENTERMOBILENUMBER')"
          @update:country="parent_country = $event"
          :disable="!values.parent_mobile_number"
        />
      </section>
      <section v-show="step === FORM_STEPS.AGREEMENT">
        <WinnerAgreement
          class="mb-5"
          :reward="
            String(dataVerification?.coin?.reward || dataVerification?.reward)
          "
        />
        <VeeCheckbox
          name="accepted_tac"
          :label="t('SILVERCOIN_WINNERSAGREEMENT_CHECKBOX')"
        />
      </section>
      <section v-show="step === FORM_STEPS.BANK_DETAILS">
        <div
          class="text-sm text-center mb-5"
          v-html="t('SILVERCOIN_BANKACC_DESC')"
        ></div>
        <VeeSelect
          class="mb-5"
          name="country"
          :label="t('SILVERCOIN_BANKACC_COUNTRY')"
          :options="RESIDENCE_OPTIONS"
        />
        <!-- <VeeSelect
          class="mb-5"
          name="currency"
          :label="t('SILVERCOIN_BANKACC_CURRENCY')"
          :options="CURRENCY_OPTIONS"
        /> -->
        <VeeInput
          class="mb-5"
          name="bank_account_name"
          :label="t('SILVERCOIN_BANKACC_NAME')"
        />
        <VeeInput
          class="mb-5"
          name="bank_name"
          :label="t('SILVERCOIN_BANKACC_BANKNAME')"
        />
        <VeeInput
          class="mb-5"
          name="bank_code"
          :label="t('SILVERCOIN_BANKACC_BANKCODE')"
        />
        <VeeInput
          class="mb-5"
          name="branch_code"
          :label="t('SILVERCOIN_BANKACC_BRANCHCODE')"
        />
        <VeeInput
          class="mb-5"
          name="bank_account_number"
          :label="t('SILVERCOIN_BANKACC_ACCNUM')"
        />
        <div
          class="italic text-sm text-center px-2 mb-5"
          v-html="t('SILVERCOIN_BANKACC_TERMS')"
        ></div>
      </section>
      <section v-show="step === FORM_STEPS.VIDEO_VERIFICATION">
        <div
          class="text-sm mb-3 text-center px-2"
          v-html="t('SILVERCOIN_VERIFICATIONVIDEO_TITLE_STEP1_2')"
        ></div>
        <div class="flex flex-col gap-5 mb-5">
          <div class="flex flex-nowrap gap-5">
            <div
              class="text-sm font-bold whitespace-nowrap"
              v-html="t('SILVERCOIN_VERIFICATIONVIDEO_STEP1_1')"
            ></div>
            <div class="text-sm">
              <div
                class="mb-3"
                v-html="t('SILVERCOIN_VERIFICATIONVIDEO_STEP1_2')"
              ></div>
              <div v-html="t('SILVERCOIN_VERIFICATIONVIDEO_STEP1_3')"></div>
            </div>
          </div>
          <div class="flex flex-nowrap gap-5">
            <div
              class="text-sm font-bold whitespace-nowrap"
              v-html="t('SILVERCOIN_VERIFICATIONVIDEO_STEP2_1')"
            ></div>
            <div v-html="t('SILVERCOIN_VERIFICATIONVIDEO_STEP2_2')"></div>
          </div>
        </div>
        <div
          class="flex flex-nowrap items-center bg-[#392985] p-2 mb-5"
          @click="handleCopy"
        >
          <div
            v-html="
              t('SILVERCOIN_VERIFICATIONVIDEO_STEP2_3', {
                HUNTER: user?.hunter_id,
                COIN: coinId,
              })
            "
          ></div>
          <Icon name="copy" :size="20" />
        </div>
        <VeeCheckbox
          class="mb-2"
          name="sent_video"
          :label="t('SILVERCOIN_SENTVERIFICATIONVIDEO_CHECKBOX')"
        />
      </section>
      <section v-show="step === FORM_STEPS.MAILING_PROOF">
        <div
          class="text-sm text-center mb-5"
          v-html="t('SILVERCOIN_MAILING_1')"
        ></div>
        <div class="flex flex-col gap-5 mb-5">
          <div class="flex flex-nowrap gap-5">
            <div
              class="text-sm font-bold whitespace-nowrap"
              v-html="t('SILVERCOIN_MAILING_STEP1_1')"
            ></div>
            <div v-html="t('SILVERCOIN_MAILING_STEP1_2')"></div>
          </div>
          <div class="flex flex-nowrap gap-5">
            <div
              class="text-sm font-bold whitespace-nowrap"
              v-html="t('SILVERCOIN_MAILING_STEP2_1')"
            ></div>
            <div v-html="t('SILVERCOIN_MAILING_STEP2_2')"></div>
          </div>
          <div class="flex flex-nowrap gap-5">
            <div
              class="text-sm font-bold whitespace-nowrap"
              v-html="t('SILVERCOIN_MAILING_STEP3_1')"
            ></div>
            <div class="text-sm" v-html="t('SILVERCOIN_MAILING_STEP3_2')"></div>
          </div>
        </div>
        <VeeCheckbox
          class="mb-2"
          name="submitted_mailing_proof"
          :label="t('SILVERCOIN_MAILING_CHECKBOX')"
        />
      </section>
      <div class="text-center mt-5">
        <Button :label="submitButtonLabel" :loading="loading" type="submit" />
      </div>
    </q-form>
  </Dialog>
</template>
