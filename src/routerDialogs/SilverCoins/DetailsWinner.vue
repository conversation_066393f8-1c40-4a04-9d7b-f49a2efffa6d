<script lang="ts" setup>
import { useTrackData } from '@composables';
import type { IWinnerInfo } from '@types';

interface Props {
  winner_info: IWinnerInfo;
}

interface Emits {
  (event: 'close'): void;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { track } = useTrackData();

onBeforeUnmount(() => {
  track('silvercoin_userdetails', {
    action: 'silvercoin_userdetails_back',
  });
});
</script>

<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button shape="square" variant="purple" @click="emits('close')">
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('SILVERCOIN_WINNER_DETAILS_TITLE')"></div>
    </template>

    <div class="text-center px-5">
      <div
        class="text-sm"
        v-html="t('SILVERCOINFOUND_WINNER_DETAILS_FIRSTNAME')"
      ></div>
      <div class="text-lg font-bold mb-4">{{ winner_info?.first_name }}</div>
      <div
        class="text-sm"
        v-html="t('SILVERCOINFOUND_WINNER_DETAILS_LASTNNAME')"
      ></div>
      <div class="text-lg font-bold mb-4">{{ winner_info?.last_name }}</div>
      <div
        class="text-sm"
        v-html="t('SILVERCOINFOUND_WINNER_DETAILS_NRIC')"
      ></div>
      <div class="text-lg font-bold mb-4">{{ winner_info?.nric }}</div>
      <div
        class="text-sm"
        v-html="t('SILVERCOINFOUND_WINNER_DETAILS_MOBILE')"
      ></div>
      <div class="text-lg font-bold mb-4">
        {{ winner_info?.mobile_number }}
      </div>
    </div>
  </Dialog>
</template>
