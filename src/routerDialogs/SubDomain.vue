<script lang="ts" setup>
import { countries } from '@helpers';
import { useUserStore, useDialogStore } from '@stores';

interface Emits {
  (event: 'close'): void;
  (event: 'closeX'): void;
  (event: 'stay'): void;
  (event: 'register'): void;
}

const storeUser = useUserStore();
const storeDialog = useDialogStore();

const { currentCountryCode, seasonCode, settings } = storeToRefs(storeUser);
const { openDialog } = useMicroRoute();
const { t } = useI18n();

const emits = defineEmits<Emits>();

const URL = computed(() => process.env.APP_END_POINT || '');
const country = computed(() => {
  return countries.find((c) => c.iso === currentCountryCode.value);
});

function onClose() {
  storeDialog.subDomainTicket = true;
  emits('close');
}
</script>

<template>
  <Dialog
    @close="
      onClose();
      emits('closeX');
    "
  >
    <template #header>
      <div v-html="t('SUB_DOMAIN_TITLE')"></div>
    </template>
    <div class="text-center">
      <div
        class="text-sm mb-5"
        v-html="
          t(
            country && country.iso !== seasonCode
              ? 'SUB_DOMAIN_CONTENT_1'
              : 'SUB_DOMAIN_CONTENT_2',
            {
              COUNTRY: country?.name,
            }
          )
        "
      ></div>
      <Icon :name="`subdomain_${seasonCode}`" class="w-full mb-5" />
      <div
        class="text-sm mb-5"
        v-html="
          t(
            !!settings &&
              !['gold_ongoing', 'silver_ongoing'].includes(
                settings.holding_page_state
              )
              ? 'SUB_DOMAIN_CONTENT_4'
              : 'SUB_DOMAIN_CONTENT_3',
            {
              COUNTRY: country?.name,
              URL: URL.replace(/^https?:\/\//, ''),
            }
          )
        "
      ></div>
      <div class="flex justify-center flex-nowrap gap-5">
        <Button
          class="!min-w-0 !w-[45%]"
          :label="t('SUB_DOMAIN_BTN_1')"
          variant="purple"
          @click="
            onClose();
            emits('stay');
          "
        />

        <Button
          v-if="
            !!settings &&
            !['gold_ongoing', 'silver_ongoing'].includes(
              settings.holding_page_state
            )
          "
          class="!min-w-0 !w-[55%]"
          :label="t('SUB_DOMAIN_BTN_2')"
          @click="
            openDialog('time_line_v2');
            onClose();
            emits('register');
          "
        />

        <a
          v-else
          :href="country?.url"
          target="_blank"
          rel="noopener noreferrer"
          @click="onClose"
          class="!w-[55%] !min-w-0"
        >
          <Button class="!min-w-0 !w-full" :label="t('SUB_DOMAIN_BTN_3')" />
        </a>
      </div>
    </div>
  </Dialog>
</template>
