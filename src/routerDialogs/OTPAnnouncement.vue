<script lang="ts" setup>
import { useUserStore, useDialogStore } from '@stores';
import { useTrackData } from '@composables';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeDialog = useDialogStore();

const { user } = storeToRefs(storeUser);

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

function handleSubmit() {
  if (!user.value?.mobile_number) openDialog('signup');
  // push('offer_wall', {
  //   scrollToElement: '#verify_mobile_number',
  // });
  else openDialog('verify_mobile_number');
  onClose();
}

function onClose() {
  storeDialog.otpAnnouncementTicked = true;
  emits('close');
}

onMounted(async () => {
  await nextTick();
  storeUser.fetchSetting();
});
</script>

<template>
  <Dialog
    @close="
      onClose();
      track('auto_window_buttons', {
        screen_type: 'otp_announcement',
        action: 'x',
      });
    "
  >
    <template #header>
      <div class="text-lg font-bold" v-html="t('otp_ann_title')" />
    </template>

    <div
      class="text-center mb-5"
      v-html="t(!user?.mobile_number ? 'otp_ann_des_2' : 'otp_ann_des')"
    ></div>
    <div class="relative px-5 w-full mb-5">
      <Icon name="otp_slides/3" class="!w-full" />
      <Icon
        name="otp_slides/small-crystal"
        :size="64"
        class="absolute -right-1 -top-4 z-[9999]"
      />
    </div>
    <div class="text-center">
      <Button
        @click="
          handleSubmit();
          track('auto_window_other', {
            screen_type: 'otp_announcement',
            action: !user?.mobile_number ? 'signup' : 'offer_wall',
          });
        "
        :label="!user?.mobile_number ? t('otp_ann_btn_3') : t('otp_ann_btn_2')"
      />
    </div>
  </Dialog>
</template>
