<script lang="ts" setup>
import { SlideIllustration } from '@components';
import { useDialogStore, useUserStore } from '@stores';

const storeUser = useUserStore();
const storeDialog = useDialogStore();

const { t } = useI18n();
const { closeDialog } = useMicroRoute();

function goToBeacon() {
  closeDialog('beacon_welcome');
  storeDialog.showBeaconGUI = true;
}

onMounted(() => {
  storeUser.updateOnboarding('first_beacon');
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('BEACON_WELCOME_HEADER')"></div>
    </template>
    <div class="text-center px-5">
      <div class="text-sm mb-4" v-html="t('BEACON_WELCOME_DESC_1')"></div>
      <SlideIllustration
        class="mb-4"
        :illustrations="
          ['beacon_1', 'beacon_2', 'beacon_3', 'beacon_4'].map((img) => ({
            name: img,
            type: 'png',
          }))
        "
      />

      <div class="text-sm mb-4" v-html="t('BEACON_WELCOME_DESC_2')"></div>
      <Button
        :label="t('BEACON_WELCOME_BTN_USE')"
        class="!w-[210px] mb-5"
        @click="goToBeacon"
      />
      <!-- <div
        class="text-sm opacity-70"
        v-html="t('BEACON_WELCOME_DESC_3', { NUMBER: beacons })"
      ></div> -->
    </div>
  </Dialog>
</template>
