<script lang="ts" setup>
import gsap, { Elastic, Linear } from 'gsap';
import { playSFX } from '@composables';
import { closeNotify } from '@helpers';

interface Emits {
  (event: 'close'): void;
}

interface Props {
  crystal: number;
  beacon?: number;
  buttonType?:
    | 'default'
    | 'fromDailyMission'
    | 'fromToolKitMission'
    | 'backToResult'
    | 'backToMap';
  hiddenButton?: boolean;
  resourceType?: 'dbs_crystal' | 'crystal-s';
}

const NO_BACK_BUTTON = ['backToMap', 'backToResult'];

const emits = defineEmits<Emits>();
withDefaults(defineProps<Props>(), {
  buttonType: 'default',
  beacon: 0,
  resourceType: 'dbs_crystal',
});

const tl = gsap.timeline();
const tl2 = gsap.timeline();
const { t } = useI18n();

const { push, closeAllDialog } = useMicroRoute();

const stars = [
  { size: 50, top: 0, left: 0, bottom: 'auto', right: 'auto' },
  { size: 30, top: 'auto', left: 'auto', bottom: '20px', right: '20px' },
];

function startAnimations() {
  tl.fromTo(
    '.upload_flare',
    {
      scale: 0,
    },
    {
      scale: 1,
      duration: 1,
      delay: 0.5,
    }
  )
    .fromTo(
      '.btn_s',
      {
        opacity: 0,
        y: 15,
      },
      {
        opacity: 1,

        y: 0,
        duration: 1,
      }
    )
    .fromTo(
      '.upload_flare',
      {
        rotate: 0,
      },
      {
        rotate: 720,
        duration: 20,
        repeat: -1,
        ease: Linear.easeNone,
      },
      '-=1'
    );
  setTimeout(() => {
    playSFX('success');
  }, 1000);
  tl2
    .fromTo(
      '.cry',
      {
        y: -500,
      },
      {
        ease: Elastic.easeInOut.config(1, 0.35),

        y: 0,
        duration: 1.5,
      }
    )
    .fromTo(
      '.a-text',
      {
        opacity: 0,
        y: 15,
      },
      {
        opacity: 1,

        y: 0,
        duration: 2,
      },
      '-=1'
    )
    .fromTo(
      '.star',
      {
        scale: 0,
      },
      {
        scale: 1,
        duration: 1,

        stagger: {
          each: 1 / 2,
          repeat: -1,
          yoyo: true,
          ease: 'none',
        },
      }
    );
  gsap.fromTo(
    '.upload_star',
    {
      opacity: 0,
    },
    {
      opacity: 1,
      yoyo: true,
      repeat: -1,
      delay: 1,
      duration: 1,
    }
  );
}

onMounted(async () => {
  await nextTick();
  startAnimations();
  closeNotify();
});

function getMore() {
  emits('close');
  push('offer_wall');
}

onBeforeUnmount(() => {
  tl?.kill();
  tl2?.kill();
  gsap.killTweensOf('.upload_star');
});
</script>
<template>
  <div
    class="fit relative z-10 flex flex-col justify-center items-center overflow-hidden bg-[#090422] text-center"
  >
    <div class="fixed z-50 flex justify-between top-3 left-3 right-3">
      <Button
        shape="square"
        variant="secondary"
        @click="emits('close')"
        :class="{
          'opacity-0 pointer-events-none': NO_BACK_BUTTON.includes(buttonType),
        }"
      >
        <Icon name="arrow-left" />
      </Button>
      <HeaderCrystal />
    </div>

    <div>
      <p class="a-text" v-html="t('PROMOCODE_CRYSTALSRECEIVED_TEXT_1')"></p>
      <p
        v-if="crystal > 0 && beacon > 0"
        class="a-text my-[10px] font-bold text-2xl"
        v-html="
          t('PROMOCODE_CRYSTALSRECEIVED_TEXT_4', {
            CRYSTAL: crystal,
            BEACON: beacon,
          })
        "
      ></p>
      <p
        v-else-if="crystal > 0 && !beacon"
        class="a-text my-[10px] font-bold text-2xl"
        v-html="
          t('PROMOCODE_CRYSTALSRECEIVED_TEXT_2', {
            CRYSTAL: crystal,
          })
        "
      ></p>
      <p
        v-else
        class="a-text my-[10px] font-bold text-2xl"
        v-html="
          t('PROMOCODE_CRYSTALSRECEIVED_TEXT_5', {
            BEACON: beacon,
          })
        "
      ></p>
      <p class="a-text" v-html="t('PROMOCODE_CRYSTALSRECEIVED_TEXT_3')"></p>

      <div
        class="relative flex pointer-events-none full-width flex-center"
        style="height: 100vw"
      >
        <Icon
          class="absolute upload_flare"
          style="top: 0; left: 50%; width: 100vw; transform: translateX(-50%)"
          name="upload_flare"
        />
        <Icon
          class="absolute upload_star full-width"
          style="top: 0; left: 0; z-index: 2"
          name="star_frame"
        />
        <div class="relative">
          <Icon
            v-if="crystal > 0 && beacon > 0"
            name="beacon-crystals"
            :size="150"
            class="relative cry"
            style="z-index: 3"
          />
          <Icon
            v-else-if="crystal > 0 && !beacon"
            :name="resourceType"
            :size="150"
            class="relative cry"
            style="z-index: 3"
          />
          <Icon
            v-else
            name="beacon"
            :size="150"
            class="relative cry"
            style="z-index: 3"
          />
          <Icon
            v-for="(item, index) in stars"
            name="star"
            class="star"
            :key="`star-${index}`"
            :width="item.size"
            :style="`position:absolute;left:${item.left};bottom:${item.bottom};right:${item.right};top:${item.top};z-index:4;`"
          />
        </div>
      </div>
      <div class="btn_s" v-if="!hiddenButton">
        <Button
          v-if="buttonType === 'default'"
          class="mx-auto"
          @click="getMore"
          :label="t('BUTTON_GETMORECRYSTALS')"
        />

        <Button
          v-if="buttonType === 'backToResult'"
          class="mx-auto"
          @click="emits('close')"
          :label="t('Back to results')"
        />

        <Button
          v-if="buttonType === 'fromDailyMission'"
          class="mx-auto"
          :label="t('BUTTON_BACK_TO_MISSIONS')"
          @click="emits('close')"
        />

        <Button
          v-if="buttonType === 'fromToolKitMission'"
          class="mx-auto"
          :label="t('BUTTON_BACK_TO_MISSIONS')"
          @click="
            emits('close');
            push('missions');
          "
        />

        <Button
          v-if="buttonType === 'backToMap'"
          class="mx-auto"
          :label="t('BUTTON_BACK_TO_MAP')"
          @click="
            emits('close');
            closeAllDialog();
          "
        />
      </div>
      <!-- <div
        v-if="tapAnyWhere"
        class="btn_s text-sm text-center"
        v-html="t('PROMOCODE_CRYSTALSRECEIVED_TAP_ANYWHERE')"
      ></div> -->
    </div>
  </div>
</template>
