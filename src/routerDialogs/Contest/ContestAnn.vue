<script lang="ts" setup>
import { useDialogStore, useUserStore } from '@stores';
import { FULL_DATE_TIME_24H_FORMAT, dateTimeFormat } from '@helpers';
import { useAsync, useTrackData } from '@composables';
import type { IContest, IUserContest } from '@types';

interface Emits {
  (e: 'close'): void;
}

interface Props {
  contest: IContest & { userContest: IUserContest };
  type: 'starting' | 'ended';
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeDialog = useDialogStore();
const storeUser = useUserStore();

const { contest: dataContest } = storeToRefs(storeUser);
const { push } = useMicroRoute();
const { t } = useI18n();
const { track } = useTrackData();

const checkbox = ref(false);

const currentRank = computed(() => {
  const data = dataContest.value?.leaderboard.find(
    (l) => l.school === props.contest.userContest?.school
  );
  return data;
});

const { loading, execute: goToContest } = useAsync({
  async fn() {
    await storeUser.getContestById(props.contest.unique_id);
  },
  onSuccess() {
    onClose();
    push('contest');
  },
});

function onClose() {
  emits('close');
  storeDialog.contestAnnouncementTicked = true;
}

function getOrdinal(n: number) {
  const s = ['th', 'st', 'nd', 'rd'];
  const v = n % 100;
  return n + (s[(v - 20) % 10] || s[v] || s[0]);
}

watch(checkbox, (value) => {
  if (value) LocalStorage.set('contest_popup', new Date().toISOString());
  else LocalStorage.remove('contest_popup');
});
</script>

<template>
  <Dialog
    @close="
      onClose();
      track('auto_window_buttons', {
        screen_type: 'contest_announcement',
        action: 'x',
      });
    "
  >
    <template #header>
      <div v-html="t(contest.title)"></div>
    </template>
    <template v-if="type === 'starting'">
      <div
        class="text-lg font-bold mb-3"
        v-html="t('CONTEST_ANN_DESC_1')"
      ></div>
      <div
        class="text-sm mb-5"
        v-html="
          !!contest.userContest?.voted_at
            ? t('CONTEST_ANN_DESC_3', {
                UNIVERSITY: t(contest.userContest.school),
                TIME: dateTimeFormat(
                  contest.userContest.voted_at,
                  FULL_DATE_TIME_24H_FORMAT
                ),
              })
            : t('CONTEST_ANN_DESC_2')
        "
      ></div>
      <div class="flex flex-nowrap items-center gap-5">
        <Button
          :label="t('CONTEST_ANN_BUTTON_LATER')"
          variant="purple"
          class="!min-w-0 !w-[180px]"
          @click="
            onClose();
            track('auto_window_buttons', {
              screen_type: 'contest_announcement',
              action: 'button',
            });
          "
        />
        <Button
          :label="t('CONTEST_ANN_BUTTON_GO_CONTEST')"
          :loading="loading"
          block
          @click="
            goToContest();
            track('auto_window_buttons', {
              screen_type: 'auto_window_other',
              other_button: 'go_contest',
            });
          "
        />
      </div>
    </template>

    <template v-if="type === 'ended' && !!currentRank" #special-box>
      <div class="p-5 text-center">
        <div
          class="text-lg font-bold mb-3"
          v-html="
            t('CONTEST_ANN_DESC_4', {
              RANK: getOrdinal(currentRank.rank),
            })
          "
        ></div>
        <div
          class="flex flex-nowrap justify-between items-center gap-4 mb-5"
          :class="{
            'top-ranking': Number(currentRank.rank) <= 3,
            ranking: Number(currentRank.rank) > 3,
          }"
        >
          <div class="flex flex-nowrap gap-4">
            <div class="text-2xl" style="min-width: 14px">
              {{ currentRank.rank || '-' }}
            </div>
            <div class="text-base text-left font-bold mt-1">
              {{ t(currentRank.school) }}
            </div>
          </div>
          <div class="flex flex-nowrap gap-4">
            <Icon name="vote" />
            <div class="text-sm">{{ currentRank.votes }}</div>
          </div>
        </div>
        <div class="text-sm mb-5" v-html="t('CONTEST_ANN_DESC_5')"></div>
        <Button
          class="!w-[200px] mb-5"
          :label="t('CONTEST_ANN_BUTTON_VIEW_LEADERBOARD')"
          :loading="loading"
          @click="
            goToContest();
            track('auto_window_buttons', {
              screen_type: 'auto_window_other',
              other_button: 'go_contest',
            });
          "
        />
        <q-checkbox
          class="mx-auto"
          v-model="checkbox"
          :label="t('CONTEST_CHECKBOX')"
          @click="
            track('auto_window_other', {
              screen_type: 'contest_announcement',
              other_button: 'checkbox',
            })
          "
        />
      </div>
    </template>
  </Dialog>
</template>
<style lang="scss" scoped>
.ranking {
  padding: 5px 15px;
  width: calc(100% + 60px);
  margin-left: -30px;
  border-radius: 10px;
  background: #5d3ac0;
}
.top-ranking {
  position: relative;
  padding: 5px 15px;
  width: calc(100% + 60px);
  margin-left: -30px;
  border-radius: 10px;
  border: 2px solid #38d2e7;
  background: linear-gradient(
      180deg,
      rgba(56, 210, 231, 0.3) 0%,
      rgba(22, 90, 115, 0.5) 100%
    ),
    linear-gradient(261deg, #341c7c 1.58%, #1a0950 60.32%);
  &::before {
    content: '';
    position: absolute;
    top: 0;
    width: 32px;
    height: 70px;
    left: -10px;
    background: url('/imgs/sparkles.png');
    background-size: 100% 100%;
  }
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    width: 32px;
    height: 70px;
    right: -10px;
    background: url('/imgs/sparkles.png');
    transform: rotate(180deg);
    background-size: 100% 100%;
  }
}
</style>
