<script lang="ts" setup>
import { SelectContest } from '@components';
import { useUserStore } from '@stores';
import { useAsync } from '@composables';
import { EVENT } from '@repositories';
import { successNotify } from '@helpers';
import type { IContest } from '@types';

interface Props {
  contest: IContest;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { t } = useI18n();

const step = ref(1);
const school = ref('');

const disable = computed(() => !school.value);

const title = computed(() => {
  return t(`VOTING_TITLE_${step.value}`);
});

const { loading, execute: handleVote } = useAsync({
  async fn() {
    await EVENT.vote({
      id: props.contest.unique_id,
      school: school.value,
    });
    await storeUser.getContestById(props.contest.unique_id);
    await storeUser.fetchReferral();
  },
  onSuccess() {
    successNotify({
      message: t('VOTED_SUCCESS'),
    });
    emits('close');
  },
});
</script>

<template>
  <Dialog :hide-close="step === 2" @close="emits('close')">
    <template #header>
      <div v-html="title"></div>
    </template>
    <template #btnTopLeft v-if="step === 2">
      <Button
        class="absolute -top-3 -left-3 tẽ"
        shape="square"
        variant="secondary"
        @click="step = 1"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <div class="text-center">
      <section v-show="step === 1">
        <div class="text-sm px-2 mb-4" v-html="t('VOTING_DESC_1')"></div>

        <SelectContest
          class="mb-5"
          :schools="contest.schools"
          @selected="(s) => (school = s)"
        />

        <Button
          :label="t('BUTTON_SUBMIT_VOTE')"
          :disable="disable"
          @click="step = 2"
        />
      </section>
      <section v-show="step === 2">
        <div class="text-sm px-2 mb-5" v-html="t('VOTING_DESC_2')"></div>
        <div
          class="text-sm mb-5"
          v-html="
            t('VOTING_DESC_2_SELECT_INFO', {
              UNIVERSITY: t(school),
            })
          "
        ></div>
        <Button
          :label="t('BUTTON_CONFIRM')"
          :loading="loading"
          @click="handleVote"
        />
      </section>
    </div>
  </Dialog>
</template>
