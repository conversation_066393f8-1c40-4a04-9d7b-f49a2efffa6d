<script setup lang="ts">
import { successNotify } from '@helpers';
import { useUserStore } from '@stores';
import { copyToClipboard } from 'quasar';

const storeUser = useUserStore();

const { user, referrals } = storeToRefs(storeUser);
const { t } = useI18n();

const URL = computed(
  () => process.env.APP_END_POINT?.replace(/^https?:\/\//, '') || ''
);

const referLink = computed(
  () => `${URL.value}?ref=${user.value?.referral_code}`
);

const SOCIALS = computed(() => [
  {
    network: 'facebook',
    icon: 'fb',
    sharelink: `https://www.facebook.com/sharer/sharer.php?u=${referLink.value}`,
    link: 'https://www.facebook.com/sqkii',
  },
  {
    network: 'instagram',
    icon: 'insta',
    link: 'https://www.instagram.com/sqkiimouse',
  },
  {
    network: 'Telegram',
    icon: 'telegram_w',
    sharelink: `https://t.me/share/url?url=${referLink.value}&text=${t(
      'REFERRAL_DESC_COPIED',
      { URL: referLink.value }
    )}`,
    link: `https://t.me/share/url?url=${referLink.value}`,
  },
  {
    network: 'whatsapp',
    icon: 'wa',
    link: '',
    sharelink: `https://api.whatsapp.com/send?text=${t('REFERRAL_DESC_COPIED', {
      URL: referLink.value,
    })}`,
  },
]);

function handleCopy() {
  const text = t('REFERRAL_DESC_COPIED', { URL: referLink.value });

  copyToClipboard(text);
  successNotify({
    message: t('REFERRAL_COPIED'),
  });
}

onMounted(async () => {
  await storeUser.fetchReferral();
});
</script>

<template>
  <Dialog>
    <template #header>
      <div v-html="t('CONTEST_REFERRAL_DIALOG_TITLE')"></div>
    </template>
    <div class="text-center">
      <template v-if="!referrals.length">
        <div
          class="text-sm mb-4"
          v-html="t('CONTEST_REFERRAL_DIALOG_DESC_1')"
        ></div>
        <div
          class="text-sm mb-4"
          v-html="t('CONTEST_REFERRAL_DIALOG_DESC_2')"
        ></div>
      </template>
      <div
        class="flex flex-nowrap justify-between items-center gap-2 rounded-md p-4 bg-[#10142B50] mb-3"
        @click="handleCopy"
      >
        <div class="text-sm">
          {{ `${URL.replace('staging.', '')}/${user?.referral_code}` }}
        </div>
        <Icon name="copy" />
      </div>
      <div class="flex justify-center items-center gap-3 mb-5">
        <a
          v-for="{ icon, link, sharelink } in SOCIALS"
          :key="icon"
          :href="sharelink || link"
          target="_blank"
          rel="noopener noreferrer"
          class="row justify-center items-center"
        >
          <Icon :name="icon" :size="30" />
        </a>
      </div>
      <div class="bg-[#10142B50] px-2 py-4 rounded-lg">
        <div
          class="flex justify-between items-center pb-2 mb-2"
          style="border-bottom: 1px solid #ffffff"
        >
          <div class="font-bold" v-html="t('REFERRAL_HUNTER_ID')"></div>
          <div class="font-bold" v-html="t('REFERRAL_VOTING_STATUS')"></div>
        </div>
        <template v-if="!!referrals.length">
          <div class="flex flex-col gap-2">
            <div
              class="flex justify-between items-center"
              v-for="r in referrals"
              :key="r.id"
            >
              <div class="text-sm">
                {{
                  r.status === 'pending'
                    ? t('REFERRAL_GUEST')
                    : t('REFERRAL_HUNTER')
                }}
                #{{ r.referee?.hunter_id }}
              </div>
              <Icon
                :name="r.status === 'claimed' ? 'charm_tick' : 'charm'"
                :size="80"
              />
            </div>
          </div>
        </template>
        <template v-else>
          <div class="text-sm py-3" v-html="t('REFERRAL_EMPTY_LIST')"></div>
        </template>
      </div>
    </div>
  </Dialog>
</template>
