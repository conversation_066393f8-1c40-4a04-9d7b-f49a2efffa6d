<script setup lang="ts">
import { useTrackData } from '@composables';
import { PixiAnims } from '@components';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const { push } = useMicroRoute();
const { t } = useI18n();
const { trackTime, track } = useTrackData();

function handleClose() {
  trackTime('tips_and_tricks');
  emits('close');
}

onMounted(() => {
  track('tipsandtricks_access');
});
</script>
<template>
  <Dialog @close="handleClose">
    <template #header>
      <div v-html="t('TIPSANDTRICKS_POPUP_HEADING')"></div>
    </template>

    <div class="text-base font-bold text-center mb-2">
      {{ t('TIPSANDTRICKS_POPUP_DESCRIPTION_2') }}
    </div>
    <div class="text-sm text-center mb-10">
      {{ t('TIPSANDTRICKS_POPUP_DESCRIPTION_1') }}
    </div>

    <div class="relative">
      <PixiAnims
        :width="70"
        :height="70"
        name="dbs-timii-waving-2"
        json="dbs-timii-waving-2.json"
        size="contain"
        :animation-speed="0.25"
        class="absolute top-[-70px] left-[-15px]"
      />
      <Expansion
        group="tiptricks"
        class="bg-[#091a3c] rounded mb-3"
        @click="
          track('tipsandtricks_toggle', {
            index: 1,
          })
        "
      >
        <template v-slot:header>
          <div class="column flex-nowrap flex-1">
            <div class="text-xs opacity-60 mb-1">
              {{ t('TIPSANDTRICKS_POPUP_1_CATEGORY') }}
            </div>
            <div class="text-base font-medium">
              {{ t('TIPSANDTRICKS_POPUP_1_TITLE') }}
            </div>
          </div>
        </template>
        <q-card style="background: transparent">
          <q-card-section>
            <div>
              <Icon
                class="gif-wrapper mb-3"
                name="gif/silver_capture_new"
                type="gif"
              />
            </div>
            <div class="text-sm text-center">
              {{ t('TIPSANDTRICKS_POPUP_1_DESC') }}
            </div>
          </q-card-section>
        </q-card>
      </Expansion>

      <Expansion
        group="tiptricks"
        class="bg-[#091a3c] rounded mb-3"
        @click="
          track('tipsandtricks_toggle', {
            index: 2,
          })
        "
      >
        <template v-slot:header>
          <div class="column flex-nowrap flex-1">
            <div class="text-xs opacity-60 mb-1">
              {{ t('TIPSANDTRICKS_POPUP_2_CATEGORY') }}
            </div>
            <div class="text-base font-medium">
              {{ t('TIPSANDTRICKS_POPUP_2_TITLE') }}
            </div>
          </div>
        </template>
        <q-card style="background: transparent">
          <q-card-section class="text-center">
            <a
              href="https://t.me/SqkiiSG"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Button :label="t('TIPSANDTRICKS_POPUP_2_BUTTON')" />
            </a>
          </q-card-section>
        </q-card>
      </Expansion>

      <Expansion
        group="tiptricks"
        class="bg-[#091a3c] rounded"
        @click="
          track('tipsandtricks_toggle', {
            index: 3,
          })
        "
      >
        <template v-slot:header>
          <div class="column flex-nowrap flex-1">
            <div class="text-xs opacity-60 mb-1">
              {{ t('TIPSANDTRICKS_POPUP_3_CATEGORY') }}
            </div>
            <div class="text-base font-medium">
              {{ t('TIPSANDTRICKS_POPUP_3_TITLE') }}
            </div>
          </div>
        </template>
        <q-card style="background: transparent">
          <q-card-section class="text-center">
            <Button
              :label="t('TIPSANDTRICKS_POPUP_3_BUTTON')"
              @click="
                handleClose();
                push('faq');
              "
            />
          </q-card-section>
        </q-card>
      </Expansion>
    </div>
  </Dialog>
</template>

<style scoped lang="scss">
.gif-wrapper {
  position: relative;
  border-radius: 12px;
  background-color: #000;
  width: 100%;
  overflow: hidden;
  border: 1px solid rgba($color: #fff, $alpha: 0.25);
}
</style>
