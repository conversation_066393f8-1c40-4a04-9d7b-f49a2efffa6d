<script lang="ts" setup>
import { useUserStore } from '@stores';
import { useAsync } from '@composables';
import { Loading } from 'quasar';
import { USER } from '@repositories';
import type { ILanguages, IUserLang } from '@types';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { user, settings } = storeToRefs(storeUser);
const { t, locale } = useI18n();
const { openDialog } = useMicroRoute();

const expand = ref(false);
const lang = ref<IUserLang>(user.value?.lang || 'en');

const languageData = computed(() => {
  if (!settings.value?.supported_languages.length) return {} as ILanguages;
  return settings.value.supported_languages.reduce((acc, lang) => {
    acc[lang] = {
      label: lang === 'en' ? t('LANG_EN') : t('LANG_TRANSLATE'),
      value: lang as IUserLang,
    };
    return acc;
  }, {} as ILanguages);
});

const { execute: handleChangeLanguage } = useAsync({
  async fn() {
    if (lang.value === user.value?.lang) {
      emits('close');
      openDialog('welcome_hunter');
      return;
    }
    Loading.show();
    LocalStorage.set('lang', lang.value);
    await USER.changeLang(lang.value);
    storeUser.updateUser({ lang: lang.value });
    locale.value = lang.value;
    Loading.hide();
    emits('close');
    openDialog('welcome_hunter');
  },
});
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        shape="square"
        variant="secondary"
        @click="
          emits('close');
          openDialog('welcome_hunter');
        "
      >
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('LANG_TITLE')"></div>
    </template>
    <div class="flex justify-center items-center">
      <Expansion
        v-model="expand"
        group="language"
        class="language my-5 !w-[250px]"
      >
        <template v-slot:header>
          <div class="column flex-nowrap flex-1">
            <div
              class="text-[8px] transition-all"
              :class="{
                'mb-2': expand,
              }"
              v-html="t('SETTING_LANGUAGE')"
            ></div>
            <div
              class="text-sm"
              :class="{
                'text-[#00e0ff] font-bold': expand,
              }"
            >
              {{ languageData[lang].label }}
            </div>
          </div>
        </template>
        <q-card style="background: transparent">
          <div
            @click="
              lang = l.value;
              expand = false;
            "
            class="text-sm mx-4 mb-3 pt-2"
            style="border-top: 0.5px solid #00e0ff"
            v-for="l in Object.values(languageData).filter(
              (l) => l.value !== lang
            )"
            :key="l.value"
            v-html="l.label"
          ></div>
        </q-card>
      </Expansion>
      <Button :label="t('LANG_BUTTON_CONFIRM')" @click="handleChangeLanguage" />
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.language {
  background: #04081d;
  border-radius: 10px;
}
</style>
