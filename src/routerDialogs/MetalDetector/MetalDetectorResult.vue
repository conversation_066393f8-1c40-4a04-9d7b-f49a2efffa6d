<script lang="ts" setup>
import { useAsync, useMetalDetector, useTick } from '@composables';
import { MAP } from '@repositories';
import { useUserStore } from '@stores';
import type { IMetalDetectorScanResult } from '@types';
import dayjs from 'dayjs';

interface Emits {
  (event: 'extended'): void;
}

interface Props {
  data: IMetalDetectorScanResult;
  itemId: string;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { crystals } = storeToRefs(storeUser);
const { t } = useI18n();
const { handleBackToMain } = useMetalDetector();
const { openDialog } = useMicroRoute();
const { now } = useTick();

let closeTimeInterval: NodeJS.Timeout;

const closeTime = ref(5);

const isExpired = computed(() => {
  return (
    props.data?.extend_offer_end_at &&
    dayjs(props.data?.extend_offer_end_at).isValid() &&
    dayjs(props.data?.extend_offer_end_at).isBefore(now.value)
  );
});

watch(isExpired, (val) => {
  if (val) handleBackToMain();
});

const { execute: handleExtendOffer, loading } = useAsync({
  async fn(item_id: string) {
    if (crystals.value < Number(props.data.extend_offer_price.price)) {
      openDialog('insufficient_crystals');
      return;
    }
    clearInterval(closeTimeInterval);
    const { data } = await MAP.extendMetalDetector(item_id);
    await storeUser.fetchUser();

    return data;
  },
  onSuccess(data) {
    if (!data) return;
    storeUser.metalDetectorItemId = data.item_id;
    emits('extended');
  },
});

watch(closeTime, (val) => {
  if (val <= 0) {
    clearInterval(closeTimeInterval);
    handleBackToMain();
  }
});

onMounted(async () => {
  await nextTick();
  closeTimeInterval = setInterval(() => {
    if (closeTime.value > 0) closeTime.value -= 1;
  }, 1000);
});

onBeforeUnmount(() => {
  clearInterval(closeTimeInterval);
});
</script>
<template>
  <Dialog hide-close @close="handleBackToMain">
    <template #header>
      <div v-html="t('METAL_DETECTOR_RESULT_HEADER')"></div>
    </template>
    <div class="text-center">
      <div
        class="text-sm mb-5"
        v-html="t('METAL_DETECTOR_RESULT_DESC_1')"
      ></div>
      <Button
        class="!w-[220px] mb-5"
        :title="t('METAL_DETECTOR_RESULT_BTN')"
        :old-amount="
          data.extend_offer_price.beacon
            ? data.extend_offer_price.base_price
            : undefined
        "
        resource-type="dbs_crystal"
        :amount="data.extend_offer_price.price"
        :loading="loading"
        @click="handleExtendOffer(itemId)"
      />
      <div
        class="text-sm"
        v-html="
          t('METAL_DETECTOR_RESULT_DESC_2', {
            TIME: closeTime,
          })
        "
      ></div>
    </div>
  </Dialog>
</template>
