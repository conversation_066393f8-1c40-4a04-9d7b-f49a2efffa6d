<script lang="ts" setup>
import { SlideIllustration } from '@components';
import { useUserStore } from '@stores';

const storeUser = useUserStore();

const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();

function goToMetalDetector() {
  closeDialog('metal_detector_welcome');
  openDialog('metal_detector_warning_popup');
}

onMounted(() => {
  storeUser.updateOnboarding('first_metal_detector');
});
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('METAL_DETECTOR_TITLE')"></div>
    </template>
    <template #icon-center>
      <Icon
        class="absolute -top-16 left-1/2 -translate-x-1/2 pointer-events-none"
        name="metal-detector"
        :size="85"
      />
    </template>
    <div class="relative text-center">
      <div class="text-sm mb-2" v-html="t('METAL_DETECTOR_DESC')"></div>
      <div
        class="frame-discount-powerup text-lg font-bold mb-5"
        v-html="t('METAL_DETECTOR_DESC_1')"
      ></div>
      <SlideIllustration
        class="mb-5"
        :illustrations="
          [
            'metal_detector_1',
            'metal_detector_2',
            'metal_detector_3',
            'metal_detector_4',
            'metal_detector_5',
            'metal_detector_6',
            'metal_detector_7',
          ].map((img) => ({
            name: img,
            type: 'png',
          }))
        "
      />
      <div class="text-sm mb-5" v-html="t('METAL_DETECTOR_DESC_2')"></div>
      <Button :label="t('METAL_DETECTOR_BTN_GO')" @click="goToMetalDetector" />
    </div>
  </Dialog>
</template>
