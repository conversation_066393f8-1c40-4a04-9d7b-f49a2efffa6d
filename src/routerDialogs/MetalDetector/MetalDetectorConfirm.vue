<script lang="ts" setup>
import { useMetalDetector } from '@composables';

const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();
const {
  hasBeacon,
  price,
  basePrice,
  hasPowerUpItem,
  handleRequestMetalDetector,
} = useMetalDetector(true);
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        shape="square"
        variant="secondary"
        @click="closeDialog('metal_detector_confirm')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('METAL_DETECTOR_CONFIRM_TITLE')"></div>
    </template>
    <template #icon-center>
      <Icon
        class="absolute -top-16 left-1/2 -translate-x-1/2 pointer-events-none"
        name="metal-detector"
        :size="85"
      />
    </template>
    <div class="relative text-center">
      <div
        class="text-sm mb-5 px-5"
        v-html="t('METAL_DETECTOR_CONFIRM_DESC')"
      ></div>
      <div class="bg-[#091A3B] rounded py-2 px-5 mb-5 mx-5">
        <div
          class="text-sm text-left mb-1"
          v-html="t('METAL_DETECTOR_CONFIRM_DESC_2')"
        ></div>
        <div class="flex gap-2" @click="openDialog('metal_detector_accuracy')">
          <div
            class="text-sm text-link italic"
            v-html="t('METAL_DETECTOR_CONFIRM_DESC_3')"
          ></div>
          <Icon name="question-mark" class="size-5" />
        </div>
      </div>
      <div
        class="text-sm mb-5 px-5 italic"
        v-html="t('METAL_DETECTOR_CONFIRM_DESC_4')"
      ></div>
      <div
        v-if="hasBeacon"
        class="frame-discount-powerup text-sm mb-5 text-center"
        v-html="t('METAL_DETECTOR_CONFIRM_DESC_1')"
      ></div>
      <Button
        :title="t('METAL_DETECTOR_CONFIRM_BTN_ACTIVE')"
        :old-amount="hasBeacon ? basePrice : undefined"
        :amount="price"
        :resource-type="hasPowerUpItem ? 'metal-detector' : 'dbs_crystal'"
        @click="handleRequestMetalDetector"
      />
    </div>
  </Dialog>
</template>
