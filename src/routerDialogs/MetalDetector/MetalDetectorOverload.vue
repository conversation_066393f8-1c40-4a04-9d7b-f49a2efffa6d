<script lang="ts" setup>
import { IMetalDetectorResult } from '@types';

interface Emits {
  (e: 'close', data: Partial<IMetalDetectorResult>): void;
}

interface Props {
  duration: number;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
</script>
<template>
  <Dialog hide-close>
    <template #icon-center>
      <Icon
        name="twemoji_warning"
        class="absolute left-1/2 -translate-x-1/2 -top-12"
        :size="62"
      />
    </template>
    <template #header>
      <div v-html="t('METAL_DETECTOR_WARNING_HEADER')"></div>
    </template>
    <div class="text-center">
      <div
        class="text-sm mb-5"
        v-html="t('METAL_DETECTOR_WARNING_DESC_1')"
      ></div>
      <div
        class="text-sm mb-5 p-2 bg-[#981515]"
        v-html="t('METAL_DETECTOR_WARNING_DESC_2')"
      ></div>
      <Button
        :label="t('METAL_DETECTOR_WARNING_BTN')"
        @click="
          emits('close', {
            duration,
          })
        "
      />
    </div>
  </Dialog>
</template>
