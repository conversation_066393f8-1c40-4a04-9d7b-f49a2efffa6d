<script lang="ts" setup>
const { t } = useI18n();
const { closeDialog } = useMicroRoute();
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        shape="square"
        variant="secondary"
        @click="closeDialog('metal_detector_accuracy')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('METAL_DETECTOR_ACCURACY_TITLE')"></div>
    </template>

    <div class="relative text-center">
      <div
        class="text-sm mb-5"
        v-html="t('METAL_DETECTOR_ACCURACY_DESC')"
      ></div>
      <Icon class="mx-auto w-[245px]" name="metal_detector_accuracy" />
    </div>
  </Dialog>
</template>
