<script lang="ts" setup>
import { useTrackData } from '@composables';

const emits = defineEmits<{
  (e: 'close'): void;
}>();
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

function go() {
  emits('close');
  openDialog('metal_detector_confirm');
}
</script>
<template>
  <Dialog
    @close="
      emits('close');
      track('metal_detector_buttons', {
        screen_type: 'sonar_detector_warning_popup',
        button: 'back',
      });
    "
  >
    <template #header>
      <div v-html="t('SONAR_WARNING_TITLE')"></div>
    </template>

    <template #icon-center>
      <Icon
        class="absolute -top-16 left-1/2 -translate-x-1/2 pointer-events-none"
        name="metal-detector"
        :size="75"
      />
    </template>
    <div class="relative text-center">
      <Icon class="mx-auto mb-5" name="sonar-warning" :size="100" />
      <div class="text-sm mb-5" v-html="t('SONAR_DETECTOR_WARNING_DESC')"></div>
      <Button :label="t('SONAR_WARNING_BUTTON_AGREE')" @click="go" />
    </div>
  </Dialog>
</template>
