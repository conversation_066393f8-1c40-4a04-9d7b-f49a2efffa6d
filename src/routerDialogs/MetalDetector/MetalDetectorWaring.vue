<script lang="ts" setup>
const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('METAL_DETECTOR_WARNING_TITLE')"></div>
    </template>

    <template #icon-center>
      <Icon
        class="absolute -top-16 left-1/2 -translate-x-1/2 pointer-events-none"
        name="metal-detector"
        :size="85"
      />
    </template>
    <div class="relative text-center">
      <Icon class="mx-auto mb-5" name="sonar-warning" :size="100" />
      <div class="text-sm mb-5" v-html="t('METAL_DETECTOR_WARNING_DESC')"></div>
      <Button
        :label="t('METAL_DETECTOR_WARNING_BUTTON_AGREE')"
        @click="
          closeDialog('metal_detector_warning');
          openDialog('metal_detector_confirm');
        "
      />
    </div>
  </Dialog>
</template>
