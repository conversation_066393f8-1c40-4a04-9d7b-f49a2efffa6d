<script lang="ts" setup>
import { useGlobal } from '@composables';
import { errorNotify, timeCountDown } from '@helpers';
import { SENTOSA } from '@repositories';
import { useMapStore, useUserStore } from '@stores';
import { IAPIResponseError } from '@types';

const storeUser = useUserStore();
const storeMap = useMapStore();

const { lastLocations } = storeToRefs(storeMap);
const { sentosaIslandBounty, isEnabledGPS } = storeToRefs(storeUser);
const { isInSentosa } = useGlobal();
const { t } = useI18n();

const cdTime = ref(0);

const { openDialog } = useMicroRoute();

const loading = ref(false);
let interval: NodeJS.Timeout;

const unClaimRewards = computed(
  () => sentosaIslandBounty.value?.rewards.filter((r) => !r.claimed) || []
);

const totalTime = computed(() => sentosaIslandBounty.value?.total_time || 0);

const isCheckedIn = computed(() => !!sentosaIslandBounty.value?.started_at);

const isClaimedAll = computed(
  () => sentosaIslandBounty.value?.rewards.every((r) => r.claimed) || false
);

const canClaim = computed(() => {
  const reward = unClaimRewards.value.at(0);
  return reward ? totalTime.value >= reward.required_time : false;
});

const nextTimeBounty = computed(() => {
  const reward = unClaimRewards.value.at(0);
  if (!reward || !isCheckedIn.value) return 0;
  const nextTime = reward.required_time - totalTime.value;
  return nextTime < 0 ? 0 : nextTime;
});

const bountyData = computed(() => sentosaIslandBounty.value?.rewards || []);

function remindGPS() {
  errorNotify({
    message: t('SENTOSA_BOUNTY_ERROR_GPS'),
  });
}

async function checkInOrClaimBounty() {
  try {
    loading.value = true;
    if (!isInSentosa.value) {
      remindGPS();
      return;
    }
    const [lng, lat] = lastLocations.value;
    const { data } = await SENTOSA.checkinIslandBounty(lat, lng);
    await storeUser.fetchSentosaIslandBounty();
    if (data.claimed.crystal > 0) {
      await storeUser.fetchUser();
      openDialog('promo_success', {
        crystal: data.claimed.crystal,
        hiddenButton: true,
        tapAnyWhere: true,
      });
    }
  } catch (error) {
    const { error_message } = error as IAPIResponseError;
    errorNotify({
      message: error_message,
    });
  } finally {
    loading.value = false;
  }
}

watchEffect(() => {
  if (nextTimeBounty.value <= 0 && !isInSentosa.value) {
    storeUser.fetchSentosaIslandBounty();
  }
});

watch(
  nextTimeBounty,
  (value) => {
    if (isInSentosa.value && value) {
      cdTime.value = nextTimeBounty.value;
      if (interval) clearInterval(interval);
      interval = setInterval(() => {
        if (cdTime.value > 0) {
          cdTime.value -= 1000;
        } else {
          clearInterval(interval);
          storeUser.fetchSentosaIslandBounty();
        }
      }, 1000);
    } else {
      clearInterval(interval);
    }
  },
  { immediate: true }
);

onMounted(() => {
  storeUser.fetchSentosaIslandBounty();
});

onUnmounted(() => {
  clearInterval(interval);
});
</script>

<template>
  <Dialog bg-header="bg-island-bounty">
    <template #header>
      <div v-html="t('SENTOSA_BOUNTY_HEADER')"></div>
    </template>
    <div class="text-center">
      <div class="text-sm mb-4" v-html="t('SENTOSA_BOUNTY_DESC')"></div>
      <div class="wrapper gap-2 mb-5">
        <div
          class="bounty-box relative"
          :class="{
            'yellow-box': totalTime >= b.required_time && !b.claimed,
            'border-box':
              isCheckedIn &&
              bountyData
                .filter((b) => b.required_time > totalTime && !b.claimed)
                .at(0)?.required_time === b.required_time,
          }"
          v-for="(b, index) in bountyData"
          :key="index"
        >
          <div
            v-if="b.claimed"
            class="absolute w-full h-full rounded-md bg-[#1E263850] top-0 left-0"
          ></div>
          <div
            class="title font-medium mb-3"
            :class="{
              '!bg-[#04081d]':
                (totalTime >= b.required_time && !b.claimed) ||
                (isCheckedIn &&
                  bountyData
                    .filter((b) => b.required_time > totalTime && !b.claimed)
                    .at(0)?.required_time === b.required_time),
              '!bg-[#04081d50]': b.claimed,
            }"
          >
            {{ index + 1 }} h
          </div>
          <div class="flex flex-nowrap items-center justify-center">
            <template v-if="!b.claimed">
              <Icon name="crystal" :size="20" />
              <div class="text-xl font-bold">{{ b.amount }}</div>
            </template>
            <Icon v-else name="check-bold" :size="20" />
          </div>
        </div>
      </div>
      <Button
        v-if="isClaimedAll"
        :label="t('SENTOSA_BOUNTY_BTN_1')"
        variant="secondary"
        class="!w-[234px]"
        disable
      />
      <Button
        v-else-if="!isEnabledGPS || !isInSentosa"
        :label="t('SENTOSA_BOUNTY_BTN_2')"
        class="!w-[234px]"
        variant="purple"
        @click="remindGPS"
      />
      <Button
        v-else-if="!isCheckedIn"
        label="Check in"
        class="!w-[234px]"
        variant="secondary"
        :loading="loading"
        @click="checkInOrClaimBounty"
      />
      <Button
        v-else-if="canClaim"
        :label="t('SENTOSA_BOUNTY_BTN_3')"
        class="!w-[234px]"
        variant="secondary"
        :loading="loading"
        @click="checkInOrClaimBounty"
      />
      <Button
        v-else
        :label="
          t('SENTOSA_BOUNTY_BTN_4', {
            TIME: timeCountDown(isInSentosa ? cdTime : nextTimeBounty),
          })
        "
        disable
        class="!w-[234px]"
      />
      <div
        class="mt-5"
        v-if="isClaimedAll"
        v-html="t('SENTOSA_BOUNTY_DESC_2')"
      ></div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.wrapper {
  display: grid;
  grid-template-columns: repeat(3, minmax(66px, 66px));
  justify-content: center;
  .bounty-box {
    width: 66px;
    height: 73px;
    padding: 8px 4px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    background: #102a5e;
    .title {
      border-radius: 3px;
      background: rgba(68, 126, 181, 0.52);
    }
    &.yellow-box {
      background: #f59200;
    }
    &.border-box {
      border: 2px solid #f59200;
      background: #091a3b;
    }
  }
}
</style>
