<script lang="ts" setup>
import { useUserStore } from '@stores';
import type { SentosaDailyReward, SentosaRewardType } from '@types';

interface Props {
  type: SentosaRewardType;
  data: SentosaDailyReward;
}

defineProps<Props>();

const { closeDialog } = useMicroRoute();
const { t } = useI18n();

const storeUser = useUserStore();

const { onboarding } = storeToRefs(storeUser);

onMounted(async () => {
  await nextTick();
  if (!onboarding.value?.first_time_claim_crystal_detector)
    await storeUser.updateOnboarding('first_time_claim_crystal_detector');
});
</script>

<template>
  <div
    class="bg-[#00000030] flex flex-col justify-center items-center p-7"
    @click="closeDialog('daily_reward_claimed')"
  >
    <div class="text-sm mb-2.5" v-html="t('SENTOSA_DAILY_CLAIMED_TITLE')"></div>
    <div
      class="text-2xl font-bold mb-6"
      v-html="
        type === 'sentosa_hint'
          ? t('SENTOSA_DAILY_CLAIMED_HINT')
          : t('SENTOSA_DAILY_CLAIMED_CRYSTAL_DETECTOR')
      "
    ></div>
    <template v-if="type === 'sentosa_hint'">
      <div class="bg p-6 text-center mb-6">
        <div
          class="text-sm text-[#481700] mb-2.5"
          v-html="t('SENTOSA_DAILY_CLAIMED_DESC_1')"
        ></div>
        <div
          class="bg-[#BB4C02] px-2.5 py-1.5 border border-[#FDC274] w-max mx-auto mb-2.5 rounded"
          v-html="data.hint.coin_name"
        ></div>
        <div
          class="text-2xl font-bold text-[#481700]"
          v-html="data.hint.content"
        ></div>
      </div>
    </template>
    <template v-else>
      <div class="-mx-16 mb-5">
        <Icon
          :name="
            data.reward_type === 'sentosa_crystal_detector'
              ? 'metal-detector-flare'
              : 'silver-metal-detector-flare'
          "
          class="w-full -my-10"
        />
      </div>
      <div
        class="text-sm text-center mb-5"
        v-html="t('SENTOSA_DAILY_CLAIMED_DESC_2')"
      ></div>
      <template v-if="!onboarding?.first_time_claim_crystal_detector">
        <div class="w-full aspect-video rounded-lg mb-5">
          <Icon name="crystal-detector-gif" type="gif" />
        </div>
      </template>
    </template>
    <div
      class="text-sm italic opacity-70"
      v-html="t('SENTOSA_DAILY_CLAIMED_DESC_3')"
    ></div>
  </div>
</template>
<style lang="scss" scoped>
.bg {
  position: relative;
  background-image: url('imgs/sentosa-daily-bg.png');
  background-size: 100% 100%;
  background-position: center;
  width: 100%;
}
</style>
