<script lang="ts" setup>
import { useDialogStore, useMapStore, useUserStore } from '@stores';
import { DailyRewardItem } from '@routerDialogs';
import { playSFX, useAsync, useTrackData } from '@composables';
import { SENTOSA } from '@repositories';
import { useWindowSize } from '@vueuse/core';
import { errorNotify } from '@helpers';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination } from 'swiper/modules';
import type { IAPIResponseError, SentosaGoldenCoin } from '@types';
import { Swiper as ISwiper } from 'swiper/types';
import 'swiper/css';
import 'swiper/css/pagination';

const storeUser = useUserStore();
const storeMap = useMapStore();
const storeDialog = useDialogStore();

const {
  sentosaDailyRewards,
  crystalsDetector,
  sentosaGoldenCoin,
  isEnabledGPS,
  user,
} = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { openDialog, closeDialog } = useMicroRoute();
const { t } = useI18n();
const { width } = useWindowSize();
const { track } = useTrackData();

const pagination = {
  clickable: true,
  dynamicBullets: true,
};
const modules = [Pagination];

const mySwiper = ref<ISwiper | undefined>();
const slide = ref(0);

function filterDays(index: number, day: number) {
  const dayRanges = [
    [1, 2, 3, 4],
    [8, 9, 10, 11],
    [15, 16, 17],
  ];
  return dayRanges[index]?.includes(day);
}

const flatDailyRewards = computed(() => sentosaDailyRewards.value.flat());

const currentDay = computed(() => {
  const date = new Date();
  return date.getDate();
});

const day = computed(() => {
  const rewards = flatDailyRewards.value.filter((d) => !d.claimed_at);
  return rewards.at(0)?.day || 1;
});

const checkedIn = computed(() => {
  const rewards = flatDailyRewards.value.filter((d) => d.claimed_at);
  const lastReward = rewards.at(-1);
  if (!lastReward) return false;
  const lastDate = new Date(lastReward.claimed_at);
  return lastDate.getDate() === currentDay.value;
});

function onSwiper(swiper: ISwiper) {
  mySwiper.value = swiper;
}

const { execute: dailyCheckin, loading } = useAsync({
  fn: async () => {
    if (day.value === 5 && !user.value?.mobile_number) {
      errorNotify({
        message: t('SENTOSA_REMINDER_LOGIN'),
      });
      return;
    }

    const [lng, lat] = lastLocations.value;
    const { data } = await SENTOSA.checkin({
      lng,
      lat,
    });
    return data;
  },
  onSuccess: async (data) => {
    if (!data) return;
    track('sentosa_daily_rewards_receive', {
      sentosa_daily_reward: data.reward_type,
      sentosa_daily_day: data.day,
    });
    await storeUser.fetchSentosaDailyRewards();
    await storeUser.fetchSentosaGoldenCoin();
    await storeUser.fetchUser();
    if (data.reward_type === 'crystal')
      openDialog('promo_success', {
        crystal: data.amount,
      });
    else
      openDialog('daily_reward_claimed', {
        type: data.reward_type,
        data,
      });
  },
  onError: (error: IAPIResponseError) => {
    const { error_message } = error;
    switch (error_message) {
      case 'not_in_sentosa':
        errorNotify({
          message: t('SENTOSA_CHECKIN_ERROR_LOCATION'),
        });
        break;
      default:
        errorNotify({
          message: error_message,
        });
        break;
    }
  },
});

function openHint(coin: SentosaGoldenCoin) {
  switch (coin.status) {
    case 'found':
      errorNotify({
        message: t('SENTOSA_NO_HINTS_ERROR'),
      });
      break;
    case 'verifying':
    case 'forfeited':
      errorNotify({
        message: t('SENTOSA_COIN_NO_LONGER_ERROR'),
      });
      break;
    default:
      openDialog('crystal_coin_hints', {
        coin,
        sentosaGoldenCoin,
      });
      break;
  }
}

function openCrystalDetector() {
  track('crystal_detector_open');
  closeDialog('sentosa_daily_reward');
  // if (!onboarding.value?.first_crystal_detector)
  //   openDialog('crystal_detector_welcome');
  // else openDialog('crystal_detector_warning_popup');
  openDialog('crystal_detector_welcome');
}

function onClose() {
  if (!checkedIn.value && !isEnabledGPS.value)
    errorNotify({
      message: t('SENTOSA_REMINDER_GPS'),
      timeout: 1000 * 60 * 5,
    });
  closeDialog('sentosa_daily_reward');
}

onMounted(async () => {
  storeDialog.showedSentosaDailyReward = true;
  track('sentosa_daily_rewards_pop');
  await Promise.all([
    storeUser.fetchSentosaDailyRewards(),
    storeUser.fetchSentosaGoldenCoin(),
  ]);

  const currentSlide = sentosaDailyRewards.value.findIndex((weeks) =>
    weeks.some((d) => d.day === day.value)
  );

  slide.value = currentSlide !== -1 ? currentSlide : 0;
});
</script>
<template>
  <Dialog @close="onClose">
    <template #header>
      <div v-html="t('SENTOSA_DAILY_REWARD_HEADER')"></div>
    </template>
    <div class="text-center">
      <div
        class="px-5 mb-5 text-sm"
        v-html="t('SENTOSA_DAILY_REWARD_TITLE')"
      ></div>

      <q-carousel
        animated
        v-model="slide"
        infinite
        swipeable
        class="h-[unset] bg-transparent text-[#141414] w-full mb-10"
      >
        <template v-for="(weeks, index) in sentosaDailyRewards" :key="index">
          <q-carousel-slide :name="index" class="bg !px-3 !pt-4">
            <div
              class="relative z-10 flex flex-col justify-between w-full h-full gap-5 px-2 flex-nowrap"
            >
              <div class="flex items-center justify-around">
                <DailyRewardItem
                  v-for="d in weeks.filter((d) => filterDays(index, d.day))"
                  :key="d.day"
                  :item="d"
                  :day="day"
                />
              </div>
              <div
                class="flex justify-center"
                :class="{
                  'gap-10':
                    weeks.filter((d) => !filterDays(index, d.day)).length === 2,
                  'gap-5':
                    weeks.filter((d) => !filterDays(index, d.day)).length === 3,
                }"
              >
                <DailyRewardItem
                  v-for="d in weeks.filter((d) => !filterDays(index, d.day))"
                  :key="d.day"
                  :item="d"
                  :day="day"
                  reverse
                />
              </div>
            </div>
          </q-carousel-slide>
        </template>
      </q-carousel>
      <div
        class="relative flex items-center justify-center gap-3"
        v-if="sentosaDailyRewards.length > 1"
        :class="{
          'mb-10': sentosaDailyRewards.length > 1,
          'mb-5': sentosaDailyRewards.length === 1,
        }"
      >
        <div
          class="bg-white rounded-full opacity-50 size-2"
          :class="{
            '!opacity-100 !bg-[#00e0ff] w-4': slide === i - 1,
          }"
          v-for="i in sentosaDailyRewards.length"
          :key="i"
          @click="slide = i - 1"
        ></div>
        <div class="absolute flex items-center justify-between w-full">
          <div
            @click="
              playSFX('button');
              slide = slide <= 0 ? sentosaDailyRewards.length - 1 : slide - 1;
            "
            class="w-[64px] h-[46px] bg-contain"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
            }"
          ></div>
          <div
            @click="
              playSFX('button');
              slide = slide >= sentosaDailyRewards.length - 1 ? 0 : slide + 1;
            "
            class="w-[64px] h-[46px] bg-contain rotate-180"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
            }"
          ></div>
        </div>
      </div>
      <Button
        v-if="!checkedIn || storeMap?.devTools?.dailyRewardMode"
        :label="t('SENTOSA_DAILY_REWARD_BTN_CHECKIN')"
        class="!w-[250px]"
        :loading="loading"
        @click="dailyCheckin"
      />
      <q-separator class="my-5 w-[150px] h-0.5 mx-auto bg-white opacity-30" />
      <div
        class="bg-[#320B5B] border-2 border-[#51178C] px-2 py-2.5 rounded-[10px] flex flex-nowrap items-center justify-between gap-3 mb-5"
      >
        <div class="flex items-center gap-3 flex-nowrap">
          <div
            class="relative flex items-center justify-center box size-12"
            style="flex: 0 0 48px"
          >
            <Icon name="sentosa-silver-detector" :size="36" />
            <div
              class="absolute bottom-0 w-full font-bold -translate-x-1/2 left-1/2 text-border tex-sm"
              v-html="
                t('SENTOSA_CRYTALS_DETECTOR_QTY', {
                  QUANTITY: crystalsDetector,
                })
              "
            ></div>
          </div>
          <div class="flex flex-col gap-1 text-left">
            <div
              class="text-sm font-bold"
              v-html="t('SENTOSA_CRYSTAL_DETECTOR')"
            ></div>
            <div class="text-sm">
              <span v-html="t('SENTOSA_CRYSTAL_DETECTOR_DESC')"> </span>
              <!-- <ruby>
                <Icon name="exclamation-mark" class="mb-1 ml-2" />
              </ruby> -->
            </div>
          </div>
        </div>
        <Button
          :label="t('SENTOSA_BTN_USE')"
          variant="secondary"
          size="small"
          :disable="crystalsDetector <= 0"
          @click="openCrystalDetector"
        />
      </div>
      <div
        class="mb-5 text-sm"
        v-html="t('SENTOSA_CRYSTAL_DETECTOR_DESC_1')"
      ></div>
      <Swiper
        :slides-per-view="width / 315"
        :initial-slide="1"
        :pagination="pagination"
        :modules="modules"
        :space-between="20"
        centered-slides
        class="setosa-coin-swiper"
        @swiper="onSwiper"
      >
        <SwiperSlide
          v-for="(coin, index) in sentosaGoldenCoin"
          :key="index"
          v-slot="{ isActive }"
        >
          <div
            @click="
              playSFX('button');
              openHint(coin);
            "
            class="flex items-center justify-between slide-item opacity-50 pointer-events-none"
            :class="{ 'slide-item-active': isActive }"
          >
            <div class="flex items-center gap-3">
              <div class="relative siz-[30px] rounded-full">
                <Icon
                  :name="
                    ['ongoing', 'verifying'].includes(coin.status)
                      ? 'sentosa-coin'
                      : 'sentosa-coin-blur'
                  "
                  :size="30"
                />
              </div>
              <div class="flex flex-col items-start">
                <div
                  class="text-sm"
                  :class="{
                    'opacity-70': ['verifying', 'found', 'forfeited'].includes(
                      coin.status
                    ),
                  }"
                >
                  {{
                    coin.status !== 'scheduled' ||
                    (coin.status === 'scheduled' && coin.hints?.length)
                      ? coin.name
                      : '???'
                  }}
                </div>
                <div class="text-sm">
                  <template v-if="coin.status === 'ongoing'">
                    <span
                      class="opacity-50"
                      v-html="t('SENTOSA_CRYSTAL_DETECTOR_DESC_2')"
                    ></span>
                  </template>
                  <template v-else-if="coin.status === 'scheduled'">
                    <span
                      class="opacity-50"
                      v-html="t('SENTOSA_CRYSTAL_DETECTOR_DESC_3')"
                    ></span>
                  </template>
                  <template v-else>
                    <span class="capitalize" :class="[coin.status]">
                      {{ coin.status }}
                    </span>
                  </template>
                </div>
              </div>
            </div>
            <Icon name="arrow-left" class="rotate-180" v-if="isActive" />
          </div>
        </SwiperSlide>

        <div
          class="absolute z-50 flex justify-between w-full bottom-7"
          v-if="sentosaGoldenCoin.length > 1"
        >
          <div
            @click="
              playSFX('button');
              mySwiper?.slidePrev();
            "
            class="w-[64px] h-[46px] bg-contain"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
              opacity: mySwiper?.activeIndex === 0 ? 0.5 : 1,
              pointerEvents: mySwiper?.activeIndex === 0 ? 'none' : 'auto',
            }"
          ></div>
          <div
            @click="
              playSFX('button');
              mySwiper?.slideNext();
            "
            class="w-[64px] h-[46px] bg-contain rotate-180"
            :style="{
              backgroundImage: `url('/imgs/button/menu_arrow_btn.png')`,
              opacity:
                mySwiper?.activeIndex === sentosaGoldenCoin.length - 1
                  ? 0.5
                  : 1,
              pointerEvents:
                mySwiper?.activeIndex === sentosaGoldenCoin.length - 1
                  ? 'none'
                  : 'auto',
            }"
          ></div>
        </div>
      </Swiper>
      <div
        class="px-5 text-sm italic"
        v-html="t('SENTOSA_CRYSTAL_DETECTOR_DESC_4')"
      ></div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.bg {
  position: relative;
  background-image: url('imgs/sentosa-daily-bg.png');
  background-size: 100% 100%;
  background-position: center;
  width: 100%;
  &::before {
    content: '';
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 245px;
    height: 65px;
    background-image: url('imgs/line-reward.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}

.box {
  border-radius: 6px;
  border: 1px solid rgba(215, 27, 232, 0);
  background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.5) 0%,
      rgba(0, 0, 0, 0.5) 100%
    ),
    linear-gradient(90deg, #7147cd 0%, #d834cf 100%);
}

.text-border {
  text-shadow: rgb(65, 6, 60) 2px 0px 0px,
    rgb(65, 6, 60) 1.75517px 0.958851px 0px,
    rgb(65, 6, 60) 1.0806px 1.68294px 0px,
    rgb(65, 6, 60) 0.141474px 1.99499px 0px,
    rgb(65, 6, 60) -0.832294px 1.81859px 0px,
    rgb(65, 6, 60) -1.60229px 1.19694px 0px,
    rgb(65, 6, 60) -1.97998px 0.28224px 0px,
    rgb(65, 6, 60) -1.87291px -0.701566px 0px,
    rgb(65, 6, 60) -1.30729px -1.5136px 0px,
    rgb(65, 6, 60) -0.421592px -1.95506px 0px,
    rgb(65, 6, 60) 0.567324px -1.91785px 0px,
    rgb(65, 6, 60) 1.41734px -1.41108px 0px,
    rgb(65, 6, 60) 1.92034px -0.558831px 0px;
}
.found {
  color: #12ec6d;
}
.verifying {
  color: #c536a5;
}
.forfeited {
  color: #9f1515;
}
.scheduled {
  color: #38cfe5;
}
</style>
<style lang="scss">
.setosa-coin-swiper {
  height: 150px;
  .slide-item {
    border-radius: 4px;
    background: #091a3c;
    padding: 8px 10px;
    opacity: 0.6;
    &.slide-item-active {
      border: 1px solid #38cfe5;
      box-shadow: 0px 0px 7px 0px #11d1f9, 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
      opacity: 1;
    }
  }
  .swiper-pagination {
    top: 55px;
    display: flex;
    align-items: center;
    width: 80px !important;
    overflow: visible;
    .swiper-pagination-bullet-active {
      background-color: #00e0ff !important;
      width: 15px !important;
      border-radius: 4px;
    }
    .swiper-pagination-bullet {
      background-color: #ffffff50;
    }
  }
}
</style>
