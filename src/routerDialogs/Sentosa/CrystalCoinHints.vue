<script lang="ts" setup>
import { SentosaGoldenCoin } from '@types';

interface Emits {
  (e: 'close'): void;
}

interface Props {
  coin: SentosaGoldenCoin;
  sentosaGoldenCoin: SentosaGoldenCoin[];
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();

const selectedCoin = ref(props.coin._id);

const activeCoins = computed(() =>
  props.sentosaGoldenCoin
    .filter((coin) => ['ongoing', 'scheduled'].includes(coin.status))
    .map((coin) => ({
      ...coin,
      name:
        coin.status !== 'scheduled' ||
        (coin.status === 'scheduled' && coin.hints?.length)
          ? coin.name
          : '???',
    }))
);

const currentCoin = computed(() =>
  props.sentosaGoldenCoin.find((coin) => coin._id === selectedCoin.value)
);

const hints = computed(() => {
  if (!currentCoin.value?.hints?.length) return [];
  return currentCoin.value.hints.sort((h1, h2) => {
    let point1 = 0;
    let point2 = 0;
    if (h1.unlocked_at) point1 += 10;
    if (h2.unlocked_at) point2 += 10;
    if (!h1.is_social_media) point1 += 1;
    if (!h2.is_social_media) point2 += 1;
    return point2 - point1;
  });
});
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        class="absolute -top-2 -left-2"
        shape="square"
        variant="secondary"
        @click="emits('close')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('CRYSTAL_COIN_HINTS_HEADER')"></div>
    </template>

    <q-select
      class="q-dark-select !mb-5"
      v-model="selectedCoin"
      :options="activeCoins"
      option-label="name"
      option-value="_id"
      options-selected-class="coin-selected"
      borderless
      behavior="menu"
      dense
      popup-content-class="dark-popup"
      emit-value
      map-options
      label="Coin"
    >
    </q-select>
    <div
      class="text-base text-center italic mb-5"
      v-if="currentCoin && currentCoin.status === 'scheduled'"
      v-html="t('CRYSTAL_COIN_HINTS_DESC_4')"
    ></div>

    <!-- <div
      class="text-base text-center italic mb-5 underline"
      v-if="currentCoin && currentCoin.status === 'ongoing'"
      v-html="'Tap here to see coin info'"
      @click="
        closeAllDialog();
        openDialog('crystal_coin_info');
      "
    ></div> -->

    <div class="flex flex-col gap-5">
      <template v-for="(h, index) in hints" :key="h._id">
        <div
          class="flex flex-col gap-2 bg-[#091A3C] p-3 rounded"
          :class="{
            'exclusive-hint': h.content && !h.is_social_media,
            'social-media-hint': h.content && h.is_social_media,
            'locked-hint ': !h.content,
          }"
        >
          <div class="flex items-center gap-1">
            <Icon
              :name="h.is_social_media ? 'social-media-hint' : 'exclusive-hint'"
            />
            <div
              class="text-sm opacity-50"
              v-html="
                h.is_social_media
                  ? t('CRYSTAL_COIN_HINTS_DESC_2', { ORDER: index + 1 })
                  : t('CRYSTAL_COIN_HINTS_DESC_1', { ORDER: index + 1 })
              "
            ></div>
          </div>
          <div class="text-base font-medium ml-1">
            <span v-if="h.content" v-html="h.content"></span>
            <span
              v-else
              class="opacity-50"
              v-html="t('CRYSTAL_COIN_HINTS_DESC_3')"
            >
            </span>
          </div>
        </div>
      </template>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.exclusive-hint {
  border: 2px solid #38cfe5;
  box-shadow: 0px 0px 7px 0px #11d1f9;
}

.social-media-hint {
  border: 2px solid #fc5ac9;
  box-shadow: 0px 0px 7px 0px #fc78d6;
}

.locked-hint {
  border: 2px solid rgba(56, 207, 229, 0.5);
}
</style>
<style lang="scss">
.q-dark-select {
  background-color: #04081d;
  height: 44px;
  border-radius: 10px;
  padding-left: 16px;
  margin: 0 10px;
  .q-field__native {
    color: white;
  }
  .q-field__label {
    z-index: 9999;
    color: white;
    opacity: 0.5;
  }
  .q-field__append {
    color: white;
  }
}
.dark-popup {
  max-height: 200px !important;
  width: calc(100% - 108px) !important;
  background: #04081d;
  color: #ffffff;
  margin: 5px 0 !important;
  left: 50% !important;
  transform: translateX(-50%);
  .q-item__section {
    .q-img--menu {
      margin-top: 8px;
    }
  }

  .q-item--active {
    color: #00e0ff;
    font-weight: bold;
  }
}
</style>
