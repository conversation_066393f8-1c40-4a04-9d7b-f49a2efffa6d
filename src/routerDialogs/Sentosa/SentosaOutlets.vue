<script lang="ts" setup>
import { useMapStore } from '@stores';

const storeMap = useMapStore();
const { mapIcons } = storeToRefs(storeMap);

const sentosaOutlets = computed(() => {
  return mapIcons.value
    .filter((icon) => icon.brand_unique_id === 'sentosa')
    .map((icon) => icon.display);
});
</script>
<template>
  <Dialog>
    <template #header>
      <div>Participating merchants</div>
    </template>
    <ul class="px-5">
      <li
        class="text-sm font-bold list-disc"
        v-for="(ul, index) in sentosaOutlets"
        :key="index"
      >
        <div class="text-sm">{{ ul.name }}</div>
        <ul class="px-5">
          <li class="text-sm list-disc">
            <div class="text-sm">{{ ul.address }}</div>
          </li>
        </ul>
      </li>
    </ul>
  </Dialog>
</template>
