<script lang="ts" setup>
import { useGlobal } from '@composables';
import { errorNotify } from '@helpers';
import { SENTOSA, USER } from '@repositories';
import { useMapStore, useUserStore } from '@stores';
import distance from '@turf/distance';
import { point } from '@turf/helpers';
import { IAPIResponseError, IBeachStationHint } from '@types';
import { Loading } from 'quasar';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeMap = useMapStore();
const { sentosaBeachCount, settings, isEnabledGPS } = storeToRefs(storeUser);
const { currentState, countdownContext, isInSentosaBeachStation } = useGlobal();
const { lastLocations, silverCoins } = storeToRefs(storeMap);
const { openDialog } = useMicroRoute();
const { t } = useI18n();

const hints = ref<IBeachStationHint>();
const hintErrMsg = ref('');
const endHint = ref(false);

const currentStateRef = computed(() => {
  if (endHint.value) return 5;
  return currentState.value;
});

const mappedHints = computed(() => {
  if (!hints.value) return [];

  return Object.keys(hints.value).map((key) => {
    const coin = silverCoins.value.find(
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      (c) => c.coin_id === hints.value![key]?.coin_unique_id
    );

    return {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      ...hints.value![key],
      coin,
    };
  });
});

const distanceToSentosaBeachStation = computed(() => {
  const sentosaRadius = 120;

  const d =
    distance(point(lastLocations.value), point([103.817962, 1.251829]), {
      units: 'meters',
    }) - sentosaRadius;
  const normalized = Math.max(d, 0);
  return {
    text:
      normalized < 1000
        ? `${normalized.toFixed(2)}m`
        : `${(normalized / 1000).toFixed(2)}km`,
    meters: normalized,
  };
});

const countRequired = computed(() => {
  return settings.value?.sentosa.beach_station_hint_2_required || 500;
});

async function openAnn() {
  Loading.show();
  const { data } = await USER.getAnn();
  const notification = data.find(
    (n) =>
      n.setting.unique_id === 'sentosa_beach_station_event' ||
      n.body.some((b) => b.data?.key === 'ANN_CONTENT_SILVERHINTDROP')
  );
  Loading.hide();
  openDialog('special_event_announcement', {
    notification,
  });
}

async function handleReceiveHint() {
  if (currentStateRef.value === 4) {
    try {
      Loading.show();
      await SENTOSA.claimHintStation();
      await getHints();
    } finally {
      Loading.hide();
    }
  }
}

async function getHints() {
  try {
    Loading.show();
    hintErrMsg.value = '';
    const { data } = await SENTOSA.sentosaBeachHint();
    hints.value = data;
    endHint.value = true;
  } catch (error) {
    const { error_message } = error as IAPIResponseError;
    hintErrMsg.value = error_message;
  } finally {
    Loading.hide();
  }
}

watch(
  currentStateRef,
  async () => {
    await getHints();
  },
  {
    immediate: true,
  }
);

onMounted(async () => {
  await nextTick();
  if (!isEnabledGPS.value && currentStateRef.value < 5)
    errorNotify({
      message: t('SENTOSA_SILVER_HINT_DROP_REMIND_GPS'),
      timeout: 0,
    });
});
</script>
<template>
  <div class="px-5 overflow-hidden py-14">
    <div class="w-full h-full bg-[#983A08] rounded-xl relative flex flex-col">
      <Button
        class="absolute z-20 -top-3 -right-3"
        flat
        @click="emits('close')"
        shape="square"
        size="small"
      >
        <Icon name="cross" :size="16" />
      </Button>
      <div
        class="absolute top-0 left-0 z-10 flex flex-col items-center w-full px-4 py-3 header rounded-t-xl"
      >
        <Icon name="sov/logo/sentosa-logo" :size="210" class="mb-5" />
        <div
          class="mb-1 text-2xl text-header"
          v-html="t('SENTOSA_SILVER_HINT_DROP_HEADER')"
        ></div>
        <div
          class="text-sm text-[#2F5C70] mb-5"
          v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_1')"
        ></div>
        <div
          v-if="currentStateRef < 5"
          class="py-2 px-1.5 text-center text-xs font-semibold"
          :class="{
            'gps-enabled': isEnabledGPS && isInSentosaBeachStation,
            'gps-error':
              !isEnabledGPS || (isEnabledGPS && !isInSentosaBeachStation),
          }"
        >
          <span
            v-if="isEnabledGPS && isInSentosaBeachStation"
            v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_2')"
          >
          </span>
          <span class="flex items-center gap-1" v-if="!isEnabledGPS">
            <Icon name="warning" :size="10" />
            {{ t('SENTOSA_SILVER_HINT_DROP_DESC_3') }}
          </span>
          <span
            class="flex items-center gap-1"
            v-if="
              isEnabledGPS &&
              !isInSentosaBeachStation &&
              distanceToSentosaBeachStation?.text
            "
          >
            <Icon name="icon_globe" :size="10" />
            {{
              t('SENTOSA_SILVER_HINT_DROP_DESC_4', {
                DISTANCE: distanceToSentosaBeachStation?.text,
              })
            }}
          </span>
        </div>
      </div>
      <div class="pointer-events-none background rounded-t-xl"></div>
      <div class="pointer-events-none middle-ground rounded-b-xl"></div>
      <div class="pointer-events-none far-ground rounded-b-xl"></div>
      <Icon
        name="sentosa-kv/beach-objects"
        class="!w-full absolute top-[40vh] z-10 pointer-events-none"
      />
      <Icon
        name="sentosa-kv/characters"
        class="!w-[85vw] absolute top-[32vh] left-1/2 -translate-x-1/2 z-20 pointer-events-none"
      />
      <Icon
        name="sentosa-kv/foreground-2"
        class="!w-full absolute bottom-0 z-10 rounded-b-xl pointer-events-none"
      />
      <div class="relative z-50 wrapper w-full h-full overflow-y-auto pb-10">
        <div class="mt-10">
          <div
            class="px-4 pb-2 pt-[22.5px] mx-10 text-base font-bold text-center bg relative mb-5 italic"
            :class="{
              'bg-30m': currentStateRef === 3,
              'bg-active': currentStateRef === 4,
              'bg-end': currentStateRef > 4,
            }"
            @click="handleReceiveHint"
          >
            <div
              v-if="
                hintErrMsg === 'not_claimed_yet' &&
                !isInSentosaBeachStation &&
                currentStateRef === 4
              "
              class="absolute top-0 -left-0.5 w-[calc(100%+4px)] h-[calc(100%+8px)] flex items-center justify-center rounded-md"
              :style="{
                background: 'rgba(0, 0, 0, 0.50)',
              }"
            ></div>
            <span v-if="[1, 2, 3].includes(currentStateRef)">
              {{ countdownContext?.manualCountdown?.days }}d
              {{ countdownContext?.manualCountdown?.hours }}h
              {{ countdownContext?.manualCountdown?.minutes }}m
              {{ countdownContext?.manualCountdown?.seconds }}s
            </span>
            <span v-else-if="currentStateRef === 4">
              <span
                v-if="
                  hintErrMsg === 'not_claimed_yet' && !isInSentosaBeachStation
                "
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_5')"
              >
              </span>
              <span
                v-else
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_6')"
                class="leading-[0.8]"
              />
              <div class="text-xs italic">
                {{ countdownContext?.manualCountdown?.minutes }}m
                {{ countdownContext?.manualCountdown?.seconds }}s
              </div>
            </span>
            <span v-else>
              <span
                class="text-xs font-semibold"
                v-if="hintErrMsg === 'not_claimed_yet'"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_7')"
              >
              </span>
              <span
                class="text-xs font-semibold"
                v-else
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_8')"
              ></span>
            </span>
            <div
              class="text-sm font-extrabold py-1 px-2.5 absolute left-1/2 -translate-x-1/2 -top-2 w-max"
              :style="{
                fontStyle: 'normal',
              }"
              :class="{
                'text-drop-cd': [1, 2, 3].includes(currentStateRef),
                'text-drop-end': currentStateRef > 4,
                'text-drop-active': currentStateRef === 4,
              }"
            >
              <span
                v-if="[1, 2, 3].includes(currentStateRef)"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_9')"
              >
              </span>
              <span
                v-else-if="currentStateRef === 4"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_10')"
              ></span>
              <span
                v-else
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_11')"
              ></span>
            </div>
          </div>

          <div
            v-if="
              currentStateRef <= 4 ||
              (currentStateRef === 5 && hintErrMsg === 'not_claimed_yet')
            "
            class="flex justify-center gap-2.5 mb-2 flex-nowrap"
          >
            <div class="drop-box">
              <div
                class="text-lg leading-[1]"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_12')"
              ></div>
              <div
                class="color-golden text-[30px] leading-[0.9]"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_13')"
              ></div>
              <div
                class="text-xs leading-[1]"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_14')"
              ></div>
            </div>
            <div
              class="drop-box"
              :class="{
                'drop-box-2':
                  currentStateRef === 5 && hintErrMsg === 'not_claimed_yet',
                glow: isInSentosaBeachStation && currentStateRef < 5,
              }"
            >
              <div
                v-if="currentStateRef === 5 && hintErrMsg === 'not_claimed_yet'"
                class="drop-box-overlay"
              ></div>
              <div
                class="color-golden text-[30px] leading-[0.9]"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_15')"
              ></div>
              <div
                class="text-xs leading-[1]"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_16')"
              ></div>
            </div>
            <div
              class="relative drop-box drop-box-2"
              :class="{
                glow: sentosaBeachCount >= countRequired && currentStateRef < 5,
              }"
            >
              <div
                class="drop-box-overlay"
                v-if="
                  sentosaBeachCount < countRequired ||
                  (currentStateRef === 5 && hintErrMsg === 'not_claimed_yet')
                "
              ></div>
              <div
                class="color-golden text-[30px] leading-[0.9]"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_17')"
              ></div>
              <div
                class="text-xs leading-[1]"
                v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_18')"
              ></div>
              <div
                class="progress text-[10px] absolute -bottom-[8px] flex justify-center items-center"
              >
                <div
                  class="progress-full"
                  :style="{
                    maxWidth: '100%',
                    width: `${(sentosaBeachCount / countRequired) * 100}%`,
                  }"
                ></div>
                <span class="-mt-0.5 z-20 relative">
                  {{
                    sentosaBeachCount >= countRequired
                      ? countRequired
                      : sentosaBeachCount
                  }}/{{ countRequired }}</span
                >
              </div>
            </div>
          </div>

          <div v-else class="flex flex-col gap-3 px-7">
            <template v-for="(h, index) in mappedHints" :key="index">
              <div
                class="relative flex flex-col gap-2 p-3 rounded exclusive-hint hint"
                :class="{
                  'hint-unlocked': !!h.coin,
                }"
              >
                <div
                  class="absolute top-0 left-0 w-full h-full hint-overlay"
                  v-if="h.coin && h.coin?.status === 'found'"
                ></div>
                <Icon
                  v-if="h.coin && h.coin?.status === 'found'"
                  name="hint-found-banner"
                  :size="95"
                  class="absolute -top-2 -right-2"
                />
                <div class="flex items-center gap-1">
                  <Icon name="exclusive-hint" />
                  <div
                    class="text-sm opacity-50"
                    v-html="
                      t('SENTOSA_SILVER_HINT_DROP_DESC_19', {
                        ORDER: index + 1,
                      })
                    "
                  ></div>
                </div>
                <div class="ml-1 text-base font-medium">
                  <span v-if="h.content" v-html="h.content"></span>
                  <span
                    v-else
                    class="opacity-50"
                    v-html="t('SENTOSA_SILVER_HINT_DROP_DESC_20')"
                  >
                  </span>
                </div>
              </div>
            </template>
          </div>
          <template
            v-if="
              currentStateRef < 5 ||
              (currentStateRef === 5 && hintErrMsg === 'not_claimed_yet')
            "
          >
            <div
              class="text-center text-[0.8em] text-[#464646] font-bold px-5 mb-2"
              v-html="
                t(
                  currentStateRef === 5
                    ? 'SENTOSA_SILVER_HINT_DROP_DESC_22'
                    : 'SENTOSA_SILVER_HINT_DROP_DESC_21'
                )
              "
            ></div>
            <div class="text-center">
              <Button
                :label="t('SENTOSA_SILVER_HINT_DROP_BTN')"
                class="!w-[210px]"
                @click="openAnn"
              />
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.wrapper {
  padding: 0 8px 20px;
  margin-top: 80%;
  @media screen and (min-width: 375px) {
    margin-top: 60%;
  }
  @media screen and (min-width: 380px) {
    margin-top: 110%;
  }
}
.header {
  background: linear-gradient(
    180deg,
    #f59200 23.5%,
    rgba(245, 146, 0, 0) 58.5%
  );
  .text-header {
    font-size: 26px;
    font-style: normal;
    font-weight: 800;
    line-height: 22px;
    -webkit-text-stroke-width: 4px;
    -webkit-text-stroke-color: transparent;
    color: #ffffff;
    background: linear-gradient(180deg, #5cc1cf 0%, #6d6bd0 100%);
    -webkit-background-clip: text;
    background-clip: text;
    display: inline-block;
    text-shadow: 0px 1px 4px rgba(0, 0, 0, 0.9);
  }
}

.middle-ground {
  width: 100%;
  height: 70vh;
  background: url('/imgs/sentosa-kv/middle-ground-2.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 5;
}

.background {
  width: 100%;
  height: 50%;
  background: url('/imgs/sentosa-kv/background.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.far-ground {
  width: 100%;
  height: 35vh;
  background: url('/imgs/sentosa-kv/far-ground.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: 14.5vh;
  left: 0;
  z-index: 2;
}

.bg {
  border-radius: 6px;
  background: rgba(9, 9, 9, 0.5);
}

.bg-30m {
  border-radius: 6px;
  background: rgba(67, 16, 12, 0.5);
  box-shadow: 0px 0px 21.9px 0px #ff2d2d inset;
}

.bg-end {
  border-radius: 6px;
  background: rgba(70, 6, 6, 0.75);
  box-shadow: 0px 0px 21.9px 0px rgba(0, 0, 0, 0.25) inset;
}

.bg-active {
  border-radius: 6px;
  border: 2px solid #a6c5ff;
  background: linear-gradient(261deg, #341c7c 1.58%, #1a0950 60.32%);
  box-shadow: 0px 6px 0px 0px #5915cf;
}

.text-drop-cd {
  border-radius: 6px;
  background: #6d6bd0;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

.text-drop-end {
  border-radius: 6px;
  background: #e8554b;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

.text-drop-active {
  border-radius: 6px;
  background: #893bff;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

.drop-box {
  position: relative;
  padding: 4px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-weight: 800;
  width: 90px;
  height: 100px;
  border-radius: 6px;
  background: #6d6bd0;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}
.drop-box-2 {
  border: 2px solid #fff;
  background: #6d6bd0;
}

.drop-box-overlay {
  z-index: 99;
  position: absolute;
  top: -2px;
  left: -2px;
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.5);
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

.progress {
  position: relative;
  width: 73px;
  height: 12px;
  border-radius: 15px;
  border: 1px solid #413f9e;
  background: #1a195a;
}

.progress-full {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 15px;
  border: 1px solid #a08052;
  background: #f59200;
  transition: width 0.5s ease-in;
}

.gps-enabled {
  border-radius: 6px;
  background: #2e7d32;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

.gps-error {
  border-radius: 6px;
  background: #d42f2f;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

.hint {
  border-radius: 4px;
  border: 2px solid rgba(56, 207, 229, 0.5);
  background: #091a3c;
}

.hint-unlocked {
  border-radius: 4px;
  border: 2px solid #38cfe5;
  background: #091a3c;
  box-shadow: 0px 0px 6.424px 0px #11d1f9;
}

.hint-overlay {
  border-radius: 4px;
  border: 2px solid rgba(56, 207, 229, 0.5);
  background: rgba(0, 0, 0, 0.75);
}

.glow {
  box-shadow: 0px 0px 8px 0px #17ffc4;
  border: 2px solid #fff !important;
}
</style>
