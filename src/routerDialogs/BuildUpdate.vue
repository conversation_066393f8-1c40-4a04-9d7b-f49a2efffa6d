<script lang="ts" setup>
import { useTrackData } from '@composables';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { track } = useTrackData();

function handleRefresh() {
  location.reload();
  track('game_refresh_popup', {
    action: 'refresh_game',
  });
  emits('close');
}
</script>

<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="t('OLDBUILD_HEADING')"></div>
    </template>

    <div class="text-center">
      <div class="text-sm mb-5" v-html="t('OLDBUILD_TEXT')"></div>
      <Button @click="handleRefresh" :label="t('OLDBUILD_BUTTON_OK')" />
    </div>
  </Dialog>
</template>
