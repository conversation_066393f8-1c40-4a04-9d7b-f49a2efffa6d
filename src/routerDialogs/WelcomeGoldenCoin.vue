<script lang="ts" setup>
import { useClick, useTick, useTrackData } from '@composables';
import { errorNotify, getSocials, timeCountDown, tryShare2 } from '@helpers';
import { useDialogStore, useUserStore } from '@stores';

interface Props {
  type?: 'default' | 'intro';
}

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();
withDefaults(defineProps<Props>(), {
  type: 'default',
});

const storeUser = useUserStore();
const storeDialog = useDialogStore();

const { settings, goldenCoinDropped } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog, closeDialog, push } = useMicroRoute();
const { track } = useTrackData();
const { now } = useTick();

const nextEmilatedAt = computed(() => {
  const time = settings.value?.next_eliminate_at
    ? +new Date(settings.value?.next_eliminate_at)
    : 0;
  return timeCountDown(time - now.value);
});

useClick('goFoundCoin', () => {
  track('golden_coin_welcome', {
    action: 'golden_coin_welcome_found',
  });
  closeDialog('welcome_golden_coin');
  openDialog('enter_serial_number');
});

async function handleShare() {
  const res = await tryShare2({
    text: t('GOLDEN_COIN_SHARE_MESSAGE', {
      URL: process.env.APP_END_POINT,
    }),
  });
  track('goldencoin_share', {
    status: res.status,
  });
  if (res.status === 'not_supported') {
    errorNotify({
      message: t('SHARE_NOT_SUPPORT'),
    });
  }
}

onBeforeUnmount(() => {
  storeDialog.welcomeGoldenDroppedTicked = true;
});

onMounted(() => {
  LocalStorage.set('welcomeGoldenShowAt', new Date().toISOString());
});
</script>
<template>
  <Dialog
    :hide-close="type === 'intro'"
    @close="
      track('golden_coin_welcome', {
        action: 'golden_coin_welcome_close',
      });
      emits('close');
    "
  >
    <template #btnTopLeft>
      <Button shape="square" variant="secondary" @click="handleShare">
        <Icon name="share" />
      </Button>
    </template>
    <template #header>
      <div
        v-html="
          type === 'default'
            ? t('WELCOME_GOLDEN_COIN_TITLE')
            : t('WELCOME_GOLDEN_COIN_TITLE_1')
        "
      ></div>
    </template>
    <div class="relative text-center">
      <div class="silver-coin">
        <div class="absolute top-10 left-1/2 -translate-x-1/2 w-full">
          <div
            class="text-sm mb-1"
            v-html="t('WELCOME_GOLDEN_COIN_DESC')"
          ></div>
          <div
            class="text-2xl font-bold mb-8"
            v-html="t('WELCOME_GOLDEN_COIN_PRIZE')"
          ></div>
        </div>
        <Icon class="mt-14" name="golden-coin" :size="140" />
      </div>

      <template v-if="type === 'intro'">
        <div class="-mt-10">
          <Button
            class="mb-5 mx-auto !w-[230px]"
            :label="t('WELCOME_GOLDEN_COIN_BUTTON_1')"
            @click="closeDialog('welcome_golden_coin')"
          />
          <div class="text-sm" v-html="t('WELCOME_GOLDEN_COIN_DESC_2')"></div>
        </div>
      </template>

      <template v-if="type === 'default'">
        <div
          class="bg-[#091a3b] rounded p-3 mb-5 -mt-10"
          v-html="
            !goldenCoinDropped
              ? t('WELCOME_GOLDEN_COIN_DESC_1')
              : t('GOLDENCOINS_TOBEFOUND', {
                  NUMBER: settings?.grids_per_elimination,
                  NEXT_ELIMINATED: nextEmilatedAt,
                })
          "
        ></div>
        <div class="flex flex-wrap sm:flex-nowrap gap-2 mb-5">
          <a
            class="flex-1 w-full"
            href="https://www.instagram.com/sqkiimouse"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Button
              class="!w-full"
              variant="purple"
              size="max-content"
              :label="t('WELCOME_GOLDEN_COIN_BUTTON_2')"
              @click="
                track('golden_coin_welcome', {
                  action: 'golden_coin_welcome_open_instagram',
                })
              "
            />
          </a>

          <Button
            class="flex-1 w-full"
            size="max-content"
            :label="t('WELCOME_GOLDEN_COIN_BUTTON')"
            @click="
              track('golden_coin_welcome', {
                action: 'golden_coin_welcome_gethints',
              });
              closeDialog('welcome_golden_coin');
              push('shop');
            "
          />
        </div>
        <div class="text-sm mb-5" v-html="t('WELCOME_GOLDEN_COIN_FOUND')"></div>
        <div class="mb-5 text-sm" v-html="t('WELCOME_GOLDEN_COIN_CTA')"></div>
        <div class="flex items-center justify-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
            @click="
              track('golden_coin_welcome', {
                action: 'golden_coin_welcome_social',
                link,
                type: icon,
              })
            "
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </template>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
  margin-top: -20px;
}
</style>
