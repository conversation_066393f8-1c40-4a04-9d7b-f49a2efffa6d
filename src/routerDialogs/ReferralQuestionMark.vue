<script lang="ts" setup>
interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('REFERRAL_PENDING_DIALOG_TITLE')"></div>
    </template>
    <div class="text-center">
      <Icon name="new_referal_screenshot" class="w-full mb-5" />
      <div class="text-sm" v-html="t('REFERRAL_PENDING_DIALOG_DESC')"></div>
    </div>
  </Dialog>
</template>
