<script lang="ts" setup>
import { useTick } from '@composables';
import { convertTime } from '@helpers';
// import gsap, { Linear, Power2 } from 'gsap';
import dayjs from 'dayjs';
// import TextPlugin from 'gsap/TextPlugin';
// gsap.registerPlugin(TextPlugin);

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

// const tl = gsap.timeline();

const { t } = useI18n();
const { now } = useTick();

const cd = ref(dayjs('2024-11-14T10:00:00+08:00').toISOString());

const timeCountDown = computed(() => {
  if (+new Date(cd.value) < now.value) return null;
  return convertTime(+new Date(cd.value) - now.value);
});

// function animated() {
//   tl.fromTo(
//     '.text',
//     { text: '' },
//     {
//       delay: 0.5,
//       text: `My dear hunters!
//           <br /><br />
//           It has been sooooo much fun seeing all of you hunt for my little coins
//           hehe. You know what...I don't want this to stop!
//           <br /><br />
//           I'm preparing something really special for all of you, so stay
//           tuned!!!
//           <br /><br />
//           Love, Sqkii <svg
//             class="absolute left-[90px] bottom-2.5"
//             xmlns="http://www.w3.org/2000/svg"
//             width="12"
//             height="10"
//             viewBox="0 0 12 10"
//             fill="none"
//           >
//             <path
//               d="M4.96191 9.32859C4.73191 9.44859 4.46191 9.40859 4.28191 9.22859C3.70191 8.64859 2.43191 7.26859 1.66191 5.52859C0.751912 3.46859 0.361912 0.668593 2.91191 0.158593C5.46191 -0.351407 5.95191 2.97859 5.95191 2.97859C5.95191 2.97859 7.26191 0.0885875 9.62191 0.918587C11.7219 1.64859 11.6319 4.53859 9.38191 6.55859C8.26191 7.55859 5.87191 8.84859 4.95191 9.32859H4.96191Z"
//               fill="#7E31FF"
//             />
//           </svg>`,
//       duration: 5,
//       ease: Linear.easeIn,
//     }
//   )
//     .fromTo(
//       '.brb',
//       { scale: 0 },
//       {
//         scale: 1,
//         duration: 1,
//         ease: Power2.easeIn,
//       }
//     )
//     .fromTo(
//       '.sqkii-doodle',
//       {
//         opacity: 0,
//         x: 10,
//         scale: 0.5,
//       },
//       {
//         opacity: 1,
//         x: 0,
//         scale: 1,
//         duration: 1,
//         ease: Power2.easeIn,
//       },
//       '-=0.5'
//     )
//     .fromTo(
//       '.clock',
//       { opacity: 0, y: 50 },
//       {
//         opacity: 1,
//         y: 0,
//         duration: 1,
//         ease: Power2.easeInOut,
//       },
//       '-=0.5'
//     );
// }

onMounted(async () => {
  await nextTick();
  // animated();
});

onBeforeUnmount(() => {
  // tl.kill();
});
</script>
<template>
  <div class="sqkii-note flex items-center px-7 py-5">
    <div class="notes text-[#7E31FF] relative" ref="noteRef">
      <Button
        class="absolute -top-4 -right-4 z-10"
        flat
        @click="emits('close')"
        shape="square"
        size="small"
      >
        <Icon name="cross" :size="16" />
      </Button>
      <div class="wrapper-paper">
        <div class="text relative">
          My dear hunters!
          <br /><br />
          It has been sooooo much fun seeing all of you hunt for my little coins
          hehe. You know what...I don't want this to stop!
          <br /><br />
          I'm preparing something really special for all of you, so stay
          tuned!!!
          <br /><br />
          Love, Sqkii
          <svg
            class="absolute left-[90px] bottom-2.5"
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="10"
            viewBox="0 0 12 10"
            fill="none"
          >
            <path
              d="M4.96191 9.32859C4.73191 9.44859 4.46191 9.40859 4.28191 9.22859C3.70191 8.64859 2.43191 7.26859 1.66191 5.52859C0.751912 3.46859 0.361912 0.668593 2.91191 0.158593C5.46191 -0.351407 5.95191 2.97859 5.95191 2.97859C5.95191 2.97859 7.26191 0.0885875 9.62191 0.918587C11.7219 1.64859 11.6319 4.53859 9.38191 6.55859C8.26191 7.55859 5.87191 8.84859 4.95191 9.32859H4.96191Z"
              fill="#7E31FF"
            />
          </svg>
        </div>
        <Icon class="brb mt-5" name="brb-post-it" :size="112" />
        <Icon
          class="sqkii-doodle absolute right-14 bottom-[30%]"
          name="sqkii-doodle"
          :size="82"
        />
        <div class="clock">
          <div class="time p-2 flex justify-between text-[#FDBA1E]">
            <div
              class="text-center -mt-2"
              v-if="timeCountDown && +timeCountDown?.days > 0"
            >
              <div class="text-[32px] leading-normal">
                {{ timeCountDown?.days ?? '00' }}
              </div>
              <div class="text-xs">
                {{ t('COUNTDOWN_TIMER_DAYS') }}
              </div>
            </div>
            <div class="text-center -mt-2">
              <div class="text-[32px] leading-normal">
                {{ timeCountDown?.hours ?? '00' }}
              </div>
              <div class="text-xs">
                {{ t('COUNTDOWN_TIMER_HOURS') }}
              </div>
            </div>
            <div class="text-center -mt-2">
              <div class="text-[32px] leading-normal">
                {{ timeCountDown?.minutes ?? '00' }}
              </div>
              <div class="text-xs">
                {{ t('COUNTDOWN_TIMER_MINUTES') }}
              </div>
            </div>
            <div
              class="text-center -mt-2"
              v-if="timeCountDown && +timeCountDown?.days <= 0"
            >
              <div class="text-[32px] leading-normal">
                {{ timeCountDown?.seconds ?? '00' }}
              </div>
              <div class="text-xs">
                {{ t('COUNTDOWN_TIMER_SECONDS') }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.sqkii-note {
  .notes {
    padding: 20px 20px 40px;
    background-image: url(/imgs/note.png);
    width: 70vw;
    min-height: 50vh;
    height: max-content;
    background-size: 100% 100%;
    .wrapper-paper {
      width: 100%;
      height: 100%;
      background-color: #f3eee5;
      background: repeating-linear-gradient(
        #f3eee5,
        #f3eee5 25px,
        #7e31ff80 27px,
        #7e31ff80 28px
      );
      .text {
        font-family: 'Games Studio';
        position: relative;
        top: 5px;
        font-weight: 800;
        font-size: 22px;
        line-height: 28px;
      }
      .clock {
        position: absolute;
        bottom: -50px;
        right: -70px;
        transform: scale(0.8);
        background-image: url(/imgs/clock.png);
        width: 246px;
        height: 183px;
        background-size: 100% 100%;
        padding: 80px 8px 28px 16px;
        @media screen and (min-width: 375px) {
          right: -50px;
          transform: scale(1);
        }
        .time {
          width: 100%;
          height: 100%;
          border-radius: 9px;
          background: linear-gradient(180deg, #6d1075 0%, #1f1341 100%);
          box-shadow: 0px 3px 0px 0px rgba(0, 0, 0, 0.25) inset;
          font-family: 'Silkscreen', sans-serif;
        }
      }
    }
  }
}
</style>
