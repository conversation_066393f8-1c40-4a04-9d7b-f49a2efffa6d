<script lang="ts" setup>
interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();

const STEPS = [
  {
    content: t('ZALO_LINE_1'),
    desc: [t('ZALO_LINE_1_0'), t('ZALO_LINE_1_1')],
  },
  {
    content: t('ZALO_LINE_2'),
    desc: [],
  },
  {
    content: t('ZALO_LINE_3'),
    desc: [],
  },
];
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('ZALO_TITLE')"></div>
    </template>
    <div class="text-center">
      <div class="text-sm mb-5 text-center" v-html="t('ZALO_CONTENT_1')"></div>
      <div class="text-lg font-bold mb-2" v-html="t('ZALO_CONTENT_2')"></div>
      <div class="relative overflow-hidden flex flex-col gap-8">
        <div
          v-for="(step, idx) in STEPS"
          :key="`step_${idx}`"
          class="flex gap-5 flex-nowrap text-left"
        >
          <div class="relative">
            <span class="num_line" />
            <p class="num_step">{{ idx + 1 }}</p>
          </div>

          <div class="flex flex-col gap-3">
            <div class="font-bold text-base" v-html="step.content"></div>
            <div class="flex flex-col gap-2" v-if="!!step.desc.length">
              <div
                v-for="desc in step.desc"
                :key="desc"
                v-html="desc"
                class="font-normal text-sm"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.num_line {
  position: absolute;
  left: 15px;
  bottom: calc(100% - 15px);
  width: 4px;
  height: 100vh;
  background: #1d1e58;
  z-index: -1;
}
.num_step {
  width: 34px;
  height: 34px;
  min-width: 34px;
  min-height: 34px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  color: #000000;
  font-weight: 700;
  justify-content: center;
  position: relative;
  z-index: 2;
  background: linear-gradient(180deg, #38e7d2 0%, #1da1d1 100%);
  &::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    z-index: 3;
    border-radius: 50%;
    border: 1px solid #000000;
  }
}
</style>
