<script lang="ts" setup>
import { RmiOutlet } from '@types';

interface Emits {
  (e: 'close'): void;
}

interface Props {
  src: string;
  data: RmiOutlet;
}

const emits = defineEmits<Emits>();

defineProps<Props>();

const { t } = useI18n();
</script>
<template>
  <div
    class="relative flex flex-col justify-center items-center bg-[#00000075] backdrop-blur-sm p-5"
  >
    <Button
      class="absolute top-2 right-2 z-[99]"
      shape="square"
      size="small"
      @click="emits('close')"
    >
      <Icon name="cross" :size="16" />
    </Button>
    <div class="max-w-full mb-2">
      <MediaRenderer
        :src="src"
        parent-height="100%"
        controls
        autoplay
        playsinline
      />
    </div>
    <div class="text-sm" v-html="t(data.name)"></div>
  </div>
</template>
