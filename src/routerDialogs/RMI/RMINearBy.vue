<script lang="ts" setup>
import { delay } from '@composables';
import { errorNotify } from '@helpers';
import { RMI } from '@repositories';
import { useMapStore, useUserStore } from '@stores';
import { useQuery } from '@tanstack/vue-query';
import distance from '@turf/distance';
import { IAPIResponseError, RmiOutlet } from '@types';
import { Loading } from 'quasar';
import { useFlyTo } from 'vue3-maplibre-gl';

interface Props {
  fromPath: string;
  data: RmiOutlet;
}

const props = defineProps<Props>();

const storeMap = useMapStore();
const storeUser = useUserStore();

const { isEnabledGPS } = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();
const { flyTo } = useFlyTo({
  map: storeMap.mapIns,
});

const OUTLET_CONFIG = {
  icons: {
    poi: 'neaby_merchants',
    normal: 'neaby_hunting_stop',
    mega: 'neaby_hunting_stop',
    landmark: 'neaby_small_merlion',
    merchant_rmi: 'neaby_merchants',
  } as const,

  backgrounds: {
    poi: 'linear-gradient(180deg, #117C83 0%, #052A48 100%)',
    normal: 'linear-gradient(180deg, #124EA9 0%, #0D1B69 100%)',
    mega: 'linear-gradient(180deg, #124EA9 0%, #0D1B69 100%)',
    landmark: 'linear-gradient(180deg, #842B7E 0%, #650F39 100%)',
    merchant_rmi: 'linear-gradient(180deg, #117C83 0%, #052A48 100%)',
  } as const,
} as const;

const rmiOutlets = ref<RmiOutlet[]>([]);

const enhanceOutletWithDistance = (outlet: RmiOutlet) => {
  const distanceInKm = distance(
    lastLocations.value,
    [outlet.location.lng, outlet.location.lat],
    { units: 'kilometers' }
  );

  return {
    ...outlet,
    distance: `~${distanceInKm.toFixed(1)}km away`,
    icon: OUTLET_CONFIG.icons[outlet.type],
    bg: OUTLET_CONFIG.backgrounds[outlet.type],
  };
};

const nearbyOutlets = computed(() => {
  if (!isEnabledGPS.value) return [];

  return rmiOutlets.value
    .filter(
      (outlet) =>
        outlet.type !== 'poi' && outlet.unique_id !== props.data.unique_id
    )
    .map(enhanceOutletWithDistance);
});

async function clickNearbyOutlet(center: [number, number], uniqueId: string) {
  await flyTo({
    center,
    zoom: 18,
    offset: [0, window.screen.availHeight * -0.35],
  });

  const targetPath =
    props.data.type === 'merchant_rmi' ? 'rmi' : 'rmi_landmark';
  const data = await getRmiOutletByUniqueId(uniqueId);

  closeDialog(props.fromPath);
  if (data) {
    await delay(200);
    openDialog(targetPath, { data });
  }
}

async function getRmiOutletByUniqueId(uniqueId: string) {
  try {
    Loading.show();
    const { data } = await RMI.detail(uniqueId);
    return data;
  } catch (error) {
    const { error_message } = error as IAPIResponseError;
    errorNotify({
      message: error_message,
    });
  } finally {
    Loading.hide();
  }
}

useQuery({
  queryKey: ['fetchRMINearBy'],
  queryFn: async () => {
    const response = await RMI.get({
      lat: props.data.location.lat,
      lng: props.data.location.lng,
    });

    rmiOutlets.value = response.data;
    return true;
  },
  enabled: isEnabledGPS,
  retry: 10,
  retryDelay: 1000,
});
</script>
<template>
  <div v-if="!!nearbyOutlets.length">
    <div class="font-bold text-sm mb-4" v-html="t('RMI_NEARBY_TITLE')"></div>
    <div
      class="overflow-x-auto slide-container flex snap-mandatory items-center gap-2.5 flex-nowrap w-full h-[150px]"
    >
      <div
        v-for="(outlet, index) in nearbyOutlets"
        :key="`${outlet.unique_id}_${index}`"
        class="min-w-[100px] max-w-[100px] h-[150px] rounded-[8px]"
        :style="{
          'box-shadow': '0px 4px 7px 0px rgba(19, 2, 33, 0.24)',
          background: outlet.bg,
        }"
        @click="
          clickNearbyOutlet(
            [outlet.location.lng, outlet.location.lat],
            outlet.unique_id
          )
        "
      >
        <Icon :name="`${outlet.icon}`" class="w-full mb-[5px]" />
        <div class="px-[5px]">
          <div
            class="text-xs font-bold mb-0.5 truncate"
            v-html="t(outlet.name)"
          ></div>
          <div class="text-[10px]">{{ outlet.distance }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
