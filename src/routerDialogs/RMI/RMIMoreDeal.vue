<script lang="ts" setup>
import { useRMI } from '@composables';
import { useMapStore } from '@stores';
import distance from '@turf/distance';
import { RmiOutlet } from '@types';
import { Loading } from 'quasar';
import { useFlyTo } from 'vue3-maplibre-gl';
import { RMI } from '@repositories';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeMap = useMapStore();

const { rmiOutlets, lastLocations } = storeToRefs(storeMap);
const { t } = useI18n();
const { currentDay, minReward } = useRMI();
const { openDialog } = useMicroRoute();
const { flyTo } = useFlyTo({
  map: storeMap.mapIns,
});

const filteredRMIOutlets = computed<RmiOutlet[]>(() =>
  rmiOutlets.value
    .filter((outlet) => outlet.type === 'merchant_rmi')
    .map((outlet) => {
      const distanceInMeters = distance(
        lastLocations.value,
        [outlet.location.lng, outlet.location.lat],
        { units: 'meters' }
      );

      return {
        ...outlet,
        distance: Number(distanceInMeters.toFixed(1)),
      };
    })
);

const randomDescription = (outlet: RmiOutlet): string => {
  if (!outlet || !outlet.description.length) return '';
  return outlet.description[
    Math.floor(Math.random() * outlet.description.length)
  ];
};

const handleOpenRMI = async (outlet: RmiOutlet): Promise<void> => {
  try {
    Loading.show();
    const { data } = await RMI.detail(outlet.unique_id);
    await flyTo({
      center: [data.location.lng, data.location.lat],
      zoom: 18,
      offset: [0, window.screen.availHeight * -0.35],
    });
    openDialog('rmi', { data });
  } catch (error) {
    console.error('error', error);
  } finally {
    Loading.hide();
  }
};
</script>
<template>
  <DbsDialog show-back :show-close="false" @back="emits('close')">
    <div
      v-for="outlet in filteredRMIOutlets"
      :key="outlet.unique_id"
      class="outlet-item mb-5 space-y-3"
      @click="handleOpenRMI(outlet)"
    >
      <div class="flex items-center gap-2">
        <div class="text-base font-bold" v-html="t(outlet.name)"></div>
        <Icon type="url" :name="outlet.badge" class="!w-[40px]" />
      </div>
      <div class="flex items-center">
        <div class="flex items-center gap-1">
          <Icon name="dbs_crystal" :size="16" />
          <span
            class="text-sm"
            :class="{
              'line-through text-[#99A0AE]':
                minReward[outlet.brand_unique_id].haveMultiplier,
            }"
          >
            {{ minReward[outlet.brand_unique_id].reward }}
          </span>
          <span
            v-if="minReward[outlet.brand_unique_id].haveMultiplier"
            v-html="minReward[outlet.brand_unique_id].finalReward"
          ></span>
        </div>
        <span class="mt-[-10px] text-xl mx-2">.</span>
        <span class="text-sm">{{ outlet.distance }}m away from you</span>
      </div>
      <div class="text-[#54D6E2]" v-html="t(randomDescription(outlet))"></div>
      <hr class="w-full h-[1px] bg-white opacity-50" />
      <div class="text-sm" v-html="t(outlet.address)"></div>
      <div class="flex items-center">
        <span>Open hours</span>
        <span class="mt-[-10px] text-xl mx-2">.</span>
        <span
          class="text-[#99A0AE]"
          v-html="t(outlet.open_hours[currentDay])"
        ></span>
      </div>
    </div>
  </DbsDialog>
</template>
<style lang="scss" scoped>
.outlet-item {
  border-radius: 10px;
  border: 1px solid rgba(133, 107, 208, 0.4);
  background: rgba(93, 58, 192, 0.3);
  padding: 14px 16px;
}
</style>
