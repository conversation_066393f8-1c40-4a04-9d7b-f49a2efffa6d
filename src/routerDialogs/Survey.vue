<script setup lang="ts">
import { useDialogStore, useUserStore } from '@stores';
import { useAsync } from '@composables';
import { USER } from '@repositories';
import { uniqBy } from 'lodash';
import type { ISurveyAnswer, ISurveyData } from '@types';
import { SurveyQuestion } from '@components';

interface ISurvey {
  data: ISurveyData;
  stage: number;
}

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeDialog = useDialogStore();
const storeUser = useUserStore();

const { currentSeason } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();

const stage = ref(0);
const dataSurvey = ref<ISurveyData[]>([]);
const DATA = computed<ISurveyData[]>(() => [
  // 1
  {
    id: 1,
    q: t('END_SURVEYQUESTION_AGE'),
    sub_q: t('END_SURVEY_DESC_AGE'),
    a: [
      {
        value: t('END_SURVEY_OPTIONS_AGE_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_AGE_2'),
      },
      {
        value: t('END_SURVEY_OPTIONS_AGE_3'),
      },
      {
        value: t('END_SURVEY_OPTIONS_AGE_4'),
      },
      {
        value: t('END_SURVEY_OPTIONS_AGE_5'),
      },
      {
        value: t('END_SURVEY_OPTIONS_AGE_6'),
      },
      {
        value: t('END_SURVEY_OPTIONS_AGE_7'),
      },
      {
        value: t('END_SURVEY_OPTIONS_AGE_8'),
      },
    ],
    selected: '',
    type: 'select',
  },
  // 2
  {
    id: 2,
    q: t('END_SURVEYQUESTION_GENDER'),
    sub_q: t('END_SURVEY_DESC_GENDER'),
    a: [
      {
        value: t('END_SURVEY_OPTIONS_GENDER_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_GENDER_2'),
      },
    ],
    type: 'select',
  },
  // 3
  {
    id: 3,
    q: t('END_SURVEYQUESTION_FINDOUTHTM'),
    sub_q: t('END_SURVEYDESC_FINDOUTHTM'),
    a: [
      {
        title: t('END_SURVEY_OPTIONS_FINDOUTHTM_1'),
        type: 'area',
        value: '',
      },
      {
        title: t('END_SURVEY_OPTIONS_FINDOUTHTM_2'),
        type: 'area',
        value: '',
      },
      {
        value: t('END_SURVEY_OPTIONS_FINDOUTHTM_3'),
      },
      {
        value: t('END_SURVEY_OPTIONS_FINDOUTHTM_4'),
      },
      {
        value: t('END_SURVEY_OPTIONS_FINDOUTHTM_5'),
      },
      {
        value: t('END_SURVEY_OPTIONS_FINDOUTHTM_6'),
      },
      {
        value: t('END_SURVEY_OPTIONS_FINDOUTHTM_7'),
      },
      {
        title: t('END_SURVEY_OPTIONS_FINDOUTHTM_8'),
        value: '',
        type: 'area',
      },
    ],

    multiple: true,
    type: 'select',
  },
  // 4
  {
    id: 4,
    q: t('END_SURVEYQUESTION_WHICH_COIN_HUNT'),
    sub_q: t('END_SURVEY_DESC_WHICH_COIN_HUNT'),
    a: [
      {
        value: t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_2'),
      },
      {
        value: t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_3'),
      },
      {
        value: t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_4'),
      },
    ],
    type: 'select',
    multiple: true,
    condition: {
      next() {
        console.log(dataSurvey.value);
        const hunt4Index = dataSurvey.value.findIndex(
          (item) =>
            item.id === 4 &&
            item.a.some(
              (a) =>
                a.selected &&
                t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_4') === a.value
            )
        );

        const hunt56Index = dataSurvey.value.findIndex(
          (item) =>
            item.id === 4 &&
            item.a.some(
              (a) =>
                a.selected &&
                (a.value === t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_1') ||
                  a.value === t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_2'))
            )
        );

        if (hunt4Index >= 0) return 5;
        if (hunt56Index >= 0) return 6;
        return 9;
      },
    },
  },
  // 5
  {
    id: 5,
    q: t('END_SURVEYQUESTION_NOTHUNT'),
    sub_q: t('END_SURVEYDESC_NOTHUNT'),
    a: [
      {
        value: t('END_SURVEY_OPTIONS_NOTHUNT_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_NOTHUNT_2'),
      },
      {
        value: t('END_SURVEY_OPTIONS_NOTHUNT_3'),
      },
      {
        value: t('END_SURVEY_OPTIONS_NOTHUNT_4'),
      },
      {
        title: t('END_SURVEY_OPTIONS_NOTHUNT_5'),
        value: '',
        type: 'area',
      },
    ],
    multiple: true,
    type: 'select',
    condition: {
      next: () => {
        const hunt56Index = dataSurvey.value.findIndex(
          (item) =>
            item.id === 4 &&
            item.a.some(
              (a) =>
                a.selected &&
                (a.value === t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_1') ||
                  a.value === t('END_SURVEY_OPTIONS_WHICH_COIN_HUNT_2'))
            )
        );
        if (hunt56Index >= 0) return 6;
        return 9;
      },
    },
  },
  // 6
  {
    id: 6,
    q: t('END_SURVEYQUESTION_HUNTING_EXPERIENCE'),
    sub_q: t('END_SURVEYDESC_HUNTING_EXPERIENCE'),
    a: [
      {
        value: 0,
      },
    ],
    min_rate_text: t('END_SURVEY_OPTIONS_HUNTING_EXPERIENCE_RATE_1'),
    max_rate_text: t('END_SURVEY_OPTIONS_HUNTING_EXPERIENCE_RATE_2'),
    type: 'rate',
    condition: {
      next: (currentData) => {
        if (+(currentData?.a[0].value || 0) > 6) return 7;
        return 8;
      },
    },
  },
  // 7
  {
    id: 7,
    q: t('END_SURVEYQUESTION_ENJOY_HTM'),
    sub_q: t('END_SURVEYDESC_ENJOY_HTM'),
    condition: {
      next: () => {
        return 9;
      },
    },
    a: [
      {
        value: t('END_SURVEY_OPTIONS_ENJOY_HTM_1'),
        active: false,
      },
      {
        value: t('END_SURVEY_OPTIONS_ENJOY_HTM_2'),
        active: false,
      },
      {
        value: t('END_SURVEY_OPTIONS_ENJOY_HTM_3'),
        active: false,
      },
      {
        value: t('END_SURVEY_OPTIONS_ENJOY_HTM_4'),
        active: false,
      },
      {
        title: t('END_SURVEY_OPTIONS_ENJOY_HTM_5'),
        value: '',
        type: 'area',
      },
    ],
    type: 'select',
    multiple: true,
  },
  // 8
  {
    id: 8,
    q: t('END_SURVEYQUESTION_NOTENJOY_HTM'),
    sub_q: t('END_SURVEYDESC_NOTENJOY_HTM'),
    a: [
      {
        value: t('END_SURVEY_OPTIONS_NOTENJOY_HTM_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_NOTENJOY_HTM_2'),
      },
      {
        value: t('END_SURVEY_OPTIONS_NOTENJOY_HTM_3'),
      },
      {
        title: t('END_SURVEY_OPTIONS_NOTENJOY_HTM_4'),
        type: 'area',
        value: '',
      },
    ],
    multiple: true,
    type: 'select',
  },
  // 9
  {
    id: 9,
    q: t('END_SURVEYQUESTION_RECC_HTM'),
    sub_q: t('END_SURVEYDESC_RECC_HTM'),
    a: [
      {
        value: 0,
      },
    ],
    min_rate_text: t('END_SURVEY_OPTIONS_RECC_HTM_1'),
    max_rate_text: t('END_SURVEY_OPTIONS_RECC_HTM_2'),
    type: 'rate',
  },
  // 10
  {
    id: 10,
    q: t('END_SURVEYQUESTION_SENTOSA_CHECKIN'),
    sub_q: t('END_SURVEYDESC_SENTOSA_CHECKIN'),
    a: [
      {
        value: 0,
      },
    ],
    min_rate_text: t('END_SURVEY_OPTIONS_SENTOSA_CHECKIN_1'),
    max_rate_text: t('END_SURVEY_OPTIONS_SENTOSA_CHECKIN_2'),
    type: 'rate',
  },
  // 11
  {
    id: 11,
    q: t('END_SURVEYQUESTION_ATTRACTIVE_DISCOVERY'),
    sub_q: t('END_SURVEYDESC_ATTRACTIVE_DISCOVERY'),
    a: [
      {
        value: 0,
      },
    ],
    min_rate_text: t('END_SURVEY_OPTIONS_ATTRACTIVE_DISCOVERY_1'),
    max_rate_text: t('END_SURVEY_OPTIONS_ATTRACTIVE_DISCOVERY_2'),
    type: 'rate',
  },
  // 12
  {
    id: 12,
    q: t('END_SURVEYQUESTION_USEFUL_HINTS'),
    sub_q: t('END_SURVEYDESC_USEFUL_HINTS'),
    a: [
      {
        value: 0,
      },
    ],
    min_rate_text: t('END_SURVEY_OPTIONS_USEFUL_HINTS_1'),
    max_rate_text: t('END_SURVEY_OPTIONS_USEFUL_HINTS_2'),
    type: 'rate',
    condition: {
      next(currentData) {
        if (+(currentData?.a[0].value || 0) > 6) return 14;
        return 13;
      },
    },
  },
  {
    id: 13,
    q: t('END_SURVEYQUESTION_HINTS_FEEDBACK'),
    sub_q: t('END_SURVEYDESC_HINTS_FEEDBACK'),
    a: [
      {
        value: '',
      },
    ],
    type: 'area',
  },
  {
    id: 14,
    q: t('END_SURVEYQUESTION_CRYSTAL_ACTIONS'),
    sub_q: t('END_SURVEYDESC_CRYSTAL_ACTIONS'),
    a: [
      {
        value: t('END_SURVEY_OPTIONS_CRYSTAL_ACTIONS_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_CRYSTAL_ACTIONS_2'),
      },
    ],
    selected: '',
    type: 'select',
    condition: {
      next: (currentData) => {
        const index = currentData.a.findIndex(
          (item: ISurveyAnswer) =>
            item.selected &&
            item.value === t('END_SURVEY_OPTIONS_CRYSTAL_ACTIONS_1')
        );

        if (index > -1) return 15;
        return 16;
      },
    },
  },
  // 15
  {
    id: 15,
    q: t('END_SURVEYQUESTION_WHY_CRYSTAL_ACTIONS'),
    sub_q: t('END_SURVEYDESC_WHY_CRYSTAL_ACTIONS'),
    condition: {
      next: () => {
        return 17;
      },
    },
    a: [
      {
        value: t('END_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_2'),
      },
      {
        value: t('END_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_3'),
      },
      {
        title: t('END_SURVEY_OPTIONS_WHY_CRYSTAL_ACTIONS_4'),
        type: 'area',
        value: '',
      },
    ],
    type: 'select',
    multiple: true,
  },
  // 16
  {
    id: 16,
    q: t('END_SURVEYQUESTION_WHY_NO_CRYSTAL_ACTIONS'),
    sub_q: t('SURVEYDESC_WHY_NO_CRYSTAL_ACTIONS'),
    a: [
      {
        value: t('END_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_2'),
      },
      {
        value: t('END_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_3'),
      },
      {
        title: t('END_SURVEY_OPTIONS_WHY_NO_CRYSTAL_ACTIONS_4'),
        type: 'area',
        value: '',
      },
    ],
    type: 'select',
    multiple: true,
  },
  // 17
  {
    id: 17,
    q: t('END_SURVEYQUESTION_POWERUP_USEFUL'),
    sub_q: t('END_SURVEY_DESC_POWERUP_USEFUL'),
    a: [
      {
        value: t('END_SURVEY_OPTIONS_POWERUP_USEFUL_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_POWERUP_USEFUL_2'),
      },
      {
        value: t('END_SURVEY_OPTIONS_POWERUP_USEFUL_3'),
      },
      {
        value: t('END_SURVEY_OPTIONS_POWERUP_USEFUL_4'),
      },
      {
        value: t('END_SURVEY_OPTIONS_POWERUP_USEFUL_5'),
      },
      // {
      //   value: t('END_SURVEY_OPTIONS_POWERUP_USEFUL_6'),
      // },
    ],
    selected: '',
    type: 'select',
    condition: {
      next: (currentData) => {
        const index = currentData.a.findIndex(
          (item: ISurveyAnswer) =>
            item.selected &&
            item.value === t('END_SURVEY_OPTIONS_POWERUP_USEFUL_5')
        );

        if (index > -1) return 19;
        return 18;
      },
    },
  },
  // 18
  {
    id: 18,
    q: t('END_SURVEYQUESTION_POWERUP_APPEALING'),
    sub_q: t('END_SURVEY_DESC_POWERUP_APPEALING'),
    condition: {
      next: () => {
        return 20;
      },
    },
    a: [
      {
        value: t('END_SURVEY_OPTIONS_POWERUP_APPEALING_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_POWERUP_APPEALING_2'),
      },
      {
        value: t('END_SURVEY_OPTIONS_POWERUP_APPEALING_3'),
      },
      {
        value: t('END_SURVEY_OPTIONS_POWERUP_APPEALING_4'),
      },
      // {
      //   value: t('END_SURVEY_OPTIONS_POWERUP_APPEALING_5'),
      // },
    ],
    selected: '',
    type: 'select',
  },

  // 19
  {
    id: 19,
    q: t('END_SURVEYQUESTION_NOT_USE_POWERUP'),
    sub_q: t('END_SURVEY_DESC_NOT_USE_POWERUP'),
    a: [
      {
        value: t('END_SURVEY_OPTIONS_NOT_USE_POWERUP_1'),
      },
      {
        value: t('END_SURVEY_OPTIONS_NOT_USE_POWERUP_2'),
      },
      {
        value: t('END_SURVEY_OPTIONS_NOT_USE_POWERUP_3'),
      },
      {
        title: t('END_SURVEY_OPTIONS_NOT_USE_POWERUP_4'),
        type: 'area',
        value: '',
      },
    ],
    type: 'select',
    multiple: true,
  },
  {
    id: 20,
    q: t('END_SURVEYQUESTION_OTHER_FEEDBACK'),
    sub_q: t('END_SURVEYDESC_OTHER_FEEDBACK'),
    a: [
      {
        value: '',
      },
    ],
    type: 'area',
  },
]);

function handleSurvey(survey: ISurvey) {
  const idx = dataSurvey.value.findIndex((item) => item.q === survey.data.q);
  if (idx < 0) dataSurvey.value = [...dataSurvey.value, survey.data];
  else dataSurvey.value[idx] = survey.data;
  stage.value = survey.stage;
}

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = dataSurvey.value.map((d) => {
      if (!currentSeason.value) return;
      let answer;
      if (!d.multiple)
        answer = d.a
          .filter((a) => a.selected || (d.type !== 'select' && a.value))
          .map((a) => a.value)[0];
      else answer = d.a.filter((a) => a.selected).map((a) => a.value);
      return {
        id: d.id,
        question: d.q,
        answer,
        season_id: currentSeason.value.id,
      };
    });
    await USER.updateEndSurvey({
      data: uniqBy(data, 'id'),
    });
    const res = await USER.claimEndSurveyReward();
    await storeUser.fetchUser();
    emits('close');
    openDialog('promo_success', {
      crystal: res.data.crystal,
    });
  },
});
</script>

<template>
  <Dialog @close="emits('close')">
    <template #btnTopLeft v-if="stage > 0">
      <Button
        shape="square"
        variant="secondary"
        @click="
          stage--;
          storeDialog.isBack = true;
        "
      >
        <Icon name="arrow-left" />
      </Button>
    </template>

    <template #header>
      <div>{{ t('SURVEY_POPUP_HEADING') }}</div>
    </template>
    <SurveyQuestion
      :data="DATA"
      :stage="stage"
      :loading="loading"
      @survey="handleSurvey"
      @submit="onSubmit"
    />
  </Dialog>
</template>
