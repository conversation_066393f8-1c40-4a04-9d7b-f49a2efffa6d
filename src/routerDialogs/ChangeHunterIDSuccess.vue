<script setup lang="ts">
import { useTrackData } from '@composables';
import gsap, { Linear } from 'gsap';

interface Emits {
  (e: 'close'): void;
}

interface Props {
  hunter_id: string;
  next_fee: number;
}

defineProps<Props>();
const emits = defineEmits<Emits>();

const tl = gsap.timeline();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = useTrackData();

function handleReChange() {
  emits('close');
  openDialog('change_hunter_id');
  track('hunterid_changed', {
    action: 'change_hunterid_withcrystals',
  });
}

function startAnimation() {
  tl.fromTo(
    '.upload_flare',
    {
      scale: 0,
    },
    {
      scale: 1,
      duration: 1,
      delay: 0.5,
    }
  ).fromTo(
    '.upload_flare',
    {
      rotate: 0,
    },
    {
      rotate: 720,
      duration: 20,
      repeat: -1,
      ease: Linear.easeNone,
    },
    '-=0.5'
  );

  gsap.fromTo(
    '.text',
    {
      scale: 0,
    },
    {
      scale: 1,
      duration: 0.5,
      stagger: 0.2,
    }
  );

  gsap.fromTo(
    '.upload_star',
    {
      opacity: 0,
    },
    {
      opacity: 1,
      yoyo: true,
      repeat: -1,
      delay: 1,
      duration: 1,
    }
  );

  const timeout = setTimeout(() => {
    //auto close after 7.5s
    emits('close');
    clearTimeout(timeout);
  }, 7500);
}

onMounted(async () => {
  await nextTick();
  startAnimation();
});

onBeforeUnmount(() => {
  tl?.kill();
  gsap.killTweensOf('.upload_star');
});
</script>
<template>
  <div
    class="fit bg-[#090422] flex flex-nowrap flex-col justify-between items-center py-10"
  >
    <HeaderCrystal class="absolute top-0 right-2" />
    <div class="font-bold text-lg" v-html="t('CHANGEHUNTERID_HEADING')"></div>

    <div
      class="absolute top-1/2 -translate-y-1/2 full-width flex flex-center"
      style="height: 100vw"
    >
      <Icon
        class="upload_flare absolute top-0 left-0 pointer-events-none"
        style="width: 100vw"
        name="upload_flare"
      />
      <Icon
        class="upload_star full-width absolute pointer-events-none top-0 left-0 z-10"
        name="star_frame"
      />
    </div>

    <div class="px-5 text-center">
      <div
        class="text-sm mb-2 text"
        v-html="t('CHANGEHUNTERID_NEWHUNTERID_1')"
      ></div>
      <div
        class="text-2xl font-bold mb-1 text"
        v-html="t('CHANGEHUNTERID_NEWHUNTERID_2')"
      ></div>
      <div class="flex flex-nowrap items-center gap-3 text">
        <div
          class="font-bold whitespace-nowrap"
          style="font-size: 15vw; line-height: 18vw"
        >
          {{ hunter_id }}
        </div>
        <Icon
          name="question-mark"
          :size="20"
          @click="openDialog('dont_like_hunter_id')"
        />
      </div>
    </div>

    <div class="flex flex-col items-center gap-3">
      <div class="text-sm" v-html="t('CHANGEHUNTERID_NEWHUNTERID_4')"></div>
      <Button
        :title="t('CHANGEHUNTERID_BUTTON_CHANGEAGAIN')"
        :amount="next_fee"
        @click="handleReChange"
      />
    </div>
  </div>
</template>
