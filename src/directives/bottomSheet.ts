import { DirectiveBinding } from 'vue';

interface BottomSheetOptions {
  minHeight?: number; // Minimum height in vh units
  maxHeight?: number; // Maximum height in vh units
  initialHeight?: number; // Initial height in vh units
  minSwipeDistance?: number;
  animationDuration?: number;
  onHeightChange?: (height: number) => void;
  resistanceThreshold?: number;
  enableKeyboard?: boolean;
  velocityThreshold?: number;
  dampingFactor?: number;
  snapPoints?: number[]; // Optional snap points for smooth positioning
}

interface VelocityPoint {
  time: number;
  y: number;
}

interface BottomSheetState {
  isDragging: boolean;
  startY: number;
  currentY: number;
  initialHeight: number;
  currentHeight: number; // Current height in vh units
  element: HTMLElement;
  options: Required<BottomSheetOptions>;
  startTime: number;
  velocityTracker: VelocityPoint[];
  animationFrame?: number;
  isAnimating: boolean;
  abortController: AbortController;
}

const defaultOptions: Required<BottomSheetOptions> = {
  minHeight: 20, // 20vh minimum
  maxHeight: 90, // 90vh maximum
  initialHeight: 40, // Start at 40vh
  minSwipeDistance: 50,
  animationDuration: 300,
  onHeightChange: () => {},
  resistanceThreshold: 100,
  enableKeyboard: true,
  velocityThreshold: 0.5,
  dampingFactor: 0.1,
  snapPoints: [], // No snap points by default - free movement
};

const activeSheets = new WeakMap<HTMLElement, BottomSheetState>();

function getVelocity(tracker: VelocityPoint[]): number {
  if (tracker.length < 2) return 0;

  // Use last 3 points for more accurate velocity calculation
  const recentPoints = tracker.slice(-3);
  if (recentPoints.length < 2) return 0;

  const timeSpan =
    recentPoints[recentPoints.length - 1].time - recentPoints[0].time;
  const distance = recentPoints[recentPoints.length - 1].y - recentPoints[0].y;

  return timeSpan > 0 ? distance / timeSpan : 0;
}

function easeOutCubic(t: number): number {
  return 1 - Math.pow(1 - t, 3);
}

function calculateResistance(
  distance: number,
  threshold: number,
  dampingFactor: number
): number {
  if (Math.abs(distance) <= threshold) return distance;

  const sign = distance >= 0 ? 1 : -1;
  const excess = Math.abs(distance) - threshold;
  const resistedExcess =
    Math.log(1 + excess / threshold) * threshold * dampingFactor;

  return sign * (threshold + resistedExcess);
}

function animateToHeight(
  state: BottomSheetState,
  targetHeight: number
): Promise<void> {
  return new Promise((resolve) => {
    const { element, options } = state;
    const animationDuration = options.animationDuration;
    const onHeightChange = options.onHeightChange;

    // Cancel any existing animation
    if (state.animationFrame) {
      cancelAnimationFrame(state.animationFrame);
    }

    state.isAnimating = true;
    const startHeight = parseFloat(element.style.height || '0');
    const startTime = performance.now();

    function animate(currentTime: number) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / animationDuration, 1);
      const easedProgress = easeOutCubic(progress);

      const currentHeight =
        startHeight + (targetHeight - startHeight) * easedProgress;
      element.style.height = `${currentHeight}vh`;
      state.currentHeight = currentHeight;

      if (progress < 1) {
        state.animationFrame = requestAnimationFrame(animate);
      } else {
        state.isAnimating = false;
        state.currentHeight = targetHeight;
        onHeightChange(targetHeight);
        resolve();
      }
    }

    state.animationFrame = requestAnimationFrame(animate);
  });
}

function findNearestSnapPoint(
  currentHeight: number,
  snapPoints: number[]
): number {
  if (snapPoints.length === 0) return currentHeight;

  let nearestPoint = snapPoints[0];
  let nearestDistance = Math.abs(snapPoints[0] - currentHeight);

  for (let i = 1; i < snapPoints.length; i++) {
    const distance = Math.abs(snapPoints[i] - currentHeight);
    if (distance < nearestDistance) {
      nearestDistance = distance;
      nearestPoint = snapPoints[i];
    }
  }

  return nearestPoint;
}

function handleTouchStart(event: TouchEvent) {
  const element = event.currentTarget as HTMLElement;
  const state = activeSheets.get(element);

  if (!state) {
    return;
  }

  if (state.isAnimating) {
    return;
  }

  // Check if touch started on handle element
  const target = event.target as HTMLElement;
  const isHandleTouch = target.closest('.bottom-sheet-handle') !== null;

  if (!isHandleTouch) {
    return;
  }

  state.isDragging = true;
  state.startY = event.touches[0].clientY;
  state.currentY = state.startY;
  state.initialHeight = parseFloat(
    element.style.height || `${state.options.initialHeight}`
  );
  state.startTime = performance.now();
  state.velocityTracker = [{ time: state.startTime, y: state.startY }];

  // Disable transitions and text selection during drag
  element.style.transition = 'none';
  element.style.userSelect = 'none';

  // Prevent default to avoid scrolling issues
  event.preventDefault();
}

function handleTouchMove(event: TouchEvent) {
  const element = event.currentTarget as HTMLElement;
  const state = activeSheets.get(element);

  if (!state || !state.isDragging) {
    return;
  }

  const currentY = event.touches[0].clientY;
  const deltaY = state.startY - currentY; // Positive when swiping up
  const currentTime = performance.now();

  // Update velocity tracker with throttling
  const lastPoint = state.velocityTracker[state.velocityTracker.length - 1];
  if (currentTime - lastPoint.time > 10) {
    // Throttle to max 100fps
    state.velocityTracker.push({ time: currentTime, y: currentY });
    if (state.velocityTracker.length > 5) {
      state.velocityTracker.shift(); // Keep only last 5 points
    }
  }

  // Calculate new height with resistance
  const resistanceThreshold = state.options.resistanceThreshold;
  const dampingFactor = state.options.dampingFactor;
  const minHeight = state.options.minHeight;
  const maxHeight = state.options.maxHeight;

  let newHeight = state.initialHeight + (deltaY / window.innerHeight) * 100;

  // Apply resistance when dragging beyond limits
  if (newHeight < minHeight) {
    const excess = minHeight - newHeight;
    const resistedExcess = calculateResistance(
      excess,
      (resistanceThreshold / window.innerHeight) * 100,
      dampingFactor
    );
    newHeight = minHeight - resistedExcess;
  } else if (newHeight > maxHeight) {
    const excess = newHeight - maxHeight;
    const resistedExcess = calculateResistance(
      excess,
      (resistanceThreshold / window.innerHeight) * 100,
      dampingFactor
    );
    newHeight = maxHeight + resistedExcess;
  }

  element.style.height = `${newHeight}vh`;
  state.currentY = currentY;
  state.currentHeight = newHeight;

  event.preventDefault();
}

function handleTouchEnd(event: TouchEvent) {
  const element = event.currentTarget as HTMLElement;
  const state = activeSheets.get(element);

  if (!state || !state.isDragging) {
    return;
  }

  state.isDragging = false;

  const deltaY = state.startY - state.currentY;
  const velocity = getVelocity(state.velocityTracker);
  const minSwipeDistance = state.options.minSwipeDistance;
  const velocityThreshold = state.options.velocityThreshold;
  const animationDuration = state.options.animationDuration;
  const minHeight = state.options.minHeight;
  const maxHeight = state.options.maxHeight;
  const snapPoints = state.options.snapPoints;

  const currentHeight = parseFloat(element.style.height || '0');
  let targetHeight = currentHeight;

  // Determine target height based on velocity and distance
  if (Math.abs(velocity) > velocityThreshold) {
    // High velocity swipe - move significantly in the direction of velocity
    const velocityFactor = Math.min(Math.abs(velocity) * 0.5, 30); // Scale velocity to reasonable height change
    if (velocity > 0) {
      // Fast swipe up
      targetHeight = Math.min(currentHeight + velocityFactor, maxHeight);
    } else {
      // Fast swipe down
      targetHeight = Math.max(currentHeight - velocityFactor, minHeight);
    }
  } else if (Math.abs(deltaY) > minSwipeDistance) {
    // Slow but sufficient distance - move in direction of swipe
    const heightChange = (Math.abs(deltaY) / window.innerHeight) * 50; // Convert to reasonable vh change
    if (deltaY > 0) {
      // Swipe up
      targetHeight = Math.min(currentHeight + heightChange, maxHeight);
    } else {
      // Swipe down
      targetHeight = Math.max(currentHeight - heightChange, minHeight);
    }
  }

  // Snap to nearest snap point if defined
  if (snapPoints.length > 0) {
    targetHeight = findNearestSnapPoint(targetHeight, snapPoints);
  }

  // Ensure target height is within bounds
  targetHeight = Math.max(minHeight, Math.min(maxHeight, targetHeight));

  // Restore transitions and animate to target height
  element.style.transition = `height ${animationDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`;
  element.style.userSelect = '';

  animateToHeight(state, targetHeight);

  event.preventDefault();
}

function handleKeydown(event: KeyboardEvent) {
  const element = event.currentTarget as HTMLElement;
  const state = activeSheets.get(element);

  if (!state || !state.options.enableKeyboard) {
    return;
  }

  const minHeight = state.options.minHeight;
  const maxHeight = state.options.maxHeight;
  const currentHeight = state.currentHeight;
  const step = 10; // 10vh step for keyboard navigation
  let targetHeight = currentHeight;

  switch (event.key) {
    case 'ArrowUp':
      targetHeight = Math.min(currentHeight + step, maxHeight);
      break;
    case 'ArrowDown':
      targetHeight = Math.max(currentHeight - step, minHeight);
      break;
    case 'Home':
      targetHeight = maxHeight;
      break;
    case 'End':
      targetHeight = minHeight;
      break;
    default:
      return;
  }

  if (targetHeight !== currentHeight) {
    event.preventDefault();
    animateToHeight(state, targetHeight);
  }
}

function setupBottomSheet(element: HTMLElement, options: BottomSheetOptions) {
  const mergedOptions: Required<BottomSheetOptions> = {
    ...defaultOptions,
    ...options,
  };

  // Clean up existing state if any
  const existingState = activeSheets.get(element);
  if (existingState) {
    destroyBottomSheet(element);
  }

  // Initialize element styles
  element.style.cssText += `
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: ${mergedOptions.initialHeight}vh;
    z-index: ${element.style.zIndex || '1000'};
    overflow: hidden;
    touch-action: none;
  `;

  // Make element focusable for keyboard navigation
  if (mergedOptions.enableKeyboard) {
    element.tabIndex = 0;
    element.setAttribute('role', 'dialog');
    element.setAttribute('aria-label', 'Bottom sheet');
  }

  // Create abort controller for cleanup
  const abortController = new AbortController();
  const signal = abortController.signal;

  const state: BottomSheetState = {
    isDragging: false,
    startY: 0,
    currentY: 0,
    initialHeight: 0,
    currentHeight: mergedOptions.initialHeight,
    element,
    options: mergedOptions,
    startTime: 0,
    velocityTracker: [],
    isAnimating: false,
    abortController,
  };

  activeSheets.set(element, state);

  // Add event listeners with abort signal for automatic cleanup
  element.addEventListener('touchstart', handleTouchStart, {
    passive: false,
    signal,
  });
  element.addEventListener('touchmove', handleTouchMove, {
    passive: false,
    signal,
  });
  element.addEventListener('touchend', handleTouchEnd, {
    passive: false,
    signal,
  });
  element.addEventListener('touchcancel', handleTouchEnd, {
    passive: false,
    signal,
  });

  if (mergedOptions.enableKeyboard) {
    element.addEventListener('keydown', handleKeydown, { signal });
  }

  // Call initial height change callback
  mergedOptions.onHeightChange(mergedOptions.initialHeight);
}

function destroyBottomSheet(element: HTMLElement) {
  const state = activeSheets.get(element);
  if (!state) return;

  // Cancel any ongoing animation
  if (state.animationFrame) {
    cancelAnimationFrame(state.animationFrame);
  }

  // Abort all event listeners
  state.abortController.abort();

  // Clean up state
  activeSheets.delete(element);
}

export const vBottomSheet = {
  mounted(el: HTMLElement, binding: DirectiveBinding<BottomSheetOptions>) {
    setupBottomSheet(el, binding.value || {});
  },

  updated(el: HTMLElement, binding: DirectiveBinding<BottomSheetOptions>) {
    // Only recreate if options actually changed
    const currentState = activeSheets.get(el);
    if (
      currentState &&
      JSON.stringify(currentState.options) !==
        JSON.stringify({ ...defaultOptions, ...binding.value })
    ) {
      setupBottomSheet(el, binding.value || {});
    }
  },

  unmounted(el: HTMLElement) {
    destroyBottomSheet(el);
  },
};

export function setBottomSheetHeight(
  element: HTMLElement,
  height: number
): Promise<void> | null {
  const state = activeSheets.get(element);
  const minHeight = state?.options.minHeight || defaultOptions.minHeight;
  const maxHeight = state?.options.maxHeight || defaultOptions.maxHeight;

  if (state && height >= minHeight && height <= maxHeight) {
    return animateToHeight(state, height);
  }

  return null;
}

export function getBottomSheetHeight(element: HTMLElement): number | null {
  const state = activeSheets.get(element);
  return state ? state.currentHeight : null;
}

export function getBottomSheetState(
  element: HTMLElement
): BottomSheetState | null {
  return activeSheets.get(element) || null;
}

export function useBottomSheetHeight(
  element: HTMLElement | (() => HTMLElement)
) {
  const getElement = typeof element === 'function' ? element : () => element;

  return {
    setHeight: (height: number) => setBottomSheetHeight(getElement(), height),
    getHeight: () => getBottomSheetHeight(getElement()),
    getState: () => getBottomSheetState(getElement()),
  };
}
