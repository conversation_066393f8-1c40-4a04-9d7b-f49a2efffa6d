import { useTrackData } from '@composables';

interface HTMLElementWithClickHandler extends HTMLElement {
  __vueClickHandler__?: (event: Event) => void;
}

interface PayloadValues {
  type: string;
  data?: Record<string, unknown>;
}

interface Binding {
  value: PayloadValues;
}

const { track } = useTrackData();

const payload = ref<PayloadValues>();

// Don't using. Still some issues

const Track = {
  name: 'track',

  mounted(el: HTMLElementWithClickHandler, binding: Binding) {
    payload.value = binding.value;
    el.__vueClickHandler__ = async () => {
      if (!payload.value) return;
      await track(payload.value.type, payload.value.data);
    };

    el.addEventListener('click', el.__vueClickHandler__);
  },

  updated(_el: HTMLElementWithClickHandler, binding: Binding) {
    payload.value = binding.value;
  },

  beforeUnmount(el: HTMLElementWithClickHandler) {
    if (el.__vueClickHandler__) {
      el.removeEventListener('click', el.__vueClickHandler__);
      delete el.__vueClickHandler__;
    }
  },
};

export default Track;
