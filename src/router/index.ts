import { route } from 'quasar/wrappers';
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router';

import routes from './routes';
import { useTrackData } from '@composables';
import { USER } from '@repositories';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default route(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history'
    ? createWebHistory
    : createWebHashHistory;

  const router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  router.beforeEach(async (to, _from, next) => {
    const { track } = useTrackData();

    const PATH = [
      'ooh',
      'ooh4s',
      'ooh6s',
      'bsd',
      'sqfb',
      'sqig',
      'sqtt',
      'sqtele',
      'dbs',
      'fortune',
      'DBSHTM',
      'prdbs',
      'interstatial',
    ];

    const path = to.redirectedFrom?.path?.replace('/', '') || '';
    if (PATH.includes(path))
      USER.trackData('custom-path', {
        path: path,
      });

    track('page_visit');
    track('version', {
      version: process.env.BUILD_VERSION,
    });

    if (to.path !== '/') return next('/');

    return next();
  });

  return router;
});
