import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('../pages/IndexPage.vue'),
      },
      {
        path: '/terms',
        component: () => import('../pages/IndexPage.vue'),
      },
      {
        path: '/vote',
        component: () => import('../pages/IndexPage.vue'),
      },
      {
        path: '/found',
        component: () => import('../pages/IndexPage.vue'),
      },
      {
        path: '/sonar',
        component: () => import('../pages/IndexPage.vue'),
      },
      {
        path: '/sqkii-voucher/set-password/:recover_token',
        component: () => import('../pages/IndexPage.vue'),
      },
      {
        path: '/sqkii-voucher/top-up',
        component: () => import('../pages/IndexPage.vue'),
      },
    ],
  },
];

export default routes;
