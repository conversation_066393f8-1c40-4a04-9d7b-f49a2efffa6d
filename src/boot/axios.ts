import Base64 from 'crypto-js/enc-base64';
import HmacSHA256 from 'crypto-js/hmac-sha256';
import axios, { AxiosError } from 'axios';
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import type { IAPIResponseError, IAPIVouchersResponseError } from '@types';

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $axios: AxiosInstance;
  }
}

enum ContentType {
  'application/json' = 'application/json',
}

interface AxiosConfigHeaders {
  'Content-Type': ContentType;
  ctime?: number;
  sig?: string;
  authorization?: string;
  'x-sign-key'?: string;
  country?: string;
}

// Default axios instance
const api = axios.create({
  baseURL: `${process.env.APP_END_POINT}/${process.env.APP_API_PREFIX}`,
  withCredentials: true,
});

api.interceptors.request.use(
  function (config: AxiosRequestConfig) {
    setHeaders(config);
    deleteEmptyValues(config);
    return config as
      | InternalAxiosRequestConfig<any>
      | Promise<InternalAxiosRequestConfig<any>>;
  },
  function (error) {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    if (response.data.status === 'success') return response.data;
    if (response.data.error_code === 401) {
      LocalStorage.clear();
      location.reload();
    }
    return Promise.reject<IAPIResponseError>(
      response?.data || { data: undefined }
    );
  },
  async (error: AxiosError<IAPIResponseError>) => {
    return Promise.reject(error.response?.data || 'Network Error');
  }
);

// Sqkii Vouchers axios instance
const vouchersAPI = axios.create({
  baseURL: `${process.env.APP_VOUCHERS_END_POINT}/${process.env.APP_VOUCHERS_API_PREFIX}`,
  withCredentials: true,
});

vouchersAPI.interceptors.request.use(
  function (config: AxiosRequestConfig) {
    setVouchersHeaders(config);
    deleteEmptyValues(config);
    return config as
      | InternalAxiosRequestConfig<any>
      | Promise<InternalAxiosRequestConfig<any>>;
  },
  function (error) {
    return Promise.reject(error);
  }
);

vouchersAPI.interceptors.response.use(
  (response) => {
    if (response.data.status === 'success') return response.data;
    if (response.data.error_code === 401) {
      LocalStorage.remove(`${process.env.APP_NAME}_SQKII_VOUCHER_TOKEN`);
    }
    return Promise.reject<IAPIVouchersResponseError>(
      response?.data || { data: undefined }
    );
  },
  async (
    error: AxiosError<IAPIVouchersResponseError & { respone: AxiosResponse }>
  ) => {
    if (error.response?.status === 401) {
      LocalStorage.remove(`${process.env.APP_NAME}_SQKII_VOUCHER_TOKEN`);
    }

    return Promise.reject(error.response?.data || 'Network Error');
  }
);

function deleteEmptyValues(config: AxiosRequestConfig) {
  if (config.data)
    config.data = deleteEmptyValue(config.data as Record<string, unknown>);

  if (config.params)
    config.params = deleteEmptyValue(config.params as Record<string, unknown>);
}

function deleteEmptyValue(data: Record<string, unknown>) {
  Object.keys(data).map((k) => {
    data[k] == void 0 && delete data[k];
  });
  return data;
}

function setHeaders(config: AxiosRequestConfig) {
  const token = LocalStorage.getItem(`${process.env.APP_NAME}_TOKEN`);

  config.headers = {
    ...generateHMACSignature(config),
    ...config.headers,
  };
  if (token)
    (
      config.headers as AxiosConfigHeaders
    ).authorization = `Bearer ${token.toString()}`;
}

function setVouchersHeaders(config: AxiosRequestConfig) {
  const token = LocalStorage.getItem(
    `${process.env.APP_NAME}_SQKII_VOUCHER_TOKEN`
  );

  config.headers = {
    ...generateVouchersHMACSignature(config),
    ...config.headers,
  };
  if (token)
    (
      config.headers as AxiosConfigHeaders
    ).authorization = `Bearer ${token.toString()}`;
}

function generateHMACSignature(config: AxiosRequestConfig) {
  const headers: AxiosConfigHeaders = {
    'Content-Type': ContentType['application/json'],
    ctime: +new Date(),
  };

  let path = config.url || '';
  if (!path.startsWith('/')) path = '/' + path;

  const method = config.method?.toUpperCase() || '';
  const contentType = headers['Content-Type'];
  const ctime = headers['ctime'] || '';

  const stringToSign = `${method}\n${contentType}\n${ctime}\n${path}\n`;

  headers.sig = Base64.stringify(
    HmacSHA256(stringToSign, String(process.env.HMAC_SECRET))
  );

  return headers;
}

function generateVouchersHMACSignature(config: AxiosRequestConfig) {
  const headers: AxiosConfigHeaders = {
    'Content-Type': ContentType['application/json'],
    ctime: +new Date(),
    'x-sign-key': process.env.SIGN_KEY,
    country: 'SG',
    // country: String(
    //   LocalStorage.getItem(`${process.env.APP_NAME}_SQKII_VOUCHER_COUNTRY`) ||
    //     'SG'
    // ),
  };

  let path = config.url || '';
  if (!path.startsWith('/')) path = '/' + path;

  const method = config.method?.toUpperCase() || '';
  const contentType = headers['Content-Type'];
  const ctime = headers['ctime'] || '';
  const country = headers['country'];

  const stringToSign = `${method}\n${contentType}\n${ctime}\n${country}\n${path}\n`;

  headers.sig = Base64.stringify(
    HmacSHA256(stringToSign, String(process.env.VOUCHERS_HMAC_SECRET))
  );

  return headers;
}

export { api, vouchersAPI };
