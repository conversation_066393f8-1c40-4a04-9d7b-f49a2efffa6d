import { boot } from 'quasar/wrappers';
import { createI18n } from 'vue-i18n';
import { getLocalize } from 'src/i18n';
import { IUserLang } from '@types';

export default boot(async ({ app }) => {
  const lang = LocalStorage.getItem('lang') as IUserLang;
  const messages = await getLocalize();

  const i18n = createI18n({
    messages,
    legacy: false,
    warnHtmlMessage: false,
    warnHtmlInMessage: 'off',
    locale: lang || process.env.APP_LANGUAGE_CODE,
    fallbackLocale: 'en',
    missingWarn: false,
  });
  app.use(i18n);
});
