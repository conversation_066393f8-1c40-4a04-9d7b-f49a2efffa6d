import { InventoryItemType } from '@constants';
import type { ICircle, TCoinStatus } from '@types';

export interface IWinnerInfoPayload {
  serial_number: string;
  first_name: string;
  last_name: string;
  mobile_number: string;
  accepted_tac: boolean;
  sent_video: boolean;
  nric: string;
  bank_name: string;
  bank_code: string;
  branch_code: string;
  bank_account_number: string;
  bank_account_name: string;
  currency: string;
  country: string;
  submitted_mailing_proof: boolean;
  reached_minimum_age: boolean;
  parent_first_name: string;
  parent_last_name: string;
  parent_nric: string;
  parent_mobile_number: string;
  fulfillment_date: string;
  fulfillment_type: 'bank' | 'cash';
  prize_amount: string;
  capitastar_account_id: string;
  email: string;
  parent_email: string;
  parent_capitastar_account_id: string;
}

export interface IVerification {
  coin: {
    _id: string;
    serial_number: string;
    coin_id: string;
    status: TCoinStatus;
    coin_number: number;
    brand_unique_id: string;
    reward: string;
    brand_name: string;
    coin_name: string;
    prefix: string;
  };
  submit_info_at: string;
  _id: string;
  type:
    | 'silver_coin'
    | 'golden_coin'
    | 'sentosa_crystal_coin'
    | 'capitaland_geneo';
  coin_id: string;
  coin_number: number;
  brand_unique_id: string;
  brand_name: string;
  reward: string;
  winner_info: IWinnerInfoPayload;
  fromMap: boolean;
  coin_name: string;
  prefix: string;
}

export interface CircleUpdatedData {
  _id: string;
  status?: TCoinStatus;
  nextShrinkAt: string;
  is_smallest_public_circle: boolean;
  freeCircle: {
    radius: number;
    center: {
      lat: number;
      lng: number;
    };
  };
}

export interface IShrinkData {
  freeCircle: ICircle;
  paidCircle: ICircle;
  canUsePowerUp: boolean;
  totalTimeShrank: number;
  totalShrinks: number;
  totalMetersShrank: number;
  bottomMessage: {
    type: 'smaller_public' | 'no_change' | 'smaller_private';
    this_use: number;
    smallest: number;
  };
}

export interface SilverCoinPowerUpSelection {
  type: InventoryItemType;
  total: number;
  title: string;
  canUseWithPowerUp: boolean;
  item_id?: string;
}
