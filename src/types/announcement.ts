export type IMediaTypes =
  | 'media'
  | 'media_custom_size'
  | 'media_with_background';

export type ITextTypes = 'text' | 'text_with_purple_frame';

export type ITriggerAction = 'close';
export type IStatusNotification = 'draft' | 'published' | 'archived';

export interface IHeaderNotification {
  close_able?: boolean;
  icon?: boolean;
  icon_url?: string;
}

export interface IFooterAction {
  id?: string;
  variant?: any;
  data?: {
    key?: string;
    link?: string;
  };
  trigger?: ITriggerAction;
  type: 'button' | 'button_link' | 'checkbox';
}

export interface IFooterNotification {
  bottom_frame?: boolean;
  frame_url?: string;
  actions?: IFooterAction[];
}

export interface IBodyNotification {
  id: string;
  type: 'heading' | 'title' | ITextTypes | IMediaTypes;
  data?: {
    key?: string;
    media_url?: string;
    bg_url?: string;
    media_size?: string;
    frame_url?: string;
  };
}

export type IFrequencyType = 'custom' | 'every_load' | 'once';

export interface ISettingNotification {
  from: string;
  to: string;
  frequencyType: IFrequencyType;
  frequency: {
    interval: number;
    period: number;
  };
  unique_id: string;
}

export interface INotification {
  _id: string;
  should_show: boolean;
  template: string;
  setting: ISettingNotification;
  header: IHeaderNotification;
  body: IBodyNotification[];
  footer: IFooterNotification;
  status: IStatusNotification;
}
