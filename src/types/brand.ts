import type { IMilestones } from '@types';

export type TBrandActionStatus =
  | 'new'
  | 'pending'
  | 'claimed'
  | 'verified'
  | 'rejected';

export interface IGlobalBrandAction {
  brand_actions: IBrandAction[];
  user_brand_actions: IBrandAction[];
  milestones: IMilestones[];
  featured_brand_actions: IFeatured[];
  bonus_brand_actions: IBonus[];
  activating_timed_mission: IActivatingTimeMission;
  timed_missions: ITimeMission[];
  unclaimed_timed_missions: IActivatingTimeMission[];
  skip_mission_price: number;
  timed_mission_cooldown: string;
  pendingMissions?: IActivatingTimeMission[];
}

export interface IBrandHook {
  handleBack: () => void;
  handleAction: (item: IBrandAction) => Promise<void>;
  claimAll: () => Promise<void>;
  showStatus: (item: IBrandAction) => void;
  checkWithFeatured: (item: IBrandAction) => boolean;
  checkWithBonus: (item: IBrandAction) => boolean | undefined;
  calculateReward: (item: IBrandAction) => string | number;
  locked_item: (item: IBrandAction) => IBrandAction | undefined;
  hasMultiplier: (item: IBrandAction) => boolean | undefined;
}

export interface IBonus {
  bonus: number;
  brand_action: string;
  created_at: string;
  disabled: boolean;
  end_at: string;
  start_at: string;
  _id: string;
}

export interface IFeatured {
  brand_action: string;
  created_at: string;
  end_at: string;
  logo: string;
  slot: string;
  start_at: string;
  title: string;
  updated_at: string;
  _id: string;
}
export interface IBrandAction {
  brand_unique_id: string;
  _id: string;
  brand: {
    _id: string;
    name: string;
  };
  brand_action: string;
  button_copy: string;
  header_copy: string;
  outlet_copy?: string;
  outlet_popup_header?: string;
  title: string;
  reward?: Record<string, number>;
  display_reward: string;
  display_reward_first_time: string;
  user_reward?: Record<string, number>;
  seen?: boolean;
  user_ba_id?: string;
  is_first_time?: boolean;
  claimed_at?: string;
  created_at?: string;
  verified_at?: string;
  updated_at?: string;
  bonus?: IBonus;
  bonus_end_at?: string;
  release_date?: string;
  close_date?: string;
  brand_id: string;
  instruction: string;
  instructions: string[];
  verify_type?: 'email' | 'mobile_number';
  rejected_at?: string;
  reject_reason?: string;
  description?: string;
  link?: string;
  outlets?: {
    name: string;
    address: string;
    s_n: string;
  }[];
  malls?: {
    name: string;
    address: string;
    s_n: string;
  }[];
  featured?: {
    start_at: string;
    end_at: string;
    slot: string;
    logo: string;
  };
  type: BrandActionType;
  lock_until?: string;
  status: TBrandActionStatus;
  data: Record<string, any>;
  metadata: Record<string, any>;
  unique_id: string;
  can_perform: boolean;
  ba_unique_id: string;
  show_on_list: boolean;
  group_id: string;
}

export type BrandActionType =
  | 'contest'
  | 'receipt_verification'
  | 'enter_promo_code'
  | 'promo_code'
  | 'sqkii_voucher'
  | 'enter_barcode'
  | 'visit_web'
  | 'survey'
  | 'verify_mobile_number'
  | 'client_verify'
  | 'sharing'
  | 'open_external_link'
  | 'read_spf_message'
  | 'location_based'
  | 'spf_quiz'
  | 'spf_sharing'
  | 'sync_account_api'
  | 'etiqa_insurance'
  | 'lendlease_sync_account'
  | 'tiger_broker_sync_account'
  | 'tiger_broker_register_boss_card'
  | 'tiger_broker_deposit'
  | 'tada_ride'
  | 'use_sqkii_voucher'
  | 'open_sentosa_app'
  | 'scan_qrcode'
  | 'head_over_sentosa'
  | 'capitaland_10min_park'
  | 'capitaland_visit_amenities'
  | 'capitaland_10min_fairprice'
  | 'capitaland_visit_fnb'
  | 'capitaland_visit_health'
  | 'capitaland_visit_type'
  | 'capitaland_10min_mrt'
  | 'capitaland_10min_fnb'
  | 'capitaland_10min_health'
  | 'capitaland_10min_park'
  | 'capitaland_visit_amenities';

export interface IActivatingTimeMission {
  _id: string;
  user: string;
  mission_unique_id: string;
  date: string;
  type: BrandActionType;
  brand_unique_id: string;
  ba_group_id: string;
  mission_group_id: string;
  ba_unique_id: string;
  status: TBrandActionStatus;
  start_at: string;
  expired_at: string;
  progress: ITimeMissionProgress;
  updated_at: string;
  brandAction: IBrandAction;
  mission: ITimeMission;
  unclaimed_timed_missions: IActivatingTimeMission[];
  nextMission?: ITimeMission;
  data?: Record<string, any>;
  is_active?: boolean;
}

export interface ITimeMissionProgress {
  current: number;
  prefix_unit: string;
  required: number;
  suffix_unit: string;
  show_unit_on_current: boolean;
}

export interface ITimeMission {
  brand_unique_id: string;
  description: string;
  duration: number;
  prev_mission_unique_id: string;
  next_mission_unique_id: string;
  reward: number;
  unique_id: string;
  type: BrandActionType;
  ba_group_id: string;
  _id: string;
}

export interface ISPFQuiz {
  question_id: string;
  question: string;
  options: string[];
  explanation_content: {
    correct_answer: string;
    wrong_answer: string;
  };
  explanation_title: {
    correct_answer: string;
    wrong_answer: string;
  };
}

export interface IPayloadSubmitQuiz {
  ba_group_id: string;
  question: string;
  option: string;
}

export type ComponentId =
  | 'gold_coin_hint_cards_front_outfit'
  | 'silver_coin_circle_shrinks_logo'
  | 'silver_coin_circle_shrinks_character'
  | 'gold_coin_location_eliminations_logo'
  | 'gold_coin_location_eliminations_character'
  | 'gold_coin_text_hint_mascot'
  | 'gold_coin_text_hint_reveals_back_logo'
  | 'gold_coin_hint_shop_kv'
  | 'brand_actions_offerwall_kv'
  | 'ensuring_fairness_kv'
  | 'menu_htm_logo'
  | 'loading_screen_kv'
  | 'welcome_screen_htm_logo'
  | 'safety_hints_pop_up_character'
  | 'safety_hints_pop_up_flag'
  | 'pre_launch_landing_htm_van'
  | 'pre_launch_landing_htm_logo'
  | 'main_game_map_border';

export interface IGetQuizData {
  quizzes: ISPFQuiz[];
  crime_advisory: string;
  quiz_completed_at: string | null;
  corrected_questions: string[];
  ba_group_id: string;
  crystal_per_question: number;
  sharing_ba_unique_id: string;
  shared_at: string;
  claimed_at: string;
  quiz_ba_unique_id: string;
}
