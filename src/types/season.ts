export interface ISeason {
  status: 'ongoing' | 'ended' | 'future';
  hunt_name: string;
  start_at: string;
  end_at: string;
  id: string;
  total_coin: number;
  features: ISeasonFeatures;
  time?: string;
  silver_reward: string;
  fake_coins: {
    coin_id: string;
    coordinates: number[];
    hide_at: string;
    show_at: string;
    radius: number;
    order: number;
  }[];
  text_boxes?: {
    id: string;
    zoom_level: number[];
    type: string;
    location: number[];
    anchor: any;
    hide_at: string;
    show_at: string;
    metadata?: Record<string, any>;
    content: string;
    radius: number;
    angle: number;
  }[];
  lang: string;
}

export interface ISeasonFeatures {
  enter_serial_number: boolean;
  referral: boolean;
  brand_action: boolean;
  hints: boolean;
  shrink_power_up: boolean;
  change_hunter_id: boolean;
  survey: boolean;
  signup: boolean;
  multiple_language: boolean;
  legend: boolean;
  login: boolean;
  tac: boolean;
  online_bonus: boolean;
  daily_mission: boolean;
  daily_reward: boolean;
  sqkii_voucher: boolean;
  pedometer: boolean;
  beacon: boolean;
}
