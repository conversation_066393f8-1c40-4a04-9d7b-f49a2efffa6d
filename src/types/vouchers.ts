import {
  SqkiiVouchersTransactionStatus,
  SqkiiVouchersTransactionType,
} from '@constants';

export interface IOutlet {
  id: string;
  code: string;
  name: string;
  type: string;
  image: string;
  country: string;
  currency: string;
  description: string;
  monday: string;
  tuesday: string;
  wednesday: string;
  thursday: string;
  friday: string;
  saturday: string;
  sunday: string;
  weekday: string;
  weekend: string;
  public_holiday: string;
  address: string;
  location: {
    type: string;
    coordinates: [number, number];
  };
  company_name: string;
  postal_code: string;
  distance: number;
  brand_type: string;
  min_spend: number;
}

export interface IVouchersUserOnboarding {
  tutorial: boolean;
  level: boolean;
  topup_survey: string;
  payment_survey: string;
}

export type IVouchersUserSettings =
  | 'top-up'
  | 'payment'
  | 'bytes_conversion'
  | 'missions'
  | 'new_games'
  | 'announcements';

export interface IVouchersUser extends IUserBalance {
  id: string;
  onboarding: IVouchersUserOnboarding;
  username: string;
  email: string;
  status: string;
  enable_transmute: boolean;
  referral_code: string;
  mobile_number: string;
  hasPin: boolean;
  wallet_id: string;
  migrated: boolean;
  can_migrate: boolean;
  mobile_number_verified: boolean;
  sync_htm: boolean;
  date_of_birth: string;
  my_game_lib: string;
  place_of_residence: {
    country: string;
    city: string;
    address_1: string;
    address_2: string;
    unit_number: string;
    postal_code: string;
  };
  setting_notification: Record<
    IVouchersUserSettings,
    {
      app: true;
      email: true;
    }
  >;
  enable_biometric: true;
  total_topup: number;
  profile_completed: false;
  level: number;
  nextLevel: number;
  level_reward: number;
  accumulated_topup: number;
  exp: number;
  country: string;
}

export interface IUserBalance {
  balance: number;
  currency: string;
}

export interface IPayloadVouchersLogin {
  email: string;
  password: string;
  sdk_linking: {
    hunter_id: string;
    user_id: string;
    mobile_number: string;
  };
}

export interface IPayloadActiveOTP {
  email: string;
  code: string;
}

export interface IPayloadChangeMail {
  otp_code: string;
  email?: string;
}

export interface IPayloadCreateAccount {
  email: string;
  password: string;
  mobile_number: string;
  country: string;
  sdk_linking: {
    hunter_id: string;
    user_id: string;
  };
}

export interface IPayloadVouchersSetPassword {
  recover_token: string;
  password: string;
}

export interface IPayloadVouchersCheckCredentials {
  email: string;
  mobile_number: string;
  country: string;
  sdk_linking: {
    hunter_id: string;
    user_id: string;
  };
}

export interface IPayloadVouchersChangePIN {
  old_pin: string;
  pin_code?: string;
}

export interface IPayloadVouchersTopUp {
  amount: number;
  pin_code: string;
  promo_code?: string;
  sdk_linking: {
    user_id: string;
  };
  campaign: string;
}

export interface IPayloadVouchersSetPinOTP {
  otp_code: string;
  pin_code?: string;
}

export interface IVouchersTopUp {
  confirm_link: string;
  redirect_url: string;
  info: {
    id: string;
    payment_id: string;
    status: string;
    amount: string;
    bytes: number;
    currency: string;
    expiry_at: string;
  };
}

export interface IVouchersTopUpDetails {
  amount: number;
  balance: number;
  bonus_bytes: number;
  bytes: number;
  country: string;
  created_at: string;
  currency: string;
  id: string;
  method: string;
  payment_id: string;
  status: string;
  timestamp: string;
  topup_id: string;
  type: string;
  updated_at: string;
  user_id: string;
  rewarded: {
    crystals: number;
    beacon: number;
  };
}

export interface IPayloadVouchersBonusRate {
  amount: number;
  item: 'crystals';
  promo_code?: string;
}

export interface ITransactionHistory {
  id: string;
  user_id: string;
  txn_id: number;
  type: SqkiiVouchersTransactionType;
  outlet_name: string;
  payment_id: string;
  amount: number;
  currency: string;
  country: string;
  status: SqkiiVouchersTransactionStatus;
  created_at: string;
  updated_at: string;
  rewarded: {
    crystals: number;
    beacon: number;
  };
  linked: {
    hunter_id: string;
    user_id: string;
    link_at: string;
  };
}

export interface IRecentOutlet {
  code: string;
  name: string;
  processed_at: string;
}

export interface IRcentOutletsData {
  recent: IRecentOutlet[];
  outlets: IRecentOutlet[];
}

export interface IPayloadPayment {
  outlet_id: string;
  amount: number;
  pin_code: string;
  sdk_linking: {
    user_id: string;
  };
  campaign: string;
}

export interface IPaymentResult {
  country: string;
  amount: number;
  user_id: string;
  username: string;
  outlet_id: string;
  outlet_name: string;
  company_id: string;
  merchant_id: string;
  owner_id: string;
  txn_id: string;
  payment_id: string;
  status: string;
  type: string;
  currency: string;
  completed_at: string;
  rewarded: {
    crystals: number;
  };
}

export interface IUsedPromoCode {
  code: string;
  bonusCrystal: number;
}
