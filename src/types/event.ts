export interface IEventUpdate {
  _id: string;
  title: string;
  created_at: string;
  date: string;
  description: string;
  image: string;
  label: string;
  metadata: {
    contest_id: string;
  };
  type: string;
  updated_at: string;
  seen: boolean;
}

export interface IUserEventUpdate {
  _id: string;
  event_update: string;
  user: string;
  created_at: string;
  seen_at: string;
  updated_at: string;
}

export interface IEvent {
  event_updates: IEventUpdate[];
  user_event_updates: IUserEventUpdate[];
}

export interface IContestLeaderboard {
  votes: number;
  last_vote: string;
  school: string;
  rank: number;
}

export interface IUserContest {
  seen_contest_at: string;
  seen_result_at: string;
  _id: string;
  user: string;
  contest: string;
  school: string;
  voted_at: string;
  created_at: string;
  updated_at: string;
}

export interface IContest {
  _id: string;
  unique_id: string;
  created_at: string;
  description: string;
  end_at: string;
  schools: string[];
  start_at: string;
  title: string;
  updated_at: string;
  reward: number;
  leaderboard: IContestLeaderboard[];
  voteData: IContestLeaderboard[];
  userContest: IUserContest;
  show_result_until: string;
}

export interface IContestList {
  contests: IContest[];
  user_contests: IUserContest[];
}

export interface IMetalSonarEvent {
  unique_id: string;
  type: string;
  start_at: string;
  end_at: string;
}
