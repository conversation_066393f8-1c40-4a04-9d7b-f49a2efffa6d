export interface IMetalSonarLocation {
  lat: number;
  lng: number;
}

export interface IMetalSonar {
  _id: string;
  radius: number;
  event: string;
  created_at: string;
  location: IMetalSonarLocation;
  found_coin: boolean;
}

export interface IMetalSonarPrice {
  radius: number;
  price: number;
  base_price: number;
  can_use_power_up: boolean;
  item_id?: string;
}

export interface IMetalSonarPricesData {
  prices: IMetalSonarPrice[];
  beacon: boolean;
  expire_at: string;
}
