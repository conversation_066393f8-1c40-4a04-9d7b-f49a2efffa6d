import type { IUserLang, RmiOutlet } from '@types';

export interface IGlobeState {
  show: boolean;
  pedometer_onboarding_success?: boolean;
  brand_filter: {
    new: number;
    pending: number;
  };
  triggerPedometerFromGUI?: boolean;
}

export interface IInstructor {
  bubbleText?: string;
  agent?: string;
  tag?: string;
  sequences: ISequences[];
  hiddenAnims?: boolean;
  bubbleAction?: boolean;
}

export interface ISequences {
  target?: string;
  message?: string;
  css?: Record<string, string>;
  backdropCss?: Record<string, string> | boolean;
  delay?: number;
  persistent?: boolean;
  hideClose?: boolean;
  actions?: {
    name?: string;
    cb?: (next: () => void, close: () => void) => void;
    closeX?: (next: () => void, close: () => void) => void;
  };
  images?: string[];
  rmiOutlet?: RmiOutlet;
}

export type ActionCallback = (
  next: (index?: number) => void,
  close: () => Promise<void>
) => void | Promise<void>;

export type TSeasonISO = 'SG' | 'MY' | 'ID' | 'TH' | 'VN' | 'JP';
export interface ICountryRegion {
  name: string;
  code: string;
  iso: TSeasonISO;
  flag: string;
  mask: string[];
  currency: string;
  currencyName: string;
  url: string;
}

export interface IContentMsg {
  key: string;
  lang: IUserLang;
  value: string;
}

export interface IPayloadTrackUserLocation {
  lng: number;
  lat: number;
  type?: string;
  coins?: {
    coin_id: string;
    radius: number;
  }[];
  metadata?: Record<string, unknown>;
}

export interface DataDialog {
  path: string;
  attrs: any;
  close: (callback?: () => void) => void;
}

export interface ITAC {
  header: string;
  title?: string;
  contents: {
    content: string;
    subs?: string[];
    style?: string;
  }[];
  styleContent?: string;
}

export interface SystemMessage {
  id: string;
  text: string;
  type: 'sqkii' | 'timii' | 'nancii' | 'shinobii' | 'rmi';
  agent?: string;
  tag?: string;
  hiddenAnims?: boolean;
  messageType: 'sequence' | 'conditional' | 'rmi_conditional';
  persistent?: boolean;
  backdropCss?: Record<string, string> | true;
  images?: string[];
  rmiOutlet?: RmiOutlet;
  condition?: () => boolean;
  onTrigger?: () => void | Promise<void>;
  onClick?: () => void | Promise<void>;
}

export interface ViewportBounds {
  west: number;
  east: number;
  south: number;
  north: number;
}

export interface BaseMarkerData {
  id: string;
  lngLat: [number, number];
}

export interface MetalSonarMarkerData extends BaseMarkerData {
  foundCoin: boolean;
}

export interface BeaconMarkerData extends BaseMarkerData {
  expireAt: string;
}

export interface FoundMarkerData extends BaseMarkerData {
  name: string;
  brandName: string;
  coinNumber: string;
}

export interface PaidMarkerData extends BaseMarkerData {
  canUsePowerUp: boolean;
}

export interface ForfeitedMarkerData extends BaseMarkerData {}

export interface TimeUpMarkerData extends BaseMarkerData {}

export interface VerifyingMarkerData extends BaseMarkerData {}

export type PopupVariant =
  | 'metal-sonar'
  | 'beacon'
  | 'found'
  | 'paid'
  | 'forfeited'
  | 'timeup'
  | 'verifying';

export interface PopupStyleConfig {
  minWidth: string;
  backgroundImage: string;
  topOffset?: string;
}

export const POPUP_STYLES: Record<PopupVariant, PopupStyleConfig> = {
  'metal-sonar': {
    minWidth: '220px',
    backgroundImage: "url('/imgs/map/metal-sonar-popup.png')",
    topOffset: '15px',
  },
  beacon: {
    minWidth: '170px',
    backgroundImage: "url('/imgs/map/winner-popup.png')",
    topOffset: '15px',
  },
  found: {
    minWidth: '180px',
    backgroundImage: "url('/imgs/map/winner-popup.png')",
    topOffset: '-75px',
  },
  paid: {
    minWidth: '85px',
    backgroundImage: "url('/imgs/map/paid-popup.png')",
    topOffset: '15px',
  },
  forfeited: {
    minWidth: '200px',
    backgroundImage: "url('/imgs/map/forfeited-popup.png')",
    topOffset: '15px',
  },
  timeup: {
    minWidth: '170px',
    backgroundImage: "url('/imgs/map/time-up-popup.png')",
    topOffset: '15px',
  },
  verifying: {
    minWidth: '170px',
    backgroundImage: "url('/imgs/map/verifying-popup.png')",
    topOffset: '15px',
  },
};
