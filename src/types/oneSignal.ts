export interface IOneSignalWelcomeNotification {
  disable?: boolean;
  title?: string;
  message?: string;
  url?: string;
}

export interface IOneSignalConfig {
  appId?: string;
  safari_web_id?: string;
  requiresUserPrivacyConsent?: boolean;
  welcomeNotification?: IOneSignalWelcomeNotification;
  autoResubscribe?: boolean;
  notifyButton?: {
    enable: boolean;
  };
  allowLocalhostAsSecureOrigin?: boolean;
  serviceWorkerPath: string;
  serviceWorkerParam: {
    scope: string;
  };
}

interface IOSNotificationActionButton {
  readonly actionId: string;
  readonly text: string;
  readonly icon?: string;
  readonly launchURL?: string;
}

interface IOSNotification {
  readonly notificationId: string;
  readonly title?: string;
  readonly body: string;
  readonly icon?: string;
  readonly badgeIcon?: string;
  readonly image?: string;
  readonly actionButtons?: IOSNotificationActionButton[];
  readonly topic?: string;
  readonly additionalData?: object;
  readonly launchURL?: string;
  readonly confirmDelivery: boolean;
}

interface NotificationClickResult {
  readonly actionId?: string;
  readonly url?: string;
}

interface NotificationClickEvent {
  readonly notification: IOSNotification;
  readonly result: NotificationClickResult;
}

export type NotificationEventName =
  | 'click'
  | 'foregroundWillDisplay'
  | 'dismiss'
  | 'permissionChange'
  | 'permissionPromptDisplay';

interface NotificationForegroundWillDisplayEvent {
  readonly notification: IOSNotification;
  preventDefault(): void;
}

interface NotificationDismissEvent {
  notification: IOSNotification;
}

export type NotificationEventTypeMap = {
  click: NotificationClickEvent;
  foregroundWillDisplay: NotificationForegroundWillDisplayEvent;
  dismiss: NotificationDismissEvent;
  permissionChange: boolean;
  permissionPromptDisplay: void;
};

export interface IOneSignalNotifications {
  permission: boolean | undefined;
  isPushSupported: () => boolean;
  requestPermission: () => Promise<void>;
  addEventListener<K extends NotificationEventName>(
    event: K,
    listener: (obj: NotificationEventTypeMap[K]) => void
  ): void;
  removeEventListener<K extends NotificationEventName>(
    event: K,
    listener: (obj: NotificationEventTypeMap[K]) => void
  ): void;
}

export interface IOneSignalUser {
  onesignalId?: string;
  externalId?: string;
  PushSubscription: IOneSignalPushSubscription;
}

export interface IOneSignal {
  init: (config: IOneSignalConfig) => any;
  login: (userId: string) => Promise<void>;
  logout: () => Promise<void>;
  Notifications: IOneSignalNotifications;
  User: IOneSignalUser;
}

export type OneSignalDeferredLoadedCallback = (onesignal: IOneSignal) => void;

export type OneSignalWrappedWindow = typeof window & {
  OneSignalDeferred: OneSignalDeferredLoadedCallback[];
  OneSignal: IOneSignal;
};

export type PushSubscriptionNamespaceProperties = {
  id: string | undefined;
  token: string | undefined;
  optedIn: boolean;
};
export type SubscriptionChangeEvent = {
  previous: PushSubscriptionNamespaceProperties;
  current: PushSubscriptionNamespaceProperties;
};
export interface IOneSignalPushSubscription {
  id: string | undefined;
  token: string | undefined;
  optedIn: boolean | undefined;
  optIn(): Promise<void>;
  optOut(): Promise<void>;
  addEventListener(
    event: 'change',
    listener: (change: SubscriptionChangeEvent) => void
  ): void;
  removeEventListener(
    event: 'change',
    listener: (change: SubscriptionChangeEvent) => void
  ): void;
}
