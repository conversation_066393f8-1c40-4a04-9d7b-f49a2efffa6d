export type THintRarity = 'C' | 'U' | 'R' | 'SR' | 'SSR';

export interface IHint {
  _id: string;
  hint_id: string;
  content: string;
  rarity: THintRarity;
  is_new: boolean;
  received_at: string;
  is_opened: boolean;
}

export interface IHintState {
  eliminate_grid_price: number;
  is_opened_all_available_grids: boolean;
  is_opened_all_text_hints: boolean;
  next_new_text_hint: number;
  text_hint_price: number;
}

export interface IBuyHintPayload {
  quantity: number;
  lat?: number;
  lng?: number;
}
