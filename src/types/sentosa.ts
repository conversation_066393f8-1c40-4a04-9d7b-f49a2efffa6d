export type SentosaRewardType =
  | 'sentosa_hint'
  | 'sentosa_crystal_detector'
  | 'crystal'
  | 'sentosa_metal_detector'
  | 'capitaland_hint';

export interface SentosaDailyReward {
  day: number;
  reward_type: SentosaRewardType;
  amount: number;
  reward_data: {
    hint_number: string;
  };
  used_at: string;
  claimed_at: string;
  hint: {
    coin_name: string;
    content: string;
  };
}

export interface SentosaGoldenCoinHint {
  _id: string;
  order: number;
  is_social_media: boolean;
  content: string;
  unlocked_at: boolean;
  available_at: string;
}

export interface SentosaGoldenCoin {
  _id: string;
  name: string;
  status: 'ongoing' | 'verifying' | 'found' | 'forfeited' | 'scheduled';
  unique_id: string;
  order: number;
  locked: boolean;
  hints: SentosaGoldenCoinHint[];
  drop_at: string;
  video_link: string;
  winner_info: { winner_name: string };
  forfeited_at: string;
  found_at: string;
  verifying_at: string;
  location: {
    lat: number;
    lng: number;
  };
  geojson_file: string;
}

export interface IBeachStationHint {
  [key: string]: {
    coin_unique_id: string;
    content: string;
  };
}

export interface ISentosaIslandBounty {
  total_time: number;
  can_claim: boolean;
  started_at: string;
  rewards: Array<{
    amount: number;
    claimed: boolean;
    required_time: number;
  }>;
}

export interface IAmenities {
  _id: string;
  unique_id: string;
  address: string;
  computed_opening_hours: number[][];
  description: string;
  display_opening_hours: string;
  images: string[];
  location: {
    type: 'Point';
    coordinates: [number, number];
  };
  mission_title: string;
  name: string;
  promo_text: string;
  reward: number;
  claimed_at: string | null;
  distanceInMeters: number | null;
  icon: string;
  canCheckIn: boolean;
  amenities: boolean;
  closed_at: string | null;
  type: string;
  layer_id: string;
}
