import { HuntingStopType, HuntingStopRewardType } from '@constants';

export interface HuntingStop {
  unique_id: string;
  brand_unique_id: string;
  rewards: Record<HuntingStopRewardType, number>;
  lock_until: string;
  fun_facts: string;
  name: string;
  address: string;
  type: HuntingStopType;
  location: {
    lat: 1.2647139;
    lng: 103.8231658;
  };
  ba_unique_ids?: string[];
}

export interface HuntingStopWithLocation {
  _id: string;
  unique_id: string;
  name: string;
  address: string;
  distance: number;
  type: 'normal' | 'mega';
  location: {
    lat: number;
    lng: number;
  };
}
