import type { SourceSpecification, LayerSpecification } from 'vue3-maplibre-gl';
import { BRAND_SOV } from '@constants';
import type { IBrandAction, IWinnerInfoPayload, TSeasonISO } from '@types';

export interface ICoordinates {
  latitude: number;
  longitude: number;
}

export type TLayer = LayerSpecification & {
  source?: string | SourceSpecification | undefined;
};

export type TLayerType = 'fill' | 'line' | 'circle' | 'symbol';

export type TGeocontrolState =
  | 'OFF'
  | 'ACTIVE_LOCK'
  | 'WAITING_ACTIVE'
  | 'ACTIVE_ERROR'
  | 'BACKGROUND'
  | 'BACKGROUND_ERROR'
  | 'UNAVAILABLE';

export type TCoinStatus =
  | 'found'
  | 'ongoing'
  | 'verifying'
  | 'forfeited'
  | 'scheduled';

export interface ICircle {
  center: {
    lat: number;
    lng: number;
  };
  radius: number;
}

export interface IWinnerInfo {
  first_name: string;
  last_name: string;
  nric: string;
  mobile_number: string;
  country: TSeasonISO;
}

export interface ISilverCoin {
  _id: string;
  coin_id: string;
  name: string;
  status: TCoinStatus;
  start_at: string;
  end_at: string;
  winner_info: IWinnerInfoPayload;
  canUsePowerUp: boolean;
  circle: ICircle;
  lastCircle: ICircle;
  freeCircle: ICircle;
  paidCircle: ICircle;
  nextShrinkAt: string;
  is_smallest_public_circle: boolean;
  coin_number: number;
  sponsored_ba_uid: string;
  order: number;
  time_up: boolean;
  fake: boolean;
  hide_at: string;
  sponsor: boolean;
  amenities: boolean;
  beacon: boolean;
  location: {
    lat: number;
    lng: number;
  };
  verification_started_at: string;
  found_at: string;
  videos: string[];
  verifyingCircle: ICircle;
  forfeited_at: string;
  type: 'silver' | 'golden' | 'sentosa-golden-coin' | 'geneo-coin';
  serial_number: string;
  submit_info_at: string;
  user: string;
  winner_name: string;
  distance: number;
  brand_unique_id: BRAND_SOV;
  reward: string;
  brand_name: string;
  show_lure_text: boolean;
  radius: number;
  geohash: string;
  isGoldenCoin: boolean;
  special_event: boolean;
  coin_name: string;
  prefix: string;
}

export interface ISafetyHint {
  coin_numbers: number[];
  content: string;
  created_at: string;
  updated_at: string;
  _id: string;
}

export interface IBrandIcon extends IBrandAction {
  location: {
    lat: number;
    lng: number;
  };
  display: {
    brand_name: string;
    name: string;
    address: string;
    brand_icon: string;
    prefer_layer: string;
    sv_client?: string;
    opening_hours: Record<string, string>;
  };
  brand_unique_id: string;
  unique_id: string;
  opening_hours: Record<string, string>;
  brand_icon: string;
}

export type TCoinGrouped =
  | 'found'
  | 'past_found'
  | 'verifying'
  | 'forfeited'
  | 'ongoing_paid'
  | 'ongoing_free'
  | 'time_up'
  | 'ongoing'
  | 'scheduled';

export type ISilverCoinProperties = { properties: ISilverCoin };

export type IGroupedSilverCoins = Record<TCoinGrouped, ISilverCoinProperties[]>;

export type TSourceGeoHashes = 'golden' | 'eliminated' | 'paidEliminated';

export interface ISourceGeoHashes {
  type: 'FeatureCollection';
  features: any[];
}

export type IMapGeoHashes = Record<TSourceGeoHashes, string[]>;

export interface IGridEliminated {
  geohashes: string[];
  next_eliminate_at?: string;
  grids_per_elimination?: number;
}

export interface IShrinkPowerUpPayload {
  item_id?: string;
  coin_id: string;
  lat?: number;
  lng?: number;
}

export interface IEliminatedPowerUpPayload {
  quantity: number;
  lat?: number;
  lng?: number;
}

export interface IMetalDetectorPayload {
  item_id?: string;
  lat: number;
  lng: number;
}

export interface IDiscountPrice {
  beacon: boolean;
  percent: number;
  price: number;
  is_free: boolean;
  expire_at: string;
  base_price: number;
  possible_smallest: number;
}

export interface IMetalDetectorScanResult {
  extend_offer_end_at: string;
  extend_offer_price: IDiscountPrice;
  remaining_time: number;
  scan_result: 'red' | 'yellow' | 'green';
  accuracy: 'low' | 'medium' | 'high';
}

export interface IMetalDetectorResult {
  item_id: string;
  remaining_time: number;
  duration: number;
}
