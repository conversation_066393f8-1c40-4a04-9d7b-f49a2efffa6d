import { InventoryItemCategory, InventoryItemType } from '@constants';

export interface InventoryItem {
  _id: string;
  item_type: InventoryItemType;
  item_category: InventoryItemCategory;
  created_at: string;
  expires_at: string;
  quantity: number;
  radius: number;
}

export interface IventoryData {
  items: InventoryItem[];
  inventory_info: {
    extended_slots: number;
    max_inventory_size: number;
    max_stack_size: number;
  };
}
