import { api } from '@/boot/axios';
import type {
  IPayloadLogin,
  IPayloadResendOTP,
  IPayloadSignup,
  IUser,
} from '@types';

export const AUTH = {
  login: (payload: IPayloadLogin) => {
    return api.post<{ token: string; user: IUser }>('/login', payload);
  },

  signup: (payload: Partial<IPayloadSignup>) => {
    return api.post<IUser>('/user/register', payload);
  },

  resendOTP: (payload: IPayloadResendOTP) => {
    return api.post<{ next_otp_at: string; expire_at?: string }>(
      '/user/resend-otp',
      payload
    );
  },

  forgotPw: (payload: Partial<IPayloadSignup>) => {
    return api.post<IUser>('/forgot-password', payload);
  },

  verifyMobileNumber(payload: { otp?: string }) {
    return api.post<{ next_otp_at: string; expire_at: string }>(
      '/user/verify-mobile-number',
      payload
    );
  },
};
