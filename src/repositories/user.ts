import { api } from '@/boot/axios';
import type {
  ISettings,
  IUser,
  IAdventureLog,
  IUserLang,
  INotification,
  IPayloadTrackUserLocation,
  IUserSettingPayload,
  ITransaction,
  ICrystalExpiring,
  IBrandSovSettings,
  ISVSubmitReferralPayload,
} from '@types';
import axios from 'axios';
import { PedometerProgress } from '../types/user';

export const USER = {
  guest: () => {
    return api.get<{ user: IUser; token: string }>('/guest');
  },

  getUser: () => {
    return api.get<IUser>('/user/me');
  },

  logout: () => {
    return api.post('/user/logout');
  },

  updateOnboarding: (payload: { type: string }) => {
    return api.post('/user/onboarding', payload);
  },

  getSettings: () => {
    return api.get<ISettings>('/setting');
  },

  getBrandSovSettings: () => {
    return api.get<IBrandSovSettings>('/brand-sov-setting');
  },

  getAdventureLog: () => {
    return api.get<IAdventureLog[]>('/adventure-logs');
  },

  trackData: (type: string, data?: any) => {
    return api.post('/track', { type, data });
  },

  updateUserSettings: (payload: IUserSettingPayload) => {
    return api.post('/user/setting', payload);
  },

  changeLang: (lang: IUserLang) => {
    return api.put('/user', {
      lang,
    });
  },

  changeHunterId: () => {
    return api.post<{ hunter_id: string; next_fee: number }>(
      '/user/change-hunter-id'
    );
  },

  updateDropCoin: (ids: string[]) => {
    return api.post('/user/coin-drop', {
      ids,
    });
  },

  updateSurvey: (payload: any) => {
    return api.post('/user/survey', payload);
  },

  claimSurveyReward: () => {
    return api.get('/user/survey-reward');
  },
  updateEndSurvey: (payload: any) => {
    return api.post('/user/end-survey', payload);
  },

  claimEndSurveyReward: () => {
    return api.get<{
      crystal: number;
    }>('/user/end-survey-reward');
  },

  checkCountryCode: () => {
    return axios.get<{
      country: string;
    }>('https://api.country.is');
  },

  getAnn: () => {
    return api.get<INotification[]>('user/announcements');
  },

  readAnn: (id: string, skip = false) => {
    return api.put(`user/announcements/${id}`, {
      skip,
    });
  },

  location: (payload: IPayloadTrackUserLocation) => {
    api.post('/user/location', payload);
  },

  getPedometerProgress: () => {
    return api.get<PedometerProgress>('/user/pedometer');
  },

  getTransaction: () => {
    return api.get<ITransaction[]>('/user/transactions');
  },

  getCrystalExpiring: () => {
    return api.get<{
      crystal_expiring: ICrystalExpiring[];
    }>('/user/crystal-expiring');
  },
  getTigerBrokerAccessToken: (body: { code: string; state: string }) => {
    return api.post('/user/tiger-broker-token', body);
  },

  setSVToken: (token: string) => {
    return api.post('/user/set-sv-token', { token });
  },

  getSVReferral: () => {
    return api.get<{ max: number; current: number }>('/user/sv-referral');
  },

  submitSVReferral: (payload: ISVSubmitReferralPayload) => {
    return api.post<Boolean>('/user/sv-referral', payload);
  },
};
