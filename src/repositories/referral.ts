import { api } from '@/boot/axios';
import type { IReferral } from '@types';

export const REFERRAL = {
  get: () => {
    return api.get<IReferral[]>('/user/referrals');
  },

  enterReferralCode: (payload: { referral_code: string }) => {
    return api.post('/user/enter-referral-code', payload);
  },

  claimReferral: (payload: { id: string }) => {
    return api.post('/user/claim-referral', payload);
  },
};
