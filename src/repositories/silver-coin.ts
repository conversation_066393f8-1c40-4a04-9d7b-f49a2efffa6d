import { api } from '@/boot/axios';
import type { IVerification, IWinnerInfo, IWinnerInfoPayload } from '@types';

export const SILVER_COIN = {
  verifySerialNumber: (payload: { serial_number: string }) => {
    return api.post<IVerification>('/silver/verify', payload);
  },

  getVerifications: () => {
    return api.get<IVerification[]>('/silver/verifications');
  },

  verifyAccept: (payload: Partial<IWinnerInfoPayload>) => {
    return api.post<IVerification>('/silver/verify', payload);
  },

  getSubmmitedDetails: (id: string) => {
    return api.post<IWinnerInfo>('/silver/submitted-details', { id });
  },
};
