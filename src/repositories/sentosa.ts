import { api } from '@/boot/axios';
import type {
  IBeachStationHint,
  ISentosaIslandBounty,
  SentosaDailyReward,
  SentosaGoldenCoin,
} from '@types';

export const SENTOSA = {
  dailyRewards: () => {
    return api.get<SentosaDailyReward[]>('/sentosa/daily-reward');
  },
  checkin: (payload: { lat: number; lng: number }) => {
    return api.post<SentosaDailyReward>('sentosa/check-in', payload);
  },
  sentosaGoldenCoins: () => {
    return api.get<SentosaGoldenCoin[]>('/sentosa/coins');
  },

  sentosaBeachCount: () => {
    return api.get<{ count: number }>('/sentosa/beach-station-count');
  },

  sentosaBeachHint: () => {
    return api.get<IBeachStationHint>('/sentosa/beach-station-hint');
  },

  claimHintStation: () => {
    return api.get('/sentosa/claim-beach-station-hint');
  },

  getSentosaIslandBounty: () => {
    return api.get<ISentosaIslandBounty>('/sentosa/island-bounty');
  },

  checkinIslandBounty: (lat: number, lng: number) => {
    return api.post<{ claimed: { crystal: number } }>(
      '/sentosa/island-bounty/check-in',
      {
        lat,
        lng,
      }
    );
  },
};
