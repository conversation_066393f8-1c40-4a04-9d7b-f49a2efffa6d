import { api } from '@/boot/axios';
import { HuntingStopRewardType } from '@constants';
import { HuntingStop, HuntingStopWithLocation } from '@types';

export const HUNTING_STOP = {
  async getNearestStop(params: { lat: number; lng: number }) {
    const { data } = await api.get<HuntingStopWithLocation[]>(
      '/hunting-stop/top-nearest',
      {
        params,
      }
    );
    return data;
  },
  getByUniqueId: (uniqueId?: string) => {
    return api.get<HuntingStop>('/hunting-stop', {
      params: { stop_id: uniqueId },
    });
  },
  claimReward: (uniqueId: string) => {
    return api.post<{
      rewards: Record<HuntingStopRewardType, number>;
      lock_until: string;
    }>('/hunting-stop/claim', {
      stop_id: uniqueId,
    });
  },
};
