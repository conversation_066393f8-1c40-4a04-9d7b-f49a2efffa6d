import { api } from '@/boot/axios';
import type {
  IMetalSonar,
  IMetalSonarLocation,
  IMetalSonarPricesData,
} from '@types';

export const METAL_SONAR = {
  list: () => {
    return api.get<IMetalSonar[]>('silver/metal-sonar');
  },

  getPrices: (payload: IMetalSonarLocation & { id: string }) => {
    return api.get<IMetalSonarPricesData>('silver/metal-sonar-prices', {
      params: payload,
    });
  },

  use: (
    payload: IMetalSonarLocation & {
      radius: number;
      id: string;
      item_id?: string;
    }
  ) => {
    return api.post<IMetalSonar>('/silver/use-metal-sonar', payload);
  },
};
