import { vouchersAPI } from '@/boot/axios';
import type {
  IOutlet,
  IPayloadActiveOTP,
  IPayloadChangeMail,
  IPayloadPayment,
  IPayloadVouchersBonusRate,
  IPayloadVouchersChangePIN,
  IPayloadVouchersCheckCredentials,
  IPayloadVouchersLogin,
  IPayloadVouchersSetPassword,
  IPayloadVouchersSetPinOTP,
  IPayloadVouchersTopUp,
  IPaymentResult,
  IRcentOutletsData,
  ITransactionHistory,
  IUserBalance,
  IVouchersTopUp,
  IVouchersTopUpDetails,
  IVouchersUser,
} from '@types';

export const VOUCHERS = {
  // outlets
  getOutlets: () => {
    return vouchersAPI.get<Record<string, IOutlet[]>>('/outlet');
  },

  // auth
  login: (payload: IPayloadVouchersLogin) => {
    return vouchersAPI.post<{ token: string; user: IVouchersUser }>(
      '/link-kee',
      payload
    );
  },

  forgotPW: (email: string) => {
    return vouchersAPI.post<{
      resend_after: string;
      resendTimes: number;
    }>('/forgot-password', { email });
  },

  setPassword: (payload: IPayloadVouchersSetPassword) => {
    return vouchersAPI.post('/set-password', payload);
  },

  checkCredentials: (payload: IPayloadVouchersCheckCredentials) => {
    return vouchersAPI.post<{ email: boolean }>('/check-credential', payload);
  },

  requestCreateAccount: (
    payload: IPayloadVouchersCheckCredentials & { password: string }
  ) => {
    return vouchersAPI.post<boolean>('/create-account', payload);
  },

  resendActiveCode: (email: string) => {
    return vouchersAPI.post<{
      resend_after: string;
      resendTimes: number;
    }>('/send-activate', { email });
  },

  activeOTP: (payload: IPayloadActiveOTP) => {
    return vouchersAPI.post<{ token: string; user: IVouchersUser }>(
      '/activate-otp',
      payload
    );
  },

  requestChangeMail: (mobile_number: string) => {
    return vouchersAPI.post<{
      resend_after: string;
      resendTimes: number;
    }>('/request-change-email', { mobile_number });
  },

  changeMail: (payload: IPayloadChangeMail) => {
    return vouchersAPI.put('/change-associated-email', payload);
  },

  // user
  getMe: () => {
    return vouchersAPI.get<{ user: IVouchersUser }>('/me');
  },

  getUserBalance: () => {
    return vouchersAPI.get<IUserBalance>('/get-balance');
  },

  setPIN: (pin_code: string) => {
    return vouchersAPI.post('/set-pin', { pin_code });
  },

  changePIN: (payload: IPayloadVouchersChangePIN) => {
    return vouchersAPI.put('/change-pin', payload);
  },

  forgotPIN: (mobile_number: string) => {
    return vouchersAPI.post<{
      resend_after: string;
      resendTimes: number;
    }>('/forgot-pin', { mobile_number });
  },

  setPinOTP: (payload: IPayloadVouchersSetPinOTP) => {
    return vouchersAPI.put<{ verify: boolean }>('/set-pin-otp', payload);
  },

  verifyPIN: (pin_code: string) => {
    return vouchersAPI.post<{ verify: boolean }>('/verify-pin-code', {
      pin_code,
    });
  },

  // get vouchers
  topUp: (payload: IPayloadVouchersTopUp) => {
    return vouchersAPI.post<IVouchersTopUp>('/topup', payload);
  },

  getTopUpById: (id: string) => {
    return vouchersAPI.get<IVouchersTopUpDetails>('/topup/reference', {
      params: { id },
    });
  },

  checkPromoCode: (code: string) => {
    return vouchersAPI.post<{ crystals: number }>('/topup/check-promocode/', {
      code,
    });
  },

  checkBonusRate: (payload: IPayloadVouchersBonusRate) => {
    return vouchersAPI.get<{ crystals: number }>('topup/bonus-rate', {
      params: payload,
    });
  },

  // transaction history
  getTransactionHistory: (params: unknown) => {
    return vouchersAPI.get<{ data: ITransactionHistory[]; total: number }>(
      '/history',
      {
        params,
      }
    );
  },

  // payment
  getRecentOutlets: () => {
    return vouchersAPI.get<IRcentOutletsData>('/payment/recent-outlet');
  },

  paymentScanQR: (qr_code: string) => {
    return vouchersAPI.get<IOutlet>('payment/scan', {
      params: {
        qr_code,
      },
    });
  },

  payment: (payload: IPayloadPayment) => {
    return vouchersAPI.post<IPaymentResult>('/payment', payload);
  },

  unsync: () => {
    return vouchersAPI.post<any>('/unlink', {});
  },
};
