import { api } from '@/boot/axios';
import type { IDailyMission, PedometerProgress } from '@types';

export const DAILYMISSION = {
  get: () => {
    return api.get<IDailyMission[]>('/dailymission');
  },

  claim: (unique_id: string) => {
    return api.post<{ crystal: number }>('/dailymission/claim', { unique_id });
  },

  walk: (lng: number, lat: number, steps: number) => {
    return api.post<{ pedometer_progress: PedometerProgress }>(
      '/dailymission/walk',
      {
        lng,
        lat,
        steps,
      }
    );
  },

  survey: (data: any) => {
    return api.post('/user/shrink-pu-survey', data);
  },
};
