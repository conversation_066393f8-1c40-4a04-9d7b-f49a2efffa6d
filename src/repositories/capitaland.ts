import { api } from '@/boot/axios';
import type { IAmenities, SentosaDailyReward, SentosaGoldenCoin } from '@types';

export const CAPITALAND = {
  dailyRewards: () => {
    return api.get<SentosaDailyReward[]>('/capitaland/daily-reward');
  },
  checkin: (payload: { lat: number; lng: number }) => {
    return api.post<SentosaDailyReward>('capitaland/check-in', payload);
  },
  geneoCoins: () => {
    return api.get<SentosaGoldenCoin[]>('/capitaland/coins');
  },
  getAmenities: () => {
    return api.get<IAmenities[]>('/capitaland/amenities');
  },
  checkinAmenities: (payload: {
    unique_id: string;
    lat: number;
    lng: number;
  }) => {
    return api.post<{ crystal: number }>(
      '/capitaland/amenities/check-in',
      payload
    );
  },
};
