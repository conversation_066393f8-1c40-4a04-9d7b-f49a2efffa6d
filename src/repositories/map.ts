import { api } from '@/boot/axios';
import type {
  ISilverCoin,
  ISafetyHint,
  IShrinkPowerUpPayload,
  IEliminatedPowerUpPayload,
  IDiscountPrice,
  IMetalDetectorPayload,
  IMetalDetectorScanResult,
  IMetalDetectorResult,
  IShrinkData,
} from '@types';

export const MAP = {
  getGeoHashes: () => {
    return api.get<string[]>('/golden/geohashes');
  },

  getEliminatedGeoHashes: () => {
    return api.get<{ free: string[]; paid: string[] }>('/golden/eliminated');
  },

  getSilverCoin: () => {
    return api.get<ISilverCoin[]>('/silver');
  },

  retrieveCoin: (id: string) => {
    return api.get<{ coin: ISilverCoin }>(`/silver/retrieve/${id}`);
  },

  getGoldenEliminated: () => {
    return api.get('/golden/eliminated');
  },

  userShrinkPowerUp: (payload: IShrinkPowerUpPayload) => {
    return api.post<IShrinkData>('/silver/shrink', payload);
  },

  userShrinkLitePowerUp: (payload: IShrinkPowerUpPayload) => {
    return api.post<IShrinkData>('/silver/shrink-lite', payload);
  },

  useEliminatedPowerUp: (payload: IEliminatedPowerUpPayload) => {
    return api.post<string[]>('/golden/use-eliminate-pu', payload);
  },

  getSafetyHints: () => {
    return api.get<ISafetyHint[]>('/silver/safety-hints');
  },

  getShrinkPrice: (payload: IShrinkPowerUpPayload) => {
    return api.post<IDiscountPrice>('/silver/shrink-price', payload);
  },

  getEliminatedPrice: (payload: IEliminatedPowerUpPayload) => {
    return api.post<IDiscountPrice>('/golden/eliminate-price', payload);
  },

  getMetalDetectorPrice: (payload: IMetalDetectorPayload) => {
    return api.post<IDiscountPrice>('/silver/metal-detector-price', payload);
  },

  useMetalDetector: (payload: IMetalDetectorPayload) => {
    return api.post<IMetalDetectorResult>('/silver/metal-detector', payload);
  },

  scanMetalDetector: (payload: IMetalDetectorPayload) => {
    return api.post<IMetalDetectorScanResult>(
      '/silver/metal-detector/scan',
      payload
    );
  },

  useCrystalDetector: (payload: IMetalDetectorPayload & { day: number }) => {
    return api.post<IMetalDetectorResult>('/sentosa/crystal-detector', payload);
  },

  scanCrystalDetector: (payload: IMetalDetectorPayload) => {
    return api.post<IMetalDetectorScanResult>(
      '/sentosa/crystal-detector/scan',
      payload
    );
  },

  extendMetalDetector: (item_id: string) => {
    return api.post<IMetalDetectorResult>('/silver/metal-detector/extend', {
      item_id,
    });
  },
};
