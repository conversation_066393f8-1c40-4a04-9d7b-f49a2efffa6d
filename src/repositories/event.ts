import { api } from '@/boot/axios';
import type { IContest, IEvent, IContestList, IMetalSonarEvent } from '@types';

export const EVENT = {
  list: () => {
    return api.get<IEvent>('/event');
  },

  contest: (contestId: string) => {
    return api.get<IContest>(`/event/contest/${contestId}`);
  },

  constestList: () => {
    return api.get<IContestList>('/event/contest');
  },

  seen: (id: string) => {
    return api.post('/event/seen', { id });
  },

  vote: (payload: { id: string; school: string }) => {
    return api.post(`/event/contest/${payload.id}/vote`, {
      school: payload.school,
    });
  },

  metalSonarEvent: () => {
    return api.get<IMetalSonarEvent>('/event/metal-sonar');
  },
};
