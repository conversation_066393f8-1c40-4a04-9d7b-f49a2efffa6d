import { api } from '@/boot/axios';
import type {
  IBuyHintPayload,
  IDiscountPrice,
  IHint,
  IHintState,
} from '@types';

export const HINT = {
  check: () => {
    return api.get<IHintState>('/hint/check-hint-shop');
  },

  list: () => {
    return api.get<IHint[]>('/hint');
  },

  buy: (payload: IBuyHintPayload) => {
    return api.post<IHint[]>('/hint/buy-hint', payload);
  },

  getHintPrice: (payload: IBuyHintPayload) => {
    return api.post<IDiscountPrice>('/hint/hint-price', payload);
  },
};
