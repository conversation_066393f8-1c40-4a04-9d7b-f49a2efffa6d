import { api } from '@/boot/axios';
import type {
  IGetQuizData,
  IGlobalBrandAction,
  IPayloadSubmitQuiz,
  ITimeMissionProgress,
  TBrandActionStatus,
} from '@types';

export const BRAND_ACTION = {
  get: () => {
    return api.get<IGlobalBrandAction>('/brandaction');
  },

  uploadFile: (data: any) => {
    return api.post('/brandaction/upload-receipt', data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },

  scanQrCode: (data: any) => {
    return api.post('/brandaction/scan-qrcode', data);
  },

  claim: (user_brand_action_id: string) => {
    return api.post<{
      beacon: number;
      crystal: number;
    }>('/brandaction/claim', {
      user_brand_action_id,
    });
  },

  visitWeb: (brand_action_id: string) => {
    return api.post('brandaction/visit-web', { brand_action_id });
  },

  openSentosaApp: (brand_action_id: string) => {
    return api.post('brandaction/open-sentosa-app', { brand_action_id });
  },

  getPhysical: (data: any) => {
    return api.post('/brandaction/physical-store', data);
  },

  getNoti: () => {
    return api.get('/brandaction/noti');
  },

  seenNoti: (data: any) => {
    return api.post('/brandaction/seen-noti', data);
  },

  seenAllNoti: (data: any) => {
    return api.post('/brandaction/seen-all-noti', data);
  },

  submitClientInfo: (data: any) => {
    return api.post('brandaction/submit-client-info', data);
  },

  claimPromo: (data: any) => {
    return api.post<{
      beacon: number;
      crystal: number;
    }>('/brandaction/claim-promo-codes', data);
  },

  claimAll: () => {
    return api.get<{
      beacon: number;
      crystal: number;
    }>('/brandaction/claim-all');
  },

  getBrandIcons: () => {
    return api.get('/brandaction/icons');
  },

  syncUntame: (payload: { credential: string; password: string }) => {
    return api.post('/brandaction/sync-untame', payload);
  },

  claimMileston: (milestone_id: string) => {
    return api.post('/brandaction/claim-milestone', { milestone_id });
  },

  untameCheckTicket: (code: string) => {
    return api.post('/brandaction/check-untame-ticket', { code });
  },

  untameTicket: (code: string) => {
    return api.post('/brandaction/untame-ticket', { code });
  },

  // Timed Missions
  giveUpMission: () => {
    return api.post('/brandaction/timed-missions/give-up');
  },

  timeMissionClaim: (user_mission_id: string) => {
    return api.post<{
      beacon: number;
      crystal: number;
    }>('/brandaction/timed-missions/claim-reward', {
      user_mission_id,
    });
  },

  getSPFQuiz: (ba_group_id: string, lat?: number, lng?: number) => {
    return api.post<IGetQuizData>('/brandaction/spf-quiz', {
      ba_group_id,
      lat,
      lng,
    });
  },

  submitSPFQuiz: (payload: IPayloadSubmitQuiz) => {
    return api.post<{ correct: boolean }>(
      '/brandaction/spf-quiz/answer-quiz',
      payload
    );
  },

  readSpfMessage: (ba_group_id: string) => {
    return api.post('/brandaction/spf-quiz/read-message', {
      ba_group_id,
    });
  },

  shareSPFQuizResult: (ba_group_id: string) => {
    return api.post('/brandaction/spf-quiz/share-quiz-result', {
      ba_group_id,
    });
  },

  claimReward: (ba_group_id: string) => {
    return api.post<{
      action_success: true;
      reward: { crystal: number; beacon: number };
    }>('/brandaction/spf-quiz/claim-reward', { ba_group_id });
  },

  updateLocationBase: (payload: { lat: number; lng: number }) => {
    return api.post<{
      updatedData: {
        progress: ITimeMissionProgress;
        status: TBrandActionStatus;
      };
      updated: boolean;
    }>('/brandaction/timed-missions/update-location-based-mission', payload);
  },
  submitEtiqaPolicies: (policies: string[]) => {
    return api.post('/brandaction/submit-etiqa-policies', {
      policies,
    });
  },
  checkReposit: () => {
    return api.post('/brandaction/check-tiger-deposits');
  },
};
