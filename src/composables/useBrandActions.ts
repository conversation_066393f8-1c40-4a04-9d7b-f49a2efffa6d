import { useBAStore, useDialogStore, useUserStore } from '@stores';
import {
  useGlobal,
  useTick,
  useTrackData,
  useHandleGoButton,
} from '@composables';
import { BRAND_ACTION } from '@repositories';
import { Loading } from 'quasar';
import type { IBrandAction, IBrandHook } from '@types';

export const useBrandActions: () => IBrandHook = () => {
  const store = useUserStore();
  const baStore = useBAStore();
  const storeDialog = useDialogStore();

  const { trackUserLocation } = useGlobal();
  const { handleGoButton, locked_item } = useHandleGoButton();
  const { push, openDialog, closeAllDialog } = useMicroRoute();
  const { crystals, settings } = storeToRefs(store);
  const { now } = useTick();
  const { trackTime, track } = useTrackData();

  const multiplierNumber = computed(() => {
    return {
      featured: settings.value?.brand_action?.multiplier?.featured || 3,
      firstTime: settings.value?.brand_action?.multiplier?.first_time || 2,
    };
  });

  const claimAll = async () => {
    Loading.show();
    try {
      const res = await BRAND_ACTION.claimAll();
      await baStore.fetchBrandAction();
      await store.fetchUser();
      await trackUserLocation('claim_all_ba_reward');
      openDialog('promo_success', {
        crystal: res.data.crystal,
        beacon: res.data.beacon || 0,
      });

      //alert trigger signup
    } finally {
      Loading.hide();
    }
  };

  const checkWithFeatured = (item: IBrandAction) => {
    return (
      !!item.featured &&
      +new Date(item.featured.start_at) <= now.value &&
      +new Date(item.featured.end_at) >= now.value &&
      item.status !== 'claimed'
    );
  };

  const checkWithBonus = (item: IBrandAction) => {
    return (
      item.bonus &&
      (+new Date(item.bonus.end_at || 0) >= now.value ||
        item.status === 'verified') &&
      item.status !== 'claimed'
    );
  };

  const showStatus = (item: IBrandAction) => {
    openDialog('ba_status', {
      brandAction: item,
    });
  };

  const hasMultiplier = (item: IBrandAction) => {
    if (item.status === 'new')
      return (
        item.is_first_time || checkWithFeatured(item) || checkWithBonus(item)
      );
    return false;
  };

  const calculateReward = (item: IBrandAction) => {
    // before 2024-10-23
    // const multiplier = checkWithFeatured(item)
    //   ? 3
    //   : item?.is_first_time
    //   ? 2
    //   : 1;

    const bonus = checkWithBonus(item) ? item.bonus?.bonus || 0 : 0;
    const baseReward = item.reward?.crystal || 0;

    // before 2024-10-23
    // const reward = ((item.reward?.crystal || 0) + bonus) * multiplier;

    // 2024-10-23 update
    // last updated at 05/11/2024
    const featuredMultiplier = checkWithFeatured(item)
      ? multiplierNumber.value.featured
      : 1;
    const firstTimeMultiplier = item.is_first_time
      ? multiplierNumber.value.firstTime
      : 1;
    const reward =
      baseReward * firstTimeMultiplier * featuredMultiplier + bonus;

    if (item.status !== 'new') return reward;

    // hardcode for spf_1
    if (item.unique_id === 'spf_1') {
      const maxBaseReward = Number(item.display_reward) || 0;
      return maxBaseReward * firstTimeMultiplier * featuredMultiplier + bonus;
    }

    let rewardText = item.display_reward || reward;

    if (item.is_first_time && item.display_reward_first_time) {
      rewardText = item.display_reward_first_time;
    }

    return rewardText;
  };

  const claim = async (item: IBrandAction) => {
    Loading.show();
    try {
      const res = await BRAND_ACTION.claim(item._id || '');
      await baStore.fetchBrandAction();
      await store.fetchUser();
      await trackUserLocation('claim_ba_reward', {
        type: item.type,
        brand_action_id: item._id,
      });
      if (item.type === 'receipt_verification') {
        track('receiptverification_reward', {
          action: 'receiptverification_reward_offerwall',
        });
      }
      openDialog('promo_success', {
        crystal: res.data.crystal,
        beacon: res.data.beacon || 0,
      });
      // TO DO: set value to trigger bottom sequences dialogs
      storeDialog.triggerGuestReminder = 'claimed_ba';
    } finally {
      Loading.hide();
    }
  };

  const handleAction = async (item: IBrandAction) => {
    if (item.status === 'claimed') return;

    track('ba_item_button', {
      user_crystal: crystals.value,
      ba_id: item._id,
      status: item.status,
      unique_id: item.unique_id,
      brand_unique_id: item.brand_unique_id,
      group_id: item.group_id,
    });
    if (item.status === 'verified') return claim(item);
    if (item.status === 'pending') return showStatus(item);
    closeAllDialog();
    handleGoButton(item);
  };

  function handleBack() {
    track('BA_back');
    trackTime('offer_wall');
    push(-1);
  }
  return {
    handleBack,
    handleAction,
    claimAll,
    showStatus,
    checkWithFeatured,
    checkWithBonus,
    calculateReward,
    locked_item,
    hasMultiplier,
  };
};
