import {
  useAsync,
  useTick,
  useHandleGoButton,
  useTrackData,
} from '@composables';
import { tryShare2 } from '@helpers';
import { BRAND_ACTION } from '@repositories';
import { useBAStore, useMapStore, useUserStore } from '@stores';
import type { IActivatingTimeMission } from '@types';

export function useTimedMission() {
  const storeBA = useBAStore();
  const storeUser = useUserStore();
  const mapStore = useMapStore();

  const { lastLocations } = storeToRefs(mapStore);
  const { crystals } = storeToRefs(storeUser);
  const {
    timeMissionData,
    skip_mission_price,
    timed_mission_cooldown,
    quizzes,
    countingWrongQuizzes,
    indexQuiz,
    holdingSpfQuizMission,
    crime_advisory,
    canSkipQuiz,
    quizzData,
  } = storeToRefs(storeBA);
  const { now } = useTick();
  const { closeDialog, openDialog, closeAllDialog } = useMicroRoute();
  const { handleGoButton } = useHandleGoButton('mission_timed');
  const { track } = useTrackData();

  const NO_GO_BTN = ['location_based'];
  const currentQuiz = computed(() => quizzes.value[indexQuiz.value]);
  const correct = ref(false);
  const completedAnswer = ref(false);

  const hasActiveMission = computed(() => {
    return !!timeMissionData.value?._id;
  });

  const expiredAt = computed(() => {
    if (!timeMissionData.value) return '';
    if (+new Date(timeMissionData.value.expired_at) < now.value) return '';
    return +new Date(timeMissionData.value.expired_at) - now.value;
  });

  const timeMissionCooldown = computed(() => {
    if (!timed_mission_cooldown.value) return '';
    if (+new Date(timed_mission_cooldown.value) < now.value) return '';
    return +new Date(timed_mission_cooldown.value) - now.value;
  });

  function handleSubmitMission(mission: IActivatingTimeMission) {
    track('home_missions', {
      type: mission.brandAction.type,
      brand_unique_id: mission.brandAction.brand_unique_id,
      group_id: mission.brandAction.group_id,
      unique_id: mission.brandAction.unique_id,
      status: mission.status,
    });
    if (mission.status === 'new') {
      handleGoButton(mission.brandAction);
    } else handleClaimMission(mission._id);
  }

  const { execute: handleClaimMission, loading: claimMissionLoading } =
    useAsync({
      async fn(id: string, from?: string) {
        const { data } = await BRAND_ACTION.timeMissionClaim(id);
        storeBA.timeMissionData = null;
        await storeBA.fetchBrandAction();
        await storeUser.fetchUser();
        return { ...data, from };
      },
      onSuccess({ crystal, beacon, from }) {
        openDialog('promo_success', {
          crystal,
          beacon: beacon || 0,
          buttonType: from === 'spf_quiz' ? 'backToResult' : 'backToMap',
          onClose() {
            closeDialog('promo_success');
            openDialog('spf_completed');
          },
        });
      },
      onError() {
        openDialog('spf_completed');
      },
    });

  const { execute: handleGiveUpMission, loading: giveUpMissionLoading } =
    useAsync({
      async fn() {
        if (
          skip_mission_price.value > 0 &&
          crystals.value < skip_mission_price.value
        ) {
          openDialog('insufficient_crystals');
          return;
        }

        await BRAND_ACTION.giveUpMission();
        storeBA.timeMissionData = null;
        await storeUser.fetchUser();
        await storeBA.fetchBrandAction();
        closeDialog('give_up_time_mission');
      },
    });

  const { execute: handleGetQuizzes, loading: getQuizzesLoading } = useAsync({
    async fn(idFromOfferWall?: string) {
      if (!timeMissionData.value && !idFromOfferWall) return;
      const [lng, lat] = lastLocations.value;
      const ba_group_id =
        idFromOfferWall ?? (timeMissionData.value?.ba_group_id || '');
      const { data } = await BRAND_ACTION.getSPFQuiz(ba_group_id, lng, lat);
      return { ...data, ba_group_id };
    },
    onSuccess(data) {
      if (!data) return;
      clearSpfQuizContext();
      storeBA.setQuizzes(data.quizzes);
      storeBA.setQuizzData(data);
      crime_advisory.value = data.crime_advisory;
      if (data.quiz_completed_at) {
        countingWrongQuizzes.value =
          data.quizzes.length - data.corrected_questions.length;
        canSkipQuiz.value = true;
      }
      holdingSpfQuizMission.value = {
        ba_group_id: data.ba_group_id,
      } as IActivatingTimeMission;
    },
  });

  const { execute: handleSubmitQuiz, loading: submitQuizLoading } = useAsync({
    async fn(option: string) {
      if (!holdingSpfQuizMission.value) return;
      const { data } = await BRAND_ACTION.submitSPFQuiz({
        ba_group_id: holdingSpfQuizMission.value.ba_group_id,
        question: currentQuiz.value.question,
        option,
      });
      if (
        data.correct &&
        quizzData.value &&
        !quizzData.value.corrected_questions.includes(
          currentQuiz.value.question
        )
      ) {
        quizzData.value.corrected_questions.push(currentQuiz.value.question);
      }
      return data;
    },
    onSuccess(data) {
      if (!data) return;
      if (!data.correct) storeBA.countingWrongQuizzes++;
      if (data.correct && storeBA.quizzData) {
        const correctedQuestions = storeBA.quizzData.corrected_questions || [];
        storeBA.quizzData.corrected_questions = Array.from(
          new Set([...correctedQuestions, currentQuiz.value.question])
        );
      }
      correct.value = data.correct;
      completedAnswer.value = true;
    },
  });

  async function handleNextQuiz() {
    indexQuiz.value++;
    correct.value = false;
    completedAnswer.value = false;
    if (!!currentQuiz.value) return;
    openDialog('spf_completed');
  }

  function clearSpfQuizContext() {
    indexQuiz.value = 0; // Need to start from the beginning after quit
    holdingSpfQuizMission.value = null; // Release holding value
    quizzes.value = []; // Clear quizzes to reduce memory usage
    storeBA.fetchBrandAction(); // need to fetch again when quit (case: re-attempt after complete) to update timed mission drawer
    canSkipQuiz.value = false;
    countingWrongQuizzes.value = 0;
  }

  async function handleShareQuiz() {
    if (!quizzData.value) return;
    const shareResult = await tryShare2({
      title: `I scored ${quizzes.value.length - countingWrongQuizzes.value}/${
        quizzes.value.length
      } on the NCPC quiz!`,

      text: `

      Think you can beat my score? Try the quiz now at ${process.env.APP_END_POINT}

      If you are unsure when faced with a potential scam, call 1799. #HTMxScamsHelpline`,
    });
    if (shareResult.status === 'success') {
      const res = await BRAND_ACTION.shareSPFQuizResult(
        quizzData.value.ba_group_id
      );
      await storeBA.fetchBrandAction();
      if (res.data.action_success) {
        const unclaimedBrandAction = storeBA.user_brand_actions.find(
          (ba) =>
            ba.status === 'verified' &&
            ba.type === 'spf_sharing' &&
            ba.ba_unique_id === quizzData.value?.sharing_ba_unique_id
        );
        if (unclaimedBrandAction) {
          const res = await BRAND_ACTION.claim(unclaimedBrandAction._id);
          if (!res.data.crystal) return;
          if (unclaimedBrandAction.type === 'receipt_verification') {
            track('receiptverification_reward', {
              action: 'receiptverification_reward_offerwall',
            });
          }
          openDialog('promo_success', {
            crystal: res.data.crystal || 0,
            beacon: 0,
            buttonType: 'backToResult',
            onClose() {
              closeDialog('promo_success');
              openDialog('spf_completed');
            },
          });
          return;
        }
      }
    }
  }

  function handleRetryQuiz() {
    indexQuiz.value = 0;
    storeBA.countingWrongQuizzes = 0;
    closeDialog('spf_completed');
    openDialog('spf_quiz');
  }

  async function handleClaimQuizReward() {
    if (!timeMissionData.value) return;
    const unclaimedQuizMission =
      timeMissionData.value.unclaimed_timed_missions.find(
        (mission) =>
          mission.type === 'spf_quiz' &&
          mission.ba_group_id === holdingSpfQuizMission.value?.ba_group_id
      );

    if (unclaimedQuizMission) {
      await handleClaimMission(unclaimedQuizMission._id, 'spf_quiz');
    } else {
      openDialog('spf_completed');
    }
  }

  const { execute: handleExitQuiz, loading: exitQuizLoading } = useAsync({
    async fn() {
      const ba_group_id = quizzData.value?.ba_group_id;
      if (!ba_group_id) return;
      const { data } = await BRAND_ACTION.claimReward(ba_group_id);
      return data;
    },
    async onSuccess(data) {
      closeAllDialog();
      await storeBA.fetchBrandAction();
      clearSpfQuizContext();
      if (data?.action_success) {
        await storeUser.fetchUser();
        openDialog('promo_success', {
          crystal: data?.reward.crystal || 0,
          beacon: data?.reward.beacon || 0,
          buttonType: 'backToMap',
          onClose() {
            closeDialog('promo_success');
            openDialog('spf_completed');
          },
        });
      }
    },
  });

  watch(expiredAt, (val) => {
    if (!val && timeMissionData.value) storeBA.fetchBrandAction();
  });

  watch(timeMissionCooldown, (val) => {
    if (!val && timeMissionData.value) storeBA.fetchBrandAction();
  });

  const loading = computed(() => {
    return [
      giveUpMissionLoading.value,
      claimMissionLoading.value,
      getQuizzesLoading.value,
      submitQuizLoading.value,
      exitQuizLoading.value,
    ].some(Boolean);
  });

  return {
    timeMissionData,
    expiredAt,
    timed_mission_cooldown,
    skip_mission_price,
    loading,
    hasActiveMission,
    timeMissionCooldown,
    NO_GO_BTN,
    quizzes,
    currentQuiz,
    countingWrongQuizzes,
    indexQuiz,
    holdingSpfQuizMission,
    crime_advisory,
    canSkipQuiz,
    correct,
    completedAnswer,
    quizzData,
    handleSubmitMission,
    handleGiveUpMission,
    handleGetQuizzes,
    handleSubmitQuiz,
    handleNextQuiz,
    handleShareQuiz,
    handleRetryQuiz,
    handleClaimQuizReward,
    clearSpfQuizContext,
    handleExitQuiz,
  };
}
