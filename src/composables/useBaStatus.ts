import { closeNotify, errorNotify, successNotify } from '@helpers';
import { BRAND_ACTION } from '@repositories';
import { useBAStore } from '@stores';
import { playSFX, useTrackData } from '@composables';
import { last } from 'lodash';
import type { IBrandAction } from '@types';

export const useBAStatus = () => {
  const { push, currentPath, openDialog } = useMicroRoute();
  const { t } = useI18n();
  const baRejected = ref();
  const store = useBAStore();
  const { track } = useTrackData();
  const checkedBaStatus = ref(false);

  const { user_brand_actions } = storeToRefs(store);
  const listBaRejected = computed(() =>
    user_brand_actions.value.filter(
      (item) => item.status === 'rejected' && !item.seen
    )
  );

  const showBaStatus = (dataItem?: IBrandAction) => {
    const ba: IBrandAction =
      dataItem || JSON.parse(JSON.stringify(baRejected.value));
    baRejected.value = undefined;
    setTimeout(
      () => {
        const dialog = openDialog('ba_status', {
          brandAction: ba,
          onClose: async () => {
            dialog.close();
            await BRAND_ACTION.seenNoti({
              id: ba?._id,
            });
            store.seenBa(ba?._id || '');
            if (listBaRejected.value.length)
              showBaStatus(
                listBaRejected.value[listBaRejected.value.length - 1]
              );
          },
        });
      },
      !!dataItem ? 300 : 0
    );
  };

  const getMessageError = (ba: IBrandAction) => {
    switch (ba.type) {
      case 'receipt_verification':
        return (
          t('RECEIPT_REJECTED_MESSAGE', {
            TITLE: t(ba.title),
          }) || ''
        );
      case 'client_verify':
        return (
          t('CLIENT_VERIFY_REJECTED_MESSAGE', {
            TITLE: t(ba.title),
          }) || ''
        );
      case 'etiqa_insurance':
        return (
          t('ETIQUA_VERIFY_REJECTED_MESSAGE', {
            TITLE: t(ba.title),
          }) || ''
        );
      case 'tiger_broker_deposit':
        return (
          t('TB_DEPOSIT_REJECTED_MESSAGE', {
            TITLE: t(ba.title),
          }) || ''
        );
      case 'tiger_broker_sync_account':
        return (
          t('TB_SYNC_REJECTED_MESSAGE', {
            TITLE: t(ba.title),
          }) || ''
        );
      case 'tiger_broker_register_boss_card':
        return (
          t('TB_BOSS_CARD_REJECTED_MESSAGE', {
            TITLE: t(ba.title),
          }) || ''
        );
      case 'tada_ride':
        return (
          t('TADA_REJECTED_MESSAGE', {
            TITLE: t(ba.title),
          }) || ''
        );
      case 'lendlease_sync_account':
        return (
          t('LEANDLEASE_SYNC_ACCOUNT_REJECTED_MESSAGE', {
            TITLE: t(ba.title),
          }) || ''
        );
      default:
        return '';
    }
  };

  const checkBaStatus = computed(() => {
    return (
      !!listBaRejected.value.length ||
      user_brand_actions.value.some((item) => item.status === 'verified')
    );
  });

  const showNotiBaSatus = () => {
    if (listBaRejected.value.length > 0) {
      baRejected.value = listBaRejected.value[listBaRejected.value.length - 1];
      const message = getMessageError(baRejected.value);
      errorNotify({
        message,
        timeout: 0,
        actions: [
          {
            handler: async () => {
              closeNotify();
              if (baRejected.value.type === 'receipt_verification')
                track('receiptverification_error_rejected', {
                  action: 'receiptverification_error_rejected_close',
                });
              await BRAND_ACTION.seenNoti({
                id: baRejected.value._id,
              });
              store.seenBa(baRejected.value._id);
              showNotiBaSatus();
            },
          },
        ],
      });
      playSFX('reject');
    } else if (
      user_brand_actions.value.some((item) => item.status === 'verified')
    ) {
      successNotify({
        message: t('HAS_CRYSTAL_BA_NOT_CLAIMED'),
      });
      playSFX('approve');
    }
    checkedBaStatus.value = true;
  };

  const clickListener = (event: any) => {
    switch (event.target.id) {
      case 'tap-detail-verified':
        closeNotify();
        push('offer_wall');
        break;
      case 'tap-detail-rejected':
        if (baRejected.value?.type === 'receipt_verification') {
          track('receiptverification_error_rejected', {
            action: 'receiptverification_error_rejected_details',
          });
        }
        closeNotify();
        push('offer_wall');
        showBaStatus();
        break;
      default:
        break;
    }
  };

  watch(currentPath, (val, oldVal) => {
    if (
      last(val.split('/')) === 'home' ||
      (last(val.split('/')) === 'offer_wall' &&
        !['home', 'menu'].includes(last(oldVal.split('/')) || ''))
    )
      showNotiBaSatus();
  });

  onMounted(async () => {
    document.addEventListener('click', clickListener);
  });
  onBeforeUnmount(() => {
    document.removeEventListener('click', clickListener);
  });

  return {
    checkBaStatus,
    showNotiBaSatus,
    checkedBaStatus,
  };
};
