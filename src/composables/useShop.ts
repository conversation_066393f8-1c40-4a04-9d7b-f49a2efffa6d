import { errorNotify, timeCountDown } from '@helpers';
import { useAsync, useTick } from '@composables';
import { HINT, MAP } from '@repositories';
import { useDialogStore, useMapStore, useUserStore } from '@stores';
import type { IAPIResponseError } from '@types';
import dayjs from 'dayjs';

export function useShop() {
  const storeUser = useUserStore();
  const storeMap = useMapStore();
  const storeDialog = useDialogStore();

  const { lastLocations } = storeToRefs(storeMap);
  const { hintState, crystals } = storeToRefs(storeUser);
  const { now } = useTick();
  const { openDialog, closeAllDialog } = useMicroRoute();

  const textPrice = computed(() => Number(hintState.value?.text_hint_price));
  const eliminatedPrice = computed(() =>
    Number(hintState.value?.eliminate_grid_price)
  );

  const timeNextDay = computed(() => {
    const endOfDay = +new Date(dayjs().endOf('day').toISOString()).getTime();
    return timeCountDown(endOfDay - now.value);
  });

  const { execute: buyTextHint, loading: loadingBuyText } = useAsync({
    fn: async (quantity: number) => {
      const [lng, lat] = lastLocations.value;
      const { data } = await HINT.buy({
        quantity,
        lng,
        lat,
      });
      await storeUser.fetchUser();
      return { quantity, data };
    },
    async onSuccess({ data, quantity }) {
      storeUser.fetchHints();
      openDialog('hint_animation', {
        type: 'text_hint',
        quantity,
        hints: data,
      });
      // TO DO: set value to trigger bottom sequences dialogs
      storeDialog.triggerGuestReminder = 'text_hints';
    },
    onError(error) {
      const { error_message } = error as IAPIResponseError;
      errorNotify({
        message: error_message,
      });
    },
  });

  const { execute: getMoreTextHint, loading: loadingGetMoreText } = useAsync({
    fn: async (quantity: number) => {
      await storeUser.checkHint();
      return { quantity };
    },
    onSuccess({ quantity }) {
      closeAllDialog();
      if (hintState.value?.is_opened_all_text_hints)
        return openDialog('text_hint');

      if (crystals.value < textPrice.value * quantity)
        return openDialog('insufficient_crystals');

      return openDialog('confirm_text_hint', {
        quantity,
      });
    },
  });

  const { execute: buyEliminatedHint, loading: loadingBuyEliminated } =
    useAsync({
      fn: async (quantity: number) => {
        const [lng, lat] = lastLocations.value;
        const { data } = await MAP.useEliminatedPowerUp({
          quantity,
          lng,
          lat,
        });
        await storeUser.fetchUser();
        return { quantity, data };
      },
      onSuccess({ data, quantity }) {
        openDialog('hint_animation', {
          type: 'eliminated_hint',
          quantity,
          grids: data,
        });
        // TO DO: set value to trigger bottom sequences dialogs
        storeDialog.triggerGuestReminder = 'grids_eliminated';
      },
      onError(error) {
        const { error_message } = error as IAPIResponseError;
        errorNotify({
          message: error_message,
        });
      },
    });

  const { execute: getMoreEliminatedHint, loading: loadingGetMoreEliminated } =
    useAsync({
      fn: async (quantity: number) => {
        await storeUser.checkHint();
        return { quantity };
      },
      onSuccess({ quantity }) {
        closeAllDialog();
        if (hintState.value?.is_opened_all_available_grids)
          return openDialog('eliminated_hint');

        if (crystals.value < eliminatedPrice.value * quantity)
          return openDialog('insufficient_crystals');

        return openDialog('confirm_eliminated_hint', {
          quantity,
        });
      },
    });

  const loading = computed(() =>
    [
      loadingGetMoreText.value,
      loadingBuyText.value,
      loadingBuyEliminated.value,
      loadingGetMoreEliminated.value,
    ].some(Boolean)
  );

  return {
    timeNextDay,
    loading,
    buyTextHint,
    getMoreTextHint,
    buyEliminatedHint,
    getMoreEliminatedHint,
  };
}
