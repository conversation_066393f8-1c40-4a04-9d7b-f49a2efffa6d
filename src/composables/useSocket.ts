import { useUserStore } from '@stores';
import { throttle } from 'lodash';
import Base64 from 'crypto-js/enc-base64';
import HmacSHA256 from 'crypto-js/hmac-sha256';
import io from 'socket.io-client';

function signHeader() {
  const ctime = +new Date();
  const method = 'GET';
  const contentType = 'application/json';
  const path = '/socket';

  const stringToSign = `${method}\n${contentType}\n${ctime}\n${path}\n`;
  const sig = Base64.stringify(
    HmacSHA256(stringToSign, process.env.HMAC_SECRET || '')
  );

  return {
    sig,
    ctime,
  };
}

export function useSocket(onConnected?: () => unknown) {
  const URL = process.env.APP_END_POINT || '';

  const storeUser = useUserStore();
  const { isAuthenticated, token, socketIO } = storeToRefs(storeUser);

  const socket = io(URL, {
    auth: {
      token: `Bearer ${token.value || ''}`,
      role: 'user',
      ...signHeader(),
    },
    transports: ['websocket'],
    withCredentials: true,
    autoConnect: false,
    forceBase64: true,
  });

  const reConnect = () => {
    destroySocket();
    connectSocket();
  };
  const throttleReConnect = throttle(reConnect, 2000);

  const onConnectError = async (e: Error) => {
    if (e.message === 'Unauthorized!') {
      storeUser.setSocket({ authenticated: false });
    }
    throttleReConnect();
  };

  const throttleOnConnectError = throttle(onConnectError, 1000);

  socket.on('authenticated', () => {
    if (onConnected) {
      onConnected();
      storeUser.fetchUser();
    }
  });

  socket.on('connect_error', throttleOnConnectError);
  socket.on('unauthorized', async () => {
    destroySocket();
  });

  function destroySocket() {
    socket.disconnect();
    storeUser.setSocket({ socket: socket });
  }

  function connectSocket() {
    if (!isAuthenticated.value) return;
    socket.auth = {
      token: `Bearer ${token.value || ''}`,
      role: 'user',
      ...signHeader(),
    };
    socket.connect();
    storeUser.setSocket({ authenticated: true, socket: socket });
  }

  onMounted(() => {
    if (isAuthenticated.value) connectSocket();
  });

  onBeforeUnmount(() => {
    destroySocket();
  });

  return {
    socket,
    authenticated: !!socketIO.value?.authenticated,
    connectSocket,
    destroySocket,
  };
}

export function useIo() {
  const storeUser = useUserStore();
  const { socketIO } = storeToRefs(storeUser);

  if (!socketIO.value.socket) {
    const { socket, authenticated } = useSocket(void 0);
    return {
      socket,
      authenticated,
    };
  }

  return {
    socket: socketIO.value.socket,
    authenticated: socketIO.value.authenticated,
  };
}
