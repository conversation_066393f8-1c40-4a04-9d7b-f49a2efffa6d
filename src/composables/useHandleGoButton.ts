import { useBAStore, useDialogStore, useUserStore } from '@stores';
import { delay, useAsync, useGlobal, useTick } from '@composables';
import { BRAND_ACTION } from '@repositories';
import type { IActivatingTimeMission, IBrandAction } from '@types';
import { last } from 'lodash';

type FROM = 'mission_timed' | 'brand_action';

export const useHandleGoButton = (from: FROM = 'brand_action') => {
  const baStore = useBAStore();
  const userStore = useUserStore();
  const storeDialog = useDialogStore();

  const { trackUserLocation } = useGlobal();
  const { push, openDialog, currentPath } = useMicroRoute();
  const { user } = storeToRefs(userStore);
  const { user_brand_actions } = storeToRefs(baStore);
  const { now } = useTick();
  // const { t } = useI18n();

  const promoAction = (item: IBrandAction, type = 'promocode') => {
    const steps = item.instructions;
    if (steps.every((el) => el))
      push(
        last(currentPath.value.split('/')) === 'offer_wall'
          ? 'ba_instruction'
          : 'offer_wall/ba_instruction',
        {
          data: item,
        }
      );
    else
      openDialog('enter_promo', {
        data: item,
        type,
      });
  };

  const verifyMobileNumberAction = () => {
    openDialog('verify_mobile_number', {
      action: async () => {
        await baStore.fetchBrandAction();
      },
    });
  };

  const locked_item = (item: IBrandAction) => {
    const value = user_brand_actions.value.find(
      (brand) =>
        item.status === 'new' &&
        brand.ba_unique_id === item.unique_id &&
        brand.lock_until &&
        +new Date(brand.lock_until) >= now.value
    );

    return value;
  };

  const { execute: visitWebAction } = useAsync({
    async fn(item: IBrandAction | IActivatingTimeMission) {
      const id = 'brandAction' in item ? item.brandAction._id : item._id;
      await BRAND_ACTION.visitWeb(id);
      await baStore.fetchBrandAction();
    },
  });

  const { execute: openSentosaAppAction } = useAsync({
    async fn(item: IBrandAction | IActivatingTimeMission) {
      const id = 'brandAction' in item ? item.brandAction._id : item._id;
      await BRAND_ACTION.openSentosaApp(id);
      await baStore.fetchBrandAction();
    },
  });

  async function handleGoButton(ba: IBrandAction) {
    trackUserLocation('brand_action', {
      type: ba.type,
      brand_action_id: ba._id,
      ba_unique_id: ba.ba_unique_id,
      from,
    });
    const lockedItem = locked_item(ba);
    if (!!lockedItem)
      return openDialog('ba_status', {
        brandAction: lockedItem,
      });
    switch (ba.type) {
      case 'contest':
        push('event');
        break;
      case 'receipt_verification':
      case 'lendlease_sync_account':
      case 'tiger_broker_deposit':
      case 'tiger_broker_register_boss_card':
      case 'tiger_broker_sync_account':
      case 'client_verify':
      case 'etiqa_insurance':
      case 'scan_qrcode':
        push(
          last(currentPath.value.split('/')) === 'offer_wall'
            ? 'ba_instruction'
            : 'offer_wall/ba_instruction',
          {
            data: ba,
          }
        );
        break;
      case 'tada_ride':
        if (!user.value?.verified_mobile_number_at) {
          openDialog('signup');
        } else {
          push(
            last(currentPath.value.split('/')) === 'offer_wall'
              ? 'ba_instruction'
              : 'offer_wall/ba_instruction',
            {
              data: ba,
            }
          );
        }
        break;
      case 'promo_code':
      case 'enter_promo_code':
        promoAction(ba);
        break;
      case 'enter_barcode':
        promoAction(ba, 'barcode');
        break;
      case 'open_external_link':
      case 'visit_web':
        if (ba.unique_id === 'spf_4')
          push(
            last(currentPath.value.split('/')) === 'offer_wall'
              ? 'ba_instruction'
              : 'offer_wall/ba_instruction',
            {
              data: ba,
            }
          );
        else {
          visitWebAction(ba);
        }
        break;
      case 'open_sentosa_app':
        push(
          last(currentPath.value.split('/')) === 'offer_wall'
            ? 'ba_instruction'
            : 'offer_wall/ba_instruction',
          {
            data: ba,
          }
        );
        break;
      case 'survey':
        openDialog('ba_survey', {
          dataBrand: ba,
        });
        break;
      case 'verify_mobile_number':
        verifyMobileNumberAction();
        break;
      case 'sqkii_voucher':
      case 'use_sqkii_voucher':
        push('sqkii_vouchers');
        break;
      case 'spf_quiz':
        openDialog('spf_welcome', {
          skipQuiz: from === 'mission_timed',
          idFromOfferWall: ba.group_id,
        });
        break;
      case 'read_spf_message':
      case 'spf_sharing':
        openDialog('spf_welcome', {
          skipQuiz: true,
          idFromOfferWall: ba.group_id,
        });
        break;
      case 'capitaland_visit_amenities':
      case 'capitaland_visit_fnb':
      case 'capitaland_visit_health':
      case 'capitaland_visit_type':
      case 'capitaland_10min_fairprice':
      case 'capitaland_10min_mrt':
      case 'capitaland_10min_fnb':
      case 'capitaland_10min_health':
      case 'capitaland_10min_park':
        const isHome = last(currentPath.value.split('/')) === 'home';
        if (!isHome) push('/home');
        await delay(500);
        storeDialog.showBottomSheet = true;
        break;
      case 'location_based':
        push('missions');
        break;
      default:
        alert(`${ba.type} Not implemented yet`);
        break;
    }
  }
  return { handleGoButton, locked_item, visitWebAction, openSentosaAppAction };
};
