interface UseAsyncOptions<T, U extends any[]> {
  fn: (...args: U) => Promise<T>;
  onError?: (error: any) => void;
  onSuccess?: (data: T) => void;
  onSettled?: () => void;
}

interface UseAsyncResult<ReturnType, Parameters extends any[], ErrorType> {
  data: Ref<ReturnType | undefined>;
  error: Ref<ErrorType | null>;
  loading: Ref<boolean>;
  status: Ref<'idle' | 'pending' | 'success' | 'error'>;
  execute: (...args: Parameters) => Promise<void>;
}

function defaultErrorHandler(error: any) {
  if (process.env.IS_TESTING_ENV) {
    console.error(error);
  }
}

export function useAsync<
  ReturnType,
  Parameters extends any[] = any[],
  ErrorType = any
>(
  options: UseAsyncOptions<ReturnType, Parameters>
): UseAsyncResult<ReturnType, Parameters, ErrorType> {
  const data = ref<ReturnType>();
  const error = ref<any>();
  const loading = ref(false);
  const status = ref<'idle' | 'pending' | 'success' | 'error'>('idle');

  const {
    fn,
    onError = defaultErrorHandler,
    onSuccess = () => {},
    onSettled = () => {},
  } = options;

  async function execute(...args: Parameters) {
    try {
      status.value = 'pending';
      loading.value = true;
      data.value = await fn(...args);
      if (!data.value) return;
      onSuccess(data.value);
      status.value = 'success';
    } catch (err) {
      error.value = err;
      onError(err);
      status.value = 'error';
    } finally {
      loading.value = false;
      onSettled();
    }
  }

  return { data, error, loading, status, execute };
}
