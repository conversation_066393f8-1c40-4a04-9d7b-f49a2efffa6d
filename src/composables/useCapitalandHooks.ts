import { computed, ref, watch, onBeforeUnmount } from 'vue';
import { useDialogStore, useMapStore, useUserStore } from '@stores';
import { delay, useTick } from '@composables';
import { IAmenities } from '@types';
import { LngLatBounds } from 'maplibre-gl';
import turfDistance from '@turf/distance';
import dayjs from 'dayjs';
import LydenWoods1 from '../components/MapPopup/geojson/lydenwoods_1.json';
import LydenWoods2 from '../components/MapPopup/geojson/lydenwoods_2.json';
import LydenWoods3 from '../components/MapPopup/geojson/lydenwoods_3.json';
import LydenWoods4 from '../components/MapPopup/geojson/lydenwoods_4.json';

const AMENITY_RANGE_METERS = 40;
const DAYS_OF_WEEK = {
  0: 'sunday',
  1: 'monday',
  2: 'tuesday',
  3: 'wednesday',
  4: 'thursday',
  5: 'friday',
  6: 'saturday',
} as const;

type DayOfWeek = keyof typeof DAYS_OF_WEEK;

const GEOJSON = {
  lydenwoods_1: LydenWoods1,
  lydenwoods_2: LydenWoods2,
  lydenwoods_3: LydenWoods3,
  lydenwoods_4: LydenWoods4,
} as const;

export function useCapitalandHooks() {
  const storeUser = useUserStore();
  const storeMap = useMapStore();
  const storeDialog = useDialogStore();

  const { capitalandAmenities, isEnabledGPS, capitalandGeneoCoin, settings } =
    storeToRefs(storeUser);
  const { lastLocations, mapIns } = storeToRefs(storeMap);
  const { now } = useTick();
  const { openDialog } = useMicroRoute();

  const mapBounds = ref<LngLatBounds>();

  const geojsonKey = computed(
    () => settings.value?.capita_land.geojson_file || 'lydenwoods_1'
  );

  const currentGeojson = computed(() => {
    return (
      GEOJSON[geojsonKey.value as keyof typeof GEOJSON] || GEOJSON.lydenwoods_1
    );
  });

  const currentLydenwoodsCoin = computed(() => {
    return capitalandGeneoCoin.value.filter(
      (c) => c.geojson_file === geojsonKey.value
    );
  });

  const getCoinByStatus = (status: string) => {
    const coins = currentLydenwoodsCoin.value.filter(
      (c) => c.status === status
    );
    return coins.length > 0 ? coins[0] : null;
  };

  const verifyingCoin = computed(() => getCoinByStatus('verifying'));
  const forfeitedCoin = computed(() => getCoinByStatus('forfeited'));
  const foundCoin = computed(() => getCoinByStatus('found'));
  const scheduledCoin = computed(() => getCoinByStatus('scheduled'));
  const ongoingCoin = computed(() => getCoinByStatus('ongoing'));

  const hasLydenWoodsOngoingCoins = computed(() => {
    return (
      !!ongoingCoin.value &&
      ongoingCoin.value.status === 'ongoing' &&
      ongoingCoin.value.geojson_file === geojsonKey.value
    );
  });

  const ongoingCoinHints = computed(() => {
    return ongoingCoin.value?.hints || [];
  });

  const allHintReleased = computed(() => {
    return ongoingCoin.value?.hints.every((h) => h.unlocked_at);
  });

  const socialHints = computed(() => {
    return ongoingCoin.value?.hints.filter((h) => h.is_social_media) || [];
  });

  const upcomingSocialHints = computed(() => {
    const hints = socialHints.value;
    return hints.filter(
      (h) => +new Date(h.available_at) > +new Date(now.value)
    );
  });

  const nextSocialHintReleaseTime = computed(() => {
    const hints = socialHints.value;
    if (hints.length === 0) return null;
    const nextHint = hints.find((h) => +new Date(h.available_at) > now.value);
    if (!nextHint) return null;
    return new Date(nextHint.available_at);
  });

  const isAllSocialHintsReleased = computed(() => {
    return socialHints.value.every((h) => h.unlocked_at);
  });

  const today = computed(() => {
    const currentTime = new Date(now.value);
    const d = dayjs(currentTime).get('day') as DayOfWeek;
    return DAYS_OF_WEEK[d];
  });

  const getGeojsonNumber = (geojsonKey: string): number => {
    return Number(geojsonKey.replace('lydenwoods_', ''));
  };

  const calculateDistance = (
    userLocation: number[],
    amenityLocation: number[]
  ): number | null => {
    if (!userLocation.every(Boolean)) return null;

    return turfDistance(
      [userLocation[0], userLocation[1]],
      [amenityLocation[0], amenityLocation[1]],
      { units: 'meters' }
    );
  };

  const getAmenityIcon = (
    amenity: IAmenities,
    distanceInMeters: number | null
  ): string => {
    const { claimed_at, type } = amenity;

    const isWithinRange =
      distanceInMeters !== null && distanceInMeters <= AMENITY_RANGE_METERS;

    const suffix = !!claimed_at ? 'visited' : isWithinRange ? 'active' : 'away';

    return `${type}-${suffix}`;
  };

  // const getClosingTime = (amenity: any, currentTime: Date): string => {
  //   const dayOfWeek = dayjs(currentTime).get('day') as DayOfWeek;
  //   const dayKey = DAYS_OF_WEEK[dayOfWeek];
  //   const openingHours = amenity.display_opening_hours[dayKey];
  //   return openingHours?.split(' - ')[1] || '';
  // };

  const processAmenity = (
    amenity: IAmenities,
    userLocation: number[]
    // currentTime: Date
  ): IAmenities => {
    const distanceInMeters = calculateDistance(
      userLocation,
      amenity.location.coordinates
    );
    const canCheckIn =
      distanceInMeters !== null && distanceInMeters <= AMENITY_RANGE_METERS;
    const icon = getAmenityIcon(amenity, distanceInMeters);
    // const closed_at = getClosingTime(amenity, currentTime);

    return {
      ...amenity,
      icon,
      distanceInMeters,
      canCheckIn,
      amenities: true,
      // closed_at,
    };
  };

  const sortAmenitiesByDistance = (a: IAmenities, b: IAmenities): number => {
    if (a.distanceInMeters === null) return 1;
    if (b.distanceInMeters === null) return -1;
    return a.distanceInMeters - b.distanceInMeters;
  };

  const amenitiesMissions = computed(() => {
    const userLocation = lastLocations.value;
    // const currentTime = new Date(now.value);

    return capitalandAmenities.value
      .map((amenity) => processAmenity(amenity, userLocation))
      .sort(sortAmenitiesByDistance);
  });

  const nearestAmenity = computed(() => {
    if (!isEnabledGPS.value || !capitalandAmenities.value.length) {
      return null;
    }

    const nearbyAmenities = amenitiesMissions.value.filter(
      (amenity) =>
        amenity.distanceInMeters !== null &&
        amenity.distanceInMeters <= AMENITY_RANGE_METERS
    );

    return nearbyAmenities[0] || null;
  });

  function firstInstuctor() {
    storeDialog.openUnifyInstructor('sqkii', {
      sequences: [
        {
          backdropCss: {
            clipPath:
              'polygon(0% 0%, 0% 100%, 5% 100%, 5% 20%, 95% 20%, 95% 65%, 0% 65%, 0% 100%, 100% 100%, 100% 0%)',
          },
          message:
            'The LyndenWoods Coins will be hidden within this boundary that is not shrinkable. <br/><br/>Remember to keep a lookout on Sqkii’s social media for hints to help you find the LyndenWoods Coin! <br/><br/> (Tap anywhere to continue.)',
          actions: {
            cb: async (next: () => void) => {
              await next();
              storeMap.mapIns?.zoomTo(16.5);
              await delay(500);
              storeDialog.hiddenAmenity = false;
            },
          },
        },
        {
          backdropCss: {
            clipPath:
              'polygon(0% 0%, 0% 100%, 5% 100%, 5% 20%, 95% 20%, 95% 65%, 0% 65%, 0% 100%, 100% 100%, 100% 0%)',
          },
          message:
            'Visit Science Park 1’s many amenities including F&B and retail outlets for extra Crystals! <br/><br/>Psst, remember to hunt for the Science Park Coins hidden within the silver circles along the way. <br/><br/>(Tap anywhere to continue.)',
          actions: {
            cb: async (next: () => void) => {
              await next();
              storeDialog.highLightedHunt = true;
            },
          },
        },
        {
          message:
            'Daily rewards are back — check in at Science Park 1 daily to get more Crystals. <br/> (Tap anywhere to continue.)',
          actions: {
            cb: async (next: () => void) => {
              await next();
              await storeUser.updateOnboarding('capitaland_onboarding_journey');
              storeDialog.highLightedHunt = false;
              openDialog('capitaland_daily_reward');
            },
          },
        },
      ],
    });
  }

  const updateMapBounds = () => {
    if (mapIns.value) {
      mapBounds.value = mapIns.value.getBounds();
    }
  };

  const setupMapListeners = () => {
    if (!mapIns.value) return;

    updateMapBounds();

    mapIns.value.on('moveend', updateMapBounds);
    mapIns.value.on('zoomend', updateMapBounds);
    mapIns.value.on('resize', updateMapBounds);
  };

  const cleanupMapListeners = () => {
    if (!mapIns.value) return;

    mapIns.value.off('moveend', updateMapBounds);
    mapIns.value.off('zoomend', updateMapBounds);
    mapIns.value.off('resize', updateMapBounds);
  };

  watch(
    mapIns,
    (newMapIns, oldMapIns) => {
      if (oldMapIns) {
        cleanupMapListeners();
      }
      if (newMapIns) {
        setupMapListeners();
      }
    },
    { immediate: true }
  );

  const isGeojsonInViewport = computed(() => {
    if (!mapBounds.value || !currentGeojson.value) return false;

    const bounds = mapBounds.value;
    const geojson = currentGeojson.value;

    // Extract coordinates from GeoJSON - handle the existing structure
    const coordinates = geojson.features?.[0]?.geometry?.coordinates?.[0];
    if (!coordinates || !Array.isArray(coordinates)) return false;

    // Check if any coordinate is within viewport bounds
    const isIntersecting = coordinates.some((coord: any) => {
      if (!Array.isArray(coord) || coord.length < 2) return false;
      const [lng, lat] = coord;
      return (
        lng >= bounds.getWest() &&
        lng <= bounds.getEast() &&
        lat >= bounds.getSouth() &&
        lat <= bounds.getNorth()
      );
    });

    // Also check if viewport is completely inside the geojson bounds
    const validCoords = coordinates.filter(
      (coord: any) => Array.isArray(coord) && coord.length >= 2
    );

    if (validCoords.length === 0) return isIntersecting;

    const geoBounds = {
      west: Math.min(...validCoords.map((c: any) => c[0])),
      east: Math.max(...validCoords.map((c: any) => c[0])),
      south: Math.min(...validCoords.map((c: any) => c[1])),
      north: Math.max(...validCoords.map((c: any) => c[1])),
    };

    const viewportInside =
      bounds.getWest() >= geoBounds.west &&
      bounds.getEast() <= geoBounds.east &&
      bounds.getSouth() >= geoBounds.south &&
      bounds.getNorth() <= geoBounds.north;

    return isIntersecting || viewportInside;
  });

  onBeforeUnmount(() => {
    cleanupMapListeners();
  });

  return {
    scheduledCoin,
    verifyingCoin,
    forfeitedCoin,
    foundCoin,
    ongoingCoin,
    ongoingCoinHints,
    allHintReleased,
    amenitiesMissions,
    nearestAmenity,
    today,
    currentGeojson,
    geojsonKey,
    isGeojsonInViewport,
    nextSocialHintReleaseTime,
    socialHints,
    isAllSocialHintsReleased,
    upcomingSocialHints,
    hasLydenWoodsOngoingCoins,
    calculateDistance,
    firstInstuctor,
    getGeojsonNumber,
  };
}
