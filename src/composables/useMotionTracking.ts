import { errorNotify, successNotify } from '@helpers';
import { DAILYMISSION } from '@repositories';
import { useMapStore, useUserStore } from '@stores';
import { throttle } from 'lodash';

interface Acceleration {
  x: number;
  y: number;
  z: number;
}

export function useMotionTracking() {
  const storeMap = useMapStore();
  const storeUser = useUserStore();

  const { user, tempStepsWalking, currentStep, stepUpdated } =
    storeToRefs(storeUser);
  const { lastLocations } = storeToRefs(storeMap);
  const { t } = useI18n();

  const THRESHOLD = 10.5;

  const stepDetected = ref(false);
  const stepCounting = ref(currentStep.value);
  const lastAcc = ref<Acceleration>({ x: 0, y: 0, z: 0 });
  const isAllow = computed(() => user.value?.setting?.pedometer);
  const showedNotify = ref(LocalStorage.getItem('showedNotify') || false);

  function lowPassFilter(
    acc: Acceleration,
    lastAcc: Acceleration,
    alpha = 0.8
  ) {
    return {
      x: alpha * lastAcc.x + (1 - alpha) * acc.x,
      y: alpha * lastAcc.y + (1 - alpha) * acc.y,
      z: alpha * lastAcc.z + (1 - alpha) * acc.z,
    };
  }

  function calculateMagnitude(acc: Acceleration) {
    return Math.sqrt(acc.x * acc.x + acc.y * acc.y + acc.z * acc.z);
  }

  function getAcceleration(event: DeviceMotionEvent): Acceleration {
    const accGravity = event.accelerationIncludingGravity;
    return {
      x: accGravity?.x || 0,
      y: accGravity?.y || 0,
      z: accGravity?.z || 0,
    };
  }

  async function handleMotion(event: DeviceMotionEvent) {
    if (!isAllow.value) return;
    const acc = getAcceleration(event);
    const filteredAcc = lowPassFilter(acc, lastAcc.value);
    const magnitude = calculateMagnitude(filteredAcc);

    if (magnitude > THRESHOLD && !stepDetected.value) {
      storeUser.tempStepsWalking += 1;
      stepCounting.value += 1;
      storeUser.currentStep = stepCounting.value;
      throttleUserWalking();
      stepDetected.value = true;
    } else if (magnitude < THRESHOLD && stepDetected.value) {
      stepDetected.value = false;
    }

    lastAcc.value = filteredAcc;
  }

  function handleDeviceOrientation(e: DeviceOrientationEvent) {
    const alpha = e.alpha;
    if (alpha === null) return;

    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW', 'N'];
    const degrees = Math.floor((alpha + 22.5) / 45);

    storeMap.directionWalking = directions[degrees];
    storeMap.headingDegrees = degrees * 45;
  }

  async function requestDeviceMotion() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    if (typeof DeviceMotionEvent.requestPermission === 'function') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      DeviceMotionEvent.requestPermission().then(
        async (permissionState: string) => {
          if (permissionState === 'granted') {
            await successRequestPermisson();
            window.addEventListener('devicemotion', handleMotion);
          } else errorRequestPermisson();
        }
      );
    } else {
      await successRequestPermisson();
      window.addEventListener('devicemotion', handleMotion);
    }
  }

  function requestDeviceOrientation() {
    if ('DeviceOrientationEvent' in window)
      window.addEventListener('deviceorientation', handleDeviceOrientation);
    else errorRequestPermisson();
  }

  const throttleUserWalking = throttle(userWalking, 3000);

  async function userWalking() {
    try {
      const [lng, lat] = lastLocations.value;
      const { data } = await DAILYMISSION.walk(
        lng,
        lat,
        tempStepsWalking.value
      );
      storeUser.updatePedometerProgress(data.pedometer_progress);
    } catch (error) {
      console.error('error', error);
    } finally {
      storeUser.tempStepsWalking = 0;
    }
  }

  async function errorRequestPermisson() {
    await storeUser.updateUserSettings('pedometer', false);
    errorNotify({
      message: t('NOTIFY_DISABLED_PEDOMETER'),
    });
    console.error('error', 'Permission denied for motion sensor.');
  }

  async function successRequestPermisson() {
    await storeUser.updateUserSettings('pedometer', true);
    if (!showedNotify.value) {
      successNotify({
        message: t('NOTIFY_ALLOW_PEDOMETER'),
      });
      LocalStorage.setItem('showedNotify', true);
      showedNotify.value = true;
    }
  }

  watch(stepUpdated, (val) => {
    if (val) {
      stepCounting.value = currentStep.value;
      storeUser.stepUpdated = false;
    }
  });

  onBeforeMount(() => {
    userWalking();
  });

  onBeforeUnmount(async () => {
    await userWalking();
    window.removeEventListener('devicemotion', handleMotion);
    window.removeEventListener('deviceorientation', handleDeviceOrientation);
  });

  return {
    requestDeviceMotion,
    requestDeviceOrientation,
    userWalking,
    isAllow,
  };
}
