import { USER } from '@repositories';

export function useTrackData() {
  const visitAt = ref(Date.now());

  // const uaParser = new userAgent();
  // const parser = uaParser.getResult();

  // const defaultPayload = {
  //   build_version: process.env.BUILD_VERSION,
  //   device: parser.device.model,
  //   os_name: parser.os.name,
  //   os_version: parser.os.version,
  //   browser_name: parser.browser.name,
  //   browser_version: parser.browser.version,
  // };

  async function trackTime(type: string, data?: Record<string, unknown>) {
    try {
      const time = +(Date.now() - visitAt.value).toFixed(0);
      if (time < 0) return;

      await USER.trackData(type, {
        ...data,
        time,
      });
    } catch (error) {
      // console.error('error', error);
    }
  }

  async function track(type: string, data?: Record<string, unknown>) {
    try {
      await USER.trackData(type, {
        ...data,
      });
    } catch (error) {
      // console.error('error', error);
    }
  }

  return {
    trackTime,
    track,
  };
}
