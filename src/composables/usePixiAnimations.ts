import {
  Assets,
  Application,
  AnimatedSprite,
  Spritesheet,
  Cache,
  utils,
} from 'pixi.js';

interface IOptions {
  id: HTMLCanvasElement;
  name: string;
  json: string;
  animationSpeed?: number;
  size?: 'cover' | 'contain';
  width?: number;
  height?: number;
}

export async function usePixiAnimations(options: IOptions) {
  const app = new Application({
    width: options.width || window.innerWidth,
    height: options.height || window.innerHeight,
    antialias: true,
    autoDensity: true,
    backgroundAlpha: 0,
    resolution: Math.max(window.devicePixelRatio, 2),
    view: options.id,
  });

  const anims = `anims/${options.json}`;

  function clearCache() {
    utils.clearTextureCache();
    Cache.remove(anims);
    Cache.reset();
  }

  if (Cache.has(anims)) clearCache();

  const spritesheet = (await Assets.load(anims)) as Spritesheet;

  const sprite = new AnimatedSprite(spritesheet.animations[options.name]);

  const textureRatio = sprite.texture.width / sprite.texture.height;
  const screenRatio = app.screen.width / app.screen.height;

  if (
    (textureRatio >= screenRatio && options.size === 'contain') ||
    (textureRatio < screenRatio && options.size === 'cover')
  )
    sprite.scale.set(app.screen.width / sprite.texture.width);
  else sprite.scale.set(app.screen.height / sprite.texture.height);

  // position
  sprite.anchor.set(0.5, 0.5);
  sprite.position.set(app.screen.width / 2, app.screen.height / 2);

  // animation speed
  sprite.animationSpeed = options.animationSpeed || 0.5;
  sprite.play();

  app.stage.addChild(sprite);

  return {
    app,
    clearCache,
  };
}
