import { computed, type ComputedRef } from 'vue';
import { storeToRefs } from 'pinia';
import { useBAStore } from '@stores';
import { groupBy, minBy } from 'lodash';
import { IBrandAction, RmiOutlet } from '@types';
import { useBrandActions } from '@composables';
import dayjs from 'dayjs';

type BrandActionsByUniqueId = Record<string, IBrandAction[]>;
type MinRewardData = Record<string, IBrandAction | undefined>;
interface RewardCalculator {
  reward: number;
  haveMultiplier: boolean | undefined;
  finalReward: string | number;
}
type MinRewardCalculators = Record<string, RewardCalculator>;

interface UseRMIReturn {
  brandByUniqueId: ComputedRef<BrandActionsByUniqueId>;
  minRewardData: ComputedRef<MinRewardData>;
  minReward: ComputedRef<MinRewardCalculators>;
  currentDay: ComputedRef<keyof RmiOutlet['open_hours']>;
}

const DAYS_OF_WEEK = [
  'sunday',
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
] as const;

export function useRMI(): UseRMIReturn {
  const storeBA = useBAStore();
  const brandHooks = useBrandActions();

  const { newBrandActions } = storeToRefs(storeBA);

  const brandByUniqueId = computed<BrandActionsByUniqueId>(() => {
    return groupBy(newBrandActions.value, 'brand_unique_id');
  });

  const { hasMultiplier, calculateReward } = brandHooks;

  const currentDay = computed(() => {
    const day = dayjs().day();
    return DAYS_OF_WEEK[day] as keyof RmiOutlet['open_hours'];
  });

  const minRewardData = computed<MinRewardData>(() => {
    const brandEntries = Object.entries(brandByUniqueId.value);

    return Object.fromEntries(
      brandEntries.map(([brandId, actions]) => {
        const minAction = minBy(
          actions,
          (action) => action.reward?.crystal ?? 0
        );
        return [brandId, minAction];
      })
    );
  });

  const minReward = computed<MinRewardCalculators>(() => {
    const brandEntries = Object.entries(minRewardData.value);

    return Object.fromEntries(
      brandEntries.map(([brandId, action]) => {
        if (!action) return [brandId, {} as RewardCalculator];

        const baseReward = action.reward?.crystal ?? 0;
        const haveMultiplier = hasMultiplier(action);
        const finalReward = calculateReward(action);

        return [
          brandId,
          {
            reward: baseReward,
            haveMultiplier,
            finalReward,
          },
        ];
      })
    );
  });

  return {
    brandByUniqueId,
    minRewardData,
    minReward,
    currentDay,
  };
}
