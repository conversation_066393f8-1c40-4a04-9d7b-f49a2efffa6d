export function useTicker(mode: 's' | 'm' = 's') {
  let secInterval: NodeJS.Timeout;
  const now = ref(Date.now());

  const stopTicker = () => {
    clearInterval(secInterval);
  };
  provide('now', now);
  onMounted(() => {
    secInterval = setInterval(
      () => {
        now.value = Date.now();
      },
      mode === 's' ? 1000 : 60000
    );
  });

  onBeforeUnmount(stopTicker);

  return {
    now,
    stopTicker,
  };
}

function useTick() {
  const now = inject<Ref<number>>('now') as Ref<number>;
  if (!now?.value) return useTicker();
  return { now };
}

export function delay(ms: number) {
  let timeoutId: NodeJS.Timeout;
  return new Promise((resolve) => {
    timeoutId = setTimeout(resolve, ms);
  }).finally(() => clearTimeout(timeoutId));
}

export { useTick };
