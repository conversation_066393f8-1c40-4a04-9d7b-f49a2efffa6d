import { useMapStore, useUserStore } from '@stores';
import { point } from '@turf/helpers';
import { useMapHelpers } from '@composables';
import { LngLatBoundsLike } from 'vue3-maplibre-gl';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import turfDistance from '@turf/distance';
import circle from '@turf/circle';
import bbox from '@turf/bbox';

export function useFreeShrinkCircle(highlight = true) {
  const storeMap = useMapStore();
  const storeUser = useUserStore();

  const { lastLocations, groupedSilverCoins } = storeToRefs(storeMap);
  const { onboarding } = storeToRefs(storeUser);
  const { makeSpecificationSource } = useMapHelpers();

  // const popup = ref<Popup | null>(null);

  const isFreeShrink = computed(() => {
    return !onboarding.value?.shrink_silver_coin;
  });

  const currentCircles = computed(() => {
    const pt = point(lastLocations.value);
    return groupedSilverCoins.value.ongoing
      .map((c) => {
        const { center, radius } = c.properties.circle;
        const distance = Math.max(
          0,
          turfDistance(lastLocations.value, [center.lng, center.lat], {
            units: 'meters',
          }) - radius
        ).toFixed(2);
        c.properties.distance = Number(distance);
        return c;
      })
      .sort((a, b) => a.properties.distance - b.properties.distance)
      .filter((coin) => booleanPointInPolygon(pt, coin as any));
  });

  const isInPolygon = computed(() => {
    return currentCircles.value.length > 0;
  });

  watch(isInPolygon, (value) => {
    if (!highlight) return;
    if (value && isFreeShrink.value) createHighlightCircle();
  });

  // watch(zoom, (value) => {
  //   if (!isInPolygon.value) return;
  //   // const target = value > 12 && value < 16;
  //   // if (target) renderPopup();
  //   // else popup.value?.remove();
  // });

  watch(isFreeShrink, (value) => {
    if (!value) removeHighlightedCircle();
  });

  function createHighlightCircle() {
    removeHighlightedCircle();

    const c = currentCircles.value[0];
    const { center, radius } = c.properties.circle;

    const source = makeSpecificationSource([c]);
    storeMap.mapIns?.addSource('source', source);
    storeMap.mapIns?.addLayer({
      id: 'free-shrink-circle',
      type: 'line',
      source,
      paint: {
        'line-color': '#12C8F2',
        'line-width': 4,
      },
    });
    storeMap.mapIns?.addLayer({
      id: 'free-shrink-circle-glow',
      type: 'line',
      source,
      paint: {
        'line-width': [
          'interpolate',
          ['linear'],
          ['zoom'],
          9,
          1,
          11,
          10,
          14,
          20,
        ],
        'line-blur': 10,
        'line-opacity': ['interpolate', ['linear'], ['zoom'], 9, 0.5, 11, 0.3],
        'line-color': '#ffffff',
      },
    });

    const _circle = circle([center.lng, center.lat], radius * 1.2, {
      units: 'meters',
    });
    const box = bbox(_circle);
    storeMap.mapIns?.fitBounds(box as LngLatBoundsLike);
  }

  function removeHighlightedCircle() {
    if (storeMap.mapIns?.getLayer('free-shrink-circle'))
      storeMap.mapIns?.removeLayer('free-shrink-circle');

    if (storeMap.mapIns?.getLayer('free-shrink-circle-glow'))
      storeMap.mapIns?.removeLayer('free-shrink-circle-glow');

    if (storeMap.mapIns?.getSource('source'))
      storeMap.mapIns?.removeSource('source');
  }

  // function renderPopup() {
  //   if (!isInPolygon.value || !isEnabledGPS.value) return;
  //   if (popup.value) popup.value.remove();
  //   const [lng, lat] = lastLocations.value;
  //   const circle = currentCircles.value[0];

  //   // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //   // @ts-ignore
  //   popup.value = newPopUp({
  //     closeOnClick: false,
  //     closeButton: false,
  //     offset: [0, -20],
  //     anchor: 'bottom',
  //     className: 'user-location-popup',
  //     lngLat: [lng, lat],
  //     html: t('FREE_SHRINK_POPUP', {
  //       REWARD: circle.properties.reward,
  //     }),
  //   });
  // }

  return {
    isFreeShrink,
    currentCircles,
    lastLocations,
  };
}
