import { useBrandSovStore } from '@stores';
import { random } from 'lodash';
import { useTrackData } from './useTrackData';
import { getSovAsset } from '@helpers';
import type { ComponentId } from '@types';
import { BRAND_SOV } from '@constants';

// Types
interface BrandSovResult {
  brand_unique_id: string | null;
  asset_pack: string | null;
  getAsset: (suffix?: string, extension?: string) => string;
  tracking: (brand_action_id?: string) => void;
}

type RandomResult<T extends ComponentId> = Record<T, BrandSovResult>;

type ComponentSetting<T extends ComponentId> = T | { id: T; track?: boolean };

type BrandSovReference = Ref<Partial<RandomResult<ComponentId>>>;

const DEFAULT_BRAND = BRAND_SOV.DBS;

const SPECIAL_BRAND_IDS = ['singapore_police_force', 'tiger_broker'] as const;

// Helper functions
function createEmptyResult<T extends ComponentId>(
  componentIds: Array<ComponentSetting<T>>
): RandomResult<T> {
  return componentIds.reduce((acc, setting) => {
    const id = typeof setting === 'string' ? setting : setting.id;
    acc[id] = {
      brand_unique_id: null,
      asset_pack: null,
      getAsset: () => '',
      tracking: () => {},
    };
    return acc;
  }, {} as RandomResult<T>);
}

function extractComponentConfig<T extends ComponentId>(
  setting: ComponentSetting<T>
) {
  return {
    id: typeof setting === 'string' ? setting : setting.id,
    shouldTrack: typeof setting === 'object' ? setting.track !== false : true,
  };
}

function createBrandSovResult(
  brandId: string,
  assetPack: string,
  track: (event: string, data: Record<string, unknown>) => void,
  componentId: string
): BrandSovResult {
  return {
    brand_unique_id: brandId,
    asset_pack: assetPack,
    getAsset: (suffix = '') => getSovAsset(assetPack, brandId, suffix),
    tracking: (brand_action_id?: string) => {
      track('brand_sov', {
        component_id: componentId,
        result: brand_action_id,
        success: true,
      });
    },
  };
}

function trackBrandSov(
  track: (event: string, data: Record<string, unknown>) => void,
  componentId: string,
  result: string,
  success: boolean,
  shouldTrack: boolean
) {
  if (shouldTrack) {
    track('brand_sov', {
      component_id: componentId,
      result,
      success,
    });
  }
}
export function useBrandSov<T extends ComponentId>(
  ...componentIds: Array<ComponentSetting<T>>
) {
  const brandSovStore = useBrandSovStore();
  const { track } = useTrackData();
  const ready = ref(false);

  // Initialize with empty results
  const randomResult = ref<RandomResult<T>>(createEmptyResult(componentIds));
  const { ready: configReady, probs, assetPacks } = storeToRefs(brandSovStore);

  // Process single component SOV
  function processComponentSov(setting: ComponentSetting<T>) {
    const { id, shouldTrack } = extractComponentConfig(setting);
    const probData = probs.value[id];
    const asset_pack = assetPacks.value[id];

    // Handle missing probability data
    if (!probData) {
      const result = createBrandSovResult(DEFAULT_BRAND, asset_pack, track, id);
      randomResult.value[id] = result;
      trackBrandSov(track, id, DEFAULT_BRAND, false, shouldTrack);
      return;
    }

    const {
      prob,
      randomRange: [min, max],
    } = probData;

    try {
      if (!prob) throw new Error('No probability data');

      const randomValue = random(min, max, true);
      const matchingProbIndex = prob.findIndex(([, p]) => randomValue < p);
      const selectedPair = prob[matchingProbIndex];

      if (!selectedPair) throw new Error('No matching probability pair');

      const [brandId] = selectedPair;
      const result = createBrandSovResult(brandId, asset_pack, track, id);
      randomResult.value[id] = result;

      trackBrandSov(track, id, brandId, true, shouldTrack);
    } catch {
      // Fallback to default brand
      const result = createBrandSovResult(DEFAULT_BRAND, asset_pack, track, id);
      randomResult.value[id] = result;

      trackBrandSov(track, id, DEFAULT_BRAND, false, shouldTrack);
    }
  }

  // Main randomization function
  function randomSov() {
    componentIds.forEach(processComponentSov);
    ready.value = true;
  }

  // Watch for config readiness
  watch(
    configReady,
    (value) => {
      if (value) randomSov();
    },
    {
      immediate: true,
    }
  );

  return {
    ready,
    randomResult,
  };
}

export function useBrandDoodleSov({
  ready,
  randomResult,
  referenceId,
  doodleId,
}: {
  ready: Ref<boolean>;
  randomResult: BrandSovReference;
  referenceId: ComponentId;
  doodleId: ComponentId;
}) {
  const _ready = ref(false);
  const doodle = ref(randomResult.value[doodleId]);

  watchEffect(() => {
    if (!ready.value) return;

    const referenceResult = randomResult.value[referenceId];
    const doodleResult = randomResult.value[doodleId];

    if (!referenceResult?.brand_unique_id || !doodleResult?.brand_unique_id) {
      return;
    }

    const referenceBrandId = referenceResult.brand_unique_id;

    // Determine the doodle brand ID based on special cases
    const doodleBrandId = SPECIAL_BRAND_IDS.includes(referenceBrandId as any)
      ? referenceBrandId
      : doodleResult.brand_unique_id;

    // Update doodle with the determined brand ID
    doodle.value = { ...doodleResult };
    doodle.value.brand_unique_id = doodleBrandId;
    _ready.value = true;

    // Track the doodle SOV
    randomResult.value[doodleId]?.tracking?.(doodleBrandId);
  });

  return doodle;
}
