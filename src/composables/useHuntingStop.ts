import { useMapStore, useUserStore } from '@stores';
import { HUNTING_STOP } from '@repositories';
import { HuntingStop, IAPIResponseError } from '@types';
import { HuntingStopRewardType } from '@constants';
import distance from '@turf/distance';

const CONFIG = {
  ALLOWED_DISTANCE: 40000, // meters
} as const;

enum ErrorType {
  // NEED_VERIFY = 'need_verify',
  GPS_DISABLED = 'gps_disabled',
  REACHED_DAILY_LIMIT = 'reached_daily_limit',
  SPEED_LIMIT = 'speed_limit',
  OUT_OF_RANGE = 'out_of_range',
}

const ERROR_MESSAGES: Record<ErrorType, string> = {
  // [ErrorType.NEED_VERIFY]: 'HUNTING_STOP_NEED_LOGIN',
  [ErrorType.REACHED_DAILY_LIMIT]: 'HUNTING_STOP_REACHED_DAILY_LIMIT',
  [ErrorType.SPEED_LIMIT]: 'HUNTING_STOP_SPEED_LIMIT',
  [ErrorType.OUT_OF_RANGE]: 'HUNTING_STOP_OUT_OF_RANGE',
  [ErrorType.GPS_DISABLED]: 'HUNTING_STOP_GPS_DISABLED',
} as const;

const REWARD_ASSET_NAMES: Record<HuntingStopRewardType, string> = {
  [HuntingStopRewardType.SHRINK]: 'silver-shrink',
  [HuntingStopRewardType.SHRINK_LITE]: 'silver-shrink-lite',
  [HuntingStopRewardType.COIN_SONAR]: 'metal-sonar',
  [HuntingStopRewardType.METAL_DETECTOR]: 'metal-detector',
  [HuntingStopRewardType.BEACON]: 'beacon',
  [HuntingStopRewardType.CRYSTAL]: 'dbs_crystal',
} as const;

const huntingStopInfo = ref<HuntingStop>();

export function useHuntingStop() {
  const storeMap = useMapStore();
  const storeUser = useUserStore();

  const { t } = useI18n();
  const { openDialog } = useMicroRoute();
  const { isEnabledGPS, user } = storeToRefs(storeUser);
  const { lastLocations, isReachSpeedLimit } = storeToRefs(storeMap);

  const errorMessage = ref('');

  const calculateDistance = (
    userCoords: number[],
    stopCoords: [number, number]
  ): number => {
    return distance(userCoords, stopCoords, { units: 'meters' });
  };

  const isWithinAllowedDistance = computed(() => {
    if (!huntingStopInfo.value) {
      console.log('isWithinAllowedDistance: huntingStopInfo is null/undefined');
      return false;
    }

    const [lng, lat] = lastLocations.value;

    // Check if user location is valid
    if (!lng || !lat || (lng === 0 && lat === 0)) {
      console.log('isWithinAllowedDistance: invalid user location', [lng, lat]);
      return false;
    }

    const { location } = huntingStopInfo.value;
    const distanceInMeters = calculateDistance(
      [lng, lat],
      [location.lng, location.lat]
    );

    console.log('Distance calculation:', {
      userLocation: [lng, lat],
      stopLocation: [location.lng, location.lat],
      distanceInMeters,
      allowedDistance: CONFIG.ALLOWED_DISTANCE,
      isWithin: distanceInMeters <= CONFIG.ALLOWED_DISTANCE,
    });

    return distanceInMeters <= CONFIG.ALLOWED_DISTANCE;
  });

  const alertMessage = computed(() => {
    if (user.value?.hunting_stop_reached_limit) {
      return ERROR_MESSAGES[ErrorType.REACHED_DAILY_LIMIT];
    }

    if (!isEnabledGPS.value) {
      return ERROR_MESSAGES[ErrorType.GPS_DISABLED];
    }

    if (isReachSpeedLimit.value) {
      return ERROR_MESSAGES[ErrorType.SPEED_LIMIT];
    }

    if (!isWithinAllowedDistance.value) {
      return ERROR_MESSAGES[ErrorType.OUT_OF_RANGE];
    }
    return '';
  });

  const hasErrorMsg = computed(
    () => Boolean(alertMessage.value) || Boolean(errorMessage.value)
  );

  const claimReward = async (uniqueId: string) => {
    try {
      errorMessage.value = '';
      const { data } = await HUNTING_STOP.claimReward(uniqueId);
      await Promise.all([storeUser.fetchUser(), storeUser.fetchInventory()]);
      return data;
    } catch (error) {
      const { error_message } = error as IAPIResponseError;
      errorMessage.value = t(error_message);
    }
  };

  const setHuntingStopInfo = (data: HuntingStop) => {
    huntingStopInfo.value = data;
  };

  const getHuntingStopInfo = async (id: string) => {
    try {
      errorMessage.value = '';
      const { data } = await HUNTING_STOP.getByUniqueId(id);
      huntingStopInfo.value = data;
      return data;
    } catch (error) {
      const { error_message } = error as IAPIResponseError;
      errorMessage.value = t(error_message);
    }
  };

  const handleUIAction = (actionId: string) => {
    const actionMap: Record<string, () => void> = {
      LOGIN: () => openDialog('login'),
      GPS: () => openDialog('gps'),
    };

    const action = actionMap[actionId];
    if (action) action();
  };

  const handleClick = (event: Event) => {
    const target = event.target as HTMLElement;
    if (target.id) handleUIAction(target.id);
  };

  onMounted(async () => {
    await nextTick();
    addEventListener('click', handleClick);
  });

  onBeforeUnmount(() => {
    removeEventListener('click', handleClick);
    huntingStopInfo.value = undefined;
  });

  return {
    huntingStopInfo,
    errorMessage,
    alertMessage,
    hasErrorMsg,
    isWithinAllowedDistance,
    rewardAssetNames: REWARD_ASSET_NAMES,
    claimReward,
    getHuntingStopInfo,
    setHuntingStopInfo,
  };
}
