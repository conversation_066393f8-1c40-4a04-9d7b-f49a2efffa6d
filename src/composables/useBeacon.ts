import { useDialogStore, useMapStore, useUserStore } from '@stores';
import { useInventory, useMapHelpers, useTick } from '@composables';
import { BEACON } from '@repositories';
import { point } from '@turf/helpers';
import type { IBeacon } from '@types';
import distance from '@turf/distance';

const BEACON_SORT_PRIORITY = {
  OWNER_PRIORITY: 1,
  NON_OWNER_PRIORITY: 0,
} as const;

export function useBeacon() {
  const storeDialog = useDialogStore();
  const storeMap = useMapStore();
  const storeUser = useUserStore();

  const { settings, activatingBeacons, user, features } =
    storeToRefs(storeUser);
  const { lastLocations } = storeToRefs(storeMap);
  const { now } = useTick();
  const { fitBounds } = useMapHelpers();
  const { itemsQuickView } = useInventory();

  const beaconRadius = computed(() => settings.value?.beacon?.radius || 0);
  const canTriggerBeacon = computed(() => {
    return itemsQuickView.value.beacon.length > 0;
  });

  const loading = ref(false);

  const calculateBeaconDistance = (beacon: IBeacon): number => {
    const [lng, lat] = lastLocations.value;
    return distance(
      point([lng, lat]),
      point([beacon.location.lng, beacon.location.lat]),
      { units: 'meters' }
    );
  };

  const isBeaconOwner = (beacon: IBeacon): boolean => {
    return beacon.user === user.value?.id;
  };

  const isBeaconActive = (beacon: IBeacon): boolean => {
    return new Date(beacon.expire_at).getTime() > now.value;
  };

  const activatingBeaconsData = computed(() => {
    return activatingBeacons.value
      .map((beacon) => ({
        ...beacon,
        is_owner: isBeaconOwner(beacon)
          ? BEACON_SORT_PRIORITY.OWNER_PRIORITY
          : BEACON_SORT_PRIORITY.NON_OWNER_PRIORITY,
        distance: calculateBeaconDistance(beacon),
      }))
      .filter(isBeaconActive);
  });

  const sortBeaconsByPriority = (beacons: IBeacon[]): IBeacon[] => {
    return beacons.sort((a, b) => {
      // Primary sort: Owner priority (owners first)
      const ownerDiff = b.is_owner - a.is_owner;
      if (ownerDiff !== 0) return ownerDiff;

      // Secondary sort: Expiration time (later expiration first)
      return new Date(b.expire_at).getTime() - new Date(a.expire_at).getTime();
    });
  };

  const sortBeaconsByDistance = (beacons: IBeacon[]): IBeacon[] => {
    return beacons.sort((a, b) => {
      // Primary sort: Distance (closer first)
      const distanceDiff = a.distance - b.distance;
      if (distanceDiff !== 0) return distanceDiff;

      // Secondary sort: Owner priority (owners first)
      const ownerDiff = b.is_owner - a.is_owner;
      if (ownerDiff !== 0) return ownerDiff;

      // Tertiary sort: Expiration time (later expiration first)
      return new Date(b.expire_at).getTime() - new Date(a.expire_at).getTime();
    });
  };

  const stayingInsideBeacons = computed(() => {
    const insideBeacons = activatingBeaconsData.value.filter(
      (beacon) => beacon.distance <= beacon.radius
    );

    return sortBeaconsByPriority([...insideBeacons]);
  });

  const getOptimalBeacon = (): IBeacon | null => {
    // If user is inside any beacon, prioritize owned beacons first
    if (stayingInsideBeacons.value.length > 0) {
      const ownedBeacons = stayingInsideBeacons.value.filter(isBeaconOwner);
      return ownedBeacons.length > 0
        ? ownedBeacons[0]
        : stayingInsideBeacons.value[0];
    }

    // If no beacons are in range, find the closest one with proper priority
    const sortedByDistance = sortBeaconsByDistance([
      ...activatingBeaconsData.value,
    ]);
    return sortedByDistance[0] || null;
  };

  const beacons = computed(() => {
    const optimalBeacon = getOptimalBeacon();
    return optimalBeacon ? [optimalBeacon] : [];
  });

  const inUsingBeacon = computed(() => {
    if (!features.value?.beacon) return null;
    return beacons.value[0] || null;
  });

  const handleBackToMain = (): void => {
    storeDialog.showBeaconGUI = false;
  };

  const beaconFitBounds = (): void => {
    if (!beaconRadius.value || !lastLocations.value?.length) {
      console.warn('Cannot fit bounds: missing radius or location data');
      return;
    }

    const [lng, lat] = lastLocations.value;
    fitBounds({
      lng,
      lat,
      radius: beaconRadius.value * 1.2,
    });
  };

  const handleUseBeacon = async () => {
    try {
      loading.value = true;
      const [lng, lat] = lastLocations.value;
      const item_id = itemsQuickView.value.beacon[0]._id;

      await BEACON.use({ lng, lat, item_id });
      await Promise.all([
        storeUser.fetchUser(),
        storeUser.fetchBeacon(),
        storeUser.fetchInventory(),
      ]);
      await handleBackToMain();
    } catch (error) {
      console.error('Error using beacon:', error);
    } finally {
      loading.value = false;
    }
  };

  return {
    beaconRadius,
    loading,
    lastLocations,
    inUsingBeacon,
    beacons,
    stayingInsideBeacons,
    canTriggerBeacon,
    handleUseBeacon,
    handleBackToMain,
    beaconFitBounds,
  };
}
