import { useDialogStore, useMapStore, useUserStore } from '@stores';
import { useAsync, useTick } from '@composables';
import {
  convertTime,
  dateTimeFormat,
  isDifferentDay,
  timeCountDown,
  isSameDay,
  deleteEmptyFields,
} from '@helpers';
import { SILVER_COIN, USER } from '@repositories';
import { first } from 'lodash';
import type { IPayloadTrackUserLocation, ISilverCoin } from '@types';
import dayjs from 'dayjs';

export function useGlobal() {
  const storeUser = useUserStore();
  const storeDialog = useDialogStore();
  const storeMap = useMapStore();

  const {
    user,
    isSeasonStarting,
    seasonCode,
    hasTriggerGPS,
    contestList,
    settings,
    dataVerification,
    notifications,
    isEnabledGPS,
    isDifferentCountry,
    features,
    goldenCoinDropped,
  } = storeToRefs(storeUser);
  const { groupedSilverCoins, lastLocations } = storeToRefs(storeMap);
  const {
    welcomeSafetyDate,
    // welcomeSafetyTicked,
    welcomeGoldenDroppedTicked,
    dailyLoginTicked,
    hoursBonusTicked,
    gpsTapped,
    enterSerialNumberTicked,
    otpAnnouncementTicked,
    subDomainTicket,
  } = storeToRefs(storeDialog);
  const { now } = useTick();
  const { openDialog } = useMicroRoute();

  const holding_showed = ref(false);

  // Build update
  const triggerBuildUpdate = computed(() => {
    const version = process.env.BUILD_VERSION;

    if (!isSeasonStarting.value || !version) return false;
    return String(settings.value?.frontend_build.version) !== String(version);
  });

  // OTP Announcement
  const isTriggerOTPAnnouncement = computed(() => {
    if (!isSeasonStarting.value) return false;
    return (
      !user.value?.verified_mobile_number_at && !otpAnnouncementTicked.value
    );
  });

  // Announcement
  const isTriggerAnnouncement = computed(() => {
    return notifications.value.length > 0 && isSeasonStarting.value;
  });

  // Welcome Hunter
  const isTriggerWelcomeHunter = computed(() => {
    return !user.value?.onboarding?.first_entry && isSeasonStarting.value;
  });

  // Contest
  function mapContest() {
    if (!contestList.value.contests.length) return [];
    return contestList.value.contests.map((contest) => {
      const userContest = contestList.value.user_contests.find(
        (userContest) => userContest.contest === contest._id
      );

      return {
        ...contest,
        userContest,
      };
    });
  }

  const listContestStarting = computed(() => {
    const data = mapContest();
    return data.filter((contest) => +new Date(contest.end_at) > +new Date());
  });

  const popupContest = computed(() => {
    const popup = LocalStorage.getItem('contest_popup');
    return popup;
  });

  const isTriggerHolding = computed(
    () =>
      !isSeasonStarting.value &&
      settings.value?.holding_page_state &&
      settings.value?.holding_page_state !== 'none' &&
      !holding_showed.value
  );

  const listContestEnded = computed(() => {
    const dontShowToday = isSameDay(
      String(popupContest.value),
      new Date().toISOString()
    );

    if (!user.value || dontShowToday) return [];

    const data = mapContest();
    const date = +new Date();
    return data.filter(
      (contest) =>
        contest.userContest?.voted_at &&
        +new Date(contest.end_at) < date &&
        (!contest.show_result_until ||
          +new Date(contest.show_result_until) > date)
    );
  });

  // is logged
  const isLogged = computed(() => {
    return !!user.value?.mobile_number;
  });

  // GPS
  const isTriggerGPS = computed(() => {
    return !gpsTapped.value && !hasTriggerGPS.value && isSeasonStarting.value;
  });

  // SubDomain
  const isTriggerSubDomain = computed(() => {
    return (
      isEnabledGPS.value && isDifferentCountry.value && !subDomainTicket.value
    );
  });

  // Welcome Safety
  const isTriggerWelcomeSafety = computed(() => {
    // if (!isSeasonStarting.value) return false;
    if (!welcomeSafetyDate.value) return true;
    const date = new Date().toISOString();
    return isDifferentDay(date, welcomeSafetyDate.value.toString());
  });

  // Golden Coin Dropped
  const isTriggerGoldenCoinDropped = computed(() => {
    const welcomeGoldenShowAt = LocalStorage.getItem('welcomeGoldenShowAt');
    const date = new Date().toISOString();

    if (!welcomeGoldenShowAt)
      return goldenCoinDropped.value && !welcomeGoldenDroppedTicked.value;

    return (
      goldenCoinDropped.value &&
      !welcomeGoldenDroppedTicked.value &&
      isDifferentDay(date, String(welcomeGoldenShowAt))
    );
  });

  const isDisableBonus = computed(() => {
    if (!seasonCode.value || !user.value) return false;
    if (seasonCode.value === 'VN')
      return isSeasonStarting.value && !user.value.verified_mobile_number_at;
    // default SG country
    return isSeasonStarting.value && !user.value.mobile_number;
  });

  // Hours Bonus
  const hoursBonusCountdown = computed(() => {
    if (!user.value?.last_claim_bonus) {
      storeDialog.hoursBonusTicked = false;
      return null;
    }
    const time = new Date(user.value?.last_claim_bonus);
    const cd = +new Date(dayjs(time).add(2, 'hour').toISOString());
    if (cd < now.value) return null;
    return {
      manual: convertTime(cd - now.value),
      auto: timeCountDown(cd - now.value),
    };
  });

  // Coin Limit
  const coinLimitCountdown = computed(() => {
    if (
      !isSeasonStarting.value ||
      !user.value?.coin_lock_until ||
      +new Date(user.value?.coin_lock_until) < now.value
    )
      return null;

    const time = +new Date(user.value?.coin_lock_until);

    return {
      manual: convertTime(time - now.value),
      auto: timeCountDown(time - now.value),
    };
  });

  const date = computed(() => dayjs(now.value));

  const startTime = computed(() => {
    return dayjs().hour(10).minute(0).second(0);
  });

  const endTime = computed(() => {
    return dayjs().hour(21).minute(59).second(59);
  });

  const isInTimeRange = computed(() => {
    if (!isSeasonStarting.value) return false;
    return date.value.isBetween(startTime.value, endTime.value);
  });

  const isTriggerHoursBonus = computed(() => {
    if (
      !isSeasonStarting.value ||
      !user.value?.verified_mobile_number_at ||
      !features.value?.online_bonus
    )
      return false;
    return (
      !hoursBonusCountdown.value &&
      isInTimeRange.value &&
      !isDisableBonus.value &&
      !hoursBonusTicked.value
    );
  });

  // Daily Login
  const isClaimDailyLogin = computed(() => {
    if (!user.value || !isSeasonStarting.value) return false;
    if (!user.value.claimed_daily_reward.length) return true;

    return (
      user.value.claimed_daily_reward.length < 8 &&
      isDifferentDay(
        user.value.claimed_daily_reward[
          user.value.claimed_daily_reward.length - 1
        ],
        new Date().toISOString()
      )
    );
  });

  const isTriggerDailyLogin = computed(() => {
    if (!user.value?.verified_mobile_number_at || !features.value?.daily_reward)
      return false;
    return (
      isClaimDailyLogin.value &&
      !isDisableBonus.value &&
      !dailyLoginTicked.value
    );
  });

  const sameDay = computed(() => {
    if (!user.value?.claimed_daily_reward) return false;
    return user.value?.claimed_daily_reward.some(
      (d) => d === dateTimeFormat(+new Date(), 'YYYY-MM-DD')
    );
  });

  function checkNewCoinDrop() {
    if (!user.value || !groupedSilverCoins.value.ongoing.length) return;
    const coins = groupedSilverCoins.value.ongoing.filter(
      (c) =>
        c.properties.status === 'ongoing' &&
        !user.value?.coin_drop.includes(c.properties._id) &&
        +new Date(user.value?.created_at as string) <
          +new Date(c.properties.start_at)
    ) as unknown as { properties: ISilverCoin }[];

    if (!!coins.length) storeMap.setCoinDrop(coins.map((c) => c.properties));
  }

  const { execute: checkVerifications } = useAsync({
    async fn() {
      const { data } = await SILVER_COIN.getVerifications();
      return data;
    },
    onSuccess(res) {
      const data = first(res);
      if (!data) return;
      storeUser.setDataVerification({
        ...data,
        fromMap: true,
        brand_unique_id: data.brand_unique_id || data.coin.brand_unique_id,
      });

      if (!data.winner_info?.sent_video)
        openDialog('enter_serial_number', {
          stage: 'continue',
        });
    },
  });

  const isTriggerEnterSerialNumber = computed(() => {
    return (
      !!dataVerification.value &&
      !dataVerification.value.submit_info_at &&
      !enterSerialNumberTicked.value
    );
  });

  async function trackUserLocation(
    type: string,
    metadata?: Record<string, unknown>
  ) {
    if (!lastLocations.value.every(Boolean)) return;
    const [lng, lat] = lastLocations.value;
    const payload = deleteEmptyFields({
      lng,
      lat,
      type,
      metadata,
    }) as IPayloadTrackUserLocation;
    await USER.location(payload);
  }

  // const currentState = computed(() => {
  //   const dates = settings.value?.dates;
  //   if (!dates) return 0;
  //   const isInTime = (start: string, end: string) => {
  //     return (
  //       new Date(start).getTime() <= now.value &&
  //       now.value < new Date(end).getTime()
  //     );
  //   };
  //   switch (true) {
  //     case dayjs(now.value).isBefore(dayjs(dates.start_state_1)):
  //       return 0;
  //     case isInTime(dates.start_state_1, dates.end_state_1):
  //       return 1;
  //     case isInTime(dates.start_state_2, dates.end_state_2):
  //       return 2;
  //     case isInTime(dates.start_state_3, dates.end_state_3):
  //       return 3;
  //     case isInTime(dates.start_state_4, dates.end_state_4):
  //       return 4;
  //     case dayjs(now.value).isAfter(dayjs(dates.end_state_4)):
  //       return 5;
  //     default:
  //       return 0;
  //   }
  // });

  // const countdownContext = computed(() => {
  //   if ([0, 1, 2, 3].includes(currentState.value)) {
  //     return {
  //       state: currentState.value,
  //       countdown: settings.value?.dates?.end_state_3
  //         ? timeCountDown(
  //             +new Date(settings.value?.dates?.end_state_3) - now.value
  //           )
  //         : '',
  //       manualCountdown: settings.value?.dates?.end_state_3
  //         ? convertTime(
  //             +new Date(settings.value?.dates?.end_state_3) - now.value
  //           )
  //         : null,
  //     };
  //   }

  //   if (currentState.value === 4) {
  //     return {
  //       state: currentState.value,
  //       manualCountdown: settings.value?.dates?.end_state_4
  //         ? convertTime(
  //             +new Date(settings.value?.dates?.end_state_4) - now.value
  //           )
  //         : null,
  //     };
  //   }

  //   return {
  //     state: currentState.value,
  //   };
  // });

  // const isInSentosaBeachStation = computed(() => {
  //   if (!lastLocations.value.every(Boolean)) return false;
  //   const pt = point(lastLocations.value);
  //   const _poly = sentosaBeachPolygon.value?.features[0]?.geometry?.coordinates;

  //   return booleanPointInPolygon(pt, polygon(_poly));
  // });

  // const isInSentosa = computed(() => {
  //   const pt = point(lastLocations.value);
  //   const value = booleanPointInPolygon(
  //     pt,
  //     polygon(sentosaGeoJson.features[0].geometry.coordinates)
  //   );
  //   return value;
  // });

  // const isSentosaIslandBountyAvailable = computed(() => {
  //   if (!settings.value?.sentosa?.island_bounty) return false;
  //   const { start_at, end_at } = settings.value.sentosa.island_bounty;
  //   return +new Date(start_at) < now.value && now.value < +new Date(end_at);
  // });

  return {
    hoursBonusCountdown,
    isInTimeRange,
    isClaimDailyLogin,
    isTriggerWelcomeHunter,
    isTriggerWelcomeSafety,
    sameDay,
    isDisableBonus,
    isTriggerDailyLogin,
    isTriggerHoursBonus,
    isTriggerGPS,
    isLogged,
    listContestStarting,
    listContestEnded,
    holding_showed,
    isTriggerHolding,
    isTriggerEnterSerialNumber,
    isTriggerOTPAnnouncement,
    isTriggerAnnouncement,
    triggerBuildUpdate,
    isTriggerSubDomain,
    coinLimitCountdown,
    isTriggerGoldenCoinDropped,
    // isInSentosaBeachStation,
    // currentState,
    // countdownContext,
    // isInSentosa,
    // isSentosaIslandBountyAvailable,
    checkNewCoinDrop,
    checkVerifications,
    trackUserLocation,
  };
}
