import { ref, onMounted, onUnmounted, nextTick, readonly } from 'vue';
import { storeToRefs } from 'pinia';
import { useMapStore } from '@stores';
import { throttle } from 'quasar';

export function useViewportBounds() {
  const storeMap = useMapStore();
  const { mapIns } = storeToRefs(storeMap);

  const viewportBounds = ref({
    west: 0,
    east: 0,
    south: 0,
    north: 0,
  });

  const updateViewportBounds = () => {
    if (!mapIns.value) return;

    const bounds = mapIns.value.getBounds();
    viewportBounds.value = {
      west: bounds.getWest(),
      east: bounds.getEast(),
      south: bounds.getSouth(),
      north: bounds.getNorth(),
    };
  };

  const throttledUpdateViewportBounds = throttle(updateViewportBounds, 200);

  const isInViewport = (lngLat: [number, number]): boolean => {
    const [lng, lat] = lngLat;
    const bounds = viewportBounds.value;

    return (
      lng >= bounds.west &&
      lng <= bounds.east &&
      lat >= bounds.south &&
      lat <= bounds.north
    );
  };

  const setupViewportTracking = () => {
    if (!mapIns.value) return;

    updateViewportBounds();
    mapIns.value.on('moveend', throttledUpdateViewportBounds);
    mapIns.value.on('zoomend', throttledUpdateViewportBounds);
  };

  const cleanupViewportTracking = () => {
    if (!mapIns.value) return;

    mapIns.value.off('moveend', throttledUpdateViewportBounds);
    mapIns.value.off('zoomend', throttledUpdateViewportBounds);
  };

  onMounted(() => {
    nextTick(() => {
      setupViewportTracking();
    });
  });

  onUnmounted(() => {
    cleanupViewportTracking();
  });

  return {
    viewportBounds: readonly(viewportBounds),
    isInViewport,
  };
}
