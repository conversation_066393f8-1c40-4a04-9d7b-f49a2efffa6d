import { Howl } from 'howler';
import { last } from 'lodash';
import { MicroRoute } from 'vue-micro-route';
import { useUserStore, useMapStore } from '@stores';

interface IUseSound {
  bgmPlaying: Ref<string>;
  play: (name?: string) => Promise<void>;
  stop: () => void;
  changeVolume: (val?: number) => void;
}

export function useBackgroundMusic({
  currentRoute,
  routesGetter,
  defaultBgm,
}: {
  currentRoute: Ref<string>;
  routesGetter: MicroRoute[];
  defaultBgm?: string;
}) {
  const bgmPlaying = ref();
  let listBgm: Record<string, Howl> = {};

  const storeUser = useUserStore();
  const storeMap = useMapStore();
  const { user } = storeToRefs(storeUser);
  const { loading } = storeToRefs(storeMap);

  const background_volume = computed(
    () =>
      ((user.value?.setting.background_music === 0
        ? 0
        : user.value?.setting.background_music || 100) /
        100) *
      0.1
  );
  let currentVolume = user.value?.setting.sound_effect ?? 0;
  const pauseBgm = () => {
    if (bgmPlaying.value) {
      listBgm[bgmPlaying.value]?.fade(background_volume.value, 0, 1000);
      currentVolume = Number(user.value?.setting.sound_effect);
      user.value &&
        storeUser.updateUser({
          setting: {
            ...user.value?.setting,
            sound_effect: 0,
          },
        });
    }
  };

  const rePlay = () => {
    if (bgmPlaying.value) {
      listBgm[bgmPlaying.value]?.fade(0, background_volume.value, 1000);
      user.value &&
        storeUser.updateUser({
          setting: {
            ...user.value?.setting,
            sound_effect: currentVolume,
          },
        });
    }
  };

  const fadeBgm = async (volume = 1) => {
    return new Promise((resolve) => {
      listBgm[bgmPlaying.value]?.once('fade', () => {
        resolve(true);
      });
      listBgm[bgmPlaying.value]?.fade(volume, 0, volume * 10000);
    });
  };

  const play = async (name?: string) => {
    if (bgmPlaying.value === name) return;

    bgmPlaying.value && (await fadeBgm(background_volume.value));

    listBgm[bgmPlaying.value]?.stop();
    bgmPlaying.value = undefined;
    if (!name) return;
    if (!listBgm[name]) {
      const src = `/audios/${name}.mp3`;
      listBgm[name] = new Howl({
        src,
        autoplay: false,
        loop: true,
      });
    }
    listBgm[name].volume(background_volume.value);
    listBgm[name].play();
    bgmPlaying.value = name;
  };

  function stop() {
    listBgm[bgmPlaying.value]?.stop();
    bgmPlaying.value = undefined;
  }

  const handleLeave = () => {
    if (document.hidden) {
      pauseBgm();
    } else {
      rePlay();
    }
  };

  const changeVolume = (val = background_volume.value) => {
    listBgm[bgmPlaying.value]?.volume(val);
  };

  watch(background_volume, (val) => {
    changeVolume(val);
  });

  //play bgm per router
  watch(currentRoute, (newPath: string) => {
    if (!newPath) return;
    const currentRouter = routesGetter.find(
      (e) => e.path === last(newPath.split('/'))
    );
    if (!!currentRouter?.bgm) play(currentRouter?.bgm || defaultBgm);
  });

  onMounted(() => {
    const crr = routesGetter.find(
      (e) => e.path === last(currentRoute.value.split('/'))
    );
    if (!loading.value) play(crr?.bgm || defaultBgm);
    else play('landing');
    if (user.value) changeVolume();

    document.addEventListener('visibilitychange', handleLeave);
  });

  onBeforeUnmount(() => {
    document.removeEventListener('visibilitychange', handleLeave);

    for (const sound in listBgm) {
      listBgm[sound]?.unload();
    }
    listBgm = {};
  });

  provide<IUseSound>('bgm', { bgmPlaying, play, changeVolume, stop });
  return { bgmPlaying, play, stop, changeVolume };
}

const listSfx: Record<string, Howl> = {};

const playSFX = async (name: string, loop?: boolean) => {
  const { user } = storeToRefs(useUserStore());
  const sound_volume =
    ((user.value?.setting.sound_effect === 0
      ? 0
      : user.value?.setting.sound_effect || 100) /
      100) *
    0.1;
  if (!listSfx[name]) {
    const src = `/audios/${name}.mp3`;
    const sfx = new Howl({
      src,
      autoplay: false,
      loop: !!loop,
      onend: () => {
        if (!loop) sfx.stop();
      },
    });
    listSfx[name] = sfx;
  }
  listSfx[name].volume(sound_volume); //get volume from audiosetting
  listSfx[name].play();
  return listSfx[name];
};

export function useSound() {
  return inject('bgm') as IUseSound;
}

export { playSFX };
