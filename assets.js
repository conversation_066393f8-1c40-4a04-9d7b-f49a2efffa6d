// build.js
const fs = require('fs');
const path = require('path');

const directoryPath = path.join(__dirname, './public/imgs');
const outputPath = path.join(__dirname, './src/pages/assets.json');

function getFiles(dirPath) {
  let entries = fs.readdirSync(dirPath, { withFileTypes: true });

  let filePaths = entries
    .filter((entry) => !entry.isDirectory())
    .map((entry) =>
      path.relative(directoryPath, path.join(dirPath, entry.name))
    );

  let dirPaths = entries.filter((entry) => entry.isDirectory());

  for (let dir of dirPaths) {
    filePaths = filePaths.concat(getFiles(path.join(dirPath, dir.name)));
  }

  return filePaths;
}

let files = getFiles(directoryPath);

fs.writeFile(outputPath, JSON.stringify(files), (err) => {
  if (err) {
    console.error('Unable to write file: ' + err);
  }
});
